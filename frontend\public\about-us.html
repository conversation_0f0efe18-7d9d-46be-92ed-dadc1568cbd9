<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>من نحن | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            font-weight: 600;
            line-height: 1.6;
            color: #333;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.5rem;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
        }

        .logo-icon {
            background: white;
            color: #667eea;
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #ffd700;
        }

        .back-home {
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 20px;
            text-decoration: none;
            color: white;
            transition: background 0.3s;
        }

        .back-home:hover {
            background: rgba(255,255,255,0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 100px 0;
            color: white;
            text-align: center;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .hero p {
            font-size: 1.3rem;
            opacity: 0.9;
            line-height: 1.8;
        }

        /* Main Content */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            padding: 80px 0;
        }

        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .section-subtitle {
            font-size: 1.2rem;
            text-align: center;
            color: #666;
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Story Section */
        .story-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            margin-bottom: 80px;
        }

        .story-content h3 {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #667eea;
        }

        .story-content p {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .story-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
        }

        /* Values Section */
        .values {
            background: #f8f9fa;
        }

        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .value-card {
            background: white;
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .value-card:hover {
            transform: translateY(-10px);
        }

        .value-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }

        .value-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        .value-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Team Section */
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
        }

        .team-member {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .team-member:hover {
            transform: translateY(-5px);
        }

        .member-photo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
        }

        .member-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }

        .member-position {
            color: #667eea;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .member-bio {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        /* Stats Section */
        .stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 3rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .stat-item p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Timeline Section */
        .timeline {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }

        .timeline::before {
            content: '';
            position: absolute;
            right: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #667eea;
            transform: translateX(50%);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 50px;
            width: 45%;
        }

        .timeline-item:nth-child(odd) {
            margin-right: auto;
            margin-left: 55%;
        }

        .timeline-item:nth-child(even) {
            margin-left: auto;
            margin-right: 55%;
        }

        .timeline-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        .timeline-year {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 15px;
        }

        .timeline-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .timeline-description {
            color: #666;
            line-height: 1.6;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .story-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .timeline::before {
                right: 20px;
                transform: none;
            }

            .timeline-item {
                width: 100%;
                margin-right: 0;
                margin-left: 40px;
            }

            .timeline-item:nth-child(odd),
            .timeline-item:nth-child(even) {
                margin-right: 0;
                margin-left: 40px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                <div class="logo-icon">🚚</div>
                <span>شركة الشحن السريع</span>
            </div>
            
            <ul class="nav-menu">
                <li><a href="home.html">الرئيسية</a></li>
                <li><a href="about-us.html">من نحن</a></li>
                <li><a href="success-partners.html">شركاء النجاح</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            
            <a href="home.html" class="back-home">← العودة للرئيسية</a>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>من نحن</h1>
            <p>نحن شركة رائدة في مجال الشحن والخدمات اللوجستية، نسعى لتقديم أفضل الحلول المبتكرة لعملائنا في جميع أنحاء المملكة العربية السعودية ودول الخليج</p>
        </div>
    </section>

    <!-- Story Section -->
    <section class="section">
        <div class="container">
            <div class="story-grid">
                <div class="story-content">
                    <h3>قصتنا</h3>
                    <p>بدأت رحلتنا في عام 2010 برؤية واضحة: تقديم خدمات شحن موثوقة وسريعة تلبي احتياجات السوق السعودي المتنامي. من خلال الاستثمار في التكنولوجيا المتقدمة والفريق المتخصص، نجحنا في بناء شبكة واسعة من الخدمات.</p>
                    <p>اليوم، نفخر بكوننا واحدة من أسرع شركات الشحن نمواً في المنطقة، حيث نخدم آلاف العملاء يومياً ونوصل مئات الآلاف من الطرود شهرياً.</p>
                </div>
                <div class="story-image">
                    📦
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="section values">
        <div class="container">
            <h2 class="section-title">قيمنا ومبادئنا</h2>
            <p class="section-subtitle">نؤمن بمجموعة من القيم الأساسية التي توجه عملنا وتحدد هويتنا</p>

            <div class="values-grid">
                <div class="value-card">
                    <div class="value-icon">🎯</div>
                    <h3>الجودة والدقة</h3>
                    <p>نلتزم بأعلى معايير الجودة في جميع خدماتنا، ونضمن وصول شحناتكم في الوقت المحدد وبأفضل حالة</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">🤝</div>
                    <h3>الثقة والشفافية</h3>
                    <p>نبني علاقات طويلة الأمد مع عملائنا من خلال الشفافية الكاملة في التعامل والأسعار</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">⚡</div>
                    <h3>السرعة والكفاءة</h3>
                    <p>نستخدم أحدث التقنيات والأنظمة لضمان سرعة التسليم وكفاءة العمليات</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">🌟</div>
                    <h3>التميز والابتكار</h3>
                    <p>نسعى دائماً للتطوير والابتكار في خدماتنا لنقدم تجربة استثنائية لعملائنا</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">🛡️</div>
                    <h3>الأمان والحماية</h3>
                    <p>نضمن أمان شحناتكم من خلال أنظمة حماية متقدمة وتأمين شامل</p>
                </div>

                <div class="value-card">
                    <div class="value-icon">💚</div>
                    <h3>المسؤولية البيئية</h3>
                    <p>نلتزم بالممارسات الصديقة للبيئة ونساهم في بناء مستقبل مستدام</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="section stats">
        <div class="container">
            <h2 class="section-title">أرقامنا تتحدث</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>500,000+</h3>
                    <p>شحنة سنوياً</p>
                </div>
                <div class="stat-item">
                    <h3>15,000+</h3>
                    <p>عميل راضٍ</p>
                </div>
                <div class="stat-item">
                    <h3>25+</h3>
                    <p>مدينة نخدمها</p>
                </div>
                <div class="stat-item">
                    <h3>99.2%</h3>
                    <p>معدل التسليم الناجح</p>
                </div>
                <div class="stat-item">
                    <h3>14</h3>
                    <p>سنة من الخبرة</p>
                </div>
                <div class="stat-item">
                    <h3>24/7</h3>
                    <p>خدمة العملاء</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Timeline Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">رحلتنا عبر السنين</h2>
            <p class="section-subtitle">تعرف على أهم المحطات في تاريخ شركتنا</p>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <span class="timeline-year">2010</span>
                        <h3 class="timeline-title">البداية</h3>
                        <p class="timeline-description">تأسيس الشركة في الرياض مع فريق صغير وحلم كبير لتطوير قطاع الشحن</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <span class="timeline-year">2013</span>
                        <h3 class="timeline-title">التوسع الأول</h3>
                        <p class="timeline-description">افتتاح فروع في جدة والدمام وبداية تغطية المدن الرئيسية</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <span class="timeline-year">2016</span>
                        <h3 class="timeline-title">التحول الرقمي</h3>
                        <p class="timeline-description">إطلاق منصة التتبع الإلكترونية وتطبيق الهاتف المحمول</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <span class="timeline-year">2019</span>
                        <h3 class="timeline-title">الشحن الدولي</h3>
                        <p class="timeline-description">بداية خدمات الشحن الدولي وتوقيع شراكات مع شركات عالمية</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <span class="timeline-year">2022</span>
                        <h3 class="timeline-title">الذكاء الاصطناعي</h3>
                        <p class="timeline-description">تطبيق تقنيات الذكاء الاصطناعي في تحسين المسارات وتوقع الطلب</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <span class="timeline-year">2024</span>
                        <h3 class="timeline-title">المستقبل</h3>
                        <p class="timeline-description">إطلاق خدمات جديدة ومبتكرة والتوسع في دول الخليج</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="section values">
        <div class="container">
            <h2 class="section-title">فريق القيادة</h2>
            <p class="section-subtitle">تعرف على الفريق الذي يقود رؤيتنا نحو المستقبل</p>

            <div class="team-grid">
                <div class="team-member">
                    <div class="member-photo">👨‍💼</div>
                    <h3 class="member-name">أحمد محمد السعد</h3>
                    <p class="member-position">الرئيس التنفيذي</p>
                    <p class="member-bio">خبرة تزيد عن 20 عاماً في قطاع الخدمات اللوجستية والشحن، حاصل على ماجستير إدارة الأعمال</p>
                </div>

                <div class="team-member">
                    <div class="member-photo">👩‍💼</div>
                    <h3 class="member-name">فاطمة أحمد الزهراني</h3>
                    <p class="member-position">مدير العمليات</p>
                    <p class="member-bio">متخصصة في تطوير العمليات وتحسين الكفاءة، مع خبرة 15 عاماً في إدارة سلاسل التوريد</p>
                </div>

                <div class="team-member">
                    <div class="member-photo">👨‍💻</div>
                    <h3 class="member-name">محمد علي القحطاني</h3>
                    <p class="member-position">مدير التكنولوجيا</p>
                    <p class="member-bio">خبير في تطوير الأنظمة والتحول الرقمي، قاد تطوير منصاتنا التقنية المتقدمة</p>
                </div>

                <div class="team-member">
                    <div class="member-photo">👩‍💻</div>
                    <h3 class="member-name">سارة خالد العتيبي</h3>
                    <p class="member-position">مدير خدمة العملاء</p>
                    <p class="member-bio">متخصصة في تجربة العملاء وضمان الجودة، تقود فريق خدمة العملاء المتميز</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Animate stats on scroll
        function animateStats() {
            const stats = document.querySelectorAll('.stat-item h3');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target;
                        const finalValue = target.textContent;
                        const numericValue = parseInt(finalValue.replace(/[^0-9]/g, ''));

                        if (numericValue) {
                            let current = 0;
                            const increment = numericValue / 50;
                            const timer = setInterval(() => {
                                current += increment;
                                if (current >= numericValue) {
                                    target.textContent = finalValue;
                                    clearInterval(timer);
                                } else {
                                    target.textContent = Math.floor(current) + finalValue.replace(/[0-9]/g, '');
                                }
                            }, 30);
                        }
                        observer.unobserve(target);
                    }
                });
            });

            stats.forEach(stat => observer.observe(stat));
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // تحسين وظائف الصفحة
        function initializeAboutPage() {
            try {
                animateStats();
                animateTimeline();
                console.log('تم تحميل صفحة من نحن بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل صفحة من نحن:', error);
            }
        }

        // تحريك عناصر الخط الزمني
        function animateTimeline() {
            const timelineItems = document.querySelectorAll('.timeline-item');
            if (!timelineItems.length) {
                console.warn('لم يتم العثور على عناصر الخط الزمني');
                return;
            }

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.3 });

            timelineItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(30px)';
                item.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(item);
            });
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            try {
                initializeAboutPage();
            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);

                // تهيئة احتياطية
                setTimeout(() => {
                    try {
                        animateStats();
                    } catch (fallbackError) {
                        console.error('خطأ في التهيئة الاحتياطية:', fallbackError);
                    }
                }, 1000);
            }
        });
    </script>

    <style>
        /* تطبيق خط SF Pro Arabic Display على جميع العناصر */
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, select, textarea, button, .btn {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }

        .nav a, .nav-links a {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }
    </style>
</body>
</html>
