export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  role: 'ADMIN' | 'MANAGER' | 'USER' | 'DISTRIBUTOR'
  language: 'ar' | 'en'
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone?: string
  language: 'ar' | 'en'
}

export interface AuthResponse {
  token: string
  user: User
}
