export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  role: 'ADMIN' | 'MANAGER' | 'USER' | 'DISTRIBUTOR'
  language: 'ar' | 'en'
  isActive: boolean
  twoFactorEnabled?: boolean
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface LoginWith2FACredentials {
  email: string
  password: string
  token: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone?: string
  language: 'ar' | 'en'
}

export interface AuthResponse {
  token?: string
  user?: User
  requiresTwoFactor?: boolean
  userId?: string
  usedBackupCode?: boolean
  remainingBackupCodes?: number
}

// Two-Factor Authentication Types
export interface TwoFactorSetup {
  secret: string
  qrCode: string
  manualEntryKey: string
}

export interface TwoFactorStatus {
  twoFactorEnabled: boolean
  backupCodesCount: number
}

export interface BackupCodesResponse {
  backupCodes: string[]
}

export interface TwoFactorVerification {
  token: string
}

export interface Enable2FARequest {
  token: string
  secret: string
}
