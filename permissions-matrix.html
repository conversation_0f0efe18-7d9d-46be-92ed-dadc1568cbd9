<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مصفوفة الصلاحيات - نظام إدارة الشحنات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --font-arabic: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1600px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .matrix-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filter-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-family: var(--font-arabic);
            background: white;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-arabic);
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .permissions-matrix {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            min-width: 1200px;
        }

        .permissions-matrix th,
        .permissions-matrix td {
            padding: 12px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .permissions-matrix th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .permissions-matrix th.permission-name {
            text-align: right;
            min-width: 300px;
            max-width: 400px;
        }

        .permissions-matrix td.permission-name {
            text-align: right;
            font-weight: 600;
            background: #f8f9fa;
            border-left: 3px solid var(--secondary-color);
        }

        .permission-description {
            font-size: 0.8rem;
            color: #666;
            margin-top: 3px;
        }

        .role-column {
            min-width: 120px;
        }

        .permission-check {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            text-align: center;
            line-height: 20px;
            font-weight: bold;
            color: white;
        }

        .check-yes {
            background: var(--success-color);
        }

        .check-no {
            background: #dee2e6;
            color: #6c757d;
        }

        .check-partial {
            background: var(--warning-color);
        }

        .category-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: bold;
            color: var(--primary-color);
            text-align: right;
        }

        .category-header td {
            border-top: 3px solid var(--secondary-color);
        }

        .permission-level {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-right: 8px;
        }

        .level-basic {
            background: #d4edda;
            color: #155724;
        }

        .level-advanced {
            background: #fff3cd;
            color: #856404;
        }

        .level-admin {
            background: #f8d7da;
            color: #721c24;
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-controls {
                justify-content: center;
            }

            .permissions-matrix {
                font-size: 0.8rem;
            }

            .permissions-matrix th,
            .permissions-matrix td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>📊</span>
                <span>نظام إدارة الشحنات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="user-management.html">إدارة المستخدمين</a>
                <a href="user-permissions-advanced.html">صلاحيات المستخدم</a>
                <a href="permissions-matrix.html" class="active">مصفوفة الصلاحيات</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1>📊 مصفوفة الصلاحيات</h1>
            <p>عرض شامل لجميع الصلاحيات والأدوار في النظام</p>
        </div>

        <div class="matrix-container">
            <!-- إحصائيات سريعة -->
            <div class="stats-summary">
                <div class="stat-card">
                    <div class="stat-number" id="totalPermissions">0</div>
                    <div class="stat-label">إجمالي الصلاحيات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalRoles">0</div>
                    <div class="stat-label">إجمالي الأدوار</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="adminPermissions">0</div>
                    <div class="stat-label">الصلاحيات الإدارية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="basicPermissions">0</div>
                    <div class="stat-label">الصلاحيات الأساسية</div>
                </div>
            </div>

            <!-- أدوات التحكم -->
            <div class="controls">
                <div class="filter-controls">
                    <label>فلترة حسب المستوى:</label>
                    <select class="filter-select" id="levelFilter" onchange="filterByLevel()">
                        <option value="all">جميع المستويات</option>
                        <option value="basic">أساسي</option>
                        <option value="advanced">متقدم</option>
                        <option value="admin">إداري</option>
                    </select>
                    
                    <label>فلترة حسب الفئة:</label>
                    <select class="filter-select" id="categoryFilter" onchange="filterByCategory()">
                        <option value="all">جميع الفئات</option>
                    </select>
                </div>
                
                <div>
                    <button class="btn btn-primary" onclick="exportMatrix()">📤 تصدير المصفوفة</button>
                    <button class="btn btn-success" onclick="printMatrix()">🖨️ طباعة</button>
                    <button class="btn btn-warning" onclick="resetFilters()">🔄 إعادة تعيين</button>
                </div>
            </div>

            <!-- مفتاح الرموز -->
            <div class="legend">
                <div class="legend-item">
                    <span class="permission-check check-yes">✓</span>
                    <span>مسموح</span>
                </div>
                <div class="legend-item">
                    <span class="permission-check check-no">✗</span>
                    <span>غير مسموح</span>
                </div>
                <div class="legend-item">
                    <span class="permission-check check-partial">~</span>
                    <span>مسموح جزئياً</span>
                </div>
                <div class="legend-item">
                    <span class="permission-level level-basic">أساسي</span>
                    <span class="permission-level level-advanced">متقدم</span>
                    <span class="permission-level level-admin">إداري</span>
                </div>
            </div>

            <!-- مصفوفة الصلاحيات -->
            <div style="overflow-x: auto;">
                <table class="permissions-matrix" id="permissionsMatrix">
                    <thead>
                        <tr>
                            <th class="permission-name">الصلاحية</th>
                            <th class="role-column">مدير النظام</th>
                            <th class="role-column">مدير</th>
                            <th class="role-column">موزع</th>
                            <th class="role-column">موظف</th>
                            <th class="role-column">مشاهد</th>
                        </tr>
                    </thead>
                    <tbody id="matrixBody">
                        <!-- سيتم ملء البيانات بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script src="js/database-simple.js"></script>
    <script>
        // الصلاحيات المتقدمة (نفس البيانات من الصفحة السابقة)
        const advancedPermissions = {
            general: {
                name: 'الصلاحيات العامة',
                permissions: [
                    { id: 'system_admin', name: 'إدارة البرنامج', desc: 'الوصول الكامل لإدارة النظام', level: 'admin' },
                    { id: 'backup_create', name: 'عمل نسخة احتياطية', desc: 'إنشاء نسخ احتياطية من البيانات', level: 'advanced' },
                    { id: 'backup_restore', name: 'استرجاع نسخة احتياطية', desc: 'استعادة البيانات من النسخ الاحتياطية', level: 'admin' },
                    { id: 'datetime_edit', name: 'تعديل التاريخ والوقت', desc: 'تعديل إعدادات التاريخ والوقت في النظام', level: 'admin' },
                    { id: 'user_active', name: 'مستخدم نشط', desc: 'تفعيل وإلغاء تفعيل المستخدمين', level: 'advanced' }
                ]
            },
            shipments: {
                name: 'إدارة الشحنات',
                permissions: [
                    { id: 'shipments_view', name: 'عرض الشحنات', desc: 'عرض قائمة الشحنات وتفاصيلها', level: 'basic' },
                    { id: 'shipments_create', name: 'إنشاء شحنة جديدة', desc: 'إضافة شحنات جديدة للنظام', level: 'basic' },
                    { id: 'shipments_edit', name: 'تعديل الشحنات', desc: 'تعديل بيانات الشحنات الموجودة', level: 'basic' },
                    { id: 'shipments_delete', name: 'حذف الشحنات', desc: 'حذف الشحنات من النظام', level: 'advanced' },
                    { id: 'shipments_track', name: 'تتبع الشحنات', desc: 'تتبع حالة الشحنات ومواقعها', level: 'basic' },
                    { id: 'shipments_print', name: 'طباعة الشحنات', desc: 'طباعة تفاصيل وإيصالات الشحنات', level: 'basic' }
                ]
            },
            customers: {
                name: 'إدارة العملاء',
                permissions: [
                    { id: 'customers_view', name: 'عرض العملاء', desc: 'عرض قائمة العملاء وبياناتهم', level: 'basic' },
                    { id: 'customers_create', name: 'إضافة عميل جديد', desc: 'إضافة عملاء جدد للنظام', level: 'basic' },
                    { id: 'customers_edit', name: 'تعديل بيانات العملاء', desc: 'تعديل معلومات العملاء الموجودين', level: 'basic' },
                    { id: 'customers_delete', name: 'حذف العملاء', desc: 'حذف العملاء من النظام', level: 'advanced' },
                    { id: 'customers_balance', name: 'رصيد العميل', desc: 'عرض وإدارة أرصدة العملاء', level: 'basic' }
                ]
            },
            financial: {
                name: 'الإدارة المالية',
                permissions: [
                    { id: 'treasury_view', name: 'عرض حركة الخزينة', desc: 'عرض تفاصيل الخزينة والأرصدة', level: 'basic' },
                    { id: 'treasury_receipts', name: 'تحليل المقبوضات', desc: 'تحليل وعرض المقبوضات المالية', level: 'basic' },
                    { id: 'treasury_expenses', name: 'تحليل المصروفات', desc: 'تحليل وعرض المصروفات المالية', level: 'basic' },
                    { id: 'treasury_transfer', name: 'تحويل بين الخزائن', desc: 'تحويل الأموال بين الخزائن', level: 'advanced' },
                    { id: 'installments', name: 'التقسيط', desc: 'إدارة عمليات التقسيط والأقساط', level: 'advanced' }
                ]
            },
            pricing: {
                name: 'إدارة الأسعار',
                permissions: [
                    { id: 'prices_edit', name: 'تعديل أسعار البيع', desc: 'تعديل أسعار الخدمات والمنتجات', level: 'advanced' },
                    { id: 'discount_add', name: 'إضافة خصم', desc: 'إضافة خصومات على الفواتير', level: 'basic' },
                    { id: 'sell_below_cost', name: 'البيع بأقل من التكلفة', desc: 'السماح بالبيع بأسعار أقل من التكلفة', level: 'admin' },
                    { id: 'tax_edit', name: 'تعديل الضريبة', desc: 'تعديل نسبة الضريبة', level: 'advanced' }
                ]
            },
            reports: {
                name: 'التقارير',
                permissions: [
                    { id: 'reports_daily', name: 'تقرير الحركة اليومية', desc: 'عرض تقرير مفصل للحركة اليومية', level: 'basic' },
                    { id: 'reports_sales', name: 'تقرير المبيعات', desc: 'تحليل مفصل لبيانات المبيعات', level: 'basic' },
                    { id: 'reports_profits', name: 'عرض الأرباح', desc: 'عرض تفاصيل الأرباح والخسائر', level: 'advanced' },
                    { id: 'reports_advanced', name: 'التقارير المتقدمة', desc: 'الوصول لجميع التقارير المتقدمة', level: 'advanced' }
                ]
            }
        };

        // أدوار النظام مع صلاحياتها
        const systemRoles = {
            admin: {
                name: 'مدير النظام',
                permissions: 'all' // جميع الصلاحيات
            },
            manager: {
                name: 'مدير',
                permissions: [
                    'backup_create', 'user_active',
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit', 'customers_balance',
                    'treasury_view', 'treasury_receipts', 'treasury_expenses', 'treasury_transfer',
                    'prices_edit', 'discount_add', 'tax_edit',
                    'reports_daily', 'reports_sales', 'reports_profits', 'reports_advanced'
                ]
            },
            distributor: {
                name: 'موزع',
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit', 'customers_balance',
                    'treasury_view', 'treasury_receipts', 'installments',
                    'discount_add', 'prices_edit',
                    'reports_daily', 'reports_sales'
                ]
            },
            employee: {
                name: 'موظف',
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit',
                    'treasury_view', 'discount_add',
                    'reports_daily'
                ]
            },
            viewer: {
                name: 'مشاهد',
                permissions: [
                    'shipments_view', 'shipments_track',
                    'customers_view', 'customers_balance',
                    'treasury_view',
                    'reports_daily', 'reports_sales'
                ]
            }
        };

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📍 تحميل مصفوفة الصلاحيات...');

            loadCategoryFilter();
            buildPermissionsMatrix();
            updateStats();

            console.log('✅ تم تحميل مصفوفة الصلاحيات بنجاح');
        });

        // تحميل فلتر الفئات
        function loadCategoryFilter() {
            const categoryFilter = document.getElementById('categoryFilter');

            Object.keys(advancedPermissions).forEach(categoryKey => {
                const category = advancedPermissions[categoryKey];
                const option = document.createElement('option');
                option.value = categoryKey;
                option.textContent = category.name;
                categoryFilter.appendChild(option);
            });
        }

        // بناء مصفوفة الصلاحيات
        function buildPermissionsMatrix() {
            const matrixBody = document.getElementById('matrixBody');
            matrixBody.innerHTML = '';

            Object.keys(advancedPermissions).forEach(categoryKey => {
                const category = advancedPermissions[categoryKey];

                // إضافة رأس الفئة
                const categoryRow = document.createElement('tr');
                categoryRow.className = 'category-header';
                categoryRow.innerHTML = `
                    <td colspan="6" class="category-header">
                        <strong>${category.name}</strong>
                    </td>
                `;
                matrixBody.appendChild(categoryRow);

                // إضافة صلاحيات الفئة
                category.permissions.forEach(permission => {
                    const row = document.createElement('tr');
                    row.className = `permission-row level-${permission.level} category-${categoryKey}`;

                    let permissionCell = `
                        <td class="permission-name">
                            <span class="permission-level level-${permission.level}">${getLevelText(permission.level)}</span>
                            <strong>${permission.name}</strong>
                            <div class="permission-description">${permission.desc}</div>
                        </td>
                    `;

                    // إضافة خلايا الأدوار
                    Object.keys(systemRoles).forEach(roleKey => {
                        const role = systemRoles[roleKey];
                        const hasPermission = checkRolePermission(roleKey, permission.id);

                        permissionCell += `
                            <td class="role-column">
                                <span class="permission-check ${hasPermission ? 'check-yes' : 'check-no'}">
                                    ${hasPermission ? '✓' : '✗'}
                                </span>
                            </td>
                        `;
                    });

                    row.innerHTML = permissionCell;
                    matrixBody.appendChild(row);
                });
            });
        }

        // فحص صلاحية الدور
        function checkRolePermission(roleKey, permissionId) {
            const role = systemRoles[roleKey];

            if (role.permissions === 'all') {
                return true; // مدير النظام له جميع الصلاحيات
            }

            return role.permissions.includes(permissionId);
        }

        // الحصول على نص المستوى
        function getLevelText(level) {
            const levels = {
                'basic': 'أساسي',
                'advanced': 'متقدم',
                'admin': 'إداري'
            };
            return levels[level] || level;
        }

        // تحديث الإحصائيات
        function updateStats() {
            let totalPermissions = 0;
            let adminPermissions = 0;
            let basicPermissions = 0;

            Object.values(advancedPermissions).forEach(category => {
                totalPermissions += category.permissions.length;
                category.permissions.forEach(permission => {
                    if (permission.level === 'admin') {
                        adminPermissions++;
                    } else if (permission.level === 'basic') {
                        basicPermissions++;
                    }
                });
            });

            document.getElementById('totalPermissions').textContent = totalPermissions;
            document.getElementById('totalRoles').textContent = Object.keys(systemRoles).length;
            document.getElementById('adminPermissions').textContent = adminPermissions;
            document.getElementById('basicPermissions').textContent = basicPermissions;
        }

        // فلترة حسب المستوى
        function filterByLevel() {
            const levelFilter = document.getElementById('levelFilter').value;
            const rows = document.querySelectorAll('.permission-row');

            rows.forEach(row => {
                if (levelFilter === 'all' || row.classList.contains(`level-${levelFilter}`)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // فلترة حسب الفئة
        function filterByCategory() {
            const categoryFilter = document.getElementById('categoryFilter').value;
            const rows = document.querySelectorAll('.permission-row, .category-header');

            rows.forEach(row => {
                if (categoryFilter === 'all') {
                    row.style.display = '';
                } else if (row.classList.contains(`category-${categoryFilter}`) ||
                          (row.classList.contains('category-header') &&
                           row.textContent.includes(advancedPermissions[categoryFilter].name))) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // إعادة تعيين الفلاتر
        function resetFilters() {
            document.getElementById('levelFilter').value = 'all';
            document.getElementById('categoryFilter').value = 'all';

            const rows = document.querySelectorAll('.permission-row, .category-header');
            rows.forEach(row => {
                row.style.display = '';
            });
        }

        // تصدير المصفوفة
        function exportMatrix() {
            let csvContent = 'الصلاحية,الوصف,المستوى,مدير النظام,مدير,موزع,موظف,مشاهد\n';

            Object.keys(advancedPermissions).forEach(categoryKey => {
                const category = advancedPermissions[categoryKey];

                // إضافة رأس الفئة
                csvContent += `\n"=== ${category.name} ===",,,,,,,\n`;

                category.permissions.forEach(permission => {
                    csvContent += `"${permission.name}","${permission.desc}","${getLevelText(permission.level)}"`;

                    Object.keys(systemRoles).forEach(roleKey => {
                        const hasPermission = checkRolePermission(roleKey, permission.id);
                        csvContent += `,${hasPermission ? 'نعم' : 'لا'}`;
                    });

                    csvContent += '\n';
                });
            });

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'permissions_matrix_' + new Date().toISOString().split('T')[0] + '.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert('✅ تم تصدير مصفوفة الصلاحيات بنجاح!');
        }

        // طباعة المصفوفة
        function printMatrix() {
            const printWindow = window.open('', '_blank');
            const matrixTable = document.getElementById('permissionsMatrix').outerHTML;

            printWindow.document.write(`
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>مصفوفة الصلاحيات</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                        th { background-color: #f2f2f2; }
                        .permission-name { text-align: right !important; }
                        .category-header { background-color: #e9ecef; font-weight: bold; }
                        .permission-level { padding: 2px 6px; border-radius: 3px; font-size: 0.8em; }
                        .level-basic { background: #d4edda; color: #155724; }
                        .level-advanced { background: #fff3cd; color: #856404; }
                        .level-admin { background: #f8d7da; color: #721c24; }
                        .permission-check { width: 20px; height: 20px; border-radius: 50%; display: inline-block; text-align: center; line-height: 20px; color: white; font-weight: bold; }
                        .check-yes { background: #28a745; }
                        .check-no { background: #dee2e6; color: #6c757d; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <h1>مصفوفة الصلاحيات - نظام إدارة الشحنات</h1>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                    ${matrixTable}
                    <script>
                        window.onload = function() {
                            setTimeout(function() {
                                window.print();
                                window.close();
                            }, 500);
                        }
                    </script>
                </body>
                </html>
            `);

            printWindow.document.close();
        }

        // إضافة أحداث لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'p':
                        e.preventDefault();
                        printMatrix();
                        break;
                    case 's':
                        e.preventDefault();
                        exportMatrix();
                        break;
                    case 'r':
                        e.preventDefault();
                        resetFilters();
                        break;
                }
            }
        });
    </script>
</body>
</html>
