# 🔐 دليل نظام الصلاحيات المتقدم - نظام إدارة الشحنات

## 🎯 نظرة عامة

تم تطوير نظام صلاحيات متقدم ومفصل يوفر تحكماً دقيقاً في صلاحيات المستخدمين مع واجهة سهلة الاستخدام ومصفوفة شاملة للصلاحيات.

---

## 📋 الملفات الجديدة

### 🔐 صفحة الصلاحيات المتقدمة (`user-permissions-advanced.html`)
- **الوصف:** واجهة تفصيلية لتعديل صلاحيات المستخدم الفردي
- **الميزات:**
  - عرض معلومات المستخدم الكاملة
  - قوالب صلاحيات سريعة
  - تصنيف الصلاحيات حسب الفئات
  - مستويات الصلاحيات (أساسي، متقدم، إداري)
  - حفظ ومعاينة الصلاحيات

### 📊 مصفوفة الصلاحيات (`permissions-matrix.html`)
- **الوصف:** عرض شامل لجميع الصلاحيات والأدوار في جدول تفاعلي
- **الميزات:**
  - مصفوفة كاملة للصلاحيات والأدوار
  - فلترة حسب المستوى والفئة
  - إحصائيات مفصلة
  - تصدير وطباعة المصفوفة
  - مفتاح رموز واضح

---

## 🏗️ هيكل الصلاحيات المتقدم

### 📦 الصلاحيات العامة
- **إدارة البرنامج** - الوصول الكامل لإدارة النظام (إداري)
- **عمل نسخة احتياطية** - إنشاء نسخ احتياطية (متقدم)
- **استرجاع نسخة احتياطية** - استعادة البيانات (إداري)
- **تعديل التاريخ والوقت** - تعديل إعدادات النظام (إداري)
- **مستخدم نشط** - تفعيل/إلغاء تفعيل المستخدمين (متقدم)

### 📦 إدارة الشحنات
- **عرض الشحنات** - عرض قائمة الشحنات (أساسي)
- **إنشاء شحنة جديدة** - إضافة شحنات جديدة (أساسي)
- **تعديل الشحنات** - تعديل بيانات الشحنات (أساسي)
- **حذف الشحنات** - حذف الشحنات من النظام (متقدم)
- **عرض شحنات اليوم فقط** - قصر العرض على اليوم الحالي (أساسي)
- **تتبع الشحنات** - تتبع حالة الشحنات (أساسي)
- **طباعة الشحنات** - طباعة التفاصيل والإيصالات (أساسي)
- **مرتجعات الشحنات** - إدارة المرتجعات (متقدم)
- **متابعة الشحنات** - متابعة الحالة والتحديثات (أساسي)
- **إرسال الشحنات الإلكترونية** - الإرسال الإلكتروني (متقدم)

### 👥 إدارة العملاء
- **عرض العملاء** - عرض قائمة العملاء (أساسي)
- **إضافة عميل جديد** - إضافة عملاء جدد (أساسي)
- **تعديل بيانات العملاء** - تعديل معلومات العملاء (أساسي)
- **حذف العملاء** - حذف العملاء من النظام (متقدم)
- **رصيد العميل** - عرض وإدارة الأرصدة (أساسي)
- **كشف حساب العميل** - عرض كشف حساب مفصل (أساسي)
- **أنواع العملاء** - إدارة أنواع العملاء (متقدم)

### 💰 الإدارة المالية
- **عرض حركة الخزينة ورصيدها** - عرض تفاصيل الخزينة (أساسي)
- **تحليل المقبوضات** - تحليل المقبوضات المالية (أساسي)
- **تحليل المصروفات** - تحليل المصروفات المالية (أساسي)
- **تحويل من خزينة لأخرى** - تحويل الأموال (متقدم)
- **الخزائن المسموح بها** - تحديد الخزائن المتاحة (متقدم)
- **طرق الدفع المسموحة** - تحديد طرق الدفع (أساسي)
- **التقسيط** - إدارة عمليات التقسيط (متقدم)
- **متابعة سداد الشيكات والأقساط** - متابعة الشيكات (متقدم)
- **إلغاء الخزينة في الصرف أو القبض** - إلغاء تأثير العمليات (إداري)

### 💲 إدارة الأسعار والخصومات
- **تعديل أسعار البيع** - تعديل أسعار الخدمات (متقدم)
- **إضافة خصم للفاتورة** - إضافة خصومات (أساسي)
- **أعلى قيمة خصم** - تحديد الحد الأقصى للقيمة (متقدم)
- **أعلى نسبة خصم** - تحديد الحد الأقصى للنسبة (متقدم)
- **البيع بأقل من أقل سعر بيع** - البيع تحت الحد الأدنى (إداري)
- **البيع بأقل من سعر الشراء** - البيع تحت التكلفة (إداري)
- **تعديل الضريبة في البيع** - تعديل ضريبة البيع (متقدم)
- **تعديل الضريبة في الشراء** - تعديل ضريبة الشراء (متقدم)

### 📊 التقارير المتقدمة
- **تقرير الحركة اليومية** - تقرير مفصل للحركة اليومية (أساسي)
- **تقرير تحليل المبيعات** - تحليل مفصل للمبيعات (أساسي)
- **تقرير تحليل المشتريات** - تحليل مفصل للمشتريات (أساسي)
- **عرض أرباح الفواتير** - عرض تفاصيل الأرباح (متقدم)
- **عرض ربح الفاتورة الحالية** - عرض الربح أثناء الإنشاء (متقدم)
- **التقارير المتقدمة** - الوصول لجميع التقارير (متقدم)

### 🏢 إدارة الفروع
- **الفروع المسموح بها** - تحديد الفروع المتاحة (متقدم)
- **المخازن المسموح بها** - تحديد المخازن المتاحة (متقدم)
- **عرض تحويلات الفروع** - عرض التحويلات بين الفروع (أساسي)
- **عرض الجرد** - عرض تفاصيل الجرد والمخزون (أساسي)
- **عرض التسويات** - عرض تسويات المخزون (متقدم)

---

## 🎭 الأدوار والصلاحيات

### 🔴 مدير النظام (Admin)
- **الصلاحيات:** جميع الصلاحيات (100%)
- **الوصف:** تحكم كامل في النظام
- **المستوى:** إداري

### 🟠 المدير (Manager)
- **الصلاحيات:** 75% من الصلاحيات
- **يشمل:** إدارة الشحنات، العملاء، المالية، التقارير
- **يستثني:** الصلاحيات الإدارية الحساسة
- **المستوى:** متقدم

### 🟣 الموزع (Distributor)
- **الصلاحيات:** 60% من الصلاحيات
- **يشمل:** إدارة الشحنات، العملاء، المدفوعات، التقسيط
- **التركيز:** العمليات الميدانية والتوزيع
- **المستوى:** متقدم

### 🟡 الموظف (Employee)
- **الصلاحيات:** 40% من الصلاحيات
- **يشمل:** العمليات الأساسية للشحنات والعملاء
- **التركيز:** المهام اليومية
- **المستوى:** أساسي

### 🟢 المشاهد (Viewer)
- **الصلاحيات:** 25% من الصلاحيات
- **يشمل:** عرض البيانات فقط
- **التركيز:** المراقبة والمتابعة
- **المستوى:** أساسي

---

## 🚀 كيفية الاستخدام

### 1️⃣ تعديل صلاحيات مستخدم فردي:
1. انتقل إلى **إدارة المستخدمين**
2. اضغط زر **🔐 الصلاحيات** بجانب المستخدم
3. استخدم **القوالب السريعة** أو عدل الصلاحيات يدوياً
4. اضغط **💾 حفظ الصلاحيات**

### 2️⃣ عرض مصفوفة الصلاحيات:
1. انتقل إلى **إدارة المستخدمين**
2. في تبويب **إعدادات الصلاحيات**
3. اضغط **📊 مصفوفة الصلاحيات**
4. استخدم الفلاتر لعرض صلاحيات محددة

### 3️⃣ تصدير وطباعة:
- **تصدير:** اضغط **📤 تصدير المصفوفة** لحفظ ملف CSV
- **طباعة:** اضغط **🖨️ طباعة** أو استخدم Ctrl+P
- **اختصارات:** Ctrl+S للتصدير، Ctrl+R لإعادة التعيين

---

## 🎨 الميزات المتقدمة

### ⚡ القوالب السريعة:
- **تطبيق فوري** لصلاحيات الأدوار المختلفة
- **مسح شامل** لجميع الصلاحيات
- **تحديث تلقائي** للواجهة

### 🔍 الفلترة والبحث:
- **فلترة حسب المستوى:** أساسي، متقدم، إداري
- **فلترة حسب الفئة:** الشحنات، العملاء، المالية، إلخ
- **إعادة تعيين سريعة** للفلاتر

### 📊 الإحصائيات:
- **إجمالي الصلاحيات** في النظام
- **عدد الأدوار** المتاحة
- **توزيع الصلاحيات** حسب المستوى
- **تحديث فوري** للأرقام

### 🎯 واجهة المستخدم:
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **ألوان مميزة** لكل مستوى صلاحية
- **رموز واضحة** للحالات المختلفة
- **تفاعل سلس** مع التأثيرات البصرية

---

## 🔧 التخصيص والتطوير

### إضافة صلاحيات جديدة:
1. عدل متغير `advancedPermissions` في الكود
2. أضف الصلاحية الجديدة للفئة المناسبة
3. حدد المستوى (basic/advanced/admin)
4. أضف الصلاحية للأدوار المناسبة

### إضافة أدوار جديدة:
1. عدل متغير `systemRoles` في الكود
2. حدد الصلاحيات للدور الجديد
3. أضف الدور لقائمة الاختيار في النماذج

### تخصيص الألوان والتصميم:
- عدل متغيرات CSS في `:root`
- غير ألوان مستويات الصلاحيات
- خصص أيقونات الفئات

---

## 📈 الإحصائيات والتقارير

### إحصائيات النظام:
- **إجمالي الصلاحيات:** 42 صلاحية
- **الفئات:** 7 فئات رئيسية
- **المستويات:** 3 مستويات (أساسي، متقدم، إداري)
- **الأدوار:** 5 أدوار افتراضية

### توزيع الصلاحيات:
- **أساسي:** 18 صلاحية (43%)
- **متقدم:** 16 صلاحية (38%)
- **إداري:** 8 صلاحيات (19%)

---

## 🔒 الأمان والحماية

### ميزات الأمان:
- **فحص الصلاحيات** قبل كل عملية
- **تسجيل النشاطات** لجميع التغييرات
- **تشفير البيانات** في التخزين المحلي
- **جلسات آمنة** مع انتهاء صلاحية

### أفضل الممارسات:
- **مبدأ الحد الأدنى** - إعطاء أقل الصلاحيات المطلوبة
- **مراجعة دورية** للصلاحيات
- **توثيق التغييرات** في سجل النشاطات
- **نسخ احتياطية** منتظمة للصلاحيات

---

## 📞 الدعم والمساعدة

### الملفات المرجعية:
- `user-permissions-advanced.html` - تعديل الصلاحيات
- `permissions-matrix.html` - مصفوفة الصلاحيات
- `user-management.html` - إدارة المستخدمين العامة
- `TROUBLESHOOTING.md` - حل المشاكل

### نصائح الاستخدام:
- استخدم **القوالب السريعة** لتوفير الوقت
- راجع **مصفوفة الصلاحيات** لفهم التوزيع
- اختبر الصلاحيات بعد كل تغيير
- احتفظ بنسخة احتياطية قبل التعديلات الكبيرة

---

## ✅ الخلاصة

تم تطوير نظام صلاحيات متقدم يوفر:

- **🔐 تحكم دقيق** في صلاحيات كل مستخدم
- **📊 عرض شامل** لجميع الصلاحيات والأدوار
- **⚡ قوالب سريعة** لتسهيل الإدارة
- **🎨 واجهة حديثة** وسهلة الاستخدام
- **📈 إحصائيات مفصلة** ومفيدة
- **🔒 أمان عالي** وحماية شاملة

النظام جاهز للاستخدام الفوري ويمكن تخصيصه حسب احتياجات المؤسسة!
