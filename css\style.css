/* Apple-inspired Design System with SF Pro Arabic */
/* نظام التصميم المستوحى من آبل مع خط SF Pro العربي */

@import url('fonts.css');

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Root Variables */
:root {
    /* Apple Colors */
    --color-primary: #007AFF;
    --color-primary-dark: #0056CC;
    --color-primary-light: #4DA2FF;
    
    --color-secondary: #34C759;
    --color-secondary-dark: #28A745;
    --color-secondary-light: #5DD87A;
    
    --color-warning: #FF9500;
    --color-warning-dark: #E6850E;
    --color-warning-light: #FFB340;
    
    --color-error: #FF3B30;
    --color-error-dark: #E6342A;
    --color-error-light: #FF6B61;
    
    /* Neutral Colors */
    --color-white: #FFFFFF;
    --color-gray-50: #F9FAFB;
    --color-gray-100: #F3F4F6;
    --color-gray-200: #E5E7EB;
    --color-gray-300: #D1D5DB;
    --color-gray-400: #9CA3AF;
    --color-gray-500: #6B7280;
    --color-gray-600: #4B5563;
    --color-gray-700: #374151;
    --color-gray-800: #1F2937;
    --color-gray-900: #111827;
    
    /* Background Colors */
    --bg-primary: var(--color-white);
    --bg-secondary: var(--color-gray-50);
    --bg-tertiary: var(--color-gray-100);
    
    /* Text Colors */
    --text-primary: var(--color-gray-900);
    --text-secondary: var(--color-gray-600);
    --text-tertiary: var(--color-gray-400);
    
    /* Border Colors */
    --border-light: var(--color-gray-200);
    --border-medium: var(--color-gray-300);
    --border-dark: var(--color-gray-400);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 20px;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Transitions */
    --transition-fast: 150ms ease-out;
    --transition-normal: 250ms ease-out;
    --transition-slow: 350ms ease-out;
}

/* Base Styles */
body {
    font-family: var(--font-arabic-display);
    font-weight: var(--font-weight-regular);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    line-height: var(--line-height-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1 {
    font-size: 2.25rem;
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin-bottom: var(--space-6);
}

h2 {
    font-size: 1.875rem;
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin-bottom: var(--space-5);
}

h3 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

h4 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    margin-bottom: var(--space-3);
}

p {
    font-size: 1rem;
    line-height: var(--line-height-relaxed);
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-family: var(--font-arabic-display);
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: var(--color-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--color-gray-100);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

.btn-secondary:hover {
    background: var(--color-gray-200);
    border-color: var(--border-medium);
}

.btn-success {
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
    color: var(--color-white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
    color: var(--color-white);
}

.btn-danger {
    background: linear-gradient(135deg, var(--color-error) 0%, var(--color-error-dark) 100%);
    color: var(--color-white);
}

.btn-small {
    padding: var(--space-2) var(--space-4);
    font-size: 0.875rem;
}

.btn-large {
    padding: var(--space-4) var(--space-8);
    font-size: 1.125rem;
}

/* Cards */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: var(--space-6);
    border: 1px solid var(--border-light);
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    border-bottom: 1px solid var(--border-light);
    padding-bottom: var(--space-4);
    margin-bottom: var(--space-6);
}

/* Forms */
.form-group {
    margin-bottom: var(--space-5);
}

.form-label {
    display: block;
    font-family: var(--font-arabic-display);
    font-size: 0.875rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    font-family: var(--font-arabic-display);
    font-size: 1rem;
    color: var(--text-primary);
    background: var(--bg-primary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-input::placeholder {
    color: var(--text-tertiary);
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-arabic-display);
}

.table th {
    background: var(--bg-tertiary);
    padding: var(--space-4) var(--space-6);
    text-align: right;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-light);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    color: var(--text-secondary);
    font-size: 0.9375rem;
}

.table tr:hover {
    background: var(--bg-secondary);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-family: var(--font-arabic-display);
    font-size: 0.75rem;
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-2xl);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-pending {
    background: rgba(255, 149, 0, 0.1);
    color: var(--color-warning-dark);
}

.status-transit {
    background: rgba(0, 122, 255, 0.1);
    color: var(--color-primary-dark);
}

.status-delivered {
    background: rgba(52, 199, 89, 0.1);
    color: var(--color-secondary-dark);
}

.status-cancelled {
    background: rgba(255, 59, 48, 0.1);
    color: var(--color-error-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --space-4: 0.75rem;
        --space-6: 1rem;
        --space-8: 1.5rem;
    }
    
    h1 {
        font-size: 1.875rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.25rem;
    }
    
    .btn {
        padding: var(--space-3) var(--space-4);
        font-size: 0.9375rem;
    }
    
    .card {
        padding: var(--space-4);
    }
    
    .table th,
    .table td {
        padding: var(--space-3) var(--space-4);
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: var(--color-gray-900);
        --bg-secondary: var(--color-gray-800);
        --bg-tertiary: var(--color-gray-700);
        --text-primary: var(--color-gray-100);
        --text-secondary: var(--color-gray-300);
        --text-tertiary: var(--color-gray-500);
        --border-light: var(--color-gray-700);
        --border-medium: var(--color-gray-600);
        --border-dark: var(--color-gray-500);
    }
}
