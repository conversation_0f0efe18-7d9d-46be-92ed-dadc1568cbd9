<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الشحنات الجديدة</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .data-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
            margin-left: 15px;
        }

        .card-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .card-details {
            margin: 15px 0;
        }

        .card-detail {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .back-btn {
                justify-content: center;
                width: 100%;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tabs-nav {
                flex-direction: column;
            }
            
            .tab-button {
                flex-direction: row;
                justify-content: center;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                    <span>🏠</span>
                    <span>العودة للوحة التحكم</span>
                    <span>←</span>
                </a>
                <div class="header-actions">
                    <button class="btn btn-info btn-sm" onclick="window.location.reload()">
                        <span>🔄</span> تحديث الصفحة
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportAllShipments()">
                        <span>📥</span> تصدير جميع الشحنات
                    </button>
                </div>
            </div>
            <h1>🚛 إدارة الشحنات الجديدة</h1>
            <p>نظام متطور لإدارة وتتبع جميع الشحنات بكفاءة عالية</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navigation Tabs -->
            <div class="tabs-nav">
                <button class="tab-button active" onclick="showTab('all-shipments')">
                    <span>📦</span>
                    <span>جميع الشحنات</span>
                </button>
                <button class="tab-button" onclick="showTab('pending-shipments')">
                    <span>⏳</span>
                    <span>الشحنات المعلقة</span>
                </button>
                <button class="tab-button" onclick="showTab('delivered-shipments')">
                    <span>✅</span>
                    <span>الشحنات المسلمة</span>
                </button>
                <button class="tab-button" onclick="showTab('cancelled-shipments')">
                    <span>❌</span>
                    <span>الشحنات الملغية</span>
                </button>
                <button class="tab-button" onclick="showTab('tracking')">
                    <span>📍</span>
                    <span>تتبع الشحنات</span>
                </button>
            </div>

            <!-- Tab Contents -->
            
            <!-- جميع الشحنات -->
            <div id="all-shipments" class="tab-content active">
                <div class="section-header">
                    <h2 class="section-title">📦 جميع الشحنات</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="addShipment()">
                            <span>➕</span> إضافة شحنة جديدة
                        </button>
                        <button class="btn btn-info" onclick="refreshShipments()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportShipments()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>
                
                <div id="allShipmentsGrid" class="data-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">📦</div>
                        <h3>لا توجد شحنات</h3>
                        <p>ابدأ بإضافة شحنة جديدة</p>
                        <button class="btn btn-primary" onclick="addShipment()">➕ إضافة شحنة</button>
                    </div>
                </div>
            </div>

            <!-- الشحنات المعلقة -->
            <div id="pending-shipments" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">⏳ الشحنات المعلقة</h2>
                    <div class="section-actions">
                        <button class="btn btn-warning" onclick="processPendingShipments()">
                            <span>⚡</span> معالجة سريعة
                        </button>
                        <button class="btn btn-info" onclick="refreshPendingShipments()">
                            <span>🔄</span> تحديث
                        </button>
                    </div>
                </div>
                
                <div id="pendingShipmentsGrid" class="data-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">⏳</div>
                        <h3>لا توجد شحنات معلقة</h3>
                        <p>جميع الشحنات قيد المعالجة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // متغيرات عامة
        let shipments = [];

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚛 تحميل نظام إدارة الشحنات الجديدة...');
            initializeShipments();
        });

        // تهيئة النظام
        function initializeShipments() {
            loadDefaultShipments();
            loadShipments();
            console.log('✅ تم تحميل نظام إدارة الشحنات بنجاح');
        }

        // تحميل البيانات الافتراضية
        function loadDefaultShipments() {
            if (!localStorage.getItem('new_shipments')) {
                const defaultShipments = [
                    {
                        id: 'SH001',
                        customerName: 'أحمد محمد علي',
                        customerPhone: '0501234567',
                        fromAddress: 'الرياض - حي النخيل',
                        toAddress: 'جدة - حي الصفا',
                        status: 'قيد التوصيل',
                        weight: '2.5 كيلو',
                        price: 45,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'SH002',
                        customerName: 'فاطمة سعد',
                        customerPhone: '0507654321',
                        fromAddress: 'الدمام - حي الشاطئ',
                        toAddress: 'الرياض - حي العليا',
                        status: 'تم التسليم',
                        weight: '1.2 كيلو',
                        price: 35,
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('new_shipments', JSON.stringify(defaultShipments));
                console.log('✅ تم إنشاء بيانات الشحنات الافتراضية');
            }
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = event.target.closest('.tab-button');
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            console.log(`📋 تم التبديل إلى تبويب: ${tabName}`);
        }

        // وظائف الشحنات
        function addShipment() {
            // إنشاء نموذج إضافة شحنة
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 800px;">
                    <div class="modal-header">
                        <h2>🚛 إضافة شحنة جديدة</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="addShipmentForm" style="text-align: right; direction: rtl;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                <div class="form-group">
                                    <label for="customerName">اسم العميل *</label>
                                    <input type="text" id="customerName" required placeholder="أدخل اسم العميل">
                                </div>
                                <div class="form-group">
                                    <label for="customerPhone">هاتف العميل *</label>
                                    <input type="tel" id="customerPhone" required placeholder="+966501234567">
                                </div>
                                <div class="form-group">
                                    <label for="fromAddress">من (العنوان) *</label>
                                    <input type="text" id="fromAddress" required placeholder="عنوان المرسل">
                                </div>
                                <div class="form-group">
                                    <label for="toAddress">إلى (العنوان) *</label>
                                    <input type="text" id="toAddress" required placeholder="عنوان المستلم">
                                </div>
                                <div class="form-group">
                                    <label for="weight">الوزن (كيلو) *</label>
                                    <input type="number" id="weight" required min="0.1" step="0.1" placeholder="1.5">
                                </div>
                                <div class="form-group">
                                    <label for="price">السعر (ريال) *</label>
                                    <input type="number" id="price" required min="1" placeholder="50">
                                </div>
                                <div class="form-group">
                                    <label for="shipmentType">نوع الشحنة</label>
                                    <select id="shipmentType">
                                        <option value="عادي">عادي</option>
                                        <option value="سريع">سريع</option>
                                        <option value="فوري">فوري</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="paymentMethod">طريقة الدفع</label>
                                    <select id="paymentMethod">
                                        <option value="نقدي">نقدي</option>
                                        <option value="آجل">آجل</option>
                                        <option value="مدفوع مسبقاً">مدفوع مسبقاً</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 20px;">
                                <label for="notes">ملاحظات</label>
                                <textarea id="notes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <button type="submit" class="btn btn-primary" style="margin-left: 10px;">✅ إضافة الشحنة</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // معالج إرسال النموذج
            document.getElementById('addShipmentForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newShipment = {
                    id: 'SHP' + String(Date.now()).slice(-6),
                    customerName: document.getElementById('customerName').value,
                    customerPhone: document.getElementById('customerPhone').value,
                    fromAddress: document.getElementById('fromAddress').value,
                    toAddress: document.getElementById('toAddress').value,
                    weight: parseFloat(document.getElementById('weight').value),
                    price: parseFloat(document.getElementById('price').value),
                    shipmentType: document.getElementById('shipmentType').value,
                    paymentMethod: document.getElementById('paymentMethod').value,
                    notes: document.getElementById('notes').value,
                    status: 'معلق',
                    createdAt: new Date().toISOString(),
                    trackingNumber: 'TRK' + String(Date.now()).slice(-9)
                };

                // إضافة للقائمة
                shipments.push(newShipment);

                // حفظ في التخزين المحلي
                localStorage.setItem('new_shipments', JSON.stringify(shipments));

                // تحديث الإحصائيات
                updateStatistics();

                // إغلاق النموذج
                modal.remove();

                alert('✅ تم إضافة الشحنة بنجاح!\nرقم التتبع: ' + newShipment.trackingNumber);
                console.log('➕ تم إضافة شحنة جديدة:', newShipment);
            });
        }

        function refreshShipments() {
            loadShipments();
            alert('✅ تم تحديث بيانات الشحنات!');
        }

        function exportShipments() {
            if (shipments.length === 0) {
                alert('❌ لا توجد شحنات للتصدير');
                return;
            }

            // إنشاء بيانات Excel
            const excelData = [
                ['رقم الشحنة', 'اسم العميل', 'هاتف العميل', 'من', 'إلى', 'الحالة', 'الوزن', 'السعر', 'تاريخ الإنشاء']
            ];

            shipments.forEach(shipment => {
                excelData.push([
                    shipment.id || '',
                    shipment.customerName || '',
                    shipment.customerPhone || '',
                    shipment.fromAddress || '',
                    shipment.toAddress || '',
                    shipment.status || '',
                    shipment.weight || '',
                    shipment.price || '',
                    new Date(shipment.createdAt).toLocaleDateString('ar-SA') || ''
                ]);
            });

            // تحويل إلى CSV مع دعم UTF-8
            const csvContent = '\uFEFF' + excelData.map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `الشحنات_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            alert('✅ تم تصدير بيانات الشحنات بصيغة Excel بنجاح!');
            console.log('✅ تم تصدير بيانات الشحنات');
        }

        function exportAllShipments() {
            exportShipments();
        }

        function loadShipments() {
            try {
                shipments = JSON.parse(localStorage.getItem('new_shipments') || '[]');
                console.log(`📦 تم تحميل ${shipments.length} شحنة`);
            } catch (error) {
                console.error('❌ خطأ في تحميل الشحنات:', error);
                shipments = [];
            }
        }

        // جعل الوظائف متاحة عالمياً
        window.addShipment = addShipment;
        window.refreshShipments = refreshShipments;
        window.exportShipments = exportShipments;
        window.exportAllShipments = exportAllShipments;
        window.showTab = showTab;

        console.log('🚛 تم تحميل نظام إدارة الشحنات الجديدة بنجاح!');
    </script>
</body>
</html>
