<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام | تشخيص الملفات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .check-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .check-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .file-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-item.exists {
            border-left-color: #28a745;
        }

        .file-item.missing {
            border-left-color: #dc3545;
        }

        .file-item.checking {
            border-left-color: #ffc107;
        }

        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status.exists {
            background: #d4edda;
            color: #155724;
        }

        .status.missing {
            background: #f8d7da;
            color: #721c24;
        }

        .status.checking {
            background: #fff3cd;
            color: #856404;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.danger {
            background: #dc3545;
        }

        .summary {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        .summary h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .quick-link {
            background: #667eea;
            color: white;
            text-decoration: none;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s;
        }

        .quick-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .quick-link.disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فحص النظام</h1>
            <p>تشخيص شامل لجميع ملفات النظام</p>
        </div>

        <div class="content">
            <div class="check-section">
                <h2>🔐 ملفات المصادقة</h2>
                <div id="authFiles"></div>
            </div>

            <div class="check-section">
                <h2>🏠 لوحات التحكم</h2>
                <div id="dashboardFiles"></div>
            </div>

            <div class="check-section">
                <h2>📄 صفحات الموقع</h2>
                <div id="websiteFiles"></div>
            </div>

            <div class="check-section">
                <h2>🎨 ملفات التصميم</h2>
                <div id="styleFiles"></div>
            </div>

            <div class="check-section">
                <h2>🛠️ أدوات الإدارة</h2>
                <div id="adminFiles"></div>
            </div>

            <div class="summary" id="summary">
                <h3>جاري الفحص... <span class="loading"></span></h3>
                <p>يرجى الانتظار حتى انتهاء فحص جميع الملفات</p>
            </div>

            <div class="quick-links" id="quickLinks" style="display: none;">
                <a href="start-here.html" class="quick-link">🏠 نقطة البداية</a>
                <a href="unified-login.html" class="quick-link" id="loginLink">🔐 تسجيل الدخول</a>
                <a href="customer-dashboard.html" class="quick-link" id="customerLink">👤 لوحة العملاء</a>
                <a href="main-dashboard.html" class="quick-link" id="adminLink">👨‍💼 لوحة المديرين</a>
                <a href="pages-management.html" class="quick-link" id="pagesLink">📄 إدارة الصفحات</a>
                <a href="home.html" class="quick-link" id="homeLink">🌐 الصفحة الرئيسية</a>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button class="btn" onclick="runCheck()">🔄 إعادة الفحص</button>
                <button class="btn success" onclick="openWorkingFiles()">✅ فتح الملفات العاملة</button>
                <button class="btn danger" onclick="showMissingFiles()">❌ عرض الملفات المفقودة</button>
            </div>
        </div>
    </div>

    <script>
        const filesToCheck = {
            auth: [
                { name: 'تسجيل الدخول الموحد', file: 'unified-login.html' },
                { name: 'تسجيل العملاء الجدد', file: 'customer-register.html' },
                { name: 'صفحة الدخول القديمة', file: 'login.html' }
            ],
            dashboard: [
                { name: 'لوحة تحكم العملاء', file: 'customer-dashboard.html' },
                { name: 'لوحة تحكم المديرين', file: 'main-dashboard.html' },
                { name: 'لوحة التحكم ثلاثية الأبعاد', file: 'dashboard-3d.html' }
            ],
            website: [
                { name: 'الصفحة الرئيسية', file: 'home.html' },
                { name: 'صفحة من نحن', file: 'about-us.html' },
                { name: 'شركاء النجاح', file: 'success-partners.html' },
                { name: 'تتبع الشحنات', file: 'shipment-tracking.html' }
            ],
            style: [
                { name: 'ملف الخطوط', file: 'css/fonts.css' },
                { name: 'ملف التصميم الرئيسي', file: 'css/style.css' },
                { name: 'نظام التصميم المشترك', file: 'shared/design-system.css' }
            ],
            admin: [
                { name: 'إدارة الصفحات', file: 'pages-management.html' },
                { name: 'المحرر البصري', file: 'visual-page-editor.html' },
                { name: 'إدارة المستخدمين', file: 'user-management.html' },
                { name: 'التقارير', file: 'reports.html' },
                { name: 'الإعدادات', file: 'settings.html' }
            ]
        };

        let checkResults = {};
        let totalFiles = 0;
        let checkedFiles = 0;

        function createFileItem(name, file, status = 'checking') {
            const statusText = {
                'checking': '🔄 جاري الفحص...',
                'exists': '✅ موجود',
                'missing': '❌ مفقود'
            };

            return `
                <div class="file-item ${status}" id="file-${file.replace(/[^a-zA-Z0-9]/g, '_')}">
                    <div>
                        <strong>${name}</strong>
                        <br><small style="color: #666;">${file}</small>
                    </div>
                    <div class="status ${status}">${statusText[status]}</div>
                </div>
            `;
        }

        function updateFileStatus(file, exists) {
            const fileId = 'file-' + file.replace(/[^a-zA-Z0-9]/g, '_');
            const element = document.getElementById(fileId);
            
            if (element) {
                const status = exists ? 'exists' : 'missing';
                element.className = `file-item ${status}`;
                
                const statusElement = element.querySelector('.status');
                statusElement.className = `status ${status}`;
                statusElement.textContent = exists ? '✅ موجود' : '❌ مفقود';
            }
            
            checkResults[file] = exists;
            checkedFiles++;
            
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('summary');
            const quickLinks = document.getElementById('quickLinks');
            
            if (checkedFiles >= totalFiles) {
                const existingFiles = Object.values(checkResults).filter(exists => exists).length;
                const missingFiles = totalFiles - existingFiles;
                
                summary.innerHTML = `
                    <h3>📊 نتائج الفحص</h3>
                    <p><strong>${existingFiles}</strong> ملف موجود من أصل <strong>${totalFiles}</strong></p>
                    ${missingFiles > 0 ? `<p style="color: #dc3545;"><strong>${missingFiles}</strong> ملف مفقود</p>` : '<p style="color: #28a745;">جميع الملفات موجودة! ✅</p>'}
                `;
                
                // تحديث الروابط السريعة
                updateQuickLinks();
                quickLinks.style.display = 'grid';
            } else {
                const progress = Math.round((checkedFiles / totalFiles) * 100);
                summary.innerHTML = `
                    <h3>جاري الفحص... ${progress}% <span class="loading"></span></h3>
                    <p>تم فحص ${checkedFiles} من ${totalFiles} ملف</p>
                `;
            }
        }

        function updateQuickLinks() {
            const links = {
                'loginLink': 'unified-login.html',
                'customerLink': 'customer-dashboard.html',
                'adminLink': 'main-dashboard.html',
                'pagesLink': 'pages-management.html',
                'homeLink': 'home.html'
            };
            
            Object.entries(links).forEach(([linkId, file]) => {
                const link = document.getElementById(linkId);
                if (link) {
                    if (!checkResults[file]) {
                        link.classList.add('disabled');
                        link.onclick = (e) => {
                            e.preventDefault();
                            alert(`الملف ${file} غير موجود!`);
                        };
                    }
                }
            });
        }

        async function checkFile(file) {
            try {
                const response = await fetch(file, { method: 'HEAD' });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        async function runCheck() {
            checkResults = {};
            checkedFiles = 0;
            totalFiles = 0;
            
            // حساب إجمالي الملفات
            Object.values(filesToCheck).forEach(category => {
                totalFiles += category.length;
            });
            
            // إنشاء عناصر الفحص
            Object.entries(filesToCheck).forEach(([category, files]) => {
                const container = document.getElementById(category + 'Files');
                container.innerHTML = '';
                
                files.forEach(({ name, file }) => {
                    container.innerHTML += createFileItem(name, file);
                });
            });
            
            // بدء الفحص
            updateSummary();
            
            for (const [category, files] of Object.entries(filesToCheck)) {
                for (const { file } of files) {
                    const exists = await checkFile(file);
                    updateFileStatus(file, exists);
                    
                    // تأخير صغير لتحسين تجربة المستخدم
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
        }

        function openWorkingFiles() {
            const workingFiles = Object.entries(checkResults)
                .filter(([file, exists]) => exists)
                .map(([file]) => file);
            
            if (workingFiles.length === 0) {
                alert('لا توجد ملفات عاملة!');
                return;
            }
            
            const importantFiles = [
                'unified-login.html',
                'customer-dashboard.html', 
                'main-dashboard.html',
                'home.html'
            ];
            
            const filesToOpen = workingFiles.filter(file => importantFiles.includes(file));
            
            if (filesToOpen.length > 0) {
                filesToOpen.forEach(file => {
                    window.open(file, '_blank');
                });
            } else {
                alert('الملفات المهمة غير موجودة!');
            }
        }

        function showMissingFiles() {
            const missingFiles = Object.entries(checkResults)
                .filter(([file, exists]) => !exists)
                .map(([file]) => file);
            
            if (missingFiles.length === 0) {
                alert('جميع الملفات موجودة! ✅');
            } else {
                alert(`الملفات المفقودة:\n\n${missingFiles.join('\n')}`);
            }
        }

        // بدء الفحص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runCheck, 500);
        });
    </script>
</body>
</html>
