<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المناديب الجديدة</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: #28a745;
            color: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .data-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #28a745;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
            margin-left: 15px;
        }

        .card-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .card-details {
            margin: 15px 0;
        }

        .card-detail {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            border-color: #28a745;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #28a745;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .back-btn {
                justify-content: center;
                width: 100%;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tabs-nav {
                flex-direction: column;
            }
            
            .tab-button {
                flex-direction: row;
                justify-content: center;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                    <span>🏠</span>
                    <span>العودة للوحة التحكم</span>
                    <span>←</span>
                </a>
                <div class="header-actions">
                    <button class="btn btn-info btn-sm" onclick="window.location.reload()">
                        <span>🔄</span> تحديث الصفحة
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportAllDistributors()">
                        <span>📥</span> تصدير جميع المناديب
                    </button>
                </div>
            </div>
            <h1>🚚 إدارة المناديب الجديدة</h1>
            <p>نظام متطور لإدارة المناديب والسائقين وتتبع أدائهم</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navigation Tabs -->
            <div class="tabs-nav">
                <button class="tab-button active" onclick="showTab('all-distributors')">
                    <span>🚚</span>
                    <span>جميع المناديب</span>
                </button>
                <button class="tab-button" onclick="showTab('active-distributors')">
                    <span>✅</span>
                    <span>المناديب النشطين</span>
                </button>
                <button class="tab-button" onclick="showTab('performance')">
                    <span>📊</span>
                    <span>تقييم الأداء</span>
                </button>
                <button class="tab-button" onclick="showTab('routes')">
                    <span>🗺️</span>
                    <span>إدارة المسارات</span>
                </button>
            </div>

            <!-- Tab Contents -->
            
            <!-- جميع المناديب -->
            <div id="all-distributors" class="tab-content active">
                <div class="section-header">
                    <h2 class="section-title">🚚 جميع المناديب</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="addDistributor()">
                            <span>➕</span> إضافة مندوب جديد
                        </button>
                        <button class="btn btn-info" onclick="refreshDistributors()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportDistributors()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalDistributors">0</div>
                        <div class="stat-label">إجمالي المناديب</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeDistributors">0</div>
                        <div class="stat-label">المناديب النشطين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalDeliveries">0</div>
                        <div class="stat-label">إجمالي التوصيلات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="avgRating">0</div>
                        <div class="stat-label">متوسط التقييم</div>
                    </div>
                </div>
                
                <div id="allDistributorsGrid" class="data-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">🚚</div>
                        <h3>لا توجد مناديب</h3>
                        <p>ابدأ بإضافة مندوب جديد</p>
                        <button class="btn btn-primary" onclick="addDistributor()">➕ إضافة مندوب</button>
                    </div>
                </div>
            </div>

            <!-- المناديب النشطين -->
            <div id="active-distributors" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">✅ المناديب النشطين</h2>
                    <div class="section-actions">
                        <button class="btn btn-warning" onclick="assignDeliveries()">
                            <span>📦</span> تعيين توصيلات
                        </button>
                        <button class="btn btn-info" onclick="trackDistributors()">
                            <span>📍</span> تتبع المواقع
                        </button>
                    </div>
                </div>
                
                <div id="activeDistributorsGrid" class="data-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">✅</div>
                        <h3>لا توجد مناديب نشطين</h3>
                        <p>لا يوجد مناديب نشطين حالياً</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // متغيرات عامة
        let distributors = [];

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚚 تحميل نظام إدارة المناديب الجديدة...');
            initializeDistributors();
        });

        // تهيئة النظام
        function initializeDistributors() {
            loadDefaultDistributors();
            loadDistributors();
            updateStatistics();
            console.log('✅ تم تحميل نظام إدارة المناديب بنجاح');
        }

        // تحميل البيانات الافتراضية
        function loadDefaultDistributors() {
            if (!localStorage.getItem('new_distributors')) {
                const defaultDistributors = [
                    {
                        id: 'DIS001',
                        name: 'خالد أحمد الزهراني',
                        phone: '0551234567',
                        area: 'الرياض الشمالية',
                        vehicleType: 'بيك أب',
                        status: 'نشط',
                        totalDeliveries: 150,
                        rating: 4.8,
                        commission: 5,
                        joinDate: '2023-01-15',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'DIS002',
                        name: 'سعد محمد العتيبي',
                        phone: '0557654321',
                        area: 'الرياض الجنوبية',
                        vehicleType: 'شاحنة صغيرة',
                        status: 'نشط',
                        totalDeliveries: 120,
                        rating: 4.6,
                        commission: 5,
                        joinDate: '2023-02-10',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'DIS003',
                        name: 'عبدالله سالم القحطاني',
                        phone: '0559876543',
                        area: 'الرياض الشرقية',
                        vehicleType: 'دراجة نارية',
                        status: 'نشط',
                        totalDeliveries: 200,
                        rating: 4.9,
                        commission: 6,
                        joinDate: '2022-11-20',
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('new_distributors', JSON.stringify(defaultDistributors));
                console.log('✅ تم إنشاء بيانات المناديب الافتراضية');
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalDistributorsEl = document.getElementById('totalDistributors');
            const activeDistributorsEl = document.getElementById('activeDistributors');
            const totalDeliveriesEl = document.getElementById('totalDeliveries');
            const avgRatingEl = document.getElementById('avgRating');

            if (totalDistributorsEl) totalDistributorsEl.textContent = distributors.length;
            if (activeDistributorsEl) activeDistributorsEl.textContent = distributors.filter(d => d.status === 'نشط').length;
            if (totalDeliveriesEl) totalDeliveriesEl.textContent = distributors.reduce((sum, d) => sum + (d.totalDeliveries || 0), 0);
            if (avgRatingEl) {
                const avgRating = distributors.length > 0 ? 
                    (distributors.reduce((sum, d) => sum + (d.rating || 0), 0) / distributors.length).toFixed(1) : 0;
                avgRatingEl.textContent = avgRating;
            }
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = event.target.closest('.tab-button');
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            console.log(`📋 تم التبديل إلى تبويب: ${tabName}`);
        }

        // وظائف المناديب
        function addDistributor() {
            const name = prompt('أدخل اسم المندوب:');
            if (!name) return;

            const phone = prompt('أدخل رقم الهاتف:');
            if (!phone) return;

            const area = prompt('أدخل المنطقة:');
            if (!area) return;

            const vehicleType = prompt('أدخل نوع المركبة (بيك أب، شاحنة صغيرة، دراجة نارية):');
            if (!vehicleType) return;

            // إنشاء مندوب جديد
            const newDistributor = {
                id: 'DIS' + String(Date.now()).slice(-6),
                name: name,
                phone: phone,
                area: area,
                vehicleType: vehicleType,
                status: 'نشط',
                totalDeliveries: 0,
                rating: 5.0,
                commission: 5,
                joinDate: new Date().toISOString().split('T')[0],
                createdAt: new Date().toISOString()
            };

            // إضافة للقائمة
            distributors.push(newDistributor);

            // حفظ في التخزين المحلي
            localStorage.setItem('new_distributors', JSON.stringify(distributors));

            // تحديث الإحصائيات
            updateStatistics();

            alert('✅ تم إضافة المندوب بنجاح!');
            console.log('➕ تم إضافة مندوب جديد:', newDistributor);
        }

        function refreshDistributors() {
            loadDistributors();
            updateStatistics();
            alert('✅ تم تحديث بيانات المناديب!');
        }

        function exportDistributors() {
            if (distributors.length === 0) {
                alert('❌ لا توجد مناديب للتصدير');
                return;
            }

            // إنشاء بيانات Excel
            const excelData = [
                ['رقم المندوب', 'الاسم', 'الهاتف', 'المنطقة', 'نوع المركبة', 'الحالة', 'إجمالي التوصيلات', 'التقييم', 'العمولة %', 'تاريخ الانضمام']
            ];

            distributors.forEach(distributor => {
                excelData.push([
                    distributor.id || '',
                    distributor.name || '',
                    distributor.phone || '',
                    distributor.area || '',
                    distributor.vehicleType || '',
                    distributor.status || '',
                    distributor.totalDeliveries || 0,
                    distributor.rating || 0,
                    distributor.commission || 0,
                    distributor.joinDate || ''
                ]);
            });

            // تحويل إلى CSV مع دعم UTF-8
            const csvContent = '\uFEFF' + excelData.map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `المناديب_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            alert('✅ تم تصدير بيانات المناديب بصيغة Excel بنجاح!');
            console.log('✅ تم تصدير بيانات المناديب');
        }

        function exportAllDistributors() {
            exportDistributors();
        }

        function assignDeliveries() {
            alert('📦 سيتم فتح نظام تعيين التوصيلات...');
            console.log('📦 تعيين توصيلات');
        }

        function trackDistributors() {
            alert('📍 سيتم فتح نظام تتبع المواقع...');
            console.log('📍 تتبع المواقع');
        }

        function loadDistributors() {
            try {
                distributors = JSON.parse(localStorage.getItem('new_distributors') || '[]');
                console.log(`🚚 تم تحميل ${distributors.length} مندوب`);
            } catch (error) {
                console.error('❌ خطأ في تحميل المناديب:', error);
                distributors = [];
            }
        }

        // جعل الوظائف متاحة عالمياً
        window.addDistributor = addDistributor;
        window.refreshDistributors = refreshDistributors;
        window.exportDistributors = exportDistributors;
        window.exportAllDistributors = exportAllDistributors;
        window.assignDeliveries = assignDeliveries;
        window.trackDistributors = trackDistributors;
        window.showTab = showTab;

        console.log('🚚 تم تحميل نظام إدارة المناديب الجديدة بنجاح!');
    </script>
</body>
</html>
