@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    إعداد نظام إدارة الشحنات
echo    Shipment Management Setup
echo ========================================
echo.

echo 📦 تثبيت جميع التبعيات...
echo 📦 Installing all dependencies...
echo.

:: Install main dependencies
echo تثبيت التبعيات الرئيسية...
echo Installing main dependencies...
npm install

:: Install backend dependencies
echo.
echo تثبيت تبعيات الخلفية...
echo Installing backend dependencies...
cd backend
npm install

:: Copy .env file if it doesn't exist
if not exist ".env" (
    echo.
    echo نسخ ملف .env...
    echo Copying .env file...
    copy .env.example .env
    echo.
    echo ⚠️  يرجى تعديل ملف backend\.env بالبيانات الصحيحة:
    echo ⚠️  Please edit backend\.env with correct configuration:
    echo.
    echo DATABASE_URL="postgresql://username:password@localhost:5432/shipment_management"
    echo JWT_SECRET="your-super-secret-jwt-key-here-make-it-at-least-32-characters-long"
    echo.
)

cd ..

:: Install frontend dependencies
echo.
echo تثبيت تبعيات الواجهة الأمامية...
echo Installing frontend dependencies...
cd frontend
npm install
cd ..

echo.
echo ✅ تم إكمال الإعداد!
echo ✅ Setup completed!
echo.
echo الخطوات التالية:
echo Next steps:
echo.
echo 1. إنشاء قاعدة بيانات PostgreSQL باسم: shipment_management
echo    Create PostgreSQL database named: shipment_management
echo.
echo 2. تعديل ملف backend\.env بمعلومات قاعدة البيانات
echo    Edit backend\.env with database information
echo.
echo 3. تشغيل: npm run setup:db (لإعداد قاعدة البيانات)
echo    Run: npm run setup:db (to setup database)
echo.
echo 4. تشغيل: start.bat (لبدء النظام)
echo    Run: start.bat (to start the system)
echo.
pause
