{"name": "shipment-management-backend", "version": "1.0.0", "description": "Backend API for Shipment Management System", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "tsx database/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:setup": "npm run db:generate && npm run db:push && npm run db:seed", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@prisma/client": "^5.7.1", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "axios": "^1.6.2", "bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "qrcode": "^1.5.4", "socket.io": "^4.7.4", "speakeasy": "^2.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "jest": "^29.7.0", "prettier": "^3.1.0", "prisma": "^5.7.1", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["shipment", "delivery", "management", "api", "express", "typescript", "prisma", "postgresql"], "author": "عصام", "license": "MIT"}