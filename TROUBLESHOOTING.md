# 🔧 دليل حل المشاكل - نظام إدارة الشحنات

## 🚨 المشاكل الشائعة وحلولها

### 1. 🗄️ مشاكل قاعدة البيانات

#### **المشكلة: "خطأ في تحميل الفروع: db is not defined"**
**الأعراض:**
- رسالة خطأ عند تحميل الصفحة
- عدم عمل وظائف البحث والتتبع
- صفحة فارغة أو غير مكتملة

**الحلول:**
1. **إعادة تحميل الصفحة** - اضغط F5 أو Ctrl+R
2. **مسح ذاكرة التخزين المؤقت** - Ctrl+Shift+R
3. **استخدام أداة تنظيف البيانات:**
   - افتح `clear-data.html`
   - اضغط "مسح جميع البيانات"
   - انتقل إلى `login.html`

#### **المشكلة: "قاعدة البيانات غير متاحة"**
**الحلول:**
1. **تحقق من تحميل الملفات:**
   - تأكد من وجود ملف `js/database.js`
   - تحقق من عدم وجود أخطاء في وحدة تحكم المتصفح (F12)

2. **اختبار قاعدة البيانات:**
   - افتح `test-database.html`
   - شاهد نتائج الاختبارات
   - إذا فشلت، امسح البيانات وأعد المحاولة

### 2. 👤 مشاكل تسجيل الدخول

#### **المشكلة: عدم تطابق بيانات المستخدمين**
**الأعراض:**
- رسالة "البريد الإلكتروني أو كلمة المرور غير صحيحة"
- عرض عناوين بريد مختلفة في الصفحات

**الحل:**
استخدم البيانات المحدثة:
- **مدير النظام:** `<EMAIL> / admin123`
- **مدير:** `<EMAIL> / manager123`
- **موزع:** `<EMAIL> / distributor123`
- **مشاهد:** `<EMAIL> / viewer123`

### 3. 🔍 مشاكل تتبع الشحنات

#### **المشكلة: "لم يتم العثور على الشحنة"**
**الحلول:**
1. **استخدم أرقام التتبع التجريبية:**
   - `SHP001` - شحنة في الطريق
   - `SHP002` - شحنة مسلمة
   - `SHP003` - شحنة معلقة
   - `KWT001` - شحنة دولية من الكويت
   - `KWT002` - شحنة كويتية مسلمة

2. **تحقق من تحميل البيانات:**
   - افتح `test-database.html`
   - تأكد من وجود الشحنات في النظام

### 4. 🔐 مشاكل الصلاحيات

#### **المشكلة: "ليس لديك صلاحية للوصول"**
**الحلول:**
1. **تحقق من دورك:**
   - مدير النظام: جميع الصلاحيات
   - مدير: صلاحيات إدارية محدودة
   - موزع: إدارة الشحنات والمدفوعات
   - مشاهد: عرض فقط

2. **إعادة تسجيل الدخول:**
   - سجل خروج
   - امسح بيانات المستخدمين من `clear-data.html`
   - سجل دخول مرة أخرى

---

## 🛠️ أدوات حل المشاكل

### 🧹 أداة تنظيف البيانات (`clear-data.html`)
**الاستخدام:**
1. افتح الملف في المتصفح
2. اختر نوع التنظيف:
   - **مسح جميع البيانات:** إعادة تعيين كاملة
   - **مسح بيانات المستخدمين فقط:** حل مشاكل تسجيل الدخول
3. أكد العملية
4. انتقل إلى `login.html`

### 🧪 أداة اختبار قاعدة البيانات (`test-database.html`)
**الاستخدام:**
1. افتح الملف في المتصفح
2. شاهد نتائج الاختبارات التلقائية
3. اضغط "عرض البيانات" لرؤية المحتوى
4. إذا فشلت الاختبارات، استخدم أداة التنظيف

---

## 📋 خطوات حل المشاكل المنهجية

### المستوى الأول: حلول سريعة
1. **إعادة تحميل الصفحة** (F5)
2. **مسح ذاكرة التخزين المؤقت** (Ctrl+Shift+R)
3. **التحقق من وحدة تحكم المتصفح** (F12)

### المستوى الثاني: تنظيف البيانات
1. **افتح `clear-data.html`**
2. **اختر "مسح بيانات المستخدمين فقط"**
3. **أعد تسجيل الدخول**

### المستوى الثالث: إعادة تعيين كاملة
1. **افتح `clear-data.html`**
2. **اختر "مسح جميع البيانات"**
3. **انتقل إلى `login.html`**
4. **استخدم البيانات التجريبية**

### المستوى الرابع: فحص تقني
1. **افتح `test-database.html`**
2. **راجع نتائج الاختبارات**
3. **تحقق من وجود ملف `js/database.js`**
4. **راجع أخطاء وحدة التحكم**

---

## 🔍 تشخيص المشاكل

### فحص وحدة تحكم المتصفح
1. **اضغط F12** لفتح أدوات المطور
2. **انتقل إلى تبويب Console**
3. **ابحث عن رسائل الخطأ الحمراء**
4. **تحقق من رسائل التحميل الخضراء**

### رسائل التحميل الطبيعية
```
📍 تحميل صفحة تتبع الشحنات...
✅ تم تحميل قاعدة البيانات بنجاح - LocalDatabase v2.0
✅ تم تحميل قاعدة البيانات بنجاح
✅ تم تحميل صفحة التتبع بنجاح
```

### رسائل الخطأ الشائعة
- `db is not defined` → مشكلة في تحميل قاعدة البيانات
- `Cannot read property` → بيانات تالفة أو مفقودة
- `Unexpected token` → خطأ في تنسيق JSON

---

## 📞 الحصول على المساعدة

### معلومات مفيدة للدعم
عند طلب المساعدة، قدم المعلومات التالية:
1. **نوع المتصفح والإصدار**
2. **رسالة الخطأ الكاملة**
3. **الخطوات التي أدت للمشكلة**
4. **نتائج `test-database.html`**
5. **محتوى وحدة تحكم المتصفح**

### نصائح الوقاية
1. **لا تعدل ملفات JavaScript يدوياً**
2. **استخدم أدوات التنظيف بدلاً من حذف الملفات**
3. **احتفظ بنسخة احتياطية من المجلد**
4. **تحديث المتصفح بانتظام**

---

## ✅ التحقق من سلامة النظام

### قائمة فحص سريعة
- [ ] تحميل `login.html` بدون أخطاء
- [ ] تسجيل دخول ناجح بالبيانات التجريبية
- [ ] تحميل `shipment-tracking.html` مع رسالة الترحيب
- [ ] البحث عن `SHP001` يعطي نتائج
- [ ] `test-database.html` يظهر نتائج إيجابية
- [ ] `user-management.html` يعرض المستخدمين

### علامات النظام السليم
- ✅ رسائل تحميل خضراء في وحدة التحكم
- ✅ عرض البيانات التجريبية بشكل صحيح
- ✅ عمل جميع الأزرار والوظائف
- ✅ تطبيق الصلاحيات حسب دور المستخدم

---

**💡 نصيحة:** احتفظ بهذا الدليل مرجعاً سريعاً لحل أي مشاكل قد تواجهها في النظام.
