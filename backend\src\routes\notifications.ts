// Notifications Routes
// مسارات الإشعارات

import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient, NotificationType } from '@prisma/client'
import { asyncHandler, AppError } from '../middleware/errorHandler'
import { authenticate, requireManagerOrAdmin } from '../middleware/auth'
import { config } from '../config/config'

const router = Router()
const prisma = new PrismaClient()

// Validation schemas
const createNotificationSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  title: z.string().min(1, 'Title is required').max(255),
  message: z.string().min(1, 'Message is required'),
  type: z.enum(['INFO', 'WARNING', 'ERROR', 'SUCCESS']),
  shipmentId: z.string().uuid('Invalid shipment ID').optional(),
})

/**
 * @swagger
 * /api/notifications:
 *   get:
 *     summary: Get user notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Items per page
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *         description: Show only unread notifications
 *     responses:
 *       200:
 *         description: Notifications retrieved successfully
 */
router.get('/', authenticate, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1
  const limit = Math.min(parseInt(req.query.limit as string) || config.constants.defaultPageSize, config.constants.maxPageSize)
  const unreadOnly = req.query.unreadOnly === 'true'
  const skip = (page - 1) * limit

  const where: any = {
    userId: req.user!.id,
  }
  
  if (unreadOnly) {
    where.isRead = false
  }

  const [notifications, total, unreadCount] = await Promise.all([
    prisma.notification.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        shipment: {
          select: {
            id: true,
            trackingNumber: true,
            status: true,
          },
        },
      },
    }),
    prisma.notification.count({ where }),
    prisma.notification.count({
      where: {
        userId: req.user!.id,
        isRead: false,
      },
    }),
  ])

  const totalPages = Math.ceil(total / limit)

  res.json({
    success: true,
    data: {
      notifications,
      unreadCount,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    },
  })
}))

/**
 * @swagger
 * /api/notifications:
 *   post:
 *     summary: Create notification (Admin only)
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: Notification created successfully
 */
router.post('/', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const notificationData = createNotificationSchema.parse(req.body)
  
  // Verify user exists
  const user = await prisma.user.findUnique({
    where: { id: notificationData.userId },
    select: { id: true },
  })
  
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND')
  }
  
  // Verify shipment exists if provided
  if (notificationData.shipmentId) {
    const shipment = await prisma.shipment.findUnique({
      where: { id: notificationData.shipmentId },
      select: { id: true },
    })
    
    if (!shipment) {
      throw new AppError('Shipment not found', 404, 'SHIPMENT_NOT_FOUND')
    }
  }
  
  const notification = await prisma.notification.create({
    data: notificationData,
    include: {
      shipment: {
        select: {
          id: true,
          trackingNumber: true,
          status: true,
        },
      },
    },
  })
  
  res.status(201).json({
    success: true,
    data: { notification },
    message: 'Notification created successfully',
  })
}))

/**
 * @swagger
 * /api/notifications/{id}/read:
 *   patch:
 *     summary: Mark notification as read
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification marked as read
 */
router.patch('/:id/read', authenticate, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  // Verify notification belongs to user
  const notification = await prisma.notification.findFirst({
    where: {
      id,
      userId: req.user!.id,
    },
  })
  
  if (!notification) {
    throw new AppError('Notification not found', 404, 'NOTIFICATION_NOT_FOUND')
  }
  
  const updatedNotification = await prisma.notification.update({
    where: { id },
    data: { isRead: true },
  })
  
  res.json({
    success: true,
    data: { notification: updatedNotification },
    message: 'Notification marked as read',
  })
}))

/**
 * @swagger
 * /api/notifications/mark-all-read:
 *   patch:
 *     summary: Mark all notifications as read
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: All notifications marked as read
 */
router.patch('/mark-all-read', authenticate, asyncHandler(async (req, res) => {
  const result = await prisma.notification.updateMany({
    where: {
      userId: req.user!.id,
      isRead: false,
    },
    data: { isRead: true },
  })
  
  res.json({
    success: true,
    data: { updatedCount: result.count },
    message: `Marked ${result.count} notifications as read`,
  })
}))

/**
 * @swagger
 * /api/notifications/{id}:
 *   delete:
 *     summary: Delete notification
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification deleted successfully
 */
router.delete('/:id', authenticate, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  // Verify notification belongs to user or user is admin
  const where: any = { id }
  if (req.user!.role !== 'ADMIN') {
    where.userId = req.user!.id
  }
  
  const notification = await prisma.notification.findFirst({ where })
  
  if (!notification) {
    throw new AppError('Notification not found', 404, 'NOTIFICATION_NOT_FOUND')
  }
  
  await prisma.notification.delete({
    where: { id },
  })
  
  res.json({
    success: true,
    message: 'Notification deleted successfully',
  })
}))

/**
 * @swagger
 * /api/notifications/cleanup:
 *   delete:
 *     summary: Cleanup old notifications (Admin only)
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Old notifications cleaned up
 */
router.delete('/cleanup', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - config.constants.notificationRetentionDays)
  
  const result = await prisma.notification.deleteMany({
    where: {
      createdAt: { lt: cutoffDate },
      isRead: true,
    },
  })
  
  res.json({
    success: true,
    data: { deletedCount: result.count },
    message: `Cleaned up ${result.count} old notifications`,
  })
}))

export default router
