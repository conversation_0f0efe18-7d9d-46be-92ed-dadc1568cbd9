'use client'

import React, { useState } from 'react'
import TwoFactorAuth from '@/components/TwoFactorAuth'

export default function SecurityPage() {
  const [activeTab, setActiveTab] = useState<'2fa' | 'password' | 'sessions'>('2fa')

  return (
    <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">إعدادات الأمان</h1>
            <p className="text-gray-600 mt-1">إدارة إعدادات الأمان والحماية لحسابك</p>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 space-x-reverse px-6">
              <button
                onClick={() => setActiveTab('2fa')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === '2fa'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                المصادقة الثنائية
              </button>
              <button
                onClick={() => setActiveTab('password')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'password'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                كلمة المرور
              </button>
              <button
                onClick={() => setActiveTab('sessions')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'sessions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                الجلسات النشطة
              </button>
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === '2fa' && (
              <div>
                <TwoFactorAuth />
              </div>
            )}

            {activeTab === 'password' && (
              <div className="max-w-2xl">
                <h3 className="text-lg font-semibold mb-4">تغيير كلمة المرور</h3>
                <form className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور الحالية
                    </label>
                    <input
                      type="password"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="••••••••"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور الجديدة
                    </label>
                    <input
                      type="password"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="••••••••"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تأكيد كلمة المرور الجديدة
                    </label>
                    <input
                      type="password"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="••••••••"
                    />
                  </div>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                  >
                    تحديث كلمة المرور
                  </button>
                </form>

                <div className="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <h4 className="font-semibold text-yellow-900 mb-2">نصائح لكلمة مرور قوية:</h4>
                  <ul className="text-yellow-800 text-sm space-y-1">
                    <li>• استخدم على الأقل 8 أحرف</li>
                    <li>• امزج بين الأحرف الكبيرة والصغيرة</li>
                    <li>• أضف أرقام ورموز خاصة</li>
                    <li>• تجنب استخدام معلومات شخصية</li>
                  </ul>
                </div>
              </div>
            )}

            {activeTab === 'sessions' && (
              <div className="max-w-2xl">
                <h3 className="text-lg font-semibold mb-4">الجلسات النشطة</h3>
                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-gray-900">الجلسة الحالية</h4>
                        <p className="text-sm text-gray-600">Windows • Chrome</p>
                        <p className="text-sm text-gray-500">آخر نشاط: الآن</p>
                      </div>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        نشطة
                      </span>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-gray-900">جلسة أخرى</h4>
                        <p className="text-sm text-gray-600">iPhone • Safari</p>
                        <p className="text-sm text-gray-500">آخر نشاط: منذ ساعتين</p>
                      </div>
                      <button className="text-red-600 hover:text-red-700 text-sm font-medium">
                        إنهاء الجلسة
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <button className="bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700">
                    إنهاء جميع الجلسات الأخرى
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Security Tips */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">نصائح الأمان</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">🔐</span>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">فعل المصادقة الثنائية</h4>
                <p className="text-blue-800 text-sm">احم حسابك بطبقة حماية إضافية</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">🔑</span>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">استخدم كلمة مرور قوية</h4>
                <p className="text-blue-800 text-sm">اختر كلمة مرور معقدة وفريدة</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">📱</span>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">راقب الجلسات النشطة</h4>
                <p className="text-blue-800 text-sm">تحقق من الأجهزة المتصلة بحسابك</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">💾</span>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-blue-900">احفظ الرموز الاحتياطية</h4>
                <p className="text-blue-800 text-sm">احتفظ بنسخة آمنة من رموز الطوارئ</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
