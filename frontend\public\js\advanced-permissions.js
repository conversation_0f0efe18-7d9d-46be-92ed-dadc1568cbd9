/**
 * نظام الصلاحيات المتقدم
 * Advanced Permissions System
 */

class AdvancedPermissionsManager {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.roles = this.initializeRoles();
        this.permissions = this.initializePermissions();
        this.userPermissions = this.loadUserPermissions();
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        try {
            const user = JSON.parse(localStorage.getItem('currentUser') || '{}');
            return user.id ? user : { id: 'guest', role: 'guest', name: 'زائر' };
        } catch (error) {
            console.error('خطأ في تحميل بيانات المستخدم:', error);
            return { id: 'guest', role: 'guest', name: 'زائر' };
        }
    }

    // تهيئة الأدوار
    initializeRoles() {
        const defaultRoles = {
            admin: {
                id: 'admin',
                name: 'مدير النظام',
                nameEn: 'System Administrator',
                description: 'صلاحيات كاملة لجميع أجزاء النظام',
                priority: 1,
                color: '#dc3545',
                permissions: ['all']
            },
            manager: {
                id: 'manager',
                name: 'مدير',
                nameEn: 'Manager',
                description: 'صلاحيات إدارية محدودة',
                priority: 2,
                color: '#fd7e14',
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit', 'customers_balance',
                    'financial_view', 'financial_payments', 'financial_invoices', 'financial_commissions',
                    'users_view', 'users_create', 'users_edit', 'users_permissions',
                    'hr_employees', 'hr_drivers', 'hr_representatives', 'hr_attendance', 'hr_payroll',
                    'reports_view', 'reports_advanced', 'reports_export', 'reports_analytics'
                ]
            },
            employee: {
                id: 'employee',
                name: 'موظف',
                nameEn: 'Employee',
                description: 'صلاحيات أساسية للعمليات اليومية',
                priority: 3,
                color: '#20c997',
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit',
                    'financial_view', 'reports_view'
                ]
            },
            distributor: {
                id: 'distributor',
                name: 'موزع',
                nameEn: 'Distributor',
                description: 'صلاحيات الموزعين والشركاء',
                priority: 4,
                color: '#6f42c1',
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_track',
                    'customers_view', 'customers_create',
                    'financial_view', 'reports_view'
                ]
            },
            driver: {
                id: 'driver',
                name: 'سائق',
                nameEn: 'Driver',
                description: 'صلاحيات السائقين',
                priority: 5,
                color: '#0dcaf0',
                permissions: [
                    'shipments_view', 'shipments_track',
                    'customers_view',
                    'hr_attendance'
                ]
            },
            representative: {
                id: 'representative',
                name: 'مندوب',
                nameEn: 'Representative',
                description: 'صلاحيات المناديب',
                priority: 6,
                color: '#198754',
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_track',
                    'customers_view', 'customers_create', 'customers_edit',
                    'financial_view', 'hr_attendance'
                ]
            }
        };

        // دمج مع الأدوار المحفوظة
        const savedRoles = JSON.parse(localStorage.getItem('systemRoles') || '{}');
        return { ...defaultRoles, ...savedRoles };
    }

    // تهيئة الصلاحيات
    initializePermissions() {
        return {
            // إدارة الشحنات
            shipments: {
                category: 'shipments',
                name: 'إدارة الشحنات',
                icon: '📦',
                permissions: {
                    shipments_view: { name: 'عرض الشحنات', description: 'عرض قائمة الشحنات والتفاصيل' },
                    shipments_create: { name: 'إنشاء شحنة', description: 'إضافة شحنات جديدة' },
                    shipments_edit: { name: 'تعديل الشحنات', description: 'تعديل بيانات الشحنات' },
                    shipments_delete: { name: 'حذف الشحنات', description: 'حذف الشحنات من النظام' },
                    shipments_track: { name: 'تتبع الشحنات', description: 'تتبع حالة الشحنات' },
                    shipments_print: { name: 'طباعة البوليصات', description: 'طباعة بوليصات الشحن' }
                }
            },

            // إدارة العملاء
            customers: {
                category: 'customers',
                name: 'إدارة العملاء',
                icon: '👥',
                permissions: {
                    customers_view: { name: 'عرض العملاء', description: 'عرض قائمة العملاء' },
                    customers_create: { name: 'إضافة عملاء', description: 'إضافة عملاء جدد' },
                    customers_edit: { name: 'تعديل العملاء', description: 'تعديل بيانات العملاء' },
                    customers_delete: { name: 'حذف العملاء', description: 'حذف العملاء من النظام' },
                    customers_balance: { name: 'إدارة الأرصدة', description: 'عرض وإدارة أرصدة العملاء' }
                }
            },

            // النظام المالي
            financial: {
                category: 'financial',
                name: 'النظام المالي',
                icon: '💰',
                permissions: {
                    financial_view: { name: 'عرض المالية', description: 'عرض التقارير المالية' },
                    financial_payments: { name: 'إدارة المدفوعات', description: 'إدارة المدفوعات والتحصيلات' },
                    financial_invoices: { name: 'إدارة الفواتير', description: 'إنشاء وإدارة الفواتير' },
                    financial_commissions: { name: 'إدارة العمولات', description: 'حساب وإدارة العمولات' }
                }
            },

            // إدارة المستخدمين
            users: {
                category: 'users',
                name: 'إدارة المستخدمين',
                icon: '👤',
                permissions: {
                    users_view: { name: 'عرض المستخدمين', description: 'عرض قائمة المستخدمين' },
                    users_create: { name: 'إضافة مستخدمين', description: 'إضافة مستخدمين جدد' },
                    users_edit: { name: 'تعديل المستخدمين', description: 'تعديل بيانات المستخدمين' },
                    users_delete: { name: 'حذف المستخدمين', description: 'حذف المستخدمين' },
                    users_permissions: { name: 'إدارة الصلاحيات', description: 'تعديل صلاحيات المستخدمين' },
                    users_roles: { name: 'إدارة الأدوار', description: 'إنشاء وتعديل الأدوار' }
                }
            },

            // الموارد البشرية
            hr: {
                category: 'hr',
                name: 'الموارد البشرية',
                icon: '🏢',
                permissions: {
                    hr_employees: { name: 'إدارة الموظفين', description: 'إدارة بيانات الموظفين' },
                    hr_drivers: { name: 'إدارة السائقين', description: 'إدارة بيانات السائقين' },
                    hr_representatives: { name: 'إدارة المناديب', description: 'إدارة بيانات المناديب' },
                    hr_attendance: { name: 'الحضور والانصراف', description: 'تتبع حضور الموظفين' },
                    hr_payroll: { name: 'إدارة الرواتب', description: 'حساب وإدارة الرواتب' }
                }
            },

            // التقارير
            reports: {
                category: 'reports',
                name: 'التقارير',
                icon: '📊',
                permissions: {
                    reports_view: { name: 'عرض التقارير', description: 'عرض التقارير الأساسية' },
                    reports_advanced: { name: 'التقارير المتقدمة', description: 'الوصول للتقارير المتقدمة' },
                    reports_export: { name: 'تصدير التقارير', description: 'تصدير التقارير بصيغ مختلفة' },
                    reports_analytics: { name: 'التحليلات', description: 'عرض التحليلات والإحصائيات' }
                }
            },

            // إعدادات النظام
            system: {
                category: 'system',
                name: 'إعدادات النظام',
                icon: '⚙️',
                permissions: {
                    system_settings: { name: 'الإعدادات العامة', description: 'تعديل إعدادات النظام' },
                    system_backup: { name: 'النسخ الاحتياطي', description: 'إنشاء واستعادة النسخ الاحتياطية' },
                    system_logs: { name: 'سجلات النظام', description: 'عرض سجلات النظام' },
                    system_maintenance: { name: 'صيانة النظام', description: 'أدوات صيانة النظام' }
                }
            }
        };
    }

    // تحميل صلاحيات المستخدم
    loadUserPermissions() {
        try {
            const rolePermissions = JSON.parse(localStorage.getItem('rolePermissions') || '{}');
            const userRole = this.currentUser.role;
            
            // إذا كان مدير النظام، له جميع الصلاحيات
            if (userRole === 'admin') {
                return ['all'];
            }
            
            // الحصول على صلاحيات الدور
            const role = this.roles[userRole];
            const defaultPermissions = role ? role.permissions : [];
            
            // الحصول على الصلاحيات المخصصة
            const customPermissions = rolePermissions[userRole] || [];
            
            // دمج الصلاحيات
            return [...new Set([...defaultPermissions, ...customPermissions])];
        } catch (error) {
            console.error('خطأ في تحميل صلاحيات المستخدم:', error);
            return [];
        }
    }

    // التحقق من صلاحية معينة
    hasPermission(permission) {
        // المدير له جميع الصلاحيات
        if (this.currentUser.role === 'admin' || this.userPermissions.includes('all')) {
            return true;
        }
        
        return this.userPermissions.includes(permission);
    }

    // التحقق من صلاحيات متعددة
    hasAnyPermission(permissions) {
        return permissions.some(permission => this.hasPermission(permission));
    }

    // التحقق من جميع الصلاحيات
    hasAllPermissions(permissions) {
        return permissions.every(permission => this.hasPermission(permission));
    }

    // الحصول على صلاحيات الدور
    getRolePermissions(roleId) {
        const role = this.roles[roleId];
        return role ? role.permissions : [];
    }

    // حفظ صلاحيات الدور
    saveRolePermissions(roleId, permissions) {
        try {
            const rolePermissions = JSON.parse(localStorage.getItem('rolePermissions') || '{}');
            rolePermissions[roleId] = permissions;
            localStorage.setItem('rolePermissions', JSON.stringify(rolePermissions));
            
            // إعادة تحميل الصلاحيات إذا كان المستخدم الحالي
            if (this.currentUser.role === roleId) {
                this.userPermissions = this.loadUserPermissions();
            }
            
            return true;
        } catch (error) {
            console.error('خطأ في حفظ صلاحيات الدور:', error);
            return false;
        }
    }

    // تطبيق الصلاحيات على واجهة المستخدم
    applyPermissionsToUI() {
        // إخفاء العناصر غير المصرح بها
        document.querySelectorAll('[data-permission]').forEach(element => {
            const requiredPermission = element.getAttribute('data-permission');
            if (!this.hasPermission(requiredPermission)) {
                element.style.display = 'none';
            }
        });

        // تعطيل الأزرار غير المصرح بها
        document.querySelectorAll('[data-permission-required]').forEach(element => {
            const requiredPermissions = element.getAttribute('data-permission-required').split(',');
            if (!this.hasAnyPermission(requiredPermissions)) {
                element.disabled = true;
                element.classList.add('disabled');
            }
        });

        // تطبيق الخط على جميع العناصر
        this.applyFontToAllElements();
    }

    // تطبيق الخط على جميع العناصر
    applyFontToAllElements() {
        const fontFamily = "'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";

        // تطبيق على جميع العناصر الموجودة
        document.querySelectorAll('*').forEach(element => {
            element.style.fontFamily = fontFamily;
            element.style.fontWeight = '600';
        });

        // إضافة مراقب للعناصر الجديدة
        if (window.MutationObserver) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) { // Element node
                            node.style.fontFamily = fontFamily;
                            node.style.fontWeight = '600';

                            // تطبيق على العناصر الفرعية أيضاً
                            node.querySelectorAll('*').forEach(child => {
                                child.style.fontFamily = fontFamily;
                                child.style.fontWeight = '600';
                            });
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // الحصول على قائمة جميع الصلاحيات
    getAllPermissions() {
        const allPermissions = [];
        Object.values(this.permissions).forEach(category => {
            Object.keys(category.permissions).forEach(permissionId => {
                allPermissions.push({
                    id: permissionId,
                    category: category.category,
                    categoryName: category.name,
                    ...category.permissions[permissionId]
                });
            });
        });
        return allPermissions;
    }

    // تسجيل عملية في سجل الصلاحيات
    logPermissionAction(action, details = {}) {
        try {
            const log = {
                timestamp: new Date().toISOString(),
                userId: this.currentUser.id,
                userRole: this.currentUser.role,
                action,
                details,
                ip: 'localhost' // يمكن تحسينه لاحقاً
            };
            
            const logs = JSON.parse(localStorage.getItem('permissionLogs') || '[]');
            logs.push(log);
            
            // الاحتفاظ بآخر 1000 سجل فقط
            if (logs.length > 1000) {
                logs.splice(0, logs.length - 1000);
            }
            
            localStorage.setItem('permissionLogs', JSON.stringify(logs));
        } catch (error) {
            console.error('خطأ في تسجيل العملية:', error);
        }
    }
}

// إنشاء مثيل عام لمدير الصلاحيات
window.permissionsManager = new AdvancedPermissionsManager();

// تطبيق الصلاحيات والخط عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (window.permissionsManager) {
        window.permissionsManager.applyPermissionsToUI();
        window.permissionsManager.applyFontToAllElements();
    }
});

// تطبيق الخط فوراً
if (window.permissionsManager) {
    window.permissionsManager.applyFontToAllElements();
}

console.log('✅ تم تحميل نظام الصلاحيات المتقدم مع خط SF Pro AR Display');
