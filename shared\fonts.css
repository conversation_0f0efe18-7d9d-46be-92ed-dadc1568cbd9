/* خطوط آبل للنظام ثنائي اللغة */

/* SF Pro Display - للعناوين والنصوص الكبيرة */
@font-face {
  font-family: 'SF Pro Display';
  src: url('https://developer.apple.com/fonts/SF-Pro-Display-Regular.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Pro-Display-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://developer.apple.com/fonts/SF-Pro-Display-Medium.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Pro-Display-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://developer.apple.com/fonts/SF-Pro-Display-Semibold.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Pro-Display-Semibold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://developer.apple.com/fonts/SF-Pro-Display-Bold.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Pro-Display-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* SF Pro Text - للنصوص العادية */
@font-face {
  font-family: 'SF Pro Text';
  src: url('https://developer.apple.com/fonts/SF-Pro-Text-Regular.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Pro-Text-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('https://developer.apple.com/fonts/SF-Pro-Text-Medium.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Pro-Text-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('https://developer.apple.com/fonts/SF-Pro-Text-Semibold.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Pro-Text-Semibold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* SF Arabic - للنصوص العربية */
@font-face {
  font-family: 'SF Arabic';
  src: url('https://developer.apple.com/fonts/SF-Arabic-Regular.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Arabic-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Arabic';
  src: url('https://developer.apple.com/fonts/SF-Arabic-Medium.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Arabic-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Arabic';
  src: url('https://developer.apple.com/fonts/SF-Arabic-Semibold.woff2') format('woff2'),
       url('https://developer.apple.com/fonts/SF-Arabic-Semibold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* متغيرات CSS للخطوط */
:root {
  --font-display: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-text: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-arabic: 'SF Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
}

/* فئات مساعدة للخطوط */
.font-display {
  font-family: var(--font-display);
}

.font-text {
  font-family: var(--font-text);
}

.font-arabic {
  font-family: var(--font-arabic);
}

/* تطبيق الخطوط حسب اللغة */
[lang="ar"], [dir="rtl"] {
  font-family: var(--font-arabic);
}

[lang="en"], [dir="ltr"] {
  font-family: var(--font-text);
}

/* أحجام الخطوط المتجاوبة */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
