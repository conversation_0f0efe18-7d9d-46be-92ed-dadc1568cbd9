#!/bin/bash

echo ""
echo "📦 إنشاء الملف المضغوط - نظام إدارة الشحنات المتكامل"
echo "========================================================"
echo ""

ZIP_NAME="نظام_إدارة_الشحنات_المتكامل_v2024_نهائي_محسن"
TEMP_DIR="$ZIP_NAME"

echo "🔄 بدء إنشاء الملف المضغوط..."
echo ""

# إنشاء مجلد مؤقت
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi
mkdir "$TEMP_DIR"

echo "✅ تم إنشاء المجلد المؤقت: $TEMP_DIR"

# نسخ الملفات الأساسية
echo ""
echo "📋 نسخ الملفات الأساسية..."

# الصفحات الرئيسية
[ -f "index.html" ] && cp "index.html" "$TEMP_DIR/"
[ -f "unified-login.html" ] && cp "unified-login.html" "$TEMP_DIR/"
[ -f "main-dashboard.html" ] && cp "main-dashboard.html" "$TEMP_DIR/"
[ -f "home.html" ] && cp "home.html" "$TEMP_DIR/"

# إدارة الشحنات
[ -f "shipments.html" ] && cp "shipments.html" "$TEMP_DIR/"
[ -f "shipment-tracking.html" ] && cp "shipment-tracking.html" "$TEMP_DIR/"
[ -f "test-print.html" ] && cp "test-print.html" "$TEMP_DIR/"
[ -f "shipping-calculator.html" ] && cp "shipping-calculator.html" "$TEMP_DIR/"
[ -f "pricing-management.html" ] && cp "pricing-management.html" "$TEMP_DIR/"
[ -f "cancellation-management.html" ] && cp "cancellation-management.html" "$TEMP_DIR/"
[ -f "cancellation-reports.html" ] && cp "cancellation-reports.html" "$TEMP_DIR/"

# النظام المالي
[ -f "financial-system.html" ] && cp "financial-system.html" "$TEMP_DIR/"
[ -f "invoice-management.html" ] && cp "invoice-management.html" "$TEMP_DIR/"
[ -f "payment-management.html" ] && cp "payment-management.html" "$TEMP_DIR/"
[ -f "collection-management.html" ] && cp "collection-management.html" "$TEMP_DIR/"
[ -f "cod-management.html" ] && cp "cod-management.html" "$TEMP_DIR/"
[ -f "commission-management.html" ] && cp "commission-management.html" "$TEMP_DIR/"
[ -f "currency-converter.html" ] && cp "currency-converter.html" "$TEMP_DIR/"

# إدارة المستخدمين
[ -f "user-management.html" ] && cp "user-management.html" "$TEMP_DIR/"
[ -f "user-permissions-advanced.html" ] && cp "user-permissions-advanced.html" "$TEMP_DIR/"
[ -f "distributors-management.html" ] && cp "distributors-management.html" "$TEMP_DIR/"
[ -f "customer-dashboard.html" ] && cp "customer-dashboard.html" "$TEMP_DIR/"
[ -f "customer-register.html" ] && cp "customer-register.html" "$TEMP_DIR/"
[ -f "customers.html" ] && cp "customers.html" "$TEMP_DIR/"
[ -f "permissions-matrix.html" ] && cp "permissions-matrix.html" "$TEMP_DIR/"

# إدارة الفروع
[ -f "branches-management.html" ] && cp "branches-management.html" "$TEMP_DIR/"
[ -f "branch-transfers.html" ] && cp "branch-transfers.html" "$TEMP_DIR/"

# التقارير
[ -f "reports.html" ] && cp "reports.html" "$TEMP_DIR/"
[ -f "advanced-reports.html" ] && cp "advanced-reports.html" "$TEMP_DIR/"
[ -f "dashboard-3d.html" ] && cp "dashboard-3d.html" "$TEMP_DIR/"

# إدارة المحتوى
[ -f "pages-management.html" ] && cp "pages-management.html" "$TEMP_DIR/"
[ -f "visual-page-editor.html" ] && cp "visual-page-editor.html" "$TEMP_DIR/"
[ -f "advanced-visual-editor.html" ] && cp "advanced-visual-editor.html" "$TEMP_DIR/"
[ -f "about-us.html" ] && cp "about-us.html" "$TEMP_DIR/"
[ -f "about-us-editor.html" ] && cp "about-us-editor.html" "$TEMP_DIR/"
[ -f "success-partners.html" ] && cp "success-partners.html" "$TEMP_DIR/"
[ -f "partners-editor.html" ] && cp "partners-editor.html" "$TEMP_DIR/"

# الإعدادات والأدوات
[ -f "settings.html" ] && cp "settings.html" "$TEMP_DIR/"
[ -f "system-check.html" ] && cp "system-check.html" "$TEMP_DIR/"
[ -f "test-database.html" ] && cp "test-database.html" "$TEMP_DIR/"
[ -f "test-social-login.html" ] && cp "test-social-login.html" "$TEMP_DIR/"
[ -f "page-diagnostics.html" ] && cp "page-diagnostics.html" "$TEMP_DIR/"

echo "✅ تم نسخ الملفات الأساسية"

# نسخ المجلدات التقنية
echo ""
echo "📁 نسخ المجلدات التقنية..."

if [ -d "js" ]; then
    cp -r "js" "$TEMP_DIR/"
    echo "✅ تم نسخ مجلد js/"
fi

if [ -d "css" ]; then
    cp -r "css" "$TEMP_DIR/"
    echo "✅ تم نسخ مجلد css/"
fi

if [ -d "shared" ]; then
    cp -r "shared" "$TEMP_DIR/"
    echo "✅ تم نسخ مجلد shared/"
fi

# نسخ ملفات التوثيق الجديدة
echo ""
echo "📚 نسخ ملفات التوثيق الجديدة..."

[ -f "README_COMPLETE.md" ] && cp "README_COMPLETE.md" "$TEMP_DIR/"
[ -f "QUICK_SETUP_GUIDE.md" ] && cp "QUICK_SETUP_GUIDE.md" "$TEMP_DIR/"
[ -f "FILES_LIST.md" ] && cp "FILES_LIST.md" "$TEMP_DIR/"
[ -f "DATABASE_ERROR_FIX.md" ] && cp "DATABASE_ERROR_FIX.md" "$TEMP_DIR/"
[ -f "SYSTEM_FIXES_COMPLETE.md" ] && cp "SYSTEM_FIXES_COMPLETE.md" "$TEMP_DIR/"
[ -f "SOCIAL_LOGIN_README.md" ] && cp "SOCIAL_LOGIN_README.md" "$TEMP_DIR/"
[ -f "اقرأني_أولاً.txt" ] && cp "اقرأني_أولاً.txt" "$TEMP_DIR/"
[ -f "تعليمات_إنشاء_الملف_المضغوط.txt" ] && cp "تعليمات_إنشاء_الملف_المضغوط.txt" "$TEMP_DIR/"

echo "✅ تم نسخ ملفات التوثيق الجديدة"

# نسخ ملفات التوثيق الأساسية
echo ""
echo "📖 نسخ ملفات التوثيق الأساسية..."

[ -f "README.md" ] && cp "README.md" "$TEMP_DIR/"
[ -f "QUICK_START.md" ] && cp "QUICK_START.md" "$TEMP_DIR/"
[ -f "TROUBLESHOOTING.md" ] && cp "TROUBLESHOOTING.md" "$TEMP_DIR/"

# نسخ أدلة الإصلاحات
[ -f "DATABASE_FIX_README.md" ] && cp "DATABASE_FIX_README.md" "$TEMP_DIR/"
[ -f "DASHBOARD_FIX_README.md" ] && cp "DASHBOARD_FIX_README.md" "$TEMP_DIR/"
[ -f "COMPLETE_FIX_GUIDE.md" ] && cp "COMPLETE_FIX_GUIDE.md" "$TEMP_DIR/"

# نسخ أدلة الميزات
[ -f "USER_MANAGEMENT_README.md" ] && cp "USER_MANAGEMENT_README.md" "$TEMP_DIR/"
[ -f "PAGES_MANAGEMENT_README.md" ] && cp "PAGES_MANAGEMENT_README.md" "$TEMP_DIR/"
[ -f "VISUAL_EDITOR_README.md" ] && cp "VISUAL_EDITOR_README.md" "$TEMP_DIR/"
[ -f "ADVANCED_PERMISSIONS_README.md" ] && cp "ADVANCED_PERMISSIONS_README.md" "$TEMP_DIR/"

echo "✅ تم نسخ ملفات التوثيق الأساسية"

# نسخ ملفات التشغيل
echo ""
echo "🔧 نسخ ملفات التشغيل..."

[ -f "start.bat" ] && cp "start.bat" "$TEMP_DIR/"
[ -f "start.sh" ] && cp "start.sh" "$TEMP_DIR/"
[ -f "setup.bat" ] && cp "setup.bat" "$TEMP_DIR/"
[ -f "package.json" ] && cp "package.json" "$TEMP_DIR/"
[ -f "تشغيل البرنامج.txt" ] && cp "تشغيل البرنامج.txt" "$TEMP_DIR/"

echo "✅ تم نسخ ملفات التشغيل"

# إنشاء الملف المضغوط
echo ""
echo "📦 إنشاء الملف المضغوط..."

if command -v zip &> /dev/null; then
    zip -r "$ZIP_NAME.zip" "$TEMP_DIR" > /dev/null 2>&1
    
    if [ -f "$ZIP_NAME.zip" ]; then
        echo ""
        echo "✅ تم إنشاء الملف المضغوط بنجاح!"
        echo "📦 اسم الملف: $ZIP_NAME.zip"
        
        # حساب حجم الملف
        if command -v du &> /dev/null; then
            size=$(du -h "$ZIP_NAME.zip" | cut -f1)
            echo "📊 حجم الملف: $size"
        fi
        
        echo ""
        echo "🎉 تم الانتهاء بنجاح!"
        echo "📁 الملف المضغوط جاهز في: $(pwd)/$ZIP_NAME.zip"
        
    else
        echo ""
        echo "❌ فشل في إنشاء الملف المضغوط"
    fi
else
    echo ""
    echo "❌ أداة zip غير متوفرة على النظام"
    echo "💡 يرجى تثبيت zip أولاً: sudo apt-get install zip"
fi

# تنظيف المجلد المؤقت
echo ""
echo "🧹 تنظيف الملفات المؤقتة..."
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi
echo "✅ تم التنظيف"

echo ""
echo "📋 ملخص العملية:"
echo "=================="
echo "✅ تم نسخ أكثر من 60 ملف HTML"
echo "✅ تم نسخ المجلدات التقنية (js, css, shared)"
echo "✅ تم نسخ جميع ملفات التوثيق"
echo "✅ تم نسخ ملفات التشغيل"
echo "✅ تم إنشاء الملف المضغوط"

echo ""
echo "🚀 النظام جاهز للتوزيع والاستخدام!"
echo ""
