<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الصلاحيات المتقدم - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        * {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-weight: 600 !important;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .role-selector {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .role-chip {
            padding: 8px 16px;
            background: #e9ecef;
            border: 2px solid transparent;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .role-chip.active {
            background: #007bff;
            color: white;
        }
        .permission-test {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 10px;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .permission-name {
            font-weight: 500;
        }
        .permission-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        .allowed { background: #d4edda; color: #155724; }
        .denied { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام الصلاحيات المتقدم</h1>
        <p>اختبار شامل لنظام الصلاحيات والأدوار</p>

        <div class="test-section">
            <h2>📊 حالة النظام</h2>
            <div id="systemStatus"></div>
        </div>

        <div class="test-section">
            <h2>👤 اختبار الأدوار</h2>
            <div class="role-selector">
                <div class="role-chip active" data-role="admin">مدير النظام</div>
                <div class="role-chip" data-role="manager">مدير</div>
                <div class="role-chip" data-role="employee">موظف</div>
                <div class="role-chip" data-role="distributor">موزع</div>
                <div class="role-chip" data-role="driver">سائق</div>
                <div class="role-chip" data-role="representative">مندوب</div>
            </div>
            <div id="roleTest"></div>
        </div>

        <div class="test-section">
            <h2>🔐 اختبار الصلاحيات</h2>
            <div id="permissionsTest"></div>
        </div>

        <div class="test-section">
            <h2>💾 اختبار حفظ واستعادة البيانات</h2>
            <button class="btn btn-primary" onclick="testSavePermissions()">اختبار حفظ الصلاحيات</button>
            <button class="btn btn-success" onclick="testLoadPermissions()">اختبار تحميل الصلاحيات</button>
            <button class="btn btn-danger" onclick="resetPermissions()">إعادة تعيين الصلاحيات</button>
            <div id="saveTest"></div>
        </div>

        <div class="test-section">
            <h2>📝 سجل العمليات</h2>
            <button class="btn btn-primary" onclick="showPermissionLogs()">عرض سجل العمليات</button>
            <div id="logsDisplay"></div>
        </div>

        <div class="test-section">
            <h2>🔗 روابط سريعة</h2>
            <a href="advanced-settings.html" class="btn btn-primary">صفحة الإعدادات المتقدمة</a>
            <a href="user-management.html" class="btn btn-success">إدارة المستخدمين</a>
            <a href="hr-management.html" class="btn btn-info">الموارد البشرية</a>
            <a href="main-dashboard.html" class="btn btn-danger">العودة للوحة التحكم</a>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script src="js/advanced-permissions.js"></script>
    <script>
        let currentTestRole = 'admin';

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 بدء اختبار نظام الصلاحيات...');
            
            setTimeout(() => {
                checkSystemStatus();
                setupRoleSelector();
                testCurrentRole();
                testAllPermissions();
            }, 200);
        });

        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            let html = '';

            // اختبار وجود نظام الصلاحيات
            if (window.permissionsManager) {
                html += '<div class="test-result success">✅ نظام الصلاحيات المتقدم محمل بنجاح</div>';
                
                // اختبار المستخدم الحالي
                const currentUser = window.permissionsManager.currentUser;
                html += `<div class="test-result info">👤 المستخدم الحالي: ${currentUser.name || 'غير محدد'} (${currentUser.role})</div>`;
                
                // اختبار الأدوار
                const roles = Object.keys(window.permissionsManager.roles);
                html += `<div class="test-result info">🔐 عدد الأدوار المتاحة: ${roles.length}</div>`;
                
                // اختبار الصلاحيات
                const permissions = window.permissionsManager.getAllPermissions();
                html += `<div class="test-result info">⚙️ عدد الصلاحيات المتاحة: ${permissions.length}</div>`;
                
            } else {
                html += '<div class="test-result error">❌ فشل في تحميل نظام الصلاحيات</div>';
            }

            statusDiv.innerHTML = html;
        }

        function setupRoleSelector() {
            document.querySelectorAll('.role-chip').forEach(chip => {
                chip.addEventListener('click', function() {
                    document.querySelectorAll('.role-chip').forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                    currentTestRole = this.dataset.role;
                    testCurrentRole();
                    testAllPermissions();
                });
            });
        }

        function testCurrentRole() {
            const roleTestDiv = document.getElementById('roleTest');
            
            if (!window.permissionsManager) {
                roleTestDiv.innerHTML = '<div class="test-result error">❌ نظام الصلاحيات غير متاح</div>';
                return;
            }

            const role = window.permissionsManager.roles[currentTestRole];
            if (!role) {
                roleTestDiv.innerHTML = '<div class="test-result error">❌ الدور غير موجود</div>';
                return;
            }

            let html = `
                <div class="test-result success">✅ الدور: ${role.name}</div>
                <div class="test-result info">📝 الوصف: ${role.description}</div>
                <div class="test-result info">🎯 الأولوية: ${role.priority}</div>
                <div class="test-result info">🔢 عدد الصلاحيات: ${role.permissions.length}</div>
            `;

            roleTestDiv.innerHTML = html;
        }

        function testAllPermissions() {
            const permissionsTestDiv = document.getElementById('permissionsTest');
            
            if (!window.permissionsManager) {
                permissionsTestDiv.innerHTML = '<div class="test-result error">❌ نظام الصلاحيات غير متاح</div>';
                return;
            }

            const allPermissions = window.permissionsManager.getAllPermissions();
            let html = '<h3>اختبار الصلاحيات للدور: ' + currentTestRole + '</h3>';

            allPermissions.forEach(permission => {
                const hasPermission = window.permissionsManager.roles[currentTestRole]?.permissions.includes(permission.id) || 
                                    window.permissionsManager.roles[currentTestRole]?.permissions.includes('all') ||
                                    currentTestRole === 'admin';
                
                html += `
                    <div class="permission-test">
                        <div class="permission-name">${permission.name}</div>
                        <div class="permission-status ${hasPermission ? 'allowed' : 'denied'}">
                            ${hasPermission ? 'مسموح' : 'مرفوض'}
                        </div>
                        <div style="font-size: 12px; color: #6c757d;">${permission.categoryName}</div>
                    </div>
                `;
            });

            permissionsTestDiv.innerHTML = html;
        }

        function testSavePermissions() {
            const saveTestDiv = document.getElementById('saveTest');
            
            try {
                // اختبار حفظ صلاحيات تجريبية
                const testPermissions = ['shipments_view', 'customers_view', 'reports_view'];
                const success = window.permissionsManager.saveRolePermissions('test_role', testPermissions);
                
                if (success) {
                    saveTestDiv.innerHTML = '<div class="test-result success">✅ تم حفظ الصلاحيات التجريبية بنجاح</div>';
                } else {
                    saveTestDiv.innerHTML = '<div class="test-result error">❌ فشل في حفظ الصلاحيات</div>';
                }
            } catch (error) {
                saveTestDiv.innerHTML = `<div class="test-result error">❌ خطأ: ${error.message}</div>`;
            }
        }

        function testLoadPermissions() {
            const saveTestDiv = document.getElementById('saveTest');
            
            try {
                const permissions = window.permissionsManager.getRolePermissions('test_role');
                saveTestDiv.innerHTML = `<div class="test-result success">✅ تم تحميل الصلاحيات: ${permissions.join(', ')}</div>`;
            } catch (error) {
                saveTestDiv.innerHTML = `<div class="test-result error">❌ خطأ في التحميل: ${error.message}</div>`;
            }
        }

        function resetPermissions() {
            const saveTestDiv = document.getElementById('saveTest');
            
            try {
                localStorage.removeItem('rolePermissions');
                localStorage.removeItem('permissionLogs');
                saveTestDiv.innerHTML = '<div class="test-result success">✅ تم إعادة تعيين جميع الصلاحيات</div>';
                
                // إعادة تحميل الصفحة
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } catch (error) {
                saveTestDiv.innerHTML = `<div class="test-result error">❌ خطأ: ${error.message}</div>`;
            }
        }

        function showPermissionLogs() {
            const logsDiv = document.getElementById('logsDisplay');
            
            try {
                const logs = JSON.parse(localStorage.getItem('permissionLogs') || '[]');
                
                if (logs.length === 0) {
                    logsDiv.innerHTML = '<div class="test-result info">📝 لا توجد عمليات مسجلة</div>';
                    return;
                }

                let html = '<h4>آخر 10 عمليات:</h4>';
                logs.slice(-10).reverse().forEach(log => {
                    html += `
                        <div class="test-result info">
                            <strong>${log.action}</strong> - ${new Date(log.timestamp).toLocaleString('ar-SA')}
                            <br>المستخدم: ${log.userId} (${log.userRole})
                            ${log.details ? '<br>التفاصيل: ' + JSON.stringify(log.details) : ''}
                        </div>
                    `;
                });

                logsDiv.innerHTML = html;
            } catch (error) {
                logsDiv.innerHTML = `<div class="test-result error">❌ خطأ في عرض السجلات: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
