<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر صفحات الموقع | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .editor-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 140px);
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .page-list {
            list-style: none;
        }

        .page-item {
            background: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s;
        }

        .page-item:hover {
            border-color: #667eea;
        }

        .page-item.active {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .page-link {
            display: block;
            padding: 15px;
            text-decoration: none;
            color: #333;
        }

        .page-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .page-desc {
            font-size: 0.9rem;
            color: #666;
        }

        .editor-area {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .editor-toolbar {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .toolbar-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .toolbar-btn:hover {
            background: #5a6fd8;
        }

        .toolbar-btn.secondary {
            background: #6c757d;
        }

        .toolbar-btn.secondary:hover {
            background: #5a6268;
        }

        .toolbar-btn.success {
            background: #28a745;
        }

        .toolbar-btn.success:hover {
            background: #218838;
        }

        .editor-content {
            flex: 1;
            display: flex;
        }

        .code-editor {
            flex: 1;
            border: none;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
            background: #f8f9fa;
        }

        .preview-frame {
            flex: 1;
            border: none;
            background: white;
        }

        .editor-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .editor-tab {
            background: none;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .editor-tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
        }

        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-top: 1px solid #dee2e6;
            border-radius: 0 0 10px 10px;
            font-size: 0.9rem;
            color: #666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .editor-layout {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .sidebar {
                order: 2;
            }
            
            .editor-area {
                order: 1;
                height: 60vh;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ محرر صفحات الموقع</h1>
        <p>تحرير وإدارة صفحات الموقع الرئيسية</p>
    </div>

    <div class="container">
        <a href="main-dashboard.html" class="back-link">← العودة للوحة التحكم</a>
        
        <div class="editor-layout">
            <div class="sidebar">
                <h3>📄 صفحات الموقع</h3>
                <ul class="page-list">
                    <li class="page-item active" onclick="loadPage('home.html')">
                        <a href="#" class="page-link">
                            <div class="page-name">🏠 الصفحة الرئيسية</div>
                            <div class="page-desc">الصفحة الرئيسية مع قسم التتبع</div>
                        </a>
                    </li>
                    <li class="page-item" onclick="loadPage('about-us.html')">
                        <a href="#" class="page-link">
                            <div class="page-name">📖 من نحن</div>
                            <div class="page-desc">قصة الشركة وقيمها</div>
                        </a>
                    </li>
                    <li class="page-item" onclick="loadPage('success-partners.html')">
                        <a href="#" class="page-link">
                            <div class="page-name">🤝 شركاء النجاح</div>
                            <div class="page-desc">الشركاء والتعاونات</div>
                        </a>
                    </li>
                </ul>

                <h3 style="margin-top: 30px;">🛠️ أدوات</h3>
                <div style="display: flex; flex-direction: column; gap: 10px;">
                    <button class="toolbar-btn" onclick="createNewPage()">➕ صفحة جديدة</button>
                    <button class="toolbar-btn secondary" onclick="duplicatePage()">📋 نسخ الصفحة</button>
                    <button class="toolbar-btn success" onclick="exportPage()">💾 تصدير</button>
                </div>
            </div>

            <div class="editor-area">
                <div class="editor-toolbar">
                    <button class="toolbar-btn" onclick="saveChanges()">💾 حفظ</button>
                    <button class="toolbar-btn secondary" onclick="previewPage()">👁️ معاينة</button>
                    <button class="toolbar-btn secondary" onclick="resetChanges()">🔄 إعادة تعيين</button>
                    <span style="margin-left: 20px; color: #666;">الصفحة الحالية: <span id="currentPage">home.html</span></span>
                </div>

                <div class="editor-tabs">
                    <button class="editor-tab active" onclick="showTab('code')">📝 الكود</button>
                    <button class="editor-tab" onclick="showTab('preview')">👁️ المعاينة</button>
                </div>

                <div class="editor-content">
                    <textarea id="codeEditor" class="code-editor" placeholder="سيتم تحميل محتوى الصفحة هنا...">
<!-- سيتم تحميل محتوى الصفحة المحددة هنا -->
<!-- يمكنك تعديل HTML, CSS, و JavaScript مباشرة -->

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الصفحة الرئيسية</title>
</head>
<body>
    <h1>مرحباً بك في محرر الصفحات</h1>
    <p>اختر صفحة من القائمة الجانبية لبدء التحرير</p>
</body>
</html>
                    </textarea>
                    <iframe id="previewFrame" class="preview-frame" style="display: none;"></iframe>
                </div>

                <div class="status-bar">
                    <span id="statusText">جاهز للتحرير</span>
                    <span id="wordCount">0 كلمة</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPageName = 'home.html';
        let originalContent = '';

        // تحميل صفحة للتحرير
        function loadPage(pageName) {
            // تحديث الصفحة النشطة
            document.querySelectorAll('.page-item').forEach(item => item.classList.remove('active'));
            event.target.closest('.page-item').classList.add('active');
            
            currentPageName = pageName;
            document.getElementById('currentPage').textContent = pageName;
            
            // محاكاة تحميل المحتوى
            const editor = document.getElementById('codeEditor');
            const sampleContent = getSampleContent(pageName);
            editor.value = sampleContent;
            originalContent = sampleContent;
            
            updateStatus(`تم تحميل ${pageName}`);
            updateWordCount();
        }

        // الحصول على محتوى نموذجي للصفحة
        function getSampleContent(pageName) {
            const contents = {
                'home.html': `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الصفحة الرئيسية | شركة الشحن السريع</title>
</head>
<body>
    <h1>مرحباً بك في شركة الشحن السريع</h1>
    <p>نقدم أفضل خدمات الشحن في المملكة</p>
    <!-- يمكنك تعديل هذا المحتوى -->
</body>
</html>`,
                'about-us.html': `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>من نحن | شركة الشحن السريع</title>
</head>
<body>
    <h1>من نحن</h1>
    <p>نحن شركة رائدة في مجال الشحن والخدمات اللوجستية</p>
    <!-- يمكنك تعديل هذا المحتوى -->
</body>
</html>`,
                'success-partners.html': `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>شركاء النجاح | شركة الشحن السريع</title>
</head>
<body>
    <h1>شركاء النجاح</h1>
    <p>نفخر بشراكاتنا مع أفضل الشركات</p>
    <!-- يمكنك تعديل هذا المحتوى -->
</body>
</html>`
            };
            
            return contents[pageName] || '<!-- محتوى الصفحة -->';
        }

        // تبديل التبويبات
        function showTab(tabName) {
            // تحديث التبويبات
            document.querySelectorAll('.editor-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            const codeEditor = document.getElementById('codeEditor');
            const previewFrame = document.getElementById('previewFrame');
            
            if (tabName === 'code') {
                codeEditor.style.display = 'block';
                previewFrame.style.display = 'none';
            } else {
                codeEditor.style.display = 'none';
                previewFrame.style.display = 'block';
                updatePreview();
            }
        }

        // تحديث المعاينة
        function updatePreview() {
            const content = document.getElementById('codeEditor').value;
            const previewFrame = document.getElementById('previewFrame');
            
            const blob = new Blob([content], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            previewFrame.src = url;
        }

        // حفظ التغييرات
        function saveChanges() {
            const content = document.getElementById('codeEditor').value;
            
            // محاكاة الحفظ
            updateStatus('جاري الحفظ...');
            
            setTimeout(() => {
                originalContent = content;
                updateStatus(`تم حفظ ${currentPageName} بنجاح`);
                alert(`تم حفظ التغييرات في ${currentPageName}\n\nملاحظة: هذا محرر تجريبي. للحفظ الفعلي، استخدم أدوات التطوير أو محرر النصوص.`);
            }, 1000);
        }

        // معاينة الصفحة
        function previewPage() {
            const content = document.getElementById('codeEditor').value;
            const newWindow = window.open('', '_blank');
            newWindow.document.write(content);
            newWindow.document.close();
        }

        // إعادة تعيين التغييرات
        function resetChanges() {
            if (confirm('هل تريد إعادة تعيين جميع التغييرات؟')) {
                document.getElementById('codeEditor').value = originalContent;
                updateStatus('تم إعادة تعيين التغييرات');
                updateWordCount();
            }
        }

        // إنشاء صفحة جديدة
        function createNewPage() {
            const pageName = prompt('اسم الصفحة الجديدة:');
            if (pageName) {
                alert(`سيتم إنشاء صفحة: ${pageName}.html\n\nهذه وظيفة تجريبية.`);
            }
        }

        // نسخ الصفحة
        function duplicatePage() {
            const newName = prompt(`نسخ ${currentPageName} باسم:`);
            if (newName) {
                alert(`سيتم نسخ ${currentPageName} إلى ${newName}\n\nهذه وظيفة تجريبية.`);
            }
        }

        // تصدير الصفحة
        function exportPage() {
            const content = document.getElementById('codeEditor').value;
            const blob = new Blob([content], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = currentPageName;
            link.click();
            
            updateStatus(`تم تصدير ${currentPageName}`);
        }

        // تحديث الحالة
        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
        }

        // تحديث عدد الكلمات
        function updateWordCount() {
            const content = document.getElementById('codeEditor').value;
            const words = content.trim().split(/\s+/).length;
            document.getElementById('wordCount').textContent = `${words} كلمة`;
        }

        // مراقبة التغييرات
        document.getElementById('codeEditor').addEventListener('input', updateWordCount);

        // تحميل الصفحة الافتراضية
        document.addEventListener('DOMContentLoaded', function() {
            loadPage('home.html');
        });
    </script>
</body>
</html>
