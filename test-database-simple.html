<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات المبسطة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-success { background: linear-gradient(135deg, #27ae60, #229954); }
        .btn-warning { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .btn-danger { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left: 5px solid #27ae60;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left: 5px solid #e74c3c;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 5px solid #17a2b8;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left: 5px solid #ffc107;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار قاعدة البيانات المبسطة</h1>
            <p>فحص شامل لحالة قاعدة البيانات ووظائفها</p>
        </div>
        
        <div id="loadingSection" class="loading">
            <div class="spinner"></div>
            <p>جاري تحميل قاعدة البيانات...</p>
        </div>
        
        <div id="results"></div>
        
        <div id="testSection" style="display: none;">
            <div class="test-section">
                <h3>🔧 اختبارات سريعة</h3>
                <button class="btn" onclick="runBasicTests()">🧪 اختبار أساسي</button>
                <button class="btn btn-success" onclick="testShipments()">📦 اختبار الشحنات</button>
                <button class="btn btn-warning" onclick="testUsers()">👥 اختبار المستخدمين</button>
                <button class="btn btn-danger" onclick="clearResults()">🗑️ مسح النتائج</button>
            </div>

            <div class="test-section">
                <h3>🔗 روابط سريعة</h3>
                <button class="btn" onclick="openDashboard()">🏠 لوحة التحكم</button>
                <button class="btn btn-success" onclick="openPermissions()">🔐 الصلاحيات</button>
                <button class="btn btn-warning" onclick="openClearData()">🗑️ مسح البيانات</button>
            </div>
        </div>
    </div>

    <script src="js/database-simple.js"></script>
    <script>
        const resultsDiv = document.getElementById('results');
        const loadingSection = document.getElementById('loadingSection');
        const testSection = document.getElementById('testSection');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            
            const time = new Date().toLocaleTimeString('ar-SA');
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 
                               type === 'warning' ? 'status-warning' : 'status-success';
            
            div.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                <strong>${time}:</strong> ${message}
            `;
            
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
            addResult('تم مسح جميع النتائج', 'info');
        }

        // انتظار تحميل قاعدة البيانات
        async function waitForDatabase() {
            let attempts = 0;
            const maxAttempts = 50;
            
            while (attempts < maxAttempts) {
                attempts++;
                
                if (typeof db !== 'undefined' && db !== null) {
                    addResult('✅ تم العثور على قاعدة البيانات', 'success');
                    
                    try {
                        const testData = db.getAllShipments();
                        addResult(`✅ قاعدة البيانات تعمل - ${testData.length} شحنة`, 'success');
                        return true;
                    } catch (error) {
                        addResult('⚠️ قاعدة البيانات موجودة لكن لا تعمل: ' + error.message, 'warning');
                    }
                }
                
                if (typeof window.dbLoaded !== 'undefined' && window.dbLoaded === true) {
                    addResult('✅ تم تأكيد تحميل قاعدة البيانات', 'success');
                    return true;
                }
                
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            addResult('❌ فشل في تحميل قاعدة البيانات بعد ' + maxAttempts + ' محاولة', 'error');
            return false;
        }

        // اختبار أساسي
        function runBasicTests() {
            addResult('🚀 بدء الاختبار الأساسي...', 'info');

            // اختبار وجود قاعدة البيانات
            if (typeof db === 'undefined') {
                addResult('❌ قاعدة البيانات غير موجودة', 'error');
                return;
            }

            addResult('✅ قاعدة البيانات موجودة', 'success');

            // اختبار الوظائف الأساسية
            const tests = [
                { name: 'getAllShipments', func: () => db.getAllShipments() },
                { name: 'getAllUsers', func: () => db.getAllUsers() },
                { name: 'getAllRoles', func: () => db.getAllRoles() },
                { name: 'getAllCustomers', func: () => db.getAllCustomers() },
                { name: 'getShipmentStats', func: () => db.getShipmentStats() }
            ];

            tests.forEach(test => {
                try {
                    const result = test.func();
                    if (result) {
                        const count = Array.isArray(result) ? result.length : 'نتيجة';
                        addResult(`✅ ${test.name}: يعمل بشكل صحيح (${count})`, 'success');
                    } else {
                        addResult(`⚠️ ${test.name}: لا توجد بيانات`, 'warning');
                    }
                } catch (error) {
                    addResult(`❌ ${test.name}: خطأ - ${error.message}`, 'error');
                }
            });

            addResult('🏁 انتهى الاختبار الأساسي', 'info');
        }

        // اختبار الشحنات
        function testShipments() {
            addResult('📦 اختبار الشحنات...', 'info');

            try {
                const shipments = db.getAllShipments();
                addResult(`📊 عدد الشحنات: ${shipments.length}`, 'info');

                if (shipments.length > 0) {
                    shipments.forEach(shipment => {
                        addResult(`📦 ${shipment.trackingNumber}: ${shipment.senderName} → ${shipment.receiverName} (${shipment.status})`, 'info');
                    });
                }

                // اختبار البحث
                const testTracking = 'SHP001';
                const foundShipment = db.findShipmentByTracking(testTracking);
                if (foundShipment) {
                    addResult(`🔍 تم العثور على الشحنة ${testTracking}`, 'success');
                } else {
                    addResult(`⚠️ لم يتم العثور على الشحنة ${testTracking}`, 'warning');
                }

                // اختبار الإحصائيات
                const stats = db.getShipmentStats();
                addResult(`📊 الإحصائيات - الكل: ${stats.total}, معلقة: ${stats.pending}, مسلمة: ${stats.delivered}`, 'info');

            } catch (error) {
                addResult('❌ خطأ في اختبار الشحنات: ' + error.message, 'error');
            }
        }

        // اختبار المستخدمين
        function testUsers() {
            addResult('👥 اختبار المستخدمين...', 'info');

            try {
                const users = db.getAllUsers();
                addResult(`👤 عدد المستخدمين: ${users.length}`, 'info');

                users.forEach(user => {
                    addResult(`👤 ${user.name} (${user.email}) - ${user.role}`, 'info');
                });

                // اختبار البحث عن مستخدم
                const testUser = db.getUserById('user1');
                if (testUser) {
                    addResult(`🔍 تم العثور على المستخدم: ${testUser.name}`, 'success');
                } else {
                    addResult('⚠️ لم يتم العثور على المستخدم التجريبي', 'warning');
                }

            } catch (error) {
                addResult('❌ خطأ في اختبار المستخدمين: ' + error.message, 'error');
            }
        }

        // فتح لوحة التحكم
        function openDashboard() {
            window.open('main-dashboard.html', '_blank');
        }

        // فتح الصلاحيات
        function openPermissions() {
            window.open('user-permissions-advanced.html', '_blank');
        }

        // فتح مسح البيانات
        function openClearData() {
            window.open('clear-data.html', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            addResult('🎉 مرحباً بك في صفحة اختبار قاعدة البيانات', 'info');
            addResult('⏳ جاري فحص قاعدة البيانات...', 'info');

            const dbLoaded = await waitForDatabase();
            
            // إخفاء رسالة التحميل
            loadingSection.style.display = 'none';
            testSection.style.display = 'block';

            if (dbLoaded) {
                addResult('🎉 قاعدة البيانات جاهزة للاختبار!', 'success');
                
                // تشغيل اختبار تلقائي
                setTimeout(() => {
                    runBasicTests();
                }, 1000);
            } else {
                addResult('❌ فشل في تحميل قاعدة البيانات', 'error');
                addResult('💡 جرب إعادة تحميل الصفحة أو مسح البيانات', 'warning');
            }
        });
    </script>
</body>
</html>
