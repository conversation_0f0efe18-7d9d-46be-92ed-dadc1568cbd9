<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display);
            font-weight: 600;
            background: var(--bg-secondary);
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, var(--color-primary) 0%, #764ba2 100%);
            color: white;
            padding: var(--space-5);
            box-shadow: var(--shadow-lg);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: 1.5rem;
            font-weight: var(--font-weight-bold);
        }

        .nav-links {
            display: flex;
            gap: var(--space-4);
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            transition: all var(--transition-fast);
            background: rgba(255, 255, 255, 0.1);
        }

        .nav-links a:hover, .nav-links a.active {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .main-content {
            max-width: 1200px;
            margin: var(--space-8) auto;
            padding: 0 var(--space-5);
        }

        .settings-grid {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: var(--space-6);
        }

        .settings-sidebar {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-md);
            height: fit-content;
        }

        .sidebar-title {
            font-size: 1.25rem;
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--space-4);
            padding-bottom: var(--space-3);
            border-bottom: 2px solid var(--border-light);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: var(--space-2);
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-lg);
            transition: all var(--transition-fast);
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: var(--color-primary);
            color: white;
            transform: translateX(-5px);
        }

        .settings-content {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-md);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-3);
            border-bottom: 2px solid var(--border-light);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-5);
        }

        .form-group {
            margin-bottom: var(--space-5);
        }

        .form-label {
            display: block;
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .form-input {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 2px solid var(--border-light);
            border-radius: var(--radius-lg);
            font-family: var(--font-arabic-display);
            font-size: 1rem;
            transition: all var(--transition-fast);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            transform: translateY(-1px);
        }

        .form-input {
            transition: all 0.3s ease;
        }

        .form-group {
            position: relative;
        }

        .form-group small {
            display: block;
            margin-top: 5px;
            transition: color 0.3s;
        }

        .form-input:focus + small {
            color: var(--color-primary);
        }

        .form-input:valid {
            border-color: #28a745;
        }

        .form-input:invalid:not(:placeholder-shown) {
            border-color: #dc3545;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            background: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-lg);
            font-family: var(--font-arabic-display);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all var(--transition-fast);
            text-decoration: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--color-gray-500);
        }

        .btn-danger {
            background: var(--color-error);
        }

        .btn-success {
            background: var(--color-secondary);
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--space-4);
        }

        .users-table th,
        .users-table td {
            padding: var(--space-3) var(--space-4);
            text-align: right;
            border-bottom: 1px solid var(--border-light);
        }

        .users-table th {
            background: var(--bg-tertiary);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--border-light);
        }

        .user-avatar-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--color-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: var(--font-weight-bold);
        }

        .role-badge {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-2xl);
            font-size: 0.8rem;
            font-weight: var(--font-weight-semibold);
        }

        .role-admin {
            background: rgba(220, 53, 69, 0.1);
            color: var(--color-error);
        }

        .role-manager {
            background: rgba(255, 193, 7, 0.1);
            color: #856404;
        }

        .role-employee {
            background: rgba(0, 123, 255, 0.1);
            color: var(--color-primary);
        }

        .logo-upload {
            border: 2px dashed var(--border-medium);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
            overflow: hidden;
        }

        .logo-upload:hover {
            border-color: var(--color-primary);
            background: rgba(0, 122, 255, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .logo-upload:active {
            transform: translateY(0);
        }

        .logo-preview {
            max-width: 200px;
            max-height: 100px;
            margin: var(--space-4) auto;
            display: block;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: var(--space-6);
            border-radius: var(--radius-xl);
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-5);
            padding-bottom: var(--space-3);
            border-bottom: 2px solid var(--border-light);
        }

        .close {
            font-size: 2rem;
            cursor: pointer;
            color: var(--text-tertiary);
            transition: color var(--transition-fast);
        }

        .close:hover {
            color: var(--text-primary);
        }

        .avatar-upload {
            text-align: center;
            margin-bottom: var(--space-4);
        }

        .avatar-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--border-light);
            margin-bottom: var(--space-3);
        }

        .avatar-placeholder {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: var(--color-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: var(--font-weight-bold);
            margin: 0 auto var(--space-3);
        }

        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                gap: var(--space-3);
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🚚</span>
                <span>نظام إدارة الشحنات</span>
            </div>

            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="shipments.html">الشحنات</a>
                <a href="customers.html">العملاء</a>
                <a href="settings.html" class="active">الإعدادات</a>
                <a href="index.html">تسجيل الخروج</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="settings-grid">
            <aside class="settings-sidebar">
                <h2 class="sidebar-title">الإعدادات</h2>
                <ul class="sidebar-menu">
                    <li>
                        <a href="#" onclick="showSection('users')" class="active">
                            <span>👥</span>
                            <span>إدارة المستخدمين</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('company')">
                            <span>🏢</span>
                            <span>بيانات الشركة</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('system')">
                            <span>⚙️</span>
                            <span>إعدادات النظام</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="showSection('backup')">
                            <span>💾</span>
                            <span>النسخ الاحتياطي</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <div class="settings-content">
                <!-- إدارة المستخدمين -->
                <section id="users-section" class="content-section active">
                    <h2 class="section-title">👥 إدارة المستخدمين والصلاحيات</h2>

                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-5);">
                        <button class="btn" onclick="openAddUserModal()">
                            ➕ إضافة مستخدم جديد
                        </button>
                    </div>

                    <table class="users-table">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الصلاحية</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- سيتم ملء البيانات هنا -->
                        </tbody>
                    </table>
                </section>

                <!-- بيانات الشركة -->
                <section id="company-section" class="content-section">
                    <h2 class="section-title">🏢 بيانات الشركة</h2>

                    <form id="companyForm">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <h4 style="color: #333; margin-bottom: 15px;">📝 معلومات أساسية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">اسم الشركة * <span style="color: #dc3545;">مطلوب</span></label>
                                    <input type="text" class="form-input" id="companyName" placeholder="أدخل اسم الشركة" required>
                                    <small style="color: #666; font-size: 0.8rem;">هذا الاسم سيظهر في جميع المستندات والفواتير</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">رقم الهاتف 📞</label>
                                    <input type="tel" class="form-input" id="companyPhone" placeholder="+966501234567" dir="ltr">
                                    <small style="color: #666; font-size: 0.8rem;">مثال: +966501234567</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">البريد الإلكتروني ✉️</label>
                                    <input type="email" class="form-input" id="companyEmail" placeholder="<EMAIL>" dir="ltr">
                                    <small style="color: #666; font-size: 0.8rem;">سيتم استخدامه للمراسلات الرسمية</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">الموقع الإلكتروني 🌐</label>
                                    <div style="display: flex; gap: 10px; align-items: center;">
                                        <input type="text" class="form-input" id="companyWebsite" placeholder="www.company.com (اختياري)" dir="ltr" style="flex: 1;">
                                        <button type="button" onclick="clearWebsite()" style="background: #6c757d; color: white; border: none; padding: 12px; border-radius: 8px; cursor: pointer; font-size: 0.9rem;" title="مسح الموقع">
                                            🗑️
                                        </button>
                                    </div>
                                    <small style="color: #666; font-size: 0.8rem;">اختياري - سيظهر في الفواتير. مثال: www.company.com أو https://company.com</small>
                                </div>
                            </div>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <h4 style="color: #333; margin-bottom: 15px;">📍 معلومات الموقع</h4>
                            <div class="form-group">
                                <label class="form-label">العنوان الكامل</label>
                                <textarea class="form-input" id="companyAddress" rows="3" placeholder="أدخل العنوان الكامل للشركة مع المدينة والرمز البريدي" style="resize: vertical; min-height: 80px;"></textarea>
                                <small style="color: #666; font-size: 0.8rem;">سيظهر في الفواتير وأوراق الشحن</small>
                            </div>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <h4 style="color: #333; margin-bottom: 15px;">🎨 الهوية البصرية</h4>
                            <div class="form-group">
                                <label class="form-label">شعار الشركة</label>
                                <div class="logo-upload" onclick="document.getElementById('logoInput').click()" style="border: 2px dashed #ddd; padding: 30px; text-align: center; border-radius: 10px; cursor: pointer; transition: all 0.3s; background: white;">
                                    <input type="file" id="logoInput" accept="image/*" style="display: none;" onchange="previewLogo(this)">
                                    <div id="logoPreview">
                                        <div style="font-size: 4rem; margin-bottom: 15px; color: #667eea;">🏢</div>
                                        <p style="font-size: 1.1rem; color: #333; margin-bottom: 10px;">اضغط لرفع شعار الشركة</p>
                                        <p style="font-size: 0.9rem; color: #666;">
                                            الصيغ المدعومة: JPG, PNG, SVG<br>
                                            الحد الأقصى: 2MB<br>
                                            الأبعاد المفضلة: 300x300 بكسل
                                        </p>
                                    </div>
                                </div>
                                <small style="color: #666; font-size: 0.8rem; display: block; margin-top: 10px;">
                                    💡 نصيحة: استخدم شعاراً بخلفية شفافة للحصول على أفضل النتائج
                                </small>
                            </div>
                        </div>

                        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                            <button type="submit" class="btn">💾 حفظ بيانات الشركة</button>
                            <button type="button" class="btn btn-secondary" onclick="resetCompanyForm()">🔄 إعادة تعيين</button>
                            <button type="button" class="btn btn-success" onclick="testCompanyData()">🧪 اختبار البيانات</button>
                        </div>
                    </form>
                </section>

                <!-- إعدادات النظام -->
                <section id="system-section" class="content-section">
                    <h2 class="section-title">⚙️ إعدادات النظام</h2>

                    <form id="systemForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">العملة الافتراضية</label>
                                <select class="form-input" id="defaultCurrency">
                                    <option value="SAR">ريال سعودي (SAR)</option>
                                    <option value="AED">درهم إماراتي (AED)</option>
                                    <option value="USD">دولار أمريكي (USD)</option>
                                    <option value="EUR">يورو (EUR)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">اللغة الافتراضية</label>
                                <select class="form-input" id="defaultLanguage">
                                    <option value="ar">العربية</option>
                                    <option value="en">English</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">المنطقة الزمنية</label>
                                <select class="form-input" id="timezone">
                                    <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                    <option value="Asia/Dubai">دبي (GMT+4)</option>
                                    <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">تنسيق التاريخ</label>
                                <select class="form-input" id="dateFormat">
                                    <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
                                    <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                    <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                                </select>
                            </div>
                        </div>

                        <button type="submit" class="btn">💾 حفظ إعدادات النظام</button>
                    </form>

                    <!-- إدارة البيانات -->
                    <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-top: 30px;">
                        <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                            🗂️ إدارة البيانات
                        </h3>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                            <button type="button" onclick="clearOldUserData()" style="background: #dc3545; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-weight: 600; transition: all 0.3s;">
                                🗑️ حذف بيانات المستخدمين القديمة
                            </button>

                            <button type="button" onclick="clearOldShipments()" style="background: #fd7e14; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-weight: 600; transition: all 0.3s;">
                                📦 حذف الشحنات القديمة
                            </button>

                            <button type="button" onclick="resetToDefaults()" style="background: #6c757d; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-weight: 600; transition: all 0.3s;">
                                🔄 إعادة تعيين النظام
                            </button>

                            <button type="button" onclick="optimizeDatabase()" style="background: #28a745; color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; font-weight: 600; transition: all 0.3s;">
                                ⚡ تحسين قاعدة البيانات
                            </button>
                        </div>

                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-top: 20px;">
                            <strong style="color: #856404;">⚠️ تحذير:</strong>
                            <p style="color: #856404; margin: 5px 0 0 0;">
                                عمليات حذف البيانات لا يمكن التراجع عنها. تأكد من إنشاء نسخة احتياطية قبل المتابعة.
                            </p>
                        </div>
                    </div>
                </section>

                <!-- النسخ الاحتياطي -->
                <section id="backup-section" class="content-section">
                    <h2 class="section-title">💾 النسخ الاحتياطي واستعادة البيانات</h2>

                    <div class="form-grid">
                        <div class="form-group">
                            <h3>تصدير البيانات</h3>
                            <p style="color: var(--text-secondary); margin-bottom: var(--space-3);">
                                قم بتصدير جميع بيانات النظام كنسخة احتياطية
                            </p>
                            <button class="btn btn-success" onclick="exportData()">
                                📤 تصدير البيانات
                            </button>
                        </div>

                        <div class="form-group">
                            <h3>استيراد البيانات</h3>
                            <p style="color: var(--text-secondary); margin-bottom: var(--space-3);">
                                استعادة البيانات من نسخة احتياطية سابقة
                            </p>
                            <input type="file" id="importInput" accept=".json" style="display: none;" onchange="importData(this)">
                            <button class="btn btn-secondary" onclick="document.getElementById('importInput').click()">
                                📥 استيراد البيانات
                            </button>
                        </div>
                    </div>

                    <div style="background: var(--bg-tertiary); padding: var(--space-4); border-radius: var(--radius-lg); margin-top: var(--space-5);">
                        <h4 style="color: var(--color-error); margin-bottom: var(--space-2);">⚠️ تحذير مهم</h4>
                        <p style="color: var(--text-secondary);">
                            استيراد البيانات سيؤدي إلى استبدال جميع البيانات الحالية. تأكد من إنشاء نسخة احتياطية قبل المتابعة.
                        </p>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل المستخدم -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="userModalTitle">إضافة مستخدم جديد</h2>
                <span class="close" onclick="closeUserModal()">&times;</span>
            </div>

            <form id="userForm">
                <div class="avatar-upload">
                    <div id="avatarPreview" class="avatar-placeholder">👤</div>
                    <input type="file" id="avatarInput" accept="image/*" style="display: none;" onchange="previewAvatar(this)">
                    <button type="button" class="btn btn-secondary" onclick="document.getElementById('avatarInput').click()">
                        📷 رفع صورة
                    </button>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">الاسم الكامل *</label>
                        <input type="text" class="form-input" id="userName" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني *</label>
                        <input type="email" class="form-input" id="userEmail" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-input" id="userPhone">
                    </div>

                    <div class="form-group">
                        <label class="form-label">الصلاحية *</label>
                        <select class="form-input" id="userRole" required>
                            <option value="">اختر الصلاحية</option>
                            <option value="admin">مدير النظام</option>
                            <option value="manager">مدير</option>
                            <option value="employee">موظف</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">كلمة المرور *</label>
                    <input type="password" class="form-input" id="userPassword" required>
                </div>

                <div class="form-group">
                    <label class="form-label">تأكيد كلمة المرور *</label>
                    <input type="password" class="form-input" id="userPasswordConfirm" required>
                </div>

                <div style="display: flex; gap: var(--space-3); justify-content: flex-end; margin-top: var(--space-5);">
                    <button type="button" class="btn btn-secondary" onclick="closeUserModal()">إلغاء</button>
                    <button type="submit" class="btn">💾 حفظ المستخدم</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        let currentEditUserId = null;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل صفحة الإعدادات...');

            try {
                loadUsers();
                loadCompanySettings();
                loadSystemSettings();
                setupEventListeners();
                addFieldListeners();

                console.log('✅ تم تحميل صفحة الإعدادات بنجاح');

                // إظهار رسالة ترحيب
                setTimeout(() => {
                    const companyName = document.getElementById('companyName').value;
                    if (companyName) {
                        console.log(`مرحباً بك في إعدادات ${companyName}`);
                    }
                }, 1000);

            } catch (error) {
                console.error('❌ خطأ في تحميل صفحة الإعدادات:', error);
                alert('حدث خطأ في تحميل الصفحة. يرجى إعادة تحميل الصفحة.');
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('userForm').addEventListener('submit', saveUser);
            document.getElementById('companyForm').addEventListener('submit', saveCompanySettings);
            document.getElementById('systemForm').addEventListener('submit', saveSystemSettings);
        }

        // عرض قسم معين
        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع روابط القائمة
            document.querySelectorAll('.sidebar-menu a').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار القسم المحدد
            document.getElementById(sectionName + '-section').classList.add('active');

            // إضافة الفئة النشطة للرابط المحدد
            event.target.classList.add('active');
        }

        // تحميل المستخدمين
        function loadUsers() {
            const users = getUsersFromStorage();
            displayUsers(users);
        }

        // عرض المستخدمين في الجدول
        function displayUsers(users) {
            const tbody = document.getElementById('usersTableBody');

            tbody.innerHTML = users.map(user => `
                <tr>
                    <td>
                        ${user.avatar ?
                            `<img src="${user.avatar}" alt="${user.name}" class="user-avatar">` :
                            `<div class="user-avatar-placeholder">${user.name.charAt(0)}</div>`
                        }
                    </td>
                    <td><strong>${user.name}</strong></td>
                    <td>${user.email}</td>
                    <td><span class="role-badge role-${user.role}">${getRoleText(user.role)}</span></td>
                    <td>${formatDate(user.createdDate)}</td>
                    <td>
                        <span style="color: ${user.isActive ? 'var(--color-secondary)' : 'var(--color-error)'};">
                            ${user.isActive ? '🟢 نشط' : '🔴 غير نشط'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-secondary btn-small" onclick="editUser('${user.id}')">تعديل</button>
                        <button class="btn btn-danger btn-small" onclick="deleteUser('${user.id}')">حذف</button>
                        <button class="btn btn-small" onclick="toggleUserStatus('${user.id}')">
                            ${user.isActive ? 'إيقاف' : 'تفعيل'}
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // فتح نافذة إضافة مستخدم
        function openAddUserModal() {
            currentEditUserId = null;
            document.getElementById('userModalTitle').textContent = 'إضافة مستخدم جديد';
            document.getElementById('userForm').reset();
            document.getElementById('avatarPreview').innerHTML = '👤';
            document.getElementById('avatarPreview').className = 'avatar-placeholder';
            document.getElementById('userModal').style.display = 'block';
        }

        // تعديل مستخدم
        function editUser(userId) {
            const users = getUsersFromStorage();
            const user = users.find(u => u.id === userId);
            if (!user) return;

            currentEditUserId = userId;
            document.getElementById('userModalTitle').textContent = 'تعديل المستخدم';

            document.getElementById('userName').value = user.name;
            document.getElementById('userEmail').value = user.email;
            document.getElementById('userPhone').value = user.phone || '';
            document.getElementById('userRole').value = user.role;

            if (user.avatar) {
                const preview = document.getElementById('avatarPreview');
                preview.innerHTML = `<img src="${user.avatar}" alt="${user.name}" class="avatar-preview">`;
                preview.className = '';
            }

            // إخفاء حقول كلمة المرور في التعديل
            document.getElementById('userPassword').required = false;
            document.getElementById('userPasswordConfirm').required = false;

            document.getElementById('userModal').style.display = 'block';
        }

        // حفظ المستخدم
        function saveUser(e) {
            e.preventDefault();

            const password = document.getElementById('userPassword').value;
            const passwordConfirm = document.getElementById('userPasswordConfirm').value;

            if (password && password !== passwordConfirm) {
                alert('كلمات المرور غير متطابقة');
                return;
            }

            const userData = {
                name: document.getElementById('userName').value,
                email: document.getElementById('userEmail').value,
                phone: document.getElementById('userPhone').value,
                role: document.getElementById('userRole').value,
                avatar: document.getElementById('avatarPreview').querySelector('img')?.src || null,
                isActive: true
            };

            if (password) {
                userData.password = password; // في التطبيق الحقيقي، يجب تشفير كلمة المرور
            }

            const users = getUsersFromStorage();

            if (currentEditUserId) {
                // تحديث مستخدم موجود
                const index = users.findIndex(u => u.id === currentEditUserId);
                if (index !== -1) {
                    users[index] = { ...users[index], ...userData };
                }
                alert('تم تحديث المستخدم بنجاح');
            } else {
                // إضافة مستخدم جديد
                const newUser = {
                    id: 'USER' + Date.now(),
                    ...userData,
                    createdDate: new Date().toISOString().split('T')[0]
                };
                users.push(newUser);
                alert('تم إضافة المستخدم بنجاح');
            }

            localStorage.setItem('users', JSON.stringify(users));
            loadUsers();
            closeUserModal();
        }

        // حذف مستخدم
        function deleteUser(userId) {
            if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;

            const users = getUsersFromStorage();
            const filteredUsers = users.filter(u => u.id !== userId);
            localStorage.setItem('users', JSON.stringify(filteredUsers));
            loadUsers();
            alert('تم حذف المستخدم بنجاح');
        }

        // تبديل حالة المستخدم
        function toggleUserStatus(userId) {
            const users = getUsersFromStorage();
            const user = users.find(u => u.id === userId);
            if (!user) return;

            user.isActive = !user.isActive;
            localStorage.setItem('users', JSON.stringify(users));
            loadUsers();

            alert(`تم ${user.isActive ? 'تفعيل' : 'إيقاف'} المستخدم بنجاح`);
        }

        // إغلاق نافذة المستخدم
        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
            document.getElementById('userPassword').required = true;
            document.getElementById('userPasswordConfirm').required = true;
            currentEditUserId = null;
        }

        // معاينة الصورة الشخصية
        function previewAvatar(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('avatarPreview');
                    preview.innerHTML = `<img src="${e.target.result}" alt="Avatar" class="avatar-preview">`;
                    preview.className = '';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // معاينة شعار الشركة
        function previewLogo(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];

                // التحقق من نوع الملف
                if (!file.type.startsWith('image/')) {
                    alert('يرجى اختيار ملف صورة صحيح');
                    input.value = '';
                    return;
                }

                // التحقق من حجم الملف (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert('حجم الملف كبير جداً. الحد الأقصى 2MB');
                    input.value = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('logoPreview').innerHTML = `
                        <img src="${e.target.result}" alt="Company Logo" class="logo-preview">
                        <p>شعار الشركة الجديد</p>
                        <button type="button" onclick="removeLogo()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 5px; margin-top: 10px; cursor: pointer;">🗑️ إزالة الشعار</button>
                    `;
                };
                reader.readAsDataURL(file);
            }
        }

        // إزالة شعار الشركة
        function removeLogo() {
            if (confirm('هل أنت متأكد من إزالة شعار الشركة؟')) {
                document.getElementById('logoPreview').innerHTML = `
                    <div style="font-size: 3rem; margin-bottom: 15px;">🏢</div>
                    <p>اضغط لرفع شعار الشركة</p>
                    <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                        الصيغ المدعومة: JPG, PNG, SVG (الحد الأقصى: 2MB)
                    </p>
                `;
                document.getElementById('logoInput').value = '';
            }
        }

        // إظهار رسالة نجاح مفصلة
        function showSuccessMessage(title, details) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 500px; width: 90%; text-align: center;">
                    <div style="font-size: 3rem; color: #28a745; margin-bottom: 20px;">✅</div>
                    <h3 style="color: #333; margin-bottom: 15px;">${title}</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: right; white-space: pre-line; font-family: monospace; font-size: 0.9rem;">
                        ${details}
                    </div>
                    <button onclick="this.closest('div').parentElement.remove()" style="background: #28a745; color: white; border: none; padding: 10px 25px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        موافق
                    </button>
                </div>
            `;

            document.body.appendChild(modal);

            // إزالة تلقائية بعد 5 ثوانٍ
            setTimeout(() => {
                if (modal.parentElement) {
                    modal.remove();
                }
            }, 5000);
        }

        // حفظ إعدادات الشركة
        function saveCompanySettings(e) {
            e.preventDefault();

            // التحقق من صحة البيانات
            const companyName = document.getElementById('companyName').value.trim();
            if (!companyName) {
                alert('يرجى إدخال اسم الشركة');
                return;
            }

            // تنسيق الموقع الإلكتروني تلقائياً
            let website = document.getElementById('companyWebsite').value.trim();
            if (website && !website.startsWith('http://') && !website.startsWith('https://')) {
                if (!website.startsWith('www.')) {
                    website = 'www.' + website;
                }
                website = 'https://' + website;
                // تحديث الحقل بالتنسيق الصحيح
                document.getElementById('companyWebsite').value = website;
            }

            const companyData = {
                name: companyName,
                phone: document.getElementById('companyPhone').value.trim(),
                email: document.getElementById('companyEmail').value.trim(),
                website: website,
                address: document.getElementById('companyAddress').value.trim(),
                logo: document.getElementById('logoPreview').querySelector('img')?.src || null,
                lastUpdated: new Date().toISOString()
            };

            try {
                localStorage.setItem('companySettings', JSON.stringify(companyData));

                // إظهار رسالة نجاح مع تفاصيل
                showSuccessMessage('تم حفظ بيانات الشركة بنجاح!', `
                    اسم الشركة: ${companyData.name}
                    الهاتف: ${companyData.phone || 'غير محدد'}
                    البريد الإلكتروني: ${companyData.email || 'غير محدد'}
                    الموقع: ${companyData.website || 'غير محدد'}
                    العنوان: ${companyData.address || 'غير محدد'}
                `);

                // تحديث عنوان الصفحة إذا تغير اسم الشركة
                document.title = `إعدادات ${companyData.name}`;

            } catch (error) {
                alert('حدث خطأ أثناء حفظ البيانات: ' + error.message);
            }
        }

        // حفظ إعدادات النظام
        function saveSystemSettings(e) {
            e.preventDefault();

            const systemData = {
                defaultCurrency: document.getElementById('defaultCurrency').value,
                defaultLanguage: document.getElementById('defaultLanguage').value,
                timezone: document.getElementById('timezone').value,
                dateFormat: document.getElementById('dateFormat').value
            };

            localStorage.setItem('systemSettings', JSON.stringify(systemData));
            alert('تم حفظ إعدادات النظام بنجاح');
        }

        // تحميل إعدادات الشركة
        function loadCompanySettings() {
            try {
                const settings = JSON.parse(localStorage.getItem('companySettings') || '{}');

                // إذا لم توجد إعدادات محفوظة، استخدم القيم الافتراضية
                const defaultSettings = {
                    name: 'شركة الشحن السريع',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    website: 'www.fastshipping.com',
                    address: 'شارع الملك فهد، الرياض، المملكة العربية السعودية'
                };

                // دمج الإعدادات المحفوظة مع الافتراضية
                const finalSettings = { ...defaultSettings, ...settings };

                // تحديث الحقول
                document.getElementById('companyName').value = finalSettings.name || '';
                document.getElementById('companyPhone').value = finalSettings.phone || '';
                document.getElementById('companyEmail').value = finalSettings.email || '';
                document.getElementById('companyWebsite').value = finalSettings.website || '';
                document.getElementById('companyAddress').value = finalSettings.address || '';

                // تحديث الشعار
                if (finalSettings.logo) {
                    document.getElementById('logoPreview').innerHTML = `
                        <img src="${finalSettings.logo}" alt="Company Logo" class="logo-preview">
                        <p>شعار الشركة الحالي</p>
                        <button type="button" onclick="removeLogo()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 5px; margin-top: 10px; cursor: pointer;">🗑️ إزالة الشعار</button>
                    `;
                } else {
                    document.getElementById('logoPreview').innerHTML = `
                        <div style="font-size: 3rem; margin-bottom: 15px;">🏢</div>
                        <p>اضغط لرفع شعار الشركة</p>
                        <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                            الصيغ المدعومة: JPG, PNG, SVG (الحد الأقصى: 2MB)
                        </p>
                    `;
                }

                // حفظ الإعدادات المحدثة إذا لم تكن موجودة
                if (Object.keys(settings).length === 0) {
                    localStorage.setItem('companySettings', JSON.stringify(finalSettings));
                }

                console.log('تم تحميل إعدادات الشركة بنجاح');

            } catch (error) {
                console.error('خطأ في تحميل إعدادات الشركة:', error);
                alert('حدث خطأ في تحميل إعدادات الشركة');
            }
        }

        // تحميل إعدادات النظام
        function loadSystemSettings() {
            const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');

            if (settings.defaultCurrency) document.getElementById('defaultCurrency').value = settings.defaultCurrency;
            if (settings.defaultLanguage) document.getElementById('defaultLanguage').value = settings.defaultLanguage;
            if (settings.timezone) document.getElementById('timezone').value = settings.timezone;
            if (settings.dateFormat) document.getElementById('dateFormat').value = settings.dateFormat;
        }

        // تصدير البيانات
        function exportData() {
            const data = {
                shipments: db.getAllShipments(),
                customers: db.getAllCustomers(),
                distributors: db.getAllDistributors(),
                users: getUsersFromStorage(),
                companySettings: JSON.parse(localStorage.getItem('companySettings') || '{}'),
                systemSettings: JSON.parse(localStorage.getItem('systemSettings') || '{}'),
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `shipment-system-backup-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('تم تصدير البيانات بنجاح');
        }

        // استيراد البيانات
        function importData(input) {
            if (!input.files || !input.files[0]) return;

            if (!confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال جميع البيانات الحالية.')) {
                input.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    // استعادة البيانات
                    if (data.shipments) localStorage.setItem('shipments', JSON.stringify(data.shipments));
                    if (data.customers) localStorage.setItem('customers', JSON.stringify(data.customers));
                    if (data.distributors) localStorage.setItem('distributors', JSON.stringify(data.distributors));
                    if (data.users) localStorage.setItem('users', JSON.stringify(data.users));
                    if (data.companySettings) localStorage.setItem('companySettings', JSON.stringify(data.companySettings));
                    if (data.systemSettings) localStorage.setItem('systemSettings', JSON.stringify(data.systemSettings));

                    alert('تم استيراد البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
                    location.reload();
                } catch (error) {
                    alert('خطأ في قراءة ملف البيانات. تأكد من صحة الملف.');
                }
            };
            reader.readAsText(input.files[0]);
            input.value = '';
        }

        // دوال مساعدة
        function getUsersFromStorage() {
            const defaultUsers = [
                {
                    id: 'USER001',
                    name: 'مدير النظام',
                    email: '<EMAIL>',
                    phone: '+966501234567',
                    role: 'admin',
                    avatar: null,
                    isActive: true,
                    createdDate: '2024-01-01'
                },
                {
                    id: 'USER002',
                    name: 'أحمد محمد',
                    email: '<EMAIL>',
                    phone: '+966502345678',
                    role: 'manager',
                    avatar: null,
                    isActive: true,
                    createdDate: '2024-01-02'
                },
                {
                    id: 'USER003',
                    name: 'فاطمة علي',
                    email: '<EMAIL>',
                    phone: '+966503456789',
                    role: 'employee',
                    avatar: null,
                    isActive: true,
                    createdDate: '2024-01-03'
                }
            ];

            const stored = localStorage.getItem('users');
            return stored ? JSON.parse(stored) : defaultUsers;
        }

        function getRoleText(role) {
            const roles = {
                'admin': 'مدير النظام',
                'manager': 'مدير',
                'employee': 'موظف'
            };
            return roles[role] || role;
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // إعادة تعيين نموذج بيانات الشركة
        function resetCompanyForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                // إعادة تحميل الإعدادات المحفوظة
                loadCompanySettings();
                alert('تم إعادة تعيين البيانات بنجاح');
            }
        }

        // اختبار بيانات الشركة
        function testCompanyData() {
            const companyData = {
                name: document.getElementById('companyName').value.trim(),
                phone: document.getElementById('companyPhone').value.trim(),
                email: document.getElementById('companyEmail').value.trim(),
                website: document.getElementById('companyWebsite').value.trim(),
                address: document.getElementById('companyAddress').value.trim()
            };

            let errors = [];
            let warnings = [];

            // التحقق من البيانات المطلوبة
            if (!companyData.name) {
                errors.push('اسم الشركة مطلوب');
            }

            // التحقق من صحة البريد الإلكتروني
            if (companyData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(companyData.email)) {
                errors.push('البريد الإلكتروني غير صحيح');
            }

            // التحقق من رقم الهاتف
            if (companyData.phone && !/^(\+966|0)?[5][0-9]{8}$/.test(companyData.phone.replace(/\s/g, ''))) {
                warnings.push('تنسيق رقم الهاتف قد يكون غير صحيح (يفضل +966xxxxxxxxx)');
            }

            // التحقق من الموقع الإلكتروني (اختياري)
            if (companyData.website) {
                // إضافة http:// إذا لم يكن موجوداً
                let website = companyData.website;
                if (!website.startsWith('http://') && !website.startsWith('https://')) {
                    if (!website.startsWith('www.')) {
                        website = 'www.' + website;
                    }
                    website = 'https://' + website;
                }

                // التحقق من صحة تنسيق الموقع
                try {
                    new URL(website);
                } catch (e) {
                    warnings.push('تنسيق الموقع الإلكتروني قد يكون غير صحيح. مثال صحيح: www.company.com');
                }
            }

            // عرض النتائج
            let message = '🧪 نتائج اختبار البيانات:\n\n';

            if (errors.length > 0) {
                message += '❌ أخطاء:\n' + errors.map(e => `• ${e}`).join('\n') + '\n\n';
            }

            if (warnings.length > 0) {
                message += '⚠️ تحذيرات:\n' + warnings.map(w => `• ${w}`).join('\n') + '\n\n';
            }

            if (errors.length === 0 && warnings.length === 0) {
                message += '✅ جميع البيانات صحيحة!';
            }

            alert(message);
        }

        // إضافة مستمعي الأحداث للحقول
        function addFieldListeners() {
            // إضافة تأثيرات بصرية عند التركيز
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.borderColor = '#667eea';
                    this.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                });

                input.addEventListener('blur', function() {
                    this.style.borderColor = '#e1e5e9';
                    this.style.boxShadow = 'none';
                });

                // حفظ تلقائي عند تغيير البيانات
                input.addEventListener('change', function() {
                    const fieldName = this.id.replace('company', '').toLowerCase();
                    console.log(`تم تغيير ${fieldName}: ${this.value}`);
                });
            });

            // مستمع خاص لحقل الموقع الإلكتروني
            const websiteInput = document.getElementById('companyWebsite');
            if (websiteInput) {
                websiteInput.addEventListener('blur', function() {
                    let website = this.value.trim();
                    const small = this.parentElement.nextElementSibling;

                    if (website && !website.startsWith('http://') && !website.startsWith('https://')) {
                        // إضافة معاينة للموقع المنسق
                        const formattedWebsite = website.startsWith('www.') ? website : 'www.' + website;
                        if (small && small.tagName === 'SMALL') {
                            small.innerHTML = `اختياري - سيظهر في الفواتير. <br><strong>سيتم حفظه كـ:</strong> https://${formattedWebsite}`;
                            small.style.color = '#667eea';
                        }
                    } else if (website === '') {
                        // الحقل فارغ
                        if (small && small.tagName === 'SMALL') {
                            small.innerHTML = '✅ الحقل اختياري - تم تركه فارغاً';
                            small.style.color = '#28a745';
                        }
                    } else {
                        // إعادة النص الأصلي
                        if (small && small.tagName === 'SMALL') {
                            small.innerHTML = 'اختياري - سيظهر في الفواتير. مثال: www.company.com أو https://company.com';
                            small.style.color = '#666';
                        }
                    }
                });

                // إزالة المعاينة عند التركيز
                websiteInput.addEventListener('focus', function() {
                    const small = this.parentElement.nextElementSibling;
                    if (small && small.tagName === 'SMALL') {
                        small.innerHTML = 'اختياري - سيظهر في الفواتير. مثال: www.company.com أو https://company.com';
                        small.style.color = '#666';
                    }
                });

                // معالجة الحقل الفارغ
                websiteInput.addEventListener('input', function() {
                    const small = this.parentElement.nextElementSibling;
                    if (this.value.trim() === '') {
                        if (small && small.tagName === 'SMALL') {
                            small.innerHTML = '✅ الحقل اختياري - يمكنك تركه فارغاً أو إدخال موقع الشركة';
                            small.style.color = '#28a745';
                        }
                    }
                });
            }
        }

        // مسح حقل الموقع الإلكتروني
        function clearWebsite() {
            const websiteInput = document.getElementById('companyWebsite');
            const small = websiteInput.parentElement.nextElementSibling;

            websiteInput.value = '';
            websiteInput.focus();

            // إعادة النص الأصلي
            if (small && small.tagName === 'SMALL') {
                small.innerHTML = 'اختياري - سيظهر في الفواتير. مثال: www.company.com أو https://company.com';
                small.style.color = '#666';
            }

            console.log('تم مسح الموقع الإلكتروني');
        }

        // حذف بيانات المستخدمين القديمة
        function clearOldUserData() {
            const confirmMessage = `هل أنت متأكد من حذف بيانات المستخدمين القديمة؟

سيتم حذف:
• جميع المستخدمين المحفوظين
• سجلات تسجيل الدخول القديمة
• إعدادات المستخدمين

سيتم الاحتفاظ بـ:
• بيانات تسجيل الدخول الحالية
• إعدادات الشركة
• بيانات الشحنات

هذا الإجراء لا يمكن التراجع عنه!`;

            if (confirm(confirmMessage)) {
                try {
                    // حفظ بيانات تسجيل الدخول الحالية
                    const currentLoginData = {
                        isLoggedIn: localStorage.getItem('isLoggedIn'),
                        userType: localStorage.getItem('userType'),
                        userEmail: localStorage.getItem('userEmail'),
                        loginTime: localStorage.getItem('loginTime')
                    };

                    // حذف بيانات المستخدمين القديمة
                    localStorage.removeItem('users');
                    localStorage.removeItem('userSessions');
                    localStorage.removeItem('userPreferences');
                    localStorage.removeItem('oldLoginData');

                    // استعادة بيانات تسجيل الدخول الحالية
                    Object.entries(currentLoginData).forEach(([key, value]) => {
                        if (value) {
                            localStorage.setItem(key, value);
                        }
                    });

                    // إعادة تحميل المستخدمين
                    loadUsers();

                    showSuccessMessage('تم حذف بيانات المستخدمين القديمة بنجاح!', `
                        تم حذف:
                        • جميع المستخدمين المحفوظين
                        • سجلات تسجيل الدخول القديمة
                        • إعدادات المستخدمين القديمة

                        تم الاحتفاظ بـ:
                        • بيانات تسجيل الدخول الحالية
                        • جلسة العمل الحالية
                    `);

                } catch (error) {
                    alert('حدث خطأ أثناء حذف البيانات: ' + error.message);
                }
            }
        }

        // حذف الشحنات القديمة
        function clearOldShipments() {
            const confirmMessage = `هل أنت متأكد من حذف الشحنات القديمة؟

سيتم حذف جميع الشحنات الأقدم من 30 يوماً والتي تم تسليمها أو إلغاؤها.

هذا الإجراء لا يمكن التراجع عنه!`;

            if (confirm(confirmMessage)) {
                try {
                    const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                    const thirtyDaysAgo = new Date();
                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

                    const filteredShipments = shipments.filter(shipment => {
                        const shipmentDate = new Date(shipment.createdAt || shipment.date);
                        const isOld = shipmentDate < thirtyDaysAgo;
                        const isCompleted = shipment.status === 'تم التسليم' || shipment.status === 'ملغي';

                        // الاحتفاظ بالشحنات الحديثة أو غير المكتملة
                        return !isOld || !isCompleted;
                    });

                    const deletedCount = shipments.length - filteredShipments.length;

                    localStorage.setItem('shipments', JSON.stringify(filteredShipments));

                    alert(`تم حذف ${deletedCount} شحنة قديمة بنجاح!`);

                } catch (error) {
                    alert('حدث خطأ أثناء حذف الشحنات: ' + error.message);
                }
            }
        }

        // إعادة تعيين النظام للإعدادات الافتراضية
        function resetToDefaults() {
            const confirmMessage = `هل أنت متأكد من إعادة تعيين النظام للإعدادات الافتراضية؟

سيتم إعادة تعيين:
• إعدادات النظام
• إعدادات العرض
• التفضيلات

سيتم الاحتفاظ بـ:
• بيانات الشركة
• بيانات الشحنات
• بيانات المستخدمين
• جلسة تسجيل الدخول الحالية

هذا الإجراء لا يمكن التراجع عنه!`;

            if (confirm(confirmMessage)) {
                try {
                    // حفظ البيانات المهمة
                    const importantData = {
                        companySettings: localStorage.getItem('companySettings'),
                        shipments: localStorage.getItem('shipments'),
                        customers: localStorage.getItem('customers'),
                        users: localStorage.getItem('users'),
                        isLoggedIn: localStorage.getItem('isLoggedIn'),
                        userType: localStorage.getItem('userType'),
                        userEmail: localStorage.getItem('userEmail')
                    };

                    // حذف الإعدادات القابلة للإعادة تعيين
                    localStorage.removeItem('systemSettings');
                    localStorage.removeItem('userPreferences');
                    localStorage.removeItem('dashboardSettings');
                    localStorage.removeItem('displaySettings');

                    // استعادة البيانات المهمة
                    Object.entries(importantData).forEach(([key, value]) => {
                        if (value) {
                            localStorage.setItem(key, value);
                        }
                    });

                    // إعادة تحميل الإعدادات
                    loadSystemSettings();

                    alert('تم إعادة تعيين النظام للإعدادات الافتراضية بنجاح!');

                } catch (error) {
                    alert('حدث خطأ أثناء إعادة التعيين: ' + error.message);
                }
            }
        }

        // تحسين قاعدة البيانات
        function optimizeDatabase() {
            try {
                let optimizedCount = 0;

                // تنظيف البيانات المكررة
                const dataKeys = ['shipments', 'customers', 'users', 'distributors'];

                dataKeys.forEach(key => {
                    const data = JSON.parse(localStorage.getItem(key) || '[]');
                    if (Array.isArray(data)) {
                        // إزالة المكررات بناءً على ID
                        const uniqueData = data.filter((item, index, self) =>
                            index === self.findIndex(t => t.id === item.id)
                        );

                        if (uniqueData.length !== data.length) {
                            localStorage.setItem(key, JSON.stringify(uniqueData));
                            optimizedCount += data.length - uniqueData.length;
                        }
                    }
                });

                // ضغط البيانات (إزالة المسافات الزائدة)
                Object.keys(localStorage).forEach(key => {
                    try {
                        const data = JSON.parse(localStorage.getItem(key));
                        localStorage.setItem(key, JSON.stringify(data));
                    } catch (e) {
                        // تجاهل البيانات غير JSON
                    }
                });

                alert(`تم تحسين قاعدة البيانات بنجاح!\n\nتم حذف ${optimizedCount} عنصر مكرر\nتم ضغط البيانات لتوفير المساحة`);

            } catch (error) {
                alert('حدث خطأ أثناء تحسين قاعدة البيانات: ' + error.message);
            }
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('userModal');
            if (event.target === modal) {
                closeUserModal();
            }
        }
    </script>
</body>
</html>