<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قاعدة البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }
        .header {
            margin-bottom: 30px;
        }
        .header h1 {
            color: #e74c3c;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 1.2rem;
        }
        .fix-section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border-right: 5px solid #e74c3c;
        }
        .fix-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #27ae60;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #e74c3c;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #17a2b8;
        }
        .steps {
            text-align: right;
            margin: 20px 0;
        }
        .steps ol {
            line-height: 2;
        }
        .steps li {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-right: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح قاعدة البيانات</h1>
            <p>أداة سريعة لحل مشاكل قاعدة البيانات</p>
        </div>

        <div id="status" class="status status-info">
            ⏳ جاري فحص حالة قاعدة البيانات...
        </div>

        <div class="fix-section">
            <h3>🚀 إصلاحات سريعة</h3>
            <button class="btn btn-success" onclick="switchToSimpleDatabase()">
                🔄 التبديل لقاعدة البيانات المبسطة
            </button>
            <button class="btn btn-warning" onclick="clearAllData()">
                🗑️ مسح جميع البيانات
            </button>
            <button class="btn btn-info" onclick="testDatabase()">
                🧪 اختبار قاعدة البيانات
            </button>
        </div>

        <div class="fix-section">
            <h3>📋 خطوات الإصلاح اليدوي</h3>
            <div class="steps">
                <ol>
                    <li><strong>إعادة تحميل الصفحة:</strong> اضغط F5 أو Ctrl+R</li>
                    <li><strong>مسح ذاكرة المتصفح:</strong> اضغط Ctrl+Shift+R</li>
                    <li><strong>فحص وحدة التحكم:</strong> اضغط F12 وتحقق من الأخطاء</li>
                    <li><strong>مسح البيانات:</strong> استخدم الزر أعلاه أو افتح clear-data.html</li>
                    <li><strong>إعادة تشغيل المتصفح:</strong> أغلق المتصفح وافتحه مرة أخرى</li>
                </ol>
            </div>
        </div>

        <div class="fix-section">
            <h3>🔗 روابط مفيدة</h3>
            <a href="main-dashboard.html" class="btn">🏠 لوحة التحكم</a>
            <a href="test-database-simple.html" class="btn btn-info">🧪 اختبار قاعدة البيانات</a>
            <a href="update-all-pages.html" class="btn btn-success">🔄 صفحة التحديث الشاملة</a>
            <a href="clear-data.html" class="btn btn-warning">🗑️ مسح البيانات</a>
        </div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');

        function updateStatus(message, type = 'info') {
            statusDiv.className = `status status-${type}`;
            statusDiv.innerHTML = message;
        }

        // التبديل لقاعدة البيانات المبسطة
        function switchToSimpleDatabase() {
            updateStatus('🔄 جاري التبديل لقاعدة البيانات المبسطة...', 'info');
            
            try {
                // تحديث جميع الملفات لتستخدم قاعدة البيانات المبسطة
                localStorage.setItem('useSimpleDatabase', 'true');
                
                updateStatus('✅ تم التبديل بنجاح! يرجى إعادة تحميل لوحة التحكم.', 'success');
                
                setTimeout(() => {
                    if (confirm('هل تريد فتح لوحة التحكم الآن؟')) {
                        window.open('main-dashboard.html', '_blank');
                    }
                }, 2000);
                
            } catch (error) {
                updateStatus('❌ خطأ في التبديل: ' + error.message, 'error');
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                updateStatus('🗑️ جاري مسح جميع البيانات...', 'info');
                
                try {
                    // مسح جميع البيانات من التخزين المحلي
                    const keysToRemove = [
                        'shipments', 'customers', 'distributors', 'users', 'roles', 
                        'activityLog', 'currentUser', 'sessionStart', 'rememberUser', 
                        'pricing', 'branches', 'settings', 'useSimpleDatabase'
                    ];
                    
                    keysToRemove.forEach(key => {
                        localStorage.removeItem(key);
                    });
                    
                    // مسح جميع البيانات الأخرى
                    localStorage.clear();
                    
                    updateStatus('✅ تم مسح جميع البيانات بنجاح! يرجى إعادة تحميل الصفحة.', 'success');
                    
                    setTimeout(() => {
                        if (confirm('هل تريد إعادة تحميل الصفحة الآن؟')) {
                            location.reload();
                        }
                    }, 2000);
                    
                } catch (error) {
                    updateStatus('❌ خطأ في مسح البيانات: ' + error.message, 'error');
                }
            }
        }

        // اختبار قاعدة البيانات
        function testDatabase() {
            updateStatus('🧪 جاري اختبار قاعدة البيانات...', 'info');
            
            setTimeout(() => {
                window.open('test-database-simple.html', '_blank');
                updateStatus('📊 تم فتح صفحة الاختبار في تبويب جديد', 'success');
            }, 1000);
        }

        // فحص حالة قاعدة البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                // فحص التخزين المحلي
                const storageItems = Object.keys(localStorage).length;
                
                if (storageItems === 0) {
                    updateStatus('⚠️ التخزين المحلي فارغ - قد تحتاج لإعادة إنشاء البيانات', 'error');
                } else {
                    updateStatus(`📊 تم العثور على ${storageItems} عنصر في التخزين المحلي`, 'info');
                    
                    // فحص البيانات المهمة
                    const hasShipments = localStorage.getItem('shipments') !== null;
                    const hasUsers = localStorage.getItem('users') !== null;
                    
                    if (hasShipments && hasUsers) {
                        updateStatus('✅ البيانات الأساسية موجودة - المشكلة قد تكون في التحميل', 'success');
                    } else {
                        updateStatus('⚠️ بعض البيانات الأساسية مفقودة', 'error');
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>
