<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم العميل | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            font-weight: 600;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .dashboard-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 100vh;
            gap: 0;
        }

        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0;
            position: fixed;
            height: 100vh;
            width: 300px;
            overflow-y: auto;
            box-shadow: 4px 0 20px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        .sidebar-header {
            padding: 30px 25px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            background: rgba(255,255,255,0.1);
        }

        .logo {
            width: 70px;
            height: 70px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 15px;
            backdrop-filter: blur(10px);
        }

        .user-info h3 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .user-info p {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
            border-right: 3px solid transparent;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(255,255,255,0.1);
            border-right-color: white;
        }

        .nav-icon {
            font-size: 1.3rem;
            width: 25px;
            text-align: center;
        }

        .main-content {
            margin-right: 280px;
            padding: 30px;
        }

        .top-bar {
            background: white;
            border-radius: 15px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.8rem;
            color: #333;
            font-weight: 600;
        }

        .user-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .notification-btn {
            position: relative;
            background: #f8f9fa;
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s;
        }

        .notification-btn:hover {
            background: #e9ecef;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-icon.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .stat-icon.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .stat-icon.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 20px;
            border-radius: 12px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
            text-decoration: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .action-btn.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .action-icon {
            font-size: 2rem;
        }

        .action-text {
            font-weight: 500;
        }

        .recent-shipments {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .shipment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .shipment-item:last-child {
            border-bottom: none;
        }

        .shipment-info h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .shipment-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .shipment-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-shipped {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-delivered {
            background: #d4edda;
            color: #155724;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                position: relative;
                width: 100%;
                height: auto;
            }
            
            .main-content {
                margin-right: 0;
                padding: 20px;
            }
            
            .top-bar {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">🚚</div>
                <div class="user-info">
                    <h3 id="userName">أحمد محمد</h3>
                    <p id="userEmail"><EMAIL></p>
                </div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                        <span class="nav-icon">🏠</span>
                        <span>لوحة التحكم</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('create-shipment')">
                        <span class="nav-icon">📦</span>
                        <span>إنشاء شحنة جديدة</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('my-shipments')">
                        <span class="nav-icon">📋</span>
                        <span>شحناتي</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('track-shipment')">
                        <span class="nav-icon">📍</span>
                        <span>تتبع الشحنات</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('profile')">
                        <span class="nav-icon">👤</span>
                        <span>الملف الشخصي</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('billing')">
                        <span class="nav-icon">💳</span>
                        <span>الفواتير والدفع</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('support')">
                        <span class="nav-icon">🎧</span>
                        <span>الدعم الفني</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- الشريط العلوي -->
            <div class="top-bar">
                <h1 class="page-title" id="pageTitle">لوحة التحكم</h1>
                <div class="user-actions">
                    <button class="notification-btn" onclick="showNotifications()">
                        🔔
                        <span class="notification-badge">3</span>
                    </button>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- محتوى لوحة التحكم -->
            <div id="dashboardSection">
                <!-- بطاقات الإحصائيات -->
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-number" id="totalShipments">12</div>
                                <div class="stat-label">إجمالي الشحنات</div>
                            </div>
                            <div class="stat-icon primary">📦</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-number" id="deliveredShipments">8</div>
                                <div class="stat-label">شحنات مُسلمة</div>
                            </div>
                            <div class="stat-icon success">✅</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-number" id="pendingShipments">3</div>
                                <div class="stat-label">شحنات قيد التوصيل</div>
                            </div>
                            <div class="stat-icon warning">🚛</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-number" id="totalSpent">2,450</div>
                                <div class="stat-label">إجمالي المبلغ (ريال)</div>
                            </div>
                            <div class="stat-icon info">💰</div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="quick-actions">
                    <h2 class="section-title">الإجراءات السريعة</h2>
                    <div class="actions-grid">
                        <a href="#" class="action-btn" onclick="showSection('create-shipment')">
                            <span class="action-icon">📦</span>
                            <span class="action-text">إنشاء شحنة جديدة</span>
                        </a>
                        <a href="#" class="action-btn secondary" onclick="showSection('track-shipment')">
                            <span class="action-icon">📍</span>
                            <span class="action-text">تتبع شحنة</span>
                        </a>
                        <a href="#" class="action-btn warning" onclick="showSection('billing')">
                            <span class="action-icon">💳</span>
                            <span class="action-text">عرض الفواتير</span>
                        </a>
                        <a href="#" class="action-btn" onclick="showSection('support')">
                            <span class="action-icon">🎧</span>
                            <span class="action-text">الدعم الفني</span>
                        </a>
                    </div>
                </div>

                <!-- الشحنات الأخيرة -->
                <div class="recent-shipments">
                    <h2 class="section-title">الشحنات الأخيرة</h2>
                    <div id="recentShipmentsList">
                        <!-- سيتم تحميل الشحنات هنا -->
                    </div>
                </div>
            </div>

            <!-- أقسام أخرى ستكون مخفية -->
            <div id="createShipmentSection" style="display: none;">
                <!-- محتوى إنشاء شحنة جديدة -->
            </div>

            <div id="myShipmentsSection" style="display: none;">
                <!-- محتوى شحناتي -->
            </div>

            <div id="trackShipmentSection" style="display: none;">
                <!-- محتوى تتبع الشحنات -->
            </div>

            <div id="profileSection" style="display: none;">
                <!-- محتوى الملف الشخصي -->
            </div>

            <div id="billingSection" style="display: none;">
                <!-- محتوى الفواتير والدفع -->
            </div>

            <div id="supportSection" style="display: none;">
                <!-- محتوى الدعم الفني -->
            </div>
        </div>
    </div>

    <script>
        // بيانات المستخدم والشحنات
        let currentUser = {
            name: 'أحمد محمد',
            email: '<EMAIL>',
            phone: '0501234567',
            address: 'الرياض، المملكة العربية السعودية'
        };

        let userShipments = [
            {
                id: 'SH001',
                recipient: 'سارة أحمد',
                destination: 'جدة',
                status: 'delivered',
                date: '2024-01-15',
                amount: 45
            },
            {
                id: 'SH002',
                recipient: 'محمد علي',
                destination: 'الدمام',
                status: 'shipped',
                date: '2024-01-18',
                amount: 35
            },
            {
                id: 'SH003',
                recipient: 'فاطمة خالد',
                destination: 'الرياض',
                status: 'pending',
                date: '2024-01-20',
                amount: 25
            }
        ];

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من تسجيل الدخول
            checkAuthentication();

            // تحميل بيانات المستخدم
            loadUserData();

            // تحميل الإحصائيات
            loadStatistics();

            // تحميل الشحنات الأخيرة
            loadRecentShipments();
        });

        // التحقق من تسجيل الدخول
        function checkAuthentication() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userType = localStorage.getItem('userType');

            if (!isLoggedIn || userType !== 'customer') {
                window.location.href = 'unified-login.html';
                return;
            }
        }

        // تحميل بيانات المستخدم
        function loadUserData() {
            const userEmail = localStorage.getItem('userEmail');
            if (userEmail) {
                currentUser.email = userEmail;
                document.getElementById('userEmail').textContent = userEmail;
            }

            document.getElementById('userName').textContent = currentUser.name;
        }

        // تحميل الإحصائيات
        function loadStatistics() {
            const totalShipments = userShipments.length;
            const deliveredShipments = userShipments.filter(s => s.status === 'delivered').length;
            const pendingShipments = userShipments.filter(s => s.status === 'pending').length;
            const totalSpent = userShipments.reduce((sum, s) => sum + s.amount, 0);

            document.getElementById('totalShipments').textContent = totalShipments;
            document.getElementById('deliveredShipments').textContent = deliveredShipments;
            document.getElementById('pendingShipments').textContent = pendingShipments;
            document.getElementById('totalSpent').textContent = totalSpent.toLocaleString();
        }

        // تحميل الشحنات الأخيرة
        function loadRecentShipments() {
            const container = document.getElementById('recentShipmentsList');
            container.innerHTML = '';

            const recentShipments = userShipments.slice(-5).reverse();

            recentShipments.forEach(shipment => {
                const shipmentElement = document.createElement('div');
                shipmentElement.className = 'shipment-item';

                const statusClass = getStatusClass(shipment.status);
                const statusText = getStatusText(shipment.status);

                shipmentElement.innerHTML = `
                    <div class="shipment-info">
                        <h4>شحنة #${shipment.id}</h4>
                        <p>إلى: ${shipment.recipient} - ${shipment.destination}</p>
                        <p>التاريخ: ${formatDate(shipment.date)}</p>
                    </div>
                    <div>
                        <div class="shipment-status ${statusClass}">${statusText}</div>
                        <p style="margin-top: 5px; color: #666; font-size: 0.9rem;">${shipment.amount} ريال</p>
                    </div>
                `;

                container.appendChild(shipmentElement);
            });
        }

        // الحصول على فئة الحالة
        function getStatusClass(status) {
            const classes = {
                'pending': 'status-pending',
                'shipped': 'status-shipped',
                'delivered': 'status-delivered'
            };
            return classes[status] || 'status-pending';
        }

        // الحصول على نص الحالة
        function getStatusText(status) {
            const texts = {
                'pending': 'قيد المعالجة',
                'shipped': 'في الطريق',
                'delivered': 'تم التسليم'
            };
            return texts[status] || 'غير محدد';
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // عرض قسم معين
        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            const sections = [
                'dashboardSection',
                'createShipmentSection',
                'myShipmentsSection',
                'trackShipmentSection',
                'profileSection',
                'billingSection',
                'supportSection'
            ];

            sections.forEach(section => {
                const element = document.getElementById(section);
                if (element) {
                    element.style.display = 'none';
                }
            });

            // إظهار القسم المطلوب
            const targetSection = document.getElementById(sectionName + 'Section');
            if (targetSection) {
                targetSection.style.display = 'block';
            }

            // تحديث الروابط النشطة
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // تحديث عنوان الصفحة
            const titles = {
                'dashboard': 'لوحة التحكم',
                'create-shipment': 'إنشاء شحنة جديدة',
                'my-shipments': 'شحناتي',
                'track-shipment': 'تتبع الشحنات',
                'profile': 'الملف الشخصي',
                'billing': 'الفواتير والدفع',
                'support': 'الدعم الفني'
            };

            document.getElementById('pageTitle').textContent = titles[sectionName] || 'لوحة التحكم';

            // تحميل محتوى القسم
            loadSectionContent(sectionName);
        }

        // تحميل محتوى القسم
        function loadSectionContent(sectionName) {
            switch(sectionName) {
                case 'create-shipment':
                    loadCreateShipmentForm();
                    break;
                case 'my-shipments':
                    loadMyShipments();
                    break;
                case 'track-shipment':
                    loadTrackingForm();
                    break;
                case 'profile':
                    loadProfile();
                    break;
                case 'billing':
                    loadBilling();
                    break;
                case 'support':
                    loadSupport();
                    break;
            }
        }

        // تحميل نموذج إنشاء شحنة
        function loadCreateShipmentForm() {
            const section = document.getElementById('createShipmentSection');
            section.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                    <h2 style="margin-bottom: 25px; color: #333;">إنشاء شحنة جديدة</h2>
                    <form id="shipmentForm" onsubmit="createShipment(event)">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">اسم المرسل إليه</label>
                                <input type="text" required style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">رقم هاتف المستلم</label>
                                <input type="tel" required style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            </div>
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">عنوان التسليم</label>
                            <textarea required style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; height: 80px;"></textarea>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">المدينة</label>
                                <select required style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                                    <option value="">اختر المدينة</option>
                                    <option value="riyadh">الرياض</option>
                                    <option value="jeddah">جدة</option>
                                    <option value="dammam">الدمام</option>
                                    <option value="mecca">مكة المكرمة</option>
                                    <option value="medina">المدينة المنورة</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">نوع الشحنة</label>
                                <select required style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                                    <option value="">اختر نوع الشحنة</option>
                                    <option value="documents">مستندات</option>
                                    <option value="electronics">إلكترونيات</option>
                                    <option value="clothing">ملابس</option>
                                    <option value="food">مواد غذائية</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">الوزن (كيلو)</label>
                                <input type="number" step="0.1" required style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">القيمة المعلنة (ريال)</label>
                                <input type="number" style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            </div>
                        </div>
                        <button type="submit" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 1.1rem; font-weight: 600; cursor: pointer; width: 100%;">
                            إنشاء الشحنة
                        </button>
                    </form>
                </div>
            `;
        }

        // إنشاء شحنة جديدة
        function createShipment(event) {
            event.preventDefault();

            // محاكاة إنشاء الشحنة
            const newShipmentId = 'SH' + String(Date.now()).slice(-6);

            alert(`تم إنشاء الشحنة بنجاح!\nرقم الشحنة: ${newShipmentId}\n\nيمكنك تتبع شحنتك من قسم "تتبع الشحنات"`);

            // إعادة تعيين النموذج
            document.getElementById('shipmentForm').reset();

            // العودة للوحة التحكم
            showSection('dashboard');
        }

        // عرض الإشعارات
        function showNotifications() {
            alert('الإشعارات:\n\n• تم تسليم الشحنة SH001 بنجاح\n• الشحنة SH002 في الطريق إليك\n• تذكير: فاتورة بقيمة 150 ريال مستحقة');
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userType');
                localStorage.removeItem('userEmail');
                window.location.href = 'unified-login.html';
            }
        }

        // وظائف إضافية للأقسام الأخرى
        function loadMyShipments() {
            const section = document.getElementById('myShipmentsSection');
            section.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                    <h2 style="margin-bottom: 25px; color: #333;">جميع شحناتي</h2>
                    <div id="allShipmentsList"></div>
                </div>
            `;

            // تحميل جميع الشحنات
            const container = document.getElementById('allShipmentsList');
            userShipments.forEach(shipment => {
                const shipmentElement = document.createElement('div');
                shipmentElement.style.cssText = 'border: 1px solid #e1e5e9; border-radius: 8px; padding: 20px; margin-bottom: 15px;';

                const statusClass = getStatusClass(shipment.status);
                const statusText = getStatusText(shipment.status);

                shipmentElement.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h4 style="color: #333; margin-bottom: 5px;">شحنة #${shipment.id}</h4>
                            <p style="color: #666; margin-bottom: 3px;">إلى: ${shipment.recipient} - ${shipment.destination}</p>
                            <p style="color: #666; font-size: 0.9rem;">التاريخ: ${formatDate(shipment.date)}</p>
                        </div>
                        <div style="text-align: left;">
                            <div class="shipment-status ${statusClass}" style="margin-bottom: 10px;">${statusText}</div>
                            <p style="color: #666; font-weight: 600;">${shipment.amount} ريال</p>
                            <button onclick="trackShipment('${shipment.id}')" style="background: #667eea; color: white; border: none; padding: 8px 15px; border-radius: 5px; margin-top: 10px; cursor: pointer;">تتبع</button>
                        </div>
                    </div>
                `;

                container.appendChild(shipmentElement);
            });
        }

        function loadTrackingForm() {
            const section = document.getElementById('trackShipmentSection');
            section.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                    <h2 style="margin-bottom: 25px; color: #333;">تتبع الشحنات</h2>
                    <div style="margin-bottom: 30px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">رقم الشحنة</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" id="trackingNumber" placeholder="أدخل رقم الشحنة" style="flex: 1; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            <button onclick="performTracking()" style="background: #667eea; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer;">تتبع</button>
                        </div>
                    </div>
                    <div id="trackingResults"></div>
                </div>
            `;
        }

        function performTracking() {
            const trackingNumber = document.getElementById('trackingNumber').value.trim();
            if (!trackingNumber) {
                alert('يرجى إدخال رقم الشحنة');
                return;
            }

            const shipment = userShipments.find(s => s.id === trackingNumber);
            const resultsDiv = document.getElementById('trackingResults');

            if (shipment) {
                resultsDiv.innerHTML = `
                    <div style="border: 1px solid #e1e5e9; border-radius: 8px; padding: 20px;">
                        <h3 style="color: #333; margin-bottom: 15px;">تفاصيل الشحنة #${shipment.id}</h3>
                        <p><strong>المستلم:</strong> ${shipment.recipient}</p>
                        <p><strong>الوجهة:</strong> ${shipment.destination}</p>
                        <p><strong>التاريخ:</strong> ${formatDate(shipment.date)}</p>
                        <p><strong>الحالة:</strong> <span class="shipment-status ${getStatusClass(shipment.status)}">${getStatusText(shipment.status)}</span></p>
                        <p><strong>المبلغ:</strong> ${shipment.amount} ريال</p>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px;">
                        لم يتم العثور على شحنة بهذا الرقم
                    </div>
                `;
            }
        }

        function trackShipment(shipmentId) {
            showSection('track-shipment');
            setTimeout(() => {
                document.getElementById('trackingNumber').value = shipmentId;
                performTracking();
            }, 100);
        }

        function loadProfile() {
            const section = document.getElementById('profileSection');
            section.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                    <h2 style="margin-bottom: 25px; color: #333;">الملف الشخصي</h2>
                    <form>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">الاسم الكامل</label>
                                <input type="text" value="${currentUser.name}" style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">البريد الإلكتروني</label>
                                <input type="email" value="${currentUser.email}" style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">رقم الهاتف</label>
                                <input type="tel" value="${currentUser.phone}" style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">تاريخ الميلاد</label>
                                <input type="date" style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                            </div>
                        </div>
                        <div style="margin-bottom: 30px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">العنوان</label>
                            <textarea style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; height: 80px;">${currentUser.address}</textarea>
                        </div>
                        <button type="button" onclick="updateProfile()" style="background: #28a745; color: white; border: none; padding: 15px 30px; border-radius: 8px; font-weight: 600; cursor: pointer;">
                            حفظ التغييرات
                        </button>
                    </form>
                </div>
            `;
        }

        function updateProfile() {
            alert('تم حفظ التغييرات بنجاح!');
        }

        function loadBilling() {
            const section = document.getElementById('billingSection');
            section.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                    <h2 style="margin-bottom: 25px; color: #333;">الفواتير والدفع</h2>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h3 style="color: #333; margin-bottom: 10px;">الرصيد الحالي</h3>
                        <p style="font-size: 2rem; font-weight: 700; color: #28a745;">2,450 ريال</p>
                    </div>
                    <h3 style="margin-bottom: 15px; color: #333;">الفواتير الأخيرة</h3>
                    <div style="border: 1px solid #e1e5e9; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                        <div style="display: flex; justify-content: space-between;">
                            <span>فاتورة يناير 2024</span>
                            <span style="color: #28a745; font-weight: 600;">مدفوعة - 450 ريال</span>
                        </div>
                    </div>
                    <div style="border: 1px solid #e1e5e9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between;">
                            <span>فاتورة ديسمبر 2023</span>
                            <span style="color: #28a745; font-weight: 600;">مدفوعة - 320 ريال</span>
                        </div>
                    </div>
                    <button onclick="addCredit()" style="background: #667eea; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer;">
                        إضافة رصيد
                    </button>
                </div>
            `;
        }

        function addCredit() {
            const amount = prompt('أدخل المبلغ المراد إضافته:');
            if (amount && !isNaN(amount)) {
                alert(`تم إضافة ${amount} ريال إلى رصيدك بنجاح!`);
            }
        }

        function loadSupport() {
            const section = document.getElementById('supportSection');
            section.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                    <h2 style="margin-bottom: 25px; color: #333;">الدعم الفني</h2>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 10px;">📞</div>
                            <h4>اتصل بنا</h4>
                            <p>920001234</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 10px;">💬</div>
                            <h4>الدردشة المباشرة</h4>
                            <button onclick="startChat()" style="background: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">ابدأ المحادثة</button>
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 10px;">✉️</div>
                            <h4>البريد الإلكتروني</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <h3 style="margin-bottom: 15px; color: #333;">إرسال رسالة</h3>
                    <form onsubmit="sendSupportMessage(event)">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">الموضوع</label>
                            <input type="text" required style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">الرسالة</label>
                            <textarea required style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; height: 120px;"></textarea>
                        </div>
                        <button type="submit" style="background: #667eea; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer;">
                            إرسال الرسالة
                        </button>
                    </form>
                </div>
            `;
        }

        function startChat() {
            alert('سيتم توصيلك بأحد ممثلي خدمة العملاء خلال دقائق...');
        }

        function sendSupportMessage(event) {
            event.preventDefault();
            alert('تم إرسال رسالتك بنجاح! سنرد عليك خلال 24 ساعة.');
            event.target.reset();
        }
    </script>

    <!-- تطبيق خط SF Pro Arabic Display على جميع العناصر -->
    <style>
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, textarea, select, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }
    </style>
</body>
</html>
