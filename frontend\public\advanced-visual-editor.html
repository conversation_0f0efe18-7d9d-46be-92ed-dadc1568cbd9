<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المحرر البصري المتقدم | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: #f5f7fa;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .editor-container {
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            height: 100vh;
        }

        .sidebar {
            background: white;
            border-right: 1px solid #e1e5e9;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.05);
        }

        .sidebar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .page-selector {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
        }

        .page-selector label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .page-select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }

        .page-select:focus {
            outline: none;
            border-color: #667eea;
        }

        .sections-list {
            padding: 20px;
        }

        .section-item {
            background: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 10px;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s;
            cursor: pointer;
        }

        .section-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .section-item.active {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .section-header {
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
        }

        .section-info h3 {
            font-size: 1rem;
            color: #333;
            margin-bottom: 3px;
        }

        .section-info p {
            font-size: 0.85rem;
            color: #666;
        }

        .main-editor {
            background: white;
            overflow-y: auto;
            position: relative;
        }

        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #e1e5e9;
            padding: 15px 20px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .toolbar-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .toolbar-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .toolbar-btn.secondary {
            background: #6c757d;
        }

        .toolbar-btn.success {
            background: #28a745;
        }

        .toolbar-btn.danger {
            background: #dc3545;
        }

        .toolbar-btn.warning {
            background: #ffc107;
            color: #333;
        }

        .editor-content {
            padding: 20px;
            min-height: calc(100vh - 80px);
        }

        .drag-drop-area {
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .drag-drop-area:hover,
        .drag-drop-area.dragover {
            border-color: #667eea;
            background: #f8f9ff;
            transform: scale(1.02);
        }

        .drag-drop-area.dragover {
            border-style: solid;
            background: #e3f2fd;
        }

        .drop-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .drop-text {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .drop-hint {
            color: #666;
            font-size: 0.95rem;
        }

        .elements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .element-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: grab;
            transition: all 0.3s;
            position: relative;
        }

        .element-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .element-card:active {
            cursor: grabbing;
        }

        .element-card.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .element-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #667eea;
        }

        .element-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .element-description {
            font-size: 0.85rem;
            color: #666;
        }

        .properties-panel {
            background: white;
            border-left: 1px solid #e1e5e9;
            overflow-y: auto;
            box-shadow: -2px 0 10px rgba(0,0,0,0.05);
        }

        .panel-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .panel-header h3 {
            color: #333;
            margin-bottom: 5px;
            font-size: 1.2rem;
        }

        .panel-content {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .color-picker {
            width: 100%;
            height: 45px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
        }

        .btn-group {
            display: flex;
            gap: 8px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 8px 15px;
            font-size: 0.85rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.95);
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
            transition: all 0.3s;
        }

        .back-link:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            transform: translateY(-100px);
            transition: transform 0.3s;
            z-index: 1000;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .save-indicator.show {
            transform: translateY(0);
        }

        .preview-area {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            min-height: 200px;
            position: relative;
        }

        .preview-area.drop-target {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .preview-placeholder {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        @media (max-width: 1024px) {
            .editor-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr auto;
            }
            
            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e1e5e9;
                max-height: 300px;
            }
            
            .properties-panel {
                border-left: none;
                border-top: 1px solid #e1e5e9;
                max-height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="save-indicator" id="saveIndicator">
        ✅ تم الحفظ تلقائياً
    </div>

    <a href="pages-management.html" class="back-link">← العودة لإدارة الصفحات</a>

    <div class="editor-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🎨 المحرر المتقدم</h2>
                <p>تحرير بصري مع السحب والإفلات</p>
            </div>
            
            <div class="page-selector">
                <label for="pageSelect">اختر الصفحة للتحرير:</label>
                <select id="pageSelect" class="page-select" onchange="loadPage(this.value)">
                    <option value="home">🏠 الصفحة الرئيسية</option>
                    <option value="about">📖 صفحة من نحن</option>
                    <option value="partners">🤝 شركاء النجاح</option>
                </select>
            </div>

            <div class="sections-list" id="sectionsList">
                <!-- سيتم تحميل أقسام الصفحة هنا -->
            </div>
        </div>

        <!-- المحرر الرئيسي -->
        <div class="main-editor">
            <div class="editor-toolbar">
                <button class="toolbar-btn" onclick="saveChanges()">
                    💾 حفظ
                </button>
                <button class="toolbar-btn secondary" onclick="previewPage()">
                    👁️ معاينة
                </button>
                <button class="toolbar-btn success" onclick="publishPage()">
                    🚀 نشر
                </button>
                <button class="toolbar-btn warning" onclick="duplicatePage()">
                    📋 نسخ الصفحة
                </button>
                <button class="toolbar-btn danger" onclick="resetChanges()">
                    🔄 إعادة تعيين
                </button>
                <span style="margin-right: 20px; color: #666; font-size: 0.9rem;">
                    الصفحة: <span id="currentPageName">الصفحة الرئيسية</span> | 
                    آخر حفظ: <span id="lastSaved">الآن</span>
                </span>
            </div>

            <div class="editor-content">
                <!-- منطقة السحب والإفلات -->
                <div class="drag-drop-area" id="dragDropArea" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <div class="drop-icon">📁</div>
                    <div class="drop-text">اسحب العناصر هنا لإضافتها</div>
                    <div class="drop-hint">أو انقر لاختيار عنصر من المكتبة</div>
                </div>

                <!-- مكتبة العناصر -->
                <h3 style="margin-bottom: 20px; color: #333;">🧩 مكتبة العناصر</h3>
                <div class="elements-grid" id="elementsGrid">
                    <!-- سيتم تحميل العناصر هنا -->
                </div>

                <!-- منطقة المعاينة -->
                <h3 style="margin-bottom: 20px; color: #333;">👁️ معاينة الصفحة</h3>
                <div class="preview-area" id="previewArea">
                    <div class="preview-placeholder">اسحب العناصر هنا لرؤية المعاينة</div>
                </div>
            </div>
        </div>

        <!-- لوحة الخصائص -->
        <div class="properties-panel">
            <div class="panel-header">
                <h3 id="panelTitle">خصائص العنصر</h3>
                <p id="panelDescription">اختر عنصراً لتحرير خصائصه</p>
            </div>
            <div class="panel-content" id="panelContent">
                <div style="text-align: center; color: #666; padding: 40px 20px;">
                    <div style="font-size: 3rem; margin-bottom: 15px;">⚙️</div>
                    <p>اختر عنصراً من المكتبة أو من الصفحة لتحرير خصائصه</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الصفحات
        let pagesData = {
            home: {
                name: 'الصفحة الرئيسية',
                sections: [
                    { id: 'hero', name: 'القسم الرئيسي', icon: '🏠', description: 'العنوان والوصف الرئيسي' },
                    { id: 'tracking', name: 'تتبع الشحنات', icon: '📍', description: 'قسم تتبع الطلبات' },
                    { id: 'services', name: 'الخدمات', icon: '⚙️', description: 'خدمات الشركة المتنوعة' },
                    { id: 'stats', name: 'الإحصائيات', icon: '📊', description: 'أرقام ومؤشرات الأداء' },
                    { id: 'footer', name: 'التذييل', icon: '📄', description: 'معلومات التواصل والروابط' }
                ]
            },
            about: {
                name: 'صفحة من نحن',
                sections: [
                    { id: 'hero', name: 'القسم الرئيسي', icon: '📖', description: 'عنوان الصفحة والمقدمة' },
                    { id: 'story', name: 'قصة الشركة', icon: '📚', description: 'تاريخ وقصة تأسيس الشركة' },
                    { id: 'values', name: 'القيم والمبادئ', icon: '⭐', description: 'قيم ومبادئ الشركة' },
                    { id: 'timeline', name: 'الخط الزمني', icon: '📅', description: 'تطور الشركة عبر السنين' },
                    { id: 'team', name: 'فريق العمل', icon: '👥', description: 'أعضاء الفريق والقيادة' },
                    { id: 'achievements', name: 'الإنجازات', icon: '🏆', description: 'إنجازات وجوائز الشركة' }
                ]
            },
            partners: {
                name: 'شركاء النجاح',
                sections: [
                    { id: 'hero', name: 'القسم الرئيسي', icon: '🤝', description: 'عنوان الصفحة والمقدمة' },
                    { id: 'strategic', name: 'الشركاء الاستراتيجيون', icon: '🏢', description: 'الشركاء الرئيسيون' },
                    { id: 'categories', name: 'تصنيف الشركاء', icon: '📂', description: 'تصنيف حسب القطاع' },
                    { id: 'success-stories', name: 'قصص النجاح', icon: '📈', description: 'قصص نجاح مع الشركاء' },
                    { id: 'testimonials', name: 'آراء الشركاء', icon: '💬', description: 'شهادات وآراء الشركاء' },
                    { id: 'join-us', name: 'انضم إلينا', icon: '➕', description: 'دعوة للشراكة الجديدة' }
                ]
            }
        };

        // عناصر المكتبة
        let elementsLibrary = [
            { id: 'text', name: 'نص', icon: '📝', description: 'فقرة نصية', type: 'content' },
            { id: 'heading', name: 'عنوان', icon: '📰', description: 'عنوان رئيسي أو فرعي', type: 'content' },
            { id: 'image', name: 'صورة', icon: '🖼️', description: 'صورة أو أيقونة', type: 'media' },
            { id: 'button', name: 'زر', icon: '🔘', description: 'زر تفاعلي', type: 'interactive' },
            { id: 'card', name: 'بطاقة', icon: '🃏', description: 'بطاقة محتوى', type: 'layout' },
            { id: 'grid', name: 'شبكة', icon: '⚏', description: 'تخطيط شبكي', type: 'layout' },
            { id: 'timeline', name: 'خط زمني', icon: '📅', description: 'عرض زمني للأحداث', type: 'special' },
            { id: 'stats', name: 'إحصائية', icon: '📊', description: 'رقم أو إحصائية', type: 'special' },
            { id: 'testimonial', name: 'شهادة', icon: '💬', description: 'شهادة عميل', type: 'special' },
            { id: 'team-member', name: 'عضو فريق', icon: '👤', description: 'بطاقة عضو فريق', type: 'special' },
            { id: 'partner-logo', name: 'شعار شريك', icon: '🏢', description: 'شعار شركة شريكة', type: 'special' },
            { id: 'contact-info', name: 'معلومات تواصل', icon: '📞', description: 'معلومات الاتصال', type: 'content' }
        ];

        let currentPage = 'home';
        let currentSection = null;
        let selectedElement = null;
        let draggedElement = null;

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadPage('home');
            loadElementsLibrary();
            setupDragAndDrop();
        });

        // تحميل صفحة
        function loadPage(pageId) {
            currentPage = pageId;
            const pageData = pagesData[pageId];

            // تحديث اسم الصفحة
            document.getElementById('currentPageName').textContent = pageData.name;

            // تحميل أقسام الصفحة
            loadSections(pageData.sections);

            // تحديث مكتبة العناصر
            loadElementsLibrary();

            // مسح المعاينة
            clearPreview();

            console.log(`تم تحميل ${pageData.name}`);
        }

        // تحميل أقسام الصفحة
        function loadSections(sections) {
            const sectionsList = document.getElementById('sectionsList');
            sectionsList.innerHTML = '';

            sections.forEach(section => {
                const sectionElement = document.createElement('div');
                sectionElement.className = 'section-item';
                sectionElement.dataset.section = section.id;
                sectionElement.onclick = () => selectSection(section.id);

                sectionElement.innerHTML = `
                    <div class="section-header">
                        <div class="section-icon">${section.icon}</div>
                        <div class="section-info">
                            <h3>${section.name}</h3>
                            <p>${section.description}</p>
                        </div>
                    </div>
                `;

                sectionsList.appendChild(sectionElement);
            });
        }

        // اختيار قسم
        function selectSection(sectionId) {
            currentSection = sectionId;

            // تحديث الواجهة
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

            // تحديث لوحة الخصائص
            updatePropertiesPanel(sectionId);

            console.log(`تم اختيار القسم: ${sectionId}`);
        }

        // تحميل مكتبة العناصر
        function loadElementsLibrary() {
            const elementsGrid = document.getElementById('elementsGrid');
            elementsGrid.innerHTML = '';

            elementsLibrary.forEach(element => {
                const elementCard = document.createElement('div');
                elementCard.className = 'element-card';
                elementCard.draggable = true;
                elementCard.dataset.elementId = element.id;

                elementCard.innerHTML = `
                    <div class="element-icon">${element.icon}</div>
                    <div class="element-title">${element.name}</div>
                    <div class="element-description">${element.description}</div>
                `;

                // إعداد السحب
                elementCard.addEventListener('dragstart', handleDragStart);
                elementCard.addEventListener('dragend', handleDragEnd);
                elementCard.addEventListener('click', () => selectElement(element));

                elementsGrid.appendChild(elementCard);
            });
        }

        // إعداد السحب والإفلات
        function setupDragAndDrop() {
            const dragDropArea = document.getElementById('dragDropArea');
            const previewArea = document.getElementById('previewArea');

            // منطقة السحب الرئيسية
            dragDropArea.addEventListener('click', () => {
                if (selectedElement) {
                    addElementToPreview(selectedElement);
                }
            });

            // منطقة المعاينة
            previewArea.addEventListener('drop', handlePreviewDrop);
            previewArea.addEventListener('dragover', handlePreviewDragOver);
            previewArea.addEventListener('dragleave', handlePreviewDragLeave);
        }

        // بداية السحب
        function handleDragStart(e) {
            draggedElement = elementsLibrary.find(el => el.id === this.dataset.elementId);
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'copy';
            e.dataTransfer.setData('text/plain', draggedElement.id);
        }

        // نهاية السحب
        function handleDragEnd(e) {
            this.classList.remove('dragging');
            draggedElement = null;
        }

        // السحب فوق المنطقة
        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
            document.getElementById('dragDropArea').classList.add('dragover');
        }

        // مغادرة منطقة السحب
        function handleDragLeave(e) {
            document.getElementById('dragDropArea').classList.remove('dragover');
        }

        // إفلات في المنطقة الرئيسية
        function handleDrop(e) {
            e.preventDefault();
            document.getElementById('dragDropArea').classList.remove('dragover');

            if (draggedElement) {
                addElementToPreview(draggedElement);
                showSaveIndicator();
            }
        }

        // السحب فوق منطقة المعاينة
        function handlePreviewDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
            document.getElementById('previewArea').classList.add('drop-target');
        }

        // مغادرة منطقة المعاينة
        function handlePreviewDragLeave(e) {
            document.getElementById('previewArea').classList.remove('drop-target');
        }

        // إفلات في منطقة المعاينة
        function handlePreviewDrop(e) {
            e.preventDefault();
            document.getElementById('previewArea').classList.remove('drop-target');

            if (draggedElement) {
                addElementToPreview(draggedElement);
                showSaveIndicator();
            }
        }

        // اختيار عنصر
        function selectElement(element) {
            selectedElement = element;

            // تحديث لوحة الخصائص
            updateElementProperties(element);

            // تحديث الواجهة
            document.querySelectorAll('.element-card').forEach(card => {
                card.style.borderColor = card.dataset.elementId === element.id ? '#667eea' : '#e1e5e9';
            });

            console.log(`تم اختيار العنصر: ${element.name}`);
        }

        // إضافة عنصر للمعاينة
        function addElementToPreview(element) {
            const previewArea = document.getElementById('previewArea');

            // إنشاء عنصر المعاينة
            const previewElement = document.createElement('div');
            previewElement.className = 'preview-element';
            previewElement.style.cssText = `
                background: white;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
                cursor: pointer;
                transition: all 0.3s;
            `;

            previewElement.innerHTML = generateElementPreview(element);

            // إضافة تفاعل
            previewElement.addEventListener('click', () => selectElement(element));
            previewElement.addEventListener('mouseover', function() {
                this.style.borderColor = '#667eea';
                this.style.transform = 'translateY(-2px)';
            });
            previewElement.addEventListener('mouseout', function() {
                this.style.borderColor = '#e1e5e9';
                this.style.transform = 'translateY(0)';
            });

            // إزالة placeholder إذا كان موجوداً
            const placeholder = previewArea.querySelector('.preview-placeholder');
            if (placeholder) {
                placeholder.remove();
            }

            previewArea.appendChild(previewElement);

            console.log(`تم إضافة ${element.name} للمعاينة`);
        }

        // إنشاء معاينة العنصر
        function generateElementPreview(element) {
            const previews = {
                text: `<p style="color: #333; line-height: 1.6;">هذا نص تجريبي يمكن تعديله من لوحة الخصائص. يمكنك تغيير المحتوى والتنسيق والألوان.</p>`,
                heading: `<h2 style="color: #667eea; margin-bottom: 10px;">عنوان تجريبي</h2>`,
                image: `<div style="background: #f8f9fa; border: 2px dashed #dee2e6; padding: 40px; text-align: center; border-radius: 8px;">
                    <div style="font-size: 3rem; margin-bottom: 10px;">🖼️</div>
                    <p style="color: #666;">صورة تجريبية - اضغط لتحديد صورة</p>
                </div>`,
                button: `<button style="background: #667eea; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; font-weight: 500;">زر تجريبي</button>`,
                card: `<div style="background: #f8f9fa; border-radius: 12px; padding: 20px; text-align: center;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">🃏</div>
                    <h3 style="color: #333; margin-bottom: 10px;">عنوان البطاقة</h3>
                    <p style="color: #666;">وصف البطاقة يمكن تعديله من الخصائص</p>
                </div>`,
                grid: `<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">عنصر 1</div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">عنصر 2</div>
                </div>`,
                timeline: `<div style="border-left: 3px solid #667eea; padding-left: 20px;">
                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h4 style="color: #667eea; margin-bottom: 5px;">2024</h4>
                        <p style="color: #333;">حدث مهم في تاريخ الشركة</p>
                    </div>
                </div>`,
                stats: `<div style="text-align: center; background: #f8f9fa; padding: 20px; border-radius: 8px;">
                    <div style="font-size: 2rem; font-weight: bold; color: #667eea; margin-bottom: 5px;">1000+</div>
                    <div style="color: #666;">عملاء راضون</div>
                </div>`,
                testimonial: `<div style="background: #f8f9fa; padding: 20px; border-radius: 12px; border-left: 4px solid #667eea;">
                    <p style="color: #333; font-style: italic; margin-bottom: 15px;">"شهادة عميل راضٍ عن الخدمة المقدمة"</p>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 40px; height: 40px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">👤</div>
                        <div>
                            <div style="font-weight: 600; color: #333;">اسم العميل</div>
                            <div style="color: #666; font-size: 0.9rem;">المنصب - الشركة</div>
                        </div>
                    </div>
                </div>`,
                'team-member': `<div style="text-align: center; background: #f8f9fa; padding: 20px; border-radius: 12px;">
                    <div style="width: 80px; height: 80px; background: #667eea; border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">👤</div>
                    <h4 style="color: #333; margin-bottom: 5px;">اسم عضو الفريق</h4>
                    <p style="color: #667eea; font-weight: 500; margin-bottom: 5px;">المنصب</p>
                    <p style="color: #666; font-size: 0.9rem;">وصف مختصر عن عضو الفريق</p>
                </div>`,
                'partner-logo': `<div style="background: white; border: 2px solid #e1e5e9; border-radius: 8px; padding: 20px; text-align: center;">
                    <div style="font-size: 3rem; margin-bottom: 10px;">🏢</div>
                    <p style="color: #666; font-size: 0.9rem;">شعار الشريك</p>
                </div>`,
                'contact-info': `<div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                        <span style="color: #667eea;">📞</span>
                        <span style="color: #333;">+966 11 123 4567</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                        <span style="color: #667eea;">✉️</span>
                        <span style="color: #333;"><EMAIL></span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span style="color: #667eea;">📍</span>
                        <span style="color: #333;">الرياض، المملكة العربية السعودية</span>
                    </div>
                </div>`
            };

            return previews[element.id] || `<div style="padding: 20px; text-align: center; color: #666;">
                <div style="font-size: 2rem; margin-bottom: 10px;">${element.icon}</div>
                <p>${element.name}</p>
            </div>`;
        }

        // تحديث لوحة الخصائص للقسم
        function updatePropertiesPanel(sectionId) {
            const panelTitle = document.getElementById('panelTitle');
            const panelDescription = document.getElementById('panelDescription');
            const panelContent = document.getElementById('panelContent');

            const pageData = pagesData[currentPage];
            const section = pageData.sections.find(s => s.id === sectionId);

            if (section) {
                panelTitle.textContent = `خصائص ${section.name}`;
                panelDescription.textContent = section.description;

                panelContent.innerHTML = generateSectionProperties(sectionId);
            }
        }

        // تحديث لوحة الخصائص للعنصر
        function updateElementProperties(element) {
            const panelTitle = document.getElementById('panelTitle');
            const panelDescription = document.getElementById('panelDescription');
            const panelContent = document.getElementById('panelContent');

            panelTitle.textContent = `خصائص ${element.name}`;
            panelDescription.textContent = element.description;

            panelContent.innerHTML = generateElementProperties(element);
        }

        // إنشاء خصائص القسم
        function generateSectionProperties(sectionId) {
            const commonProperties = `
                <div class="form-group">
                    <label>عنوان القسم</label>
                    <input type="text" class="form-control" placeholder="أدخل عنوان القسم">
                </div>
                <div class="form-group">
                    <label>وصف القسم</label>
                    <textarea class="form-control" rows="3" placeholder="أدخل وصف القسم"></textarea>
                </div>
                <div class="form-group">
                    <label>لون الخلفية</label>
                    <input type="color" class="color-picker" value="#ffffff">
                </div>
                <div class="form-group">
                    <label>المسافات الداخلية</label>
                    <select class="form-control">
                        <option>صغيرة</option>
                        <option selected>متوسطة</option>
                        <option>كبيرة</option>
                    </select>
                </div>
                <div class="btn-group">
                    <button class="btn-small btn-primary" onclick="applyChanges()">تطبيق</button>
                    <button class="btn-small btn-secondary" onclick="resetSection()">إعادة تعيين</button>
                </div>
            `;

            return commonProperties;
        }

        // إنشاء خصائص العنصر
        function generateElementProperties(element) {
            const baseProperties = `
                <div class="form-group">
                    <label>نوع العنصر</label>
                    <input type="text" class="form-control" value="${element.name}" readonly>
                </div>
            `;

            const specificProperties = {
                text: `
                    <div class="form-group">
                        <label>النص</label>
                        <textarea class="form-control" rows="4" placeholder="أدخل النص هنا">هذا نص تجريبي يمكن تعديله</textarea>
                    </div>
                    <div class="form-group">
                        <label>حجم الخط</label>
                        <select class="form-control">
                            <option>صغير</option>
                            <option selected>متوسط</option>
                            <option>كبير</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>لون النص</label>
                        <input type="color" class="color-picker" value="#333333">
                    </div>
                `,
                heading: `
                    <div class="form-group">
                        <label>نص العنوان</label>
                        <input type="text" class="form-control" placeholder="أدخل العنوان" value="عنوان تجريبي">
                    </div>
                    <div class="form-group">
                        <label>مستوى العنوان</label>
                        <select class="form-control">
                            <option>H1 - عنوان رئيسي</option>
                            <option selected>H2 - عنوان فرعي</option>
                            <option>H3 - عنوان صغير</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>لون العنوان</label>
                        <input type="color" class="color-picker" value="#667eea">
                    </div>
                `,
                button: `
                    <div class="form-group">
                        <label>نص الزر</label>
                        <input type="text" class="form-control" placeholder="أدخل نص الزر" value="زر تجريبي">
                    </div>
                    <div class="form-group">
                        <label>رابط الزر</label>
                        <input type="url" class="form-control" placeholder="https://example.com">
                    </div>
                    <div class="form-group">
                        <label>لون الزر</label>
                        <input type="color" class="color-picker" value="#667eea">
                    </div>
                    <div class="form-group">
                        <label>حجم الزر</label>
                        <select class="form-control">
                            <option>صغير</option>
                            <option selected>متوسط</option>
                            <option>كبير</option>
                        </select>
                    </div>
                `,
                image: `
                    <div class="form-group">
                        <label>رفع صورة</label>
                        <input type="file" class="form-control" accept="image/*">
                    </div>
                    <div class="form-group">
                        <label>نص بديل للصورة</label>
                        <input type="text" class="form-control" placeholder="وصف الصورة">
                    </div>
                    <div class="form-group">
                        <label>حجم الصورة</label>
                        <select class="form-control">
                            <option>صغير</option>
                            <option selected>متوسط</option>
                            <option>كبير</option>
                            <option>ملء الشاشة</option>
                        </select>
                    </div>
                `
            };

            const elementSpecific = specificProperties[element.id] || '';

            return baseProperties + elementSpecific + `
                <div class="btn-group">
                    <button class="btn-small btn-primary" onclick="updateElement()">تحديث</button>
                    <button class="btn-small btn-danger" onclick="deleteElement()">حذف</button>
                </div>
            `;
        }

        // مسح المعاينة
        function clearPreview() {
            const previewArea = document.getElementById('previewArea');
            previewArea.innerHTML = '<div class="preview-placeholder">اسحب العناصر هنا لرؤية المعاينة</div>';
        }

        // إظهار مؤشر الحفظ
        function showSaveIndicator() {
            const indicator = document.getElementById('saveIndicator');
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }

        // تحديث وقت آخر حفظ
        function updateLastSaved() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('lastSaved').textContent = timeString;
        }

        // وظائف شريط الأدوات
        function saveChanges() {
            // حفظ التغييرات
            const pageData = {
                page: currentPage,
                sections: currentSection,
                elements: Array.from(document.querySelectorAll('.preview-element')).map(el => ({
                    type: el.dataset.elementType || 'unknown',
                    content: el.innerHTML
                })),
                timestamp: new Date().toISOString()
            };

            localStorage.setItem(`pageEditor_${currentPage}`, JSON.stringify(pageData));
            showSaveIndicator();
            updateLastSaved();

            alert('تم حفظ التغييرات بنجاح!');
        }

        function previewPage() {
            // فتح معاينة الصفحة
            const currentPageData = pagesData[currentPage];
            const previewWindow = window.open('', '_blank', 'width=1200,height=800');

            previewWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>معاينة ${currentPageData.name}</title>
                    <style>
                        body { font-family: 'Cairo', sans-serif; margin: 0; padding: 20px; }
                        .preview-container { max-width: 1200px; margin: 0 auto; }
                        .preview-element { margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <div class="preview-container">
                        <h1>معاينة ${currentPageData.name}</h1>
                        ${document.getElementById('previewArea').innerHTML}
                    </div>
                </body>
                </html>
            `);
            previewWindow.document.close();
        }

        function publishPage() {
            if (confirm(`هل أنت متأكد من نشر ${pagesData[currentPage].name}؟`)) {
                // محاكاة النشر
                saveChanges();
                alert(`تم نشر ${pagesData[currentPage].name} بنجاح!`);
            }
        }

        function duplicatePage() {
            const pageName = prompt('أدخل اسم الصفحة الجديدة:');
            if (pageName && pageName.trim()) {
                // نسخ بيانات الصفحة الحالية
                const currentData = localStorage.getItem(`pageEditor_${currentPage}`);
                if (currentData) {
                    const newPageId = pageName.toLowerCase().replace(/\s+/g, '-');
                    localStorage.setItem(`pageEditor_${newPageId}`, currentData);
                    alert(`تم نسخ الصفحة باسم "${pageName}" بنجاح!`);
                } else {
                    alert('لا توجد بيانات محفوظة للصفحة الحالية');
                }
            }
        }

        function resetChanges() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
                clearPreview();
                selectedElement = null;
                currentSection = null;

                // إعادة تعيين لوحة الخصائص
                document.getElementById('panelContent').innerHTML = `
                    <div style="text-align: center; color: #666; padding: 40px 20px;">
                        <div style="font-size: 3rem; margin-bottom: 15px;">⚙️</div>
                        <p>اختر عنصراً من المكتبة أو من الصفحة لتحرير خصائصه</p>
                    </div>
                `;

                alert('تم إعادة تعيين التغييرات');
            }
        }

        // وظائف العناصر
        function applyChanges() {
            showSaveIndicator();
            alert('تم تطبيق التغييرات على القسم');
        }

        function resetSection() {
            if (confirm('هل تريد إعادة تعيين خصائص هذا القسم؟')) {
                updatePropertiesPanel(currentSection);
                alert('تم إعادة تعيين خصائص القسم');
            }
        }

        function updateElement() {
            showSaveIndicator();
            alert('تم تحديث العنصر');
        }

        function deleteElement() {
            if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                // حذف العنصر المحدد من المعاينة
                const previewElements = document.querySelectorAll('.preview-element');
                if (previewElements.length > 0) {
                    previewElements[previewElements.length - 1].remove();
                    showSaveIndicator();
                    alert('تم حذف العنصر');
                }
            }
        }

        // تحميل البيانات المحفوظة عند بدء التشغيل
        function loadSavedData() {
            const savedData = localStorage.getItem(`pageEditor_${currentPage}`);
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    console.log('تم تحميل البيانات المحفوظة:', data);
                } catch (error) {
                    console.error('خطأ في تحميل البيانات المحفوظة:', error);
                }
            }
        }

        // تحميل البيانات المحفوظة
        setTimeout(loadSavedData, 1000);
    </script>
</body>
</html>
