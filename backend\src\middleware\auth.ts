// Authentication Middleware
// وسطاء المصادقة

import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { PrismaClient, UserRole } from '@prisma/client'
import { config } from '../config/config'
import { createAuthError, createForbiddenError, AppError } from './errorHandler'
import { logger } from '../utils/logger'

const prisma = new PrismaClient()

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string
        email: string
        role: UserRole
        firstName: string
        lastName: string
        language: string
      }
    }
  }
}

// JWT payload interface
interface JWTPayload {
  userId: string
  email: string
  role: UserRole
  iat: number
  exp: number
}

// Extract token from request
function extractToken(req: Request): string | null {
  const authHeader = req.headers.authorization
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }
  
  return null
}

// Verify JWT token
function verifyToken(token: string): JWTPayload {
  try {
    return jwt.verify(token, config.jwt.secret) as JWTPayload
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new AppError('Token expired', 401, 'TOKEN_EXPIRED')
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new AppError('Invalid token', 401, 'INVALID_TOKEN')
    }
    throw new AppError('Authentication failed', 401, 'AUTH_FAILED')
  }
}

// Authentication middleware
export async function authenticate(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req)
    
    if (!token) {
      throw createAuthError('Access token required')
    }
    
    const payload = verifyToken(token)
    
    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        language: true,
        isActive: true,
      },
    })
    
    if (!user) {
      throw createAuthError('User not found')
    }
    
    if (!user.isActive) {
      throw createAuthError('Account is deactivated')
    }
    
    // Attach user to request
    req.user = user
    
    // Log successful authentication
    logger.debug('User authenticated', {
      userId: user.id,
      email: user.email,
      role: user.role,
      ip: req.ip,
    })
    
    next()
  } catch (error) {
    next(error)
  }
}

// Optional authentication middleware (doesn't throw if no token)
export async function optionalAuth(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req)
    
    if (token) {
      const payload = verifyToken(token)
      
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          language: true,
          isActive: true,
        },
      })
      
      if (user && user.isActive) {
        req.user = user
      }
    }
    
    next()
  } catch (error) {
    // Don't throw error for optional auth
    next()
  }
}

// Authorization middleware factory
export function authorize(...roles: UserRole[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createAuthError('Authentication required')
    }
    
    if (roles.length > 0 && !roles.includes(req.user.role)) {
      logger.warn('Unauthorized access attempt', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: roles,
        path: req.path,
        method: req.method,
        ip: req.ip,
      })
      
      throw createForbiddenError('Insufficient permissions')
    }
    
    next()
  }
}

// Resource ownership middleware
export function requireOwnership(resourceUserIdField: string = 'userId') {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw createAuthError('Authentication required')
      }
      
      // Admins can access any resource
      if (req.user.role === 'ADMIN') {
        return next()
      }
      
      const resourceId = req.params.id
      if (!resourceId) {
        throw new AppError('Resource ID required', 400, 'MISSING_RESOURCE_ID')
      }
      
      // This is a generic check - specific implementations should override
      // For now, we'll just pass through and let the route handler check ownership
      next()
    } catch (error) {
      next(error)
    }
  }
}

// Distributor-specific middleware
export function requireDistributor(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  if (!req.user) {
    throw createAuthError('Authentication required')
  }
  
  if (req.user.role !== 'DISTRIBUTOR' && req.user.role !== 'ADMIN') {
    throw createForbiddenError('Distributor access required')
  }
  
  next()
}

// Manager or Admin middleware
export function requireManagerOrAdmin(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  if (!req.user) {
    throw createAuthError('Authentication required')
  }
  
  if (!['MANAGER', 'ADMIN'].includes(req.user.role)) {
    throw createForbiddenError('Manager or Admin access required')
  }
  
  next()
}

// Admin only middleware
export function requireAdmin(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  if (!req.user) {
    throw createAuthError('Authentication required')
  }
  
  if (req.user.role !== 'ADMIN') {
    throw createForbiddenError('Admin access required')
  }
  
  next()
}

// Generate JWT token
export function generateToken(user: {
  id: string
  email: string
  role: UserRole
}): string {
  const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
    userId: user.id,
    email: user.email,
    role: user.role,
  }
  
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  })
}

// Refresh token middleware (for future implementation)
export async function refreshToken(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  // TODO: Implement refresh token logic
  next()
}
