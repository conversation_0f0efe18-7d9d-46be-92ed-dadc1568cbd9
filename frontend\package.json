{"name": "shipment-management-frontend", "version": "1.0.0", "description": "Frontend for Shipment Management System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "clean": "rimraf .next out dist", "analyze": "cross-env ANALYZE=true next build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@next/font": "^14.0.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-progress": "^1.0.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "zustand": "^4.4.7", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "next-i18next": "^15.2.0", "react-i18next": "^13.5.0", "i18next": "^23.7.6", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-datepicker": "^4.24.0", "react-hot-toast": "^2.4.1", "@supabase/supabase-js": "^2.38.5", "framer-motion": "^10.16.16", "react-intersection-observer": "^9.5.3"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-datepicker": "^4.19.4", "@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@playwright/test": "^1.40.1", "@storybook/addon-essentials": "^7.6.3", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/blocks": "^7.6.3", "@storybook/nextjs": "^7.6.3", "@storybook/react": "^7.6.3", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.3", "cross-env": "^7.0.3", "rimraf": "^5.0.5", "@next/bundle-analyzer": "^14.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["shipment", "delivery", "management", "nextjs", "react", "typescript", "tailwindcss", "arabic", "bilingual"], "author": "عصام", "license": "MIT"}