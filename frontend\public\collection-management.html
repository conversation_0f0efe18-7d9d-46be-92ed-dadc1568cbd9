<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التحصيل من المناديب - النظام المالي</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: var(--font-arabic-display);
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total {
            border-left-color: #28a745;
        }

        .stat-card.pending {
            border-left-color: #ffc107;
        }

        .stat-card.completed {
            border-left-color: #17a2b8;
        }

        .stat-card.overdue {
            border-left-color: #dc3545;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input,
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            min-width: 200px;
        }

        .search-input:focus,
        .filter-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .collections-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-completed {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .status-overdue {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
        }

        .form-input {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-data h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input,
            .filter-select {
                min-width: auto;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                justify-content: center;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <a href="financial-system.html" class="back-link">← العودة للنظام المالي</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>💵</span>
                <span>إدارة التحصيل من المناديب</span>
            </div>

            <nav class="nav-links">
                <a href="financial-system.html">الرئيسية</a>
                <a href="collection-management.html" class="active">التحصيل</a>
                <a href="payment-management.html">المدفوعات</a>
                <a href="commission-management.html">العمولات</a>
                <a href="invoice-management.html">الفواتير</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">💵 إدارة التحصيل من المناديب</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="openAddModal()">
                    ➕ تحصيل جديد
                </button>
                <button class="btn" onclick="loadCollections()">
                    🔄 تحديث القائمة
                </button>
            </div>
        </div>

        <!-- إحصائيات التحصيل -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="card-icon">💰</div>
                <div class="card-amount" id="totalCollections">0</div>
                <div class="card-label">إجمالي التحصيلات</div>
            </div>

            <div class="stat-card pending">
                <div class="card-icon">⏳</div>
                <div class="card-amount" id="pendingCollections">0</div>
                <div class="card-label">تحصيلات معلقة</div>
            </div>

            <div class="stat-card completed">
                <div class="card-icon">✅</div>
                <div class="card-amount" id="completedCollections">0</div>
                <div class="card-label">تحصيلات مكتملة</div>
            </div>

            <div class="stat-card overdue">
                <div class="card-icon">⚠️</div>
                <div class="card-amount" id="overdueCollections">0</div>
                <div class="card-label">تحصيلات متأخرة</div>
            </div>
        </div>

        <!-- أدوات التحكم والبحث -->
        <div class="controls">
            <input type="text" id="searchInput" class="search-input" placeholder="🔍 البحث في التحصيلات...">

            <select id="statusFilter" class="filter-select">
                <option value="">جميع الحالات</option>
                <option value="مكتمل">مكتمل</option>
                <option value="معلق">معلق</option>
                <option value="متأخر">متأخر</option>
            </select>

            <select id="distributorFilter" class="filter-select">
                <option value="">جميع المناديب</option>
            </select>

            <button class="btn" onclick="clearFilters()">مسح الفلاتر</button>
        </div>

        <!-- جدول التحصيلات -->
        <div class="collections-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم التحصيل</th>
                        <th>المندوب</th>
                        <th>المبلغ</th>
                        <th>العملة</th>
                        <th>تاريخ التحصيل</th>
                        <th>الحالة</th>
                        <th>عدد الشحنات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="collectionsTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>

            <!-- رسالة عدم وجود بيانات -->
            <div id="noDataMessage" class="no-data" style="display: none;">
                <h3>💵 لا توجد تحصيلات</h3>
                <p>لم يتم العثور على أي تحصيلات. ابدأ بإضافة تحصيل جديد.</p>
                <button class="btn btn-success" onclick="openAddModal()" style="margin-top: 15px;">
                    ➕ إضافة تحصيل جديد
                </button>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل التحصيل -->
    <div id="collectionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة تحصيل جديد</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>

            <div class="modal-body">
                <form id="collectionForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">المندوب *</label>
                            <select class="form-input" id="distributorId" required>
                                <option value="">اختر المندوب</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">المبلغ *</label>
                            <input type="number" class="form-input" id="amount" step="0.01" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">العملة</label>
                            <select class="form-input" id="currency">
                                <option value="SAR">ريال سعودي (SAR)</option>
                                <option value="KWD">دينار كويتي (KWD)</option>
                                <option value="USD">دولار أمريكي (USD)</option>
                                <option value="EUR">يورو (EUR)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">تاريخ التحصيل</label>
                            <input type="date" class="form-input" id="collectionDate">
                        </div>

                        <div class="form-group">
                            <label class="form-label">الحالة</label>
                            <select class="form-input" id="status">
                                <option value="مكتمل">مكتمل</option>
                                <option value="معلق">معلق</option>
                                <option value="متأخر">متأخر</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">الشحنات المرتبطة</label>
                            <input type="text" class="form-input" id="shipments" placeholder="أرقام الشحنات مفصولة بفاصلة">
                        </div>

                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-input" id="notes" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn" onclick="closeModal()">إلغاء</button>
                        <button type="submit" class="btn btn-success">💾 حفظ التحصيل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/database-simple.js"></script>
    <script src="js/financial-database.js"></script>
    <script>
        let currentEditId = null;

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💵 تحميل صفحة إدارة التحصيل...');

            try {
                loadCollections();
                loadDistributors();
                setupEventListeners();
                loadStats();

                // تعيين تاريخ اليوم كافتراضي
                document.getElementById('collectionDate').value = new Date().toISOString().split('T')[0];

                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('collectionForm').addEventListener('submit', saveCollection);
            document.getElementById('searchInput').addEventListener('input', filterCollections);
            document.getElementById('statusFilter').addEventListener('change', filterCollections);
            document.getElementById('distributorFilter').addEventListener('change', filterCollections);
        }

        // تحميل قائمة المناديب
        function loadDistributors() {
            try {
                const distributors = db.getAllDistributors();
                const distributorSelect = document.getElementById('distributorId');
                const distributorFilter = document.getElementById('distributorFilter');

                // مسح الخيارات الحالية
                distributorSelect.innerHTML = '<option value="">اختر المندوب</option>';
                distributorFilter.innerHTML = '<option value="">جميع المناديب</option>';

                distributors.forEach(distributor => {
                    const option1 = document.createElement('option');
                    option1.value = distributor.id;
                    option1.textContent = distributor.name;
                    distributorSelect.appendChild(option1);

                    const option2 = document.createElement('option');
                    option2.value = distributor.name;
                    option2.textContent = distributor.name;
                    distributorFilter.appendChild(option2);
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل المناديب:', error);
            }
        }

        // تحميل وعرض التحصيلات
        function loadCollections() {
            try {
                const collections = financialDb.getAllCollections();
                displayCollections(collections);
                console.log('💵 تم تحميل', collections.length, 'تحصيل');
            } catch (error) {
                console.error('❌ خطأ في تحميل التحصيلات:', error);
                alert('خطأ في تحميل التحصيلات: ' + error.message);
            }
        }

        // عرض التحصيلات في الجدول
        function displayCollections(collections) {
            const tbody = document.getElementById('collectionsTableBody');
            const noDataMessage = document.getElementById('noDataMessage');

            if (collections.length === 0) {
                tbody.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }

            noDataMessage.style.display = 'none';

            tbody.innerHTML = collections.map(collection =>
                `<tr>
                    <td><strong>${collection.id}</strong></td>
                    <td>${collection.distributorName}</td>
                    <td>${(collection.amount || 0).toFixed(2)}</td>
                    <td>${collection.currency || 'SAR'}</td>
                    <td>${formatDate(collection.collectionDate)}</td>
                    <td><span class="status-badge status-${getStatusClass(collection.status)}">${collection.status}</span></td>
                    <td>${collection.shipments ? collection.shipments.length : 0}</td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-small" onclick="viewCollection('${collection.id}')">عرض</button>
                            <button class="btn btn-small btn-warning" onclick="editCollection('${collection.id}')">تعديل</button>
                            <button class="btn btn-small btn-danger" onclick="deleteCollection('${collection.id}')">حذف</button>
                        </div>
                    </td>
                </tr>`
            ).join('');
        }

        // تحميل الإحصائيات
        function loadStats() {
            try {
                const collections = financialDb.getAllCollections();

                const totalAmount = collections.reduce((sum, c) => sum + (parseFloat(c.amount) || 0), 0);
                const pendingCount = collections.filter(c => c.status === 'معلق').length;
                const completedCount = collections.filter(c => c.status === 'مكتمل').length;
                const overdueCount = collections.filter(c => c.status === 'متأخر').length;

                document.getElementById('totalCollections').textContent = totalAmount.toFixed(2);
                document.getElementById('pendingCollections').textContent = pendingCount;
                document.getElementById('completedCollections').textContent = completedCount;
                document.getElementById('overdueCollections').textContent = overdueCount;

            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات:', error);
            }
        }

        // فلترة التحصيلات
        function filterCollections() {
            try {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;
                const distributorFilter = document.getElementById('distributorFilter').value;

                const allCollections = financialDb.getAllCollections();

                const filteredCollections = allCollections.filter(collection => {
                    const matchesSearch = !searchTerm ||
                        collection.id.toLowerCase().includes(searchTerm) ||
                        collection.distributorName.toLowerCase().includes(searchTerm) ||
                        (collection.notes && collection.notes.toLowerCase().includes(searchTerm));

                    const matchesStatus = !statusFilter || collection.status === statusFilter;
                    const matchesDistributor = !distributorFilter || collection.distributorName === distributorFilter;

                    return matchesSearch && matchesStatus && matchesDistributor;
                });

                displayCollections(filteredCollections);
            } catch (error) {
                console.error('❌ خطأ في فلترة التحصيلات:', error);
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('distributorFilter').value = '';
            loadCollections();
        }

        // فتح نافذة إضافة تحصيل
        function openAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = 'إضافة تحصيل جديد';
            document.getElementById('collectionForm').reset();
            document.getElementById('collectionDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('collectionModal').style.display = 'block';
        }

        // تعديل تحصيل
        function editCollection(id) {
            console.log('✏️ تعديل التحصيل:', id);

            try {
                const collection = financialDb.getCollectionById(id);
                if (!collection) {
                    alert('لم يتم العثور على التحصيل');
                    return;
                }

                currentEditId = id;
                document.getElementById('modalTitle').textContent = 'تعديل التحصيل - ' + collection.id;

                // ملء النموذج بالبيانات الحالية
                document.getElementById('distributorId').value = collection.distributorId || '';
                document.getElementById('amount').value = collection.amount || '';
                document.getElementById('currency').value = collection.currency || 'SAR';
                document.getElementById('collectionDate').value = collection.collectionDate || '';
                document.getElementById('status').value = collection.status || 'معلق';
                document.getElementById('shipments').value = collection.shipments ? collection.shipments.join(', ') : '';
                document.getElementById('notes').value = collection.notes || '';

                document.getElementById('collectionModal').style.display = 'block';

                console.log('✅ تم ملء النموذج للتعديل');
            } catch (error) {
                console.error('❌ خطأ في تعديل التحصيل:', error);
                alert('خطأ في تعديل التحصيل: ' + error.message);
            }
        }

        // عرض تفاصيل التحصيل
        function viewCollection(id) {
            try {
                const collection = financialDb.getCollectionById(id);
                if (!collection) {
                    alert('لم يتم العثور على التحصيل');
                    return;
                }

                alert(`تفاصيل التحصيل ${collection.id}:\n\n` +
                      `المندوب: ${collection.distributorName}\n` +
                      `المبلغ: ${collection.amount} ${collection.currency}\n` +
                      `تاريخ التحصيل: ${formatDate(collection.collectionDate)}\n` +
                      `الحالة: ${collection.status}\n` +
                      `عدد الشحنات: ${collection.shipments ? collection.shipments.length : 0}\n` +
                      `تاريخ الإنشاء: ${formatDate(collection.createdDate)}\n` +
                      `ملاحظات: ${collection.notes || 'لا توجد ملاحظات'}`);
            } catch (error) {
                console.error('❌ خطأ في عرض التحصيل:', error);
                alert('خطأ في عرض التحصيل: ' + error.message);
            }
        }

        // حذف تحصيل
        function deleteCollection(id) {
            try {
                const collection = financialDb.getCollectionById(id);
                if (!collection) {
                    alert('لم يتم العثور على التحصيل');
                    return;
                }

                if (confirm(`هل أنت متأكد من حذف التحصيل ${collection.id}؟`)) {
                    // هنا يمكن إضافة وظيفة الحذف في قاعدة البيانات المالية
                    alert('سيتم إضافة وظيفة الحذف قريباً');
                    // loadCollections();
                    // loadStats();
                }
            } catch (error) {
                console.error('❌ خطأ في حذف التحصيل:', error);
                alert('خطأ في حذف التحصيل: ' + error.message);
            }
        }

        // حفظ التحصيل
        function saveCollection(e) {
            e.preventDefault();

            console.log('💾 حفظ التحصيل - currentEditId:', currentEditId);

            try {
                const distributorId = document.getElementById('distributorId').value;
                const distributors = db.getAllDistributors();
                const distributor = distributors.find(d => d.id === distributorId);

                const formData = {
                    distributorId: distributorId,
                    distributorName: distributor ? distributor.name : '',
                    amount: parseFloat(document.getElementById('amount').value) || 0,
                    currency: document.getElementById('currency').value,
                    collectionDate: document.getElementById('collectionDate').value,
                    status: document.getElementById('status').value,
                    shipments: document.getElementById('shipments').value.split(',').map(s => s.trim()).filter(s => s),
                    notes: document.getElementById('notes').value.trim()
                };

                // التحقق من البيانات المطلوبة
                if (!formData.distributorId || !formData.amount) {
                    alert('يرجى ملء المندوب والمبلغ');
                    return;
                }

                let savedCollection;
                if (currentEditId) {
                    // تحديث تحصيل موجود
                    savedCollection = financialDb.updateCollection(currentEditId, formData);
                    if (savedCollection) {
                        alert('تم تحديث التحصيل بنجاح');
                    } else {
                        alert('خطأ في تحديث التحصيل');
                        return;
                    }
                } else {
                    // إضافة تحصيل جديد
                    savedCollection = financialDb.addCollection(formData);
                    if (savedCollection) {
                        alert(`تم إنشاء التحصيل بنجاح\nرقم التحصيل: ${savedCollection.id}`);
                    } else {
                        alert('خطأ في إنشاء التحصيل');
                        return;
                    }
                }

                // إعادة تحميل البيانات
                loadCollections();
                loadStats();
                closeModal();

                console.log('✅ تم حفظ التحصيل بنجاح');

            } catch (error) {
                console.error('❌ خطأ في حفظ التحصيل:', error);
                alert('خطأ في حفظ التحصيل: ' + error.message);
            }
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('collectionModal').style.display = 'none';
            currentEditId = null;
        }

        // دوال مساعدة
        function getStatusClass(status) {
            const statusMap = {
                'مكتمل': 'completed',
                'معلق': 'pending',
                'متأخر': 'overdue'
            };
            return statusMap[status] || 'pending';
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('collectionModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>