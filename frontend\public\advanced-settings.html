<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات المتقدمة - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        * {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-weight: 600 !important;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .settings-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .settings-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            border: none;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px;
            font-weight: 600;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active:hover {
            background: #0056b3;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .permission-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s;
        }

        .permission-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }

        .permission-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .permission-icon {
            font-size: 24px;
            margin-left: 10px;
        }

        .permission-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .role-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }

        .role-chip {
            padding: 8px 16px;
            background: #e9ecef;
            border: 2px solid transparent;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .role-chip.selected {
            background: #007bff;
            color: white;
            border-color: #0056b3;
        }

        .permission-list {
            list-style: none;
            padding: 0;
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-checkbox {
            margin-left: 10px;
            transform: scale(1.2);
        }

        .permission-label {
            flex: 1;
            font-weight: 500;
        }

        .permission-description {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .save-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 20px;
        }

        .save-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .user-role-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-admin { background: #dc3545; color: white; }
        .role-manager { background: #fd7e14; color: white; }
        .role-employee { background: #20c997; color: white; }
        .role-distributor { background: #6f42c1; color: white; }
        .role-driver { background: #0dcaf0; color: white; }
        .role-representative { background: #198754; color: white; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>⚙️</span>
                <span>نظام إدارة الشحنات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="shipments.html">الشحنات</a>
                <a href="user-management.html">إدارة المستخدمين</a>
                <a href="hr-management.html">الموارد البشرية</a>
                <a href="advanced-settings.html" class="active">الإعدادات المتقدمة</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="settings-container">
            <div class="settings-header">
                <h1>⚙️ الإعدادات المتقدمة ونظام الصلاحيات</h1>
                <p>إدارة شاملة لصلاحيات المستخدمين والمناديب والسائقين وموظفي الشركة</p>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalDrivers">0</div>
                    <div class="stat-label">السائقين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalRepresentatives">0</div>
                    <div class="stat-label">المناديب</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalEmployees">0</div>
                    <div class="stat-label">الموظفين</div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="settings-tabs">
                <button class="tab-button active" onclick="showTab('roles')">🔐 إدارة الأدوار</button>
                <button class="tab-button" onclick="showTab('permissions')">⚙️ الصلاحيات التفصيلية</button>
                <button class="tab-button" onclick="showTab('users')">👥 إدارة المستخدمين</button>
                <button class="tab-button" onclick="showTab('hr')">🏢 الموارد البشرية</button>
                <button class="tab-button" onclick="showTab('system')">🛠️ إعدادات النظام</button>
            </div>

            <!-- محتوى التبويبات -->
            <div id="rolesTab" class="tab-content active">
                <h2>🔐 إدارة الأدوار والصلاحيات</h2>
                
                <div class="role-selector">
                    <div class="role-chip selected" data-role="admin">مدير النظام</div>
                    <div class="role-chip" data-role="manager">مدير</div>
                    <div class="role-chip" data-role="employee">موظف</div>
                    <div class="role-chip" data-role="distributor">موزع</div>
                    <div class="role-chip" data-role="driver">سائق</div>
                    <div class="role-chip" data-role="representative">مندوب</div>
                </div>

                <div class="permissions-grid" id="permissionsGrid">
                    <!-- سيتم ملء الصلاحيات هنا -->
                </div>

                <button class="save-button" onclick="savePermissions()">💾 حفظ التغييرات</button>
            </div>

            <div id="permissionsTab" class="tab-content">
                <h2>⚙️ الصلاحيات التفصيلية</h2>
                <div id="detailedPermissions">
                    <!-- سيتم ملء الصلاحيات التفصيلية هنا -->
                </div>
            </div>

            <div id="usersTab" class="tab-content">
                <h2>👥 إدارة المستخدمين</h2>
                <div id="usersManagement">
                    <!-- سيتم ملء إدارة المستخدمين هنا -->
                </div>
            </div>

            <div id="hrTab" class="tab-content">
                <h2>🏢 إدارة الموارد البشرية</h2>
                <div id="hrManagement">
                    <!-- سيتم ملء إدارة الموارد البشرية هنا -->
                </div>
            </div>

            <div id="systemTab" class="tab-content">
                <h2>🛠️ إعدادات النظام</h2>
                <div id="systemSettings">
                    <!-- سيتم ملء إعدادات النظام هنا -->
                </div>
            </div>
        </div>
    </main>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script src="js/database.js"></script>
    <script src="js/permissions.js"></script>
    <script src="js/advanced-permissions.js"></script>
    <script>
        // متغيرات عامة
        let currentRole = 'admin';
        let permissionsData = {};
        let usersData = [];

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📍 تحميل صفحة الإعدادات المتقدمة...');

            // انتظار تحميل نظام الصلاحيات المتقدم
            setTimeout(() => {
                if (window.permissionsManager) {
                    permissionsData = window.permissionsManager.permissions;
                    loadStats();
                    loadPermissionsGrid();
                    setupEventListeners();
                    console.log('✅ تم تحميل الصفحة بنجاح');
                } else {
                    console.error('❌ فشل في تحميل نظام الصلاحيات');
                }
            }, 100);
        });

        // تحميل الإحصائيات
        function loadStats() {
            try {
                const users = JSON.parse(localStorage.getItem('users') || '[]');
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');

                document.getElementById('totalUsers').textContent = users.length;
                document.getElementById('totalDrivers').textContent = users.filter(u => u.role === 'driver').length;
                document.getElementById('totalRepresentatives').textContent = users.filter(u => u.role === 'representative').length;
                document.getElementById('totalEmployees').textContent = employees.length;
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // تحميل شبكة الصلاحيات
        function loadPermissionsGrid() {
            const grid = document.getElementById('permissionsGrid');
            if (!grid || !permissionsData) return;

            grid.innerHTML = '';

            Object.keys(permissionsData).forEach(categoryKey => {
                const category = permissionsData[categoryKey];
                const card = document.createElement('div');
                card.className = 'permission-card';

                const permissionsList = Object.keys(category.permissions).map(permissionId => {
                    const permission = category.permissions[permissionId];
                    return `
                        <li class="permission-item">
                            <input type="checkbox"
                                   class="permission-checkbox"
                                   id="${permissionId}"
                                   data-permission="${permissionId}"
                                   ${hasRolePermission(currentRole, permissionId) ? 'checked' : ''}>
                            <label for="${permissionId}" class="permission-label">
                                ${permission.name}
                                <div class="permission-description">${permission.description}</div>
                            </label>
                        </li>
                    `;
                }).join('');

                card.innerHTML = `
                    <div class="permission-header">
                        <span class="permission-icon">${category.icon}</span>
                        <span class="permission-title">${category.name}</span>
                    </div>
                    <ul class="permission-list">
                        ${permissionsList}
                    </ul>
                `;

                grid.appendChild(card);
            });
        }

        // التحقق من صلاحية الدور
        function hasRolePermission(role, permission) {
            if (window.permissionsManager) {
                const rolePermissions = window.permissionsManager.getRolePermissions(role);
                return rolePermissions.includes(permission) || role === 'admin' || rolePermissions.includes('all');
            }
            return role === 'admin';
        }

        // الحصول على صلاحيات الدور
        function getRolePermissions(role) {
            if (window.permissionsManager) {
                return window.permissionsManager.getRolePermissions(role);
            }
            return [];
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // مستمع تغيير الدور
            document.querySelectorAll('.role-chip').forEach(chip => {
                chip.addEventListener('click', function() {
                    document.querySelectorAll('.role-chip').forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    currentRole = this.dataset.role;
                    loadPermissionsGrid();
                });
            });
        }

        // عرض التبويب
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المطلوب
            document.getElementById(tabName + 'Tab').classList.add('active');

            // إضافة الفئة النشطة للزر
            event.target.classList.add('active');

            // تحميل محتوى التبويب
            loadTabContent(tabName);
        }

        // تحميل محتوى التبويب
        function loadTabContent(tabName) {
            switch(tabName) {
                case 'permissions':
                    loadDetailedPermissions();
                    break;
                case 'users':
                    loadUsersManagement();
                    break;
                case 'hr':
                    loadHRManagement();
                    break;
                case 'system':
                    loadSystemSettings();
                    break;
            }
        }

        // تحميل الصلاحيات التفصيلية
        function loadDetailedPermissions() {
            const container = document.getElementById('detailedPermissions');
            container.innerHTML = `
                <div class="permissions-matrix">
                    <h3>🔍 مصفوفة الصلاحيات التفصيلية</h3>
                    <p>عرض تفصيلي لجميع الصلاحيات حسب الأدوار</p>

                    <div style="overflow-x: auto; margin-top: 20px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">الصلاحية</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">مدير النظام</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">مدير</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">موظف</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">موزع</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">سائق</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6;">مندوب</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generatePermissionsMatrix()}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // إنشاء مصفوفة الصلاحيات
        function generatePermissionsMatrix() {
            let html = '';
            const roles = ['admin', 'manager', 'employee', 'distributor', 'driver', 'representative'];

            if (permissionsData) {
                Object.values(permissionsData).forEach(category => {
                    Object.keys(category.permissions).forEach(permissionId => {
                        const permission = category.permissions[permissionId];
                        html += `
                            <tr>
                                <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: 500;">
                                    ${permission.name}
                                    <div style="font-size: 11px; color: #6c757d; font-weight: normal;">
                                        ${category.name}
                                    </div>
                                </td>
                                ${roles.map(role => `
                                    <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">
                                        ${hasRolePermission(role, permissionId) ? '✅' : '❌'}
                                    </td>
                                `).join('')}
                            </tr>
                        `;
                    });
                });
            }

            return html;
        }

        // حفظ الصلاحيات
        function savePermissions() {
            try {
                const permissions = [];
                document.querySelectorAll('.permission-checkbox:checked').forEach(checkbox => {
                    permissions.push(checkbox.dataset.permission);
                });

                // حفظ باستخدام نظام الصلاحيات المتقدم
                if (window.permissionsManager) {
                    const success = window.permissionsManager.saveRolePermissions(currentRole, permissions);

                    if (success) {
                        // تسجيل العملية
                        window.permissionsManager.logPermissionAction('role_permissions_updated', {
                            role: currentRole,
                            permissions: permissions,
                            count: permissions.length
                        });

                        alert('✅ تم حفظ الصلاحيات بنجاح!');
                        console.log('تم حفظ الصلاحيات للدور:', currentRole, permissions);
                    } else {
                        throw new Error('فشل في حفظ الصلاحيات');
                    }
                } else {
                    throw new Error('نظام الصلاحيات غير متاح');
                }
            } catch (error) {
                console.error('خطأ في حفظ الصلاحيات:', error);
                alert('❌ حدث خطأ في حفظ الصلاحيات: ' + error.message);
            }
        }

        // تحميل إدارة المستخدمين
        function loadUsersManagement() {
            const container = document.getElementById('usersManagement');
            container.innerHTML = `
                <div class="users-management">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>👥 قائمة المستخدمين</h3>
                        <button class="btn btn-success" onclick="addNewUser()">➕ إضافة مستخدم جديد</button>
                    </div>

                    <div id="usersTable">
                        <!-- سيتم ملء جدول المستخدمين هنا -->
                    </div>
                </div>
            `;

            loadUsersTable();
        }

        // تحميل جدول المستخدمين
        function loadUsersTable() {
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const tableContainer = document.getElementById('usersTable');

            if (users.length === 0) {
                tableContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #6c757d;">
                        <p>لا توجد مستخدمين مسجلين في النظام</p>
                    </div>
                `;
                return;
            }

            let html = `
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الاسم</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">البريد الإلكتروني</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الدور</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الحالة</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            users.forEach(user => {
                html += `
                    <tr>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">${user.name || user.firstName + ' ' + user.lastName}</td>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">${user.email}</td>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">
                            <span class="user-role-badge role-${user.role}">${getRoleName(user.role)}</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">
                            ${user.isActive ? '<span style="color: green;">نشط</span>' : '<span style="color: red;">غير نشط</span>'}
                        </td>
                        <td style="padding: 8px; border: 1px solid #dee2e6;">
                            <button onclick="editUser('${user.id}')" style="margin: 2px; padding: 4px 8px; background: #007bff; color: white; border: none; border-radius: 4px;">تعديل</button>
                            <button onclick="deleteUser('${user.id}')" style="margin: 2px; padding: 4px 8px; background: #dc3545; color: white; border: none; border-radius: 4px;">حذف</button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            tableContainer.innerHTML = html;
        }

        // الحصول على اسم الدور
        function getRoleName(role) {
            const roleNames = {
                admin: 'مدير النظام',
                manager: 'مدير',
                employee: 'موظف',
                distributor: 'موزع',
                driver: 'سائق',
                representative: 'مندوب'
            };
            return roleNames[role] || role;
        }

        // تحميل إدارة الموارد البشرية
        function loadHRManagement() {
            const container = document.getElementById('hrManagement');
            container.innerHTML = `
                <div class="hr-management">
                    <h3>🏢 إدارة الموارد البشرية</h3>
                    <p>ربط مع نظام الموارد البشرية الموجود</p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center;">
                            <h4>👨‍💼 الموظفين</h4>
                            <p style="font-size: 24px; font-weight: bold; color: #007bff;">${document.getElementById('totalEmployees').textContent}</p>
                            <button onclick="window.location.href='hr-management.html'" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 5px;">إدارة الموظفين</button>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center;">
                            <h4>🚗 السائقين</h4>
                            <p style="font-size: 24px; font-weight: bold; color: #28a745;">${document.getElementById('totalDrivers').textContent}</p>
                            <button onclick="window.location.href='vehicle-management.html'" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 5px;">إدارة السائقين</button>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center;">
                            <h4>📦 المناديب</h4>
                            <p style="font-size: 24px; font-weight: bold; color: #fd7e14;">${document.getElementById('totalRepresentatives').textContent}</p>
                            <button onclick="window.location.href='distributors-management.html'" style="padding: 8px 16px; background: #fd7e14; color: white; border: none; border-radius: 5px;">إدارة المناديب</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // تحميل إعدادات النظام
        function loadSystemSettings() {
            const container = document.getElementById('systemSettings');
            container.innerHTML = `
                <div class="system-settings">
                    <h3>🛠️ إعدادات النظام العامة</h3>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <h4>🔐 إعدادات الأمان</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li style="margin: 10px 0;">
                                    <label>
                                        <input type="checkbox" checked> تفعيل المصادقة الثنائية
                                    </label>
                                </li>
                                <li style="margin: 10px 0;">
                                    <label>
                                        <input type="checkbox" checked> تسجيل العمليات
                                    </label>
                                </li>
                                <li style="margin: 10px 0;">
                                    <label>
                                        <input type="checkbox"> إجبار تغيير كلمة المرور
                                    </label>
                                </li>
                            </ul>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <h4>📊 إعدادات التقارير</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li style="margin: 10px 0;">
                                    <label>
                                        <input type="checkbox" checked> التقارير التلقائية
                                    </label>
                                </li>
                                <li style="margin: 10px 0;">
                                    <label>
                                        <input type="checkbox" checked> إشعارات البريد الإلكتروني
                                    </label>
                                </li>
                                <li style="margin: 10px 0;">
                                    <label>
                                        <input type="checkbox"> تصدير تلقائي للبيانات
                                    </label>
                                </li>
                            </ul>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <h4>🔄 النسخ الاحتياطي</h4>
                            <p>آخر نسخة احتياطية: ${new Date().toLocaleDateString('ar-SA')}</p>
                            <button onclick="createBackup()" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 5px; margin: 5px;">إنشاء نسخة احتياطية</button>
                            <button onclick="restoreBackup()" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 5px; margin: 5px;">استعادة نسخة احتياطية</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // وظائف إضافية
        function addNewUser() {
            alert('سيتم فتح نافذة إضافة مستخدم جديد');
            // يمكن ربطها بصفحة إضافة المستخدمين
        }

        function editUser(userId) {
            alert('تعديل المستخدم: ' + userId);
        }

        function deleteUser(userId) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                // حذف المستخدم من localStorage
                let users = JSON.parse(localStorage.getItem('users') || '[]');
                users = users.filter(user => user.id !== userId);
                localStorage.setItem('users', JSON.stringify(users));
                loadUsersTable();
                loadStats();
            }
        }

        function createBackup() {
            const data = {
                users: localStorage.getItem('users'),
                employees: localStorage.getItem('employees'),
                shipments: localStorage.getItem('shipments'),
                customers: localStorage.getItem('customers'),
                permissions: localStorage.getItem('rolePermissions'),
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
            a.click();

            alert('✅ تم إنشاء النسخة الاحتياطية بنجاح!');
        }

        function restoreBackup() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);

                            // استعادة البيانات
                            Object.keys(data).forEach(key => {
                                if (key !== 'timestamp' && data[key]) {
                                    localStorage.setItem(key, data[key]);
                                }
                            });

                            alert('✅ تم استعادة النسخة الاحتياطية بنجاح!');
                            location.reload();
                        } catch (error) {
                            alert('❌ خطأ في استعادة النسخة الاحتياطية');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }
    </script>
</body>
</html>
