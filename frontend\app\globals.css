@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Apple fonts */
@import url('../shared/fonts.css');
@import url('../shared/design-system.css');

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-text;
  }
  
  /* Arabic text styling */
  [lang="ar"], [dir="rtl"] {
    @apply font-arabic;
    line-height: 1.8;
    letter-spacing: 0.02em;
  }
  
  /* English text styling */
  [lang="en"], [dir="ltr"] {
    @apply font-text;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }
  
  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold;
  }
  
  /* RTL support */
  .rtl {
    direction: rtl;
  }
  
  .ltr {
    direction: ltr;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  /* Loading animation */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-500;
  }
  
  /* Fade in animation */
  .fade-in {
    @apply animate-fade-in;
  }
  
  /* Slide animations */
  .slide-in-top {
    @apply animate-slide-in-from-top;
  }
  
  .slide-in-bottom {
    @apply animate-slide-in-from-bottom;
  }
  
  .slide-in-left {
    @apply animate-slide-in-from-left;
  }
  
  .slide-in-right {
    @apply animate-slide-in-from-right;
  }
}

@layer components {
  /* Button variants */
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus-ring;
  }
  
  .btn-secondary {
    @apply bg-secondary-500 hover:bg-secondary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus-ring;
  }
  
  .btn-outline {
    @apply border border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus-ring;
  }
  
  .btn-ghost {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus-ring;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
  }
  
  .card-header {
    @apply border-b border-gray-200 pb-4 mb-4;
  }
  
  /* Form styles */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-error {
    @apply text-red-500 text-sm mt-1;
  }
  
  /* Status badges */
  .status-pending {
    @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .status-in-transit {
    @apply bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .status-delivered {
    @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .status-cancelled {
    @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  /* Navigation styles */
  .nav-link {
    @apply flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200;
  }
  
  .nav-link.active {
    @apply text-primary-600 bg-primary-50;
  }
  
  /* Table styles */
  .table {
    @apply w-full border-collapse;
  }
  
  .table th {
    @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
  }
  
  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
  }
  
  .table tr:hover {
    @apply bg-gray-50;
  }
}

@layer utilities {
  /* Text direction utilities */
  .text-start {
    text-align: start;
  }
  
  .text-end {
    text-align: end;
  }
  
  /* Margin utilities for RTL */
  .ms-auto {
    margin-inline-start: auto;
  }
  
  .me-auto {
    margin-inline-end: auto;
  }
  
  /* Padding utilities for RTL */
  .ps-4 {
    padding-inline-start: 1rem;
  }
  
  .pe-4 {
    padding-inline-end: 1rem;
  }
}
