@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    تشغيل نظام إدارة الشحنات
echo    Running Shipment Management System
echo ========================================
echo.

:: Set Node.js path for this session
set "PATH=%PATH%;C:\Program Files\nodejs"

echo 🔍 فحص Node.js...
echo 🔍 Checking Node.js...

:: Test Node.js
"C:\Program Files\nodejs\node.exe" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير موجود في المسار المتوقع
    echo ❌ Node.js not found in expected path
    echo.
    echo يرجى التأكد من تثبيت Node.js في:
    echo Please ensure Node.js is installed in:
    echo C:\Program Files\nodejs\
    pause
    exit /b 1
)

echo ✅ Node.js موجود
echo ✅ Node.js found

:: Test npm
"C:\Program Files\nodejs\npm.cmd" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير موجود
    echo ❌ npm not found
    pause
    exit /b 1
)

echo ✅ npm موجود
echo ✅ npm found

echo.
echo 📦 فحص التبعيات...
echo 📦 Checking dependencies...

:: Check if main dependencies are installed
if not exist "node_modules" (
    echo.
    echo 📦 تثبيت التبعيات الرئيسية...
    echo 📦 Installing main dependencies...
    "C:\Program Files\nodejs\npm.cmd" install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات الرئيسية
        echo ❌ Failed to install main dependencies
        pause
        exit /b 1
    )
)

:: Check backend dependencies
if not exist "backend\node_modules" (
    echo.
    echo 📦 تثبيت تبعيات الخلفية...
    echo 📦 Installing backend dependencies...
    cd backend
    "..\C:\Program Files\nodejs\npm.cmd" install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات الخلفية
        echo ❌ Failed to install backend dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

:: Check frontend dependencies
if not exist "frontend\node_modules" (
    echo.
    echo 📦 تثبيت تبعيات الواجهة الأمامية...
    echo 📦 Installing frontend dependencies...
    cd frontend
    "..\C:\Program Files\nodejs\npm.cmd" install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات الواجهة الأمامية
        echo ❌ Failed to install frontend dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

:: Check .env file
if not exist "backend\.env" (
    echo.
    echo ⚙️ إنشاء ملف البيئة...
    echo ⚙️ Creating environment file...
    cd backend
    copy .env.example .env >nul
    cd ..
    echo.
    echo ⚠️  يرجى تعديل ملف backend\.env بالبيانات الصحيحة:
    echo ⚠️  Please edit backend\.env with correct data:
    echo.
    echo DATABASE_URL="postgresql://username:password@localhost:5432/shipment_management"
    echo JWT_SECRET="your-32-character-secret-key-here"
    echo.
    echo هل تريد فتح ملف .env للتعديل الآن؟
    echo Do you want to open .env file for editing now?
    set /p edit="(y/n): "
    if /i "%edit%"=="y" (
        notepad backend\.env
        echo.
        echo بعد حفظ الملف، اضغط أي مفتاح للمتابعة...
        echo After saving the file, press any key to continue...
        pause >nul
    )
)

echo.
echo 🚀 بدء تشغيل النظام...
echo 🚀 Starting the system...
echo.
echo الواجهة الأمامية: http://localhost:3000
echo Frontend: http://localhost:3000
echo.
echo API الخلفي: http://localhost:3001
echo Backend API: http://localhost:3001
echo.
echo اضغط Ctrl+C لإيقاف النظام
echo Press Ctrl+C to stop the system
echo.

:: Start the application using full paths
"C:\Program Files\nodejs\npm.cmd" run dev
