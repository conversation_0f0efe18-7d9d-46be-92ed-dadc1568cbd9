<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب عميل جديد | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1000px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 700px;
        }

        .register-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .register-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        .logo-section {
            position: relative;
            z-index: 2;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .welcome-text h1 {
            font-size: 2.2rem;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .welcome-text p {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .register-right {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow-y: auto;
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-header h2 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .register-header p {
            color: #666;
            font-size: 1rem;
        }

        .register-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group {
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control.error {
            border-color: #dc3545;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 1.2rem;
        }

        .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 10px;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .terms-checkbox {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .terms-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .terms-checkbox label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0;
        }

        .terms-checkbox a {
            color: #667eea;
            text-decoration: none;
        }

        .terms-checkbox a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .register-container {
                grid-template-columns: 1fr;
                max-width: 500px;
            }
            
            .register-left {
                padding: 40px 30px;
                min-height: 200px;
            }
            
            .register-right {
                padding: 40px 30px;
            }
            
            .welcome-text h1 {
                font-size: 1.8rem;
            }
            
            .logo {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-left">
            <div class="logo-section">
                <div class="logo">🚚</div>
                <div class="welcome-text">
                    <h1>انضم إلينا</h1>
                    <p>أنشئ حسابك الآن واستمتع بخدمات الشحن السريع والموثوق في جميع أنحاء المملكة</p>
                </div>
            </div>
        </div>

        <div class="register-right">
            <div class="register-header">
                <h2>إنشاء حساب جديد</h2>
                <p>املأ البيانات التالية لإنشاء حسابك</p>
            </div>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <form class="register-form" id="registerForm" onsubmit="handleRegister(event)">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">الاسم الأول</label>
                        <input type="text" id="firstName" class="form-control" placeholder="أدخل الاسم الأول" required>
                    </div>
                    <div class="form-group">
                        <label for="lastName">اسم العائلة</label>
                        <input type="text" id="lastName" class="form-control" placeholder="أدخل اسم العائلة" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" class="form-control" placeholder="أدخل البريد الإلكتروني" required>
                </div>

                <div class="form-group">
                    <label for="phone">رقم الهاتف</label>
                    <input type="tel" id="phone" class="form-control" placeholder="05xxxxxxxx" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <div style="position: relative;">
                            <input type="password" id="password" class="form-control" placeholder="أدخل كلمة المرور" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">👁️</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">تأكيد كلمة المرور</label>
                        <div style="position: relative;">
                            <input type="password" id="confirmPassword" class="form-control" placeholder="أعد إدخال كلمة المرور" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">👁️</button>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="city">المدينة</label>
                    <select id="city" class="form-control" required>
                        <option value="">اختر المدينة</option>
                        <option value="riyadh">الرياض</option>
                        <option value="jeddah">جدة</option>
                        <option value="dammam">الدمام</option>
                        <option value="mecca">مكة المكرمة</option>
                        <option value="medina">المدينة المنورة</option>
                        <option value="khobar">الخبر</option>
                        <option value="taif">الطائف</option>
                        <option value="tabuk">تبوك</option>
                        <option value="buraidah">بريدة</option>
                        <option value="khamis">خميس مشيط</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="address">العنوان التفصيلي</label>
                    <textarea id="address" class="form-control" rows="3" placeholder="أدخل العنوان التفصيلي" required></textarea>
                </div>

                <div class="terms-checkbox">
                    <input type="checkbox" id="terms" required>
                    <label for="terms">
                        أوافق على <a href="#" onclick="showTerms()">الشروط والأحكام</a> و <a href="#" onclick="showPrivacy()">سياسة الخصوصية</a>
                    </label>
                </div>

                <button type="submit" class="register-btn" id="registerBtn">
                    إنشاء الحساب
                </button>
            </form>

            <div class="login-link">
                <p>لديك حساب بالفعل؟ <a href="unified-login.html">تسجيل الدخول</a></p>
            </div>
        </div>
    </div>

    <script>
        // تبديل إظهار كلمة المرور
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleBtn = passwordInput.nextElementSibling;

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        // معالجة تسجيل العضوية
        function handleRegister(event) {
            event.preventDefault();

            // جمع البيانات
            const formData = {
                firstName: document.getElementById('firstName').value.trim(),
                lastName: document.getElementById('lastName').value.trim(),
                email: document.getElementById('email').value.trim(),
                phone: document.getElementById('phone').value.trim(),
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value,
                city: document.getElementById('city').value,
                address: document.getElementById('address').value.trim(),
                terms: document.getElementById('terms').checked
            };

            // التحقق من البيانات
            if (!validateForm(formData)) {
                return;
            }

            // إظهار حالة التحميل
            const registerBtn = document.getElementById('registerBtn');
            registerBtn.disabled = true;
            registerBtn.innerHTML = 'جاري إنشاء الحساب... <span class="loading"></span>';

            // محاكاة عملية التسجيل
            setTimeout(() => {
                // محاكاة نجاح التسجيل
                showSuccess('تم إنشاء حسابك بنجاح! جاري تسجيل الدخول...');

                // حفظ بيانات المستخدم
                localStorage.setItem('userType', 'customer');
                localStorage.setItem('userEmail', formData.email);
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('userName', `${formData.firstName} ${formData.lastName}`);

                // التحويل للوحة التحكم
                setTimeout(() => {
                    window.location.href = 'customer-dashboard.html';
                }, 2000);
            }, 3000);
        }

        // التحقق من صحة البيانات
        function validateForm(data) {
            // التحقق من الحقول المطلوبة
            if (!data.firstName || !data.lastName || !data.email || !data.phone ||
                !data.password || !data.confirmPassword || !data.city || !data.address) {
                showError('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }

            // التحقق من البريد الإلكتروني
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                showError('يرجى إدخال بريد إلكتروني صحيح');
                return false;
            }

            // التحقق من رقم الهاتف
            const phoneRegex = /^05\d{8}$/;
            if (!phoneRegex.test(data.phone)) {
                showError('يرجى إدخال رقم هاتف صحيح (05xxxxxxxx)');
                return false;
            }

            // التحقق من كلمة المرور
            if (data.password.length < 6) {
                showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }

            // التحقق من تطابق كلمة المرور
            if (data.password !== data.confirmPassword) {
                showError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return false;
            }

            // التحقق من الموافقة على الشروط
            if (!data.terms) {
                showError('يجب الموافقة على الشروط والأحكام');
                return false;
            }

            return true;
        }

        // إظهار رسالة خطأ
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            successDiv.style.display = 'none';
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            // إعادة تفعيل الزر
            const registerBtn = document.getElementById('registerBtn');
            registerBtn.disabled = false;
            registerBtn.innerHTML = 'إنشاء الحساب';

            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // إظهار رسالة نجاح
        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            errorDiv.style.display = 'none';
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // عرض الشروط والأحكام
        function showTerms() {
            alert(`الشروط والأحكام:

1. يجب أن يكون المستخدم مقيماً في المملكة العربية السعودية
2. يتحمل العميل مسؤولية دقة البيانات المدخلة
3. تطبق رسوم الشحن حسب الوزن والمسافة
4. الشركة غير مسؤولة عن تأخير التسليم بسبب ظروف خارجة عن إرادتها
5. يحق للشركة رفض شحن المواد الخطرة أو المحظورة
6. العميل مسؤول عن تعبئة وتغليف الشحنة بشكل مناسب
7. تطبق سياسة الاسترداد خلال 24 ساعة من إنشاء الشحنة
8. يجب الإبلاغ عن أي مشاكل خلال 48 ساعة من التسليم

بالموافقة على هذه الشروط، فإنك تقر بقراءتها وفهمها والالتزام بها.`);
        }

        // عرض سياسة الخصوصية
        function showPrivacy() {
            alert(`سياسة الخصوصية:

1. نحن نحترم خصوصيتك ونحمي بياناتك الشخصية
2. نستخدم بياناتك فقط لتقديم خدمات الشحن
3. لا نشارك بياناتك مع أطراف ثالثة دون موافقتك
4. نحتفظ ببياناتك بشكل آمن ومشفر
5. يمكنك طلب حذف بياناتك في أي وقت
6. نستخدم ملفات تعريف الارتباط لتحسين تجربتك
7. نرسل إشعارات متعلقة بشحناتك فقط
8. يمكنك تحديث بياناتك من لوحة التحكم

نحن ملتزمون بحماية خصوصيتك وفقاً لقوانين حماية البيانات في المملكة.`);
        }

        // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userType = localStorage.getItem('userType');

            if (isLoggedIn === 'true' && userType) {
                // المستخدم مسجل دخول بالفعل، توجيهه للوحة التحكم المناسبة
                if (userType === 'admin') {
                    window.location.href = 'main-dashboard.html';
                } else {
                    window.location.href = 'customer-dashboard.html';
                }
            }
        });

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات بصرية للحقول
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // التحقق من كلمة المرور في الوقت الفعلي
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');

            function checkPasswordMatch() {
                if (confirmPasswordInput.value && passwordInput.value !== confirmPasswordInput.value) {
                    confirmPasswordInput.style.borderColor = '#dc3545';
                } else {
                    confirmPasswordInput.style.borderColor = '#e1e5e9';
                }
            }

            passwordInput.addEventListener('input', checkPasswordMatch);
            confirmPasswordInput.addEventListener('input', checkPasswordMatch);
        });
    </script>

    <!-- تطبيق خط SF Pro Arabic Display على جميع العناصر -->
    <style>
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, textarea, select, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }
    </style>
</body>
</html>
