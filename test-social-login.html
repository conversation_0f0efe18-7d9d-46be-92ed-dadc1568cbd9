<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول الاجتماعي</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }

        .test-header p {
            color: #666;
            font-size: 1.1rem;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-btn {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .status-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .status-box h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e3f2fd;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: 600;
            color: #333;
        }

        .status-value {
            color: #666;
            font-family: monospace;
        }

        .back-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s;
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li::before {
            content: "✅";
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <a href="unified-login.html" class="back-link">← العودة لتسجيل الدخول</a>
    
    <div class="test-container">
        <div class="test-header">
            <h1>🔐 اختبار تسجيل الدخول الاجتماعي</h1>
            <p>اختبر الميزات الجديدة لتسجيل الدخول عبر الشبكات الاجتماعية</p>
        </div>

        <div class="test-section">
            <h3>🚀 اختبار سريع</h3>
            <a href="unified-login.html" class="test-btn">
                افتح صفحة تسجيل الدخول المحدثة
            </a>
            <p style="color: #666; font-size: 0.9rem; margin-top: 10px;">
                ستجد أزرار تسجيل الدخول عبر Google وApple وFacebook أسفل النموذج التقليدي
            </p>
        </div>

        <div class="test-section">
            <h3>✨ الميزات الجديدة</h3>
            <ul class="feature-list">
                <li>تسجيل الدخول عبر Google</li>
                <li>تسجيل الدخول عبر Apple</li>
                <li>تسجيل الدخول عبر Facebook</li>
                <li>تصميم أنيق ومتجاوب</li>
                <li>تكامل كامل مع النظام</li>
                <li>أمان وخصوصية عالية</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📋 خطوات الاختبار</h3>
            <ol style="color: #666; line-height: 1.6;">
                <li>اضغط على "افتح صفحة تسجيل الدخول المحدثة"</li>
                <li>اختر نوع المستخدم (عميل/موظف/مدير)</li>
                <li>جرب أحد أزرار الشبكات الاجتماعية</li>
                <li>انتظر رسالة النجاح والتحويل التلقائي</li>
                <li>تحقق من حفظ البيانات في localStorage</li>
            </ol>
        </div>

        <div class="status-box">
            <h4>📊 حالة تسجيل الدخول الحالية</h4>
            <div id="loginStatus">
                <div class="status-item">
                    <span class="status-label">حالة تسجيل الدخول:</span>
                    <span class="status-value" id="isLoggedIn">غير مسجل</span>
                </div>
                <div class="status-item">
                    <span class="status-label">نوع المستخدم:</span>
                    <span class="status-value" id="userType">غير محدد</span>
                </div>
                <div class="status-item">
                    <span class="status-label">البريد الإلكتروني:</span>
                    <span class="status-value" id="userEmail">غير متوفر</span>
                </div>
                <div class="status-item">
                    <span class="status-label">مزود تسجيل الدخول:</span>
                    <span class="status-value" id="loginProvider">غير محدد</span>
                </div>
                <div class="status-item">
                    <span class="status-label">وقت تسجيل الدخول:</span>
                    <span class="status-value" id="loginTime">غير متوفر</span>
                </div>
            </div>
            
            <button onclick="clearLoginData()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 8px; margin-top: 15px; cursor: pointer; width: 100%;">
                🗑️ مسح بيانات تسجيل الدخول
            </button>
        </div>
    </div>

    <script>
        // تحديث حالة تسجيل الدخول
        function updateLoginStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const userType = localStorage.getItem('userType') || 'غير محدد';
            const userEmail = localStorage.getItem('userEmail') || 'غير متوفر';
            const loginProvider = localStorage.getItem('loginProvider') || 'تقليدي';
            const loginTime = localStorage.getItem('loginTime');

            document.getElementById('isLoggedIn').textContent = isLoggedIn ? 'مسجل دخول' : 'غير مسجل';
            document.getElementById('userType').textContent = userType;
            document.getElementById('userEmail').textContent = userEmail;
            document.getElementById('loginProvider').textContent = loginProvider;
            
            if (loginTime) {
                const date = new Date(loginTime);
                document.getElementById('loginTime').textContent = date.toLocaleString('ar-SA');
            } else {
                document.getElementById('loginTime').textContent = 'غير متوفر';
            }

            // تغيير لون الحالة
            const statusBox = document.querySelector('.status-box');
            if (isLoggedIn) {
                statusBox.style.background = '#e8f5e8';
                statusBox.style.borderColor = '#4caf50';
                statusBox.querySelector('h4').style.color = '#2e7d32';
            } else {
                statusBox.style.background = '#e3f2fd';
                statusBox.style.borderColor = '#2196f3';
                statusBox.querySelector('h4').style.color = '#1976d2';
            }
        }

        // مسح بيانات تسجيل الدخول
        function clearLoginData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات تسجيل الدخول؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userType');
                localStorage.removeItem('userEmail');
                localStorage.removeItem('userName');
                localStorage.removeItem('userPicture');
                localStorage.removeItem('loginProvider');
                localStorage.removeItem('loginTime');
                
                updateLoginStatus();
                alert('تم مسح بيانات تسجيل الدخول بنجاح!');
            }
        }

        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateLoginStatus();
            
            // تحديث الحالة كل ثانية للتحقق من التغييرات
            setInterval(updateLoginStatus, 1000);
        });

        // إضافة معلومات إضافية
        console.log('🔐 صفحة اختبار تسجيل الدخول الاجتماعي');
        console.log('📋 الميزات المتاحة:');
        console.log('  ✅ Google OAuth (محاكاة)');
        console.log('  ✅ Apple Sign In (محاكاة)');
        console.log('  ✅ Facebook Login (محاكاة)');
        console.log('  ✅ تكامل كامل مع النظام');
        console.log('  ✅ حفظ آمن للبيانات');
    </script>
</body>
</html>
