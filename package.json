{"name": "shipment-management-system", "version": "1.0.0", "description": "نظام متكامل لإدارة الشحنات والتوصيل مع دعم اللغتين العربية والإنجليزية", "private": true, "workspaces": ["frontend", "backend", "mobile", "shared"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "clean": "rimraf node_modules frontend/node_modules backend/node_modules mobile/node_modules shared/node_modules", "setup": "npm run install:all && npm run setup:db", "setup:db": "cd backend && npm run db:setup", "setup:quick": "npm run install:all && echo 'تم تثبيت التبعيات بنجاح! يرجى إعداد قاعدة البيانات وملف .env في مجلد backend'", "db:reset": "cd backend && npm run db:reset", "db:seed": "cd backend && npm run db:seed", "db:studio": "cd backend && npm run db:studio"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["shipment", "delivery", "management", "arabic", "english", "bilingual", "logistics", "tracking"], "author": "عصام", "license": "MIT"}