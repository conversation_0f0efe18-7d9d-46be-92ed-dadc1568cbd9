<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السيارات - نظام إدارة الشحنات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }

        .logo span:first-child {
            font-size: 2rem;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #666;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .page-title {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .tabs-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tabs-header {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            background: none;
            border: none;
            font-size: 1.1rem;
            font-weight: 600;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-button.active {
            color: #667eea;
            background: white;
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #667eea;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .search-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            width: 300px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .vehicles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .vehicle-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .vehicle-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .vehicle-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .vehicle-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .vehicle-info h3 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.2rem;
        }

        .vehicle-type {
            color: #666;
            font-size: 0.9rem;
            background: #f8f9fa;
            padding: 4px 12px;
            border-radius: 15px;
            display: inline-block;
        }

        .vehicle-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .detail-label {
            color: #666;
            font-weight: 500;
        }

        .detail-value {
            color: #333;
            font-weight: 600;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-maintenance {
            background: #fff3cd;
            color: #856404;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .vehicle-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 15px;
        }

        @media (max-width: 768px) {
            .vehicles-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .search-input {
                width: 200px;
            }

            .action-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .nav-links {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🚗</span>
                <span>إدارة السيارات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="vehicle-management.html" class="active">إدارة السيارات</a>
                <a href="hr-management.html">الموارد البشرية</a>
                <a href="distributors-management.html">المناديب</a>
                <a href="financial-system.html">النظام المالي</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">🚗 إدارة السيارات</h1>
            <p class="page-subtitle">نظام شامل لإدارة أسطول السيارات والتكاليف والصيانة</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">🚗</span>
                <div class="stat-number" id="totalVehicles">0</div>
                <div class="stat-label">إجمالي السيارات</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-number" id="activeVehicles">0</div>
                <div class="stat-label">السيارات النشطة</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">🔧</span>
                <div class="stat-number" id="maintenanceVehicles">0</div>
                <div class="stat-label">في الصيانة</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">💰</span>
                <div class="stat-number" id="totalCosts">0</div>
                <div class="stat-label">إجمالي التكاليف (ريال)</div>
            </div>
        </div>

        <!-- التبويبات الرئيسية -->
        <div class="tabs-container">
            <div class="tabs-header">
                <button class="tab-button active" onclick="showTab('vehicles')">🚗 السيارات</button>
                <button class="tab-button" onclick="showTab('maintenance')">🔧 الصيانة</button>
                <button class="tab-button" onclick="showTab('costs')">💰 التكاليف</button>
                <button class="tab-button" onclick="showTab('rental')">🏠 الإيجار</button>
                <button class="tab-button" onclick="showTab('installments')">📊 التقسيط</button>
            </div>

            <!-- تبويب السيارات -->
            <div id="vehicles" class="tab-content active">
                <div class="action-bar">
                    <div class="search-container">
                        <input type="text" class="search-input" id="vehicleSearch" placeholder="البحث عن سيارة...">
                        <button class="btn btn-primary" onclick="searchVehicles()">🔍 بحث</button>
                    </div>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="btn btn-success" onclick="openAddVehicleModal()">➕ إضافة سيارة جديدة</button>
                        <button class="btn btn-info" onclick="exportVehicles()">📤 تصدير البيانات</button>
                        <button class="btn btn-warning" onclick="loadVehicles()">🔄 تحديث</button>
                    </div>
                </div>

                <div class="vehicles-grid" id="vehiclesGrid">
                    <!-- سيتم ملء البيانات بواسطة JavaScript -->
                </div>
            </div>

            <!-- تبويب الصيانة -->
            <div id="maintenance" class="tab-content">
                <div class="action-bar">
                    <h3>🔧 إدارة الصيانة</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="btn btn-success" onclick="openMaintenanceModal()">➕ إضافة صيانة</button>
                        <button class="btn btn-warning" onclick="scheduleMaintenanceReminder()">⏰ تذكير الصيانة</button>
                        <button class="btn btn-info" onclick="viewMaintenanceHistory()">📋 تاريخ الصيانة</button>
                    </div>
                </div>

                <div id="maintenanceContent">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>

            <!-- تبويب التكاليف -->
            <div id="costs" class="tab-content">
                <div class="action-bar">
                    <h3>💰 إدارة التكاليف</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="btn btn-success" onclick="addCostRecord()">➕ إضافة تكلفة</button>
                        <button class="btn btn-info" onclick="generateCostReport()">📊 تقرير التكاليف</button>
                        <button class="btn btn-warning" onclick="exportCosts()">📤 تصدير التكاليف</button>
                    </div>
                </div>

                <div id="costsContent">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>

            <!-- تبويب الإيجار -->
            <div id="rental" class="tab-content">
                <div class="action-bar">
                    <h3>🏠 إدارة الإيجار</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="btn btn-success" onclick="addRentalContract()">📝 عقد إيجار جديد</button>
                        <button class="btn btn-warning" onclick="renewRentalContract()">🔄 تجديد العقد</button>
                        <button class="btn btn-info" onclick="viewRentalHistory()">📋 تاريخ الإيجار</button>
                    </div>
                </div>

                <div id="rentalContent">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>

            <!-- تبويب التقسيط -->
            <div id="installments" class="tab-content">
                <div class="action-bar">
                    <h3>📊 إدارة التقسيط</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="btn btn-success" onclick="addInstallmentPlan()">📋 خطة تقسيط جديدة</button>
                        <button class="btn btn-warning" onclick="payInstallment()">💳 دفع قسط</button>
                        <button class="btn btn-info" onclick="viewInstallmentSchedule()">📅 جدول الأقساط</button>
                    </div>
                </div>

                <div id="installmentsContent">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة سيارة جديدة -->
    <div id="addVehicleModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
            <h3 style="margin-bottom: 20px; color: #333;">🚗 إضافة سيارة جديدة</h3>

            <form id="addVehicleForm" style="display: grid; gap: 15px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">نوع السيارة:</label>
                        <select id="vehicleType" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            <option value="شاحنة صغيرة">شاحنة صغيرة</option>
                            <option value="شاحنة متوسطة">شاحنة متوسطة</option>
                            <option value="شاحنة كبيرة">شاحنة كبيرة</option>
                            <option value="فان">فان</option>
                            <option value="دراجة نارية">دراجة نارية</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">رقم اللوحة:</label>
                        <input type="text" id="plateNumber" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="مثال: أ ب ج 1234">
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">الماركة:</label>
                        <input type="text" id="brand" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="مثال: تويوتا">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">الموديل:</label>
                        <input type="text" id="model" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="مثال: هايلكس">
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">سنة الصنع:</label>
                        <input type="number" id="year" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="2023" min="1990" max="2030">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">نوع الملكية:</label>
                        <select id="ownershipType" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            <option value="مملوكة">مملوكة</option>
                            <option value="مؤجرة">مؤجرة</option>
                            <option value="تقسيط">تقسيط</option>
                            <option value="ليزينج">ليزينج</option>
                        </select>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">سعر الشراء (ريال):</label>
                        <input type="number" id="purchasePrice" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="50000">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">تاريخ الشراء:</label>
                        <input type="date" id="purchaseDate" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                    </div>
                </div>

                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">السائق المخصص:</label>
                    <select id="assignedDriver" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                        <option value="">اختر السائق</option>
                        <!-- سيتم ملؤها من قاعدة البيانات -->
                    </select>
                </div>

                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">ملاحظات:</label>
                    <textarea id="notes" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; height: 80px;" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" onclick="closeAddVehicleModal()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                    <button type="submit" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">حفظ السيارة</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        // متغيرات عامة
        let vehicles = [];
        let maintenanceRecords = [];
        let costRecords = [];
        let rentalContracts = [];
        let installmentPlans = [];

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📍 تحميل صفحة إدارة السيارات...');

            // تحميل البيانات
            loadVehicles();
            loadDriversForDropdown();
            updateStats();

            // إعداد نموذج إضافة السيارة
            document.getElementById('addVehicleForm').addEventListener('submit', handleAddVehicle);

            console.log('✅ تم تحميل صفحة السيارات بنجاح');
        });

        // تحميل السيارات
        function loadVehicles() {
            try {
                // محاولة تحميل البيانات من localStorage
                const savedVehicles = localStorage.getItem('vehicles');
                const savedMaintenance = localStorage.getItem('maintenanceRecords');
                const savedCosts = localStorage.getItem('costRecords');

                if (savedVehicles) {
                    vehicles = JSON.parse(savedVehicles);
                    console.log('✅ تم تحميل السيارات من التخزين المحلي:', vehicles.length);
                } else {
                    // إنشاء بيانات تجريبية فقط إذا لم توجد بيانات محفوظة
                    vehicles = [
                    {
                        id: 'v1',
                        type: 'شاحنة صغيرة',
                        plateNumber: 'أ ب ج 1234',
                        brand: 'تويوتا',
                        model: 'هايلكس',
                        year: 2022,
                        ownershipType: 'مملوكة',
                        purchasePrice: 85000,
                        purchaseDate: '2022-01-15',
                        assignedDriver: 'أحمد محمد',
                        status: 'نشطة',
                        mileage: 45000,
                        lastMaintenance: '2024-01-15',
                        nextMaintenance: '2024-04-15',
                        notes: 'سيارة في حالة ممتازة'
                    },
                    {
                        id: 'v2',
                        type: 'شاحنة متوسطة',
                        plateNumber: 'د هـ و 5678',
                        brand: 'إيسوزو',
                        model: 'NPR',
                        year: 2021,
                        ownershipType: 'تقسيط',
                        purchasePrice: 120000,
                        purchaseDate: '2021-06-10',
                        assignedDriver: 'محمد علي',
                        status: 'في الصيانة',
                        mileage: 78000,
                        lastMaintenance: '2024-02-01',
                        nextMaintenance: '2024-05-01',
                        notes: 'تحتاج صيانة دورية'
                    },
                    {
                        id: 'v3',
                        type: 'فان',
                        plateNumber: 'ز ح ط 9012',
                        brand: 'هيونداي',
                        model: 'H100',
                        year: 2023,
                        ownershipType: 'مؤجرة',
                        purchasePrice: 0,
                        purchaseDate: '2023-03-01',
                        assignedDriver: 'سالم أحمد',
                        status: 'نشطة',
                        mileage: 25000,
                        lastMaintenance: '2024-01-20',
                        nextMaintenance: '2024-04-20',
                        notes: 'عقد إيجار لمدة سنتين'
                    }
                    ];
                    saveVehicles(); // حفظ البيانات التجريبية
                    console.log('✅ تم إنشاء بيانات تجريبية للسيارات:', vehicles.length);
                }

                // تحميل بيانات الصيانة والتكاليف
                if (savedMaintenance) {
                    maintenanceRecords = JSON.parse(savedMaintenance);
                }
                if (savedCosts) {
                    costRecords = JSON.parse(savedCosts);
                }

                displayVehicles();
                console.log('✅ تم تحميل جميع البيانات بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل السيارات:', error);
                vehicles = [];
                maintenanceRecords = [];
                costRecords = [];
                displayVehicles();
            }
        }

        // حفظ السيارات في localStorage
        function saveVehicles() {
            try {
                localStorage.setItem('vehicles', JSON.stringify(vehicles));
                localStorage.setItem('maintenanceRecords', JSON.stringify(maintenanceRecords));
                localStorage.setItem('costRecords', JSON.stringify(costRecords));
                console.log('✅ تم حفظ البيانات في التخزين المحلي');
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
            }
        }

        // عرض السيارات
        function displayVehicles() {
            const grid = document.getElementById('vehiclesGrid');

            if (!vehicles || vehicles.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #666;">
                        <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;">🚗</div>
                        <h3 style="margin-bottom: 10px; font-size: 1.5rem;">لا توجد سيارات مسجلة</h3>
                        <p style="margin-bottom: 30px; font-size: 1.1rem;">ابدأ بإضافة أول سيارة في الأسطول</p>
                        <button class="btn btn-success" onclick="openAddVehicleModal()">
                            ➕ إضافة سيارة جديدة
                        </button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = vehicles.map(vehicle => `
                <div class="vehicle-card">
                    <div class="vehicle-header">
                        <div class="vehicle-icon">${getVehicleIcon(vehicle.type)}</div>
                        <div class="vehicle-info">
                            <h3>${vehicle.brand} ${vehicle.model}</h3>
                            <span class="vehicle-type">${vehicle.type}</span>
                        </div>
                    </div>

                    <div class="vehicle-details">
                        <div class="detail-row">
                            <span class="detail-label">رقم اللوحة:</span>
                            <span class="detail-value">${vehicle.plateNumber}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">سنة الصنع:</span>
                            <span class="detail-value">${vehicle.year}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">نوع الملكية:</span>
                            <span class="detail-value">${vehicle.ownershipType}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">السائق:</span>
                            <span class="detail-value">${vehicle.assignedDriver || 'غير محدد'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">المسافة المقطوعة:</span>
                            <span class="detail-value">${vehicle.mileage?.toLocaleString()} كم</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الحالة:</span>
                            <span class="detail-value">
                                <span class="status-badge ${getStatusClass(vehicle.status)}">
                                    ${vehicle.status} ${getStatusIcon(vehicle.status)}
                                </span>
                            </span>
                        </div>
                    </div>

                    <div class="vehicle-actions">
                        <button class="btn btn-info btn-sm" onclick="viewVehicleDetails('${vehicle.id}')">
                            👁️ التفاصيل
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="editVehicle('${vehicle.id}')">
                            ✏️ تعديل
                        </button>
                        <button class="btn btn-success btn-sm" onclick="addMaintenance('${vehicle.id}')">
                            🔧 صيانة
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="addCost('${vehicle.id}')">
                            💰 تكلفة
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteVehicle('${vehicle.id}', '${vehicle.plateNumber}')">
                            🗑️ حذف
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // الحصول على أيقونة السيارة
        function getVehicleIcon(type) {
            const icons = {
                'شاحنة صغيرة': '🚐',
                'شاحنة متوسطة': '🚚',
                'شاحنة كبيرة': '🚛',
                'فان': '🚐',
                'دراجة نارية': '🏍️'
            };
            return icons[type] || '🚗';
        }

        // الحصول على فئة الحالة
        function getStatusClass(status) {
            const classes = {
                'نشطة': 'status-active',
                'في الصيانة': 'status-maintenance',
                'غير نشطة': 'status-inactive'
            };
            return classes[status] || 'status-inactive';
        }

        // الحصول على أيقونة الحالة
        function getStatusIcon(status) {
            const icons = {
                'نشطة': '✅',
                'في الصيانة': '🔧',
                'غير نشطة': '❌'
            };
            return icons[status] || '❓';
        }

        // تحديث الإحصائيات
        function updateStats() {
            try {
                const totalVehicles = vehicles.length;
                const activeVehicles = vehicles.filter(v => v.status === 'نشطة').length;
                const maintenanceVehicles = vehicles.filter(v => v.status === 'في الصيانة').length;
                const totalCosts = vehicles.reduce((sum, v) => sum + (v.purchasePrice || 0), 0);

                document.getElementById('totalVehicles').textContent = totalVehicles;
                document.getElementById('activeVehicles').textContent = activeVehicles;
                document.getElementById('maintenanceVehicles').textContent = maintenanceVehicles;
                document.getElementById('totalCosts').textContent = totalCosts.toLocaleString();

                console.log('✅ تم تحديث إحصائيات السيارات');
            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // تحميل السائقين للقائمة المنسدلة
        function loadDriversForDropdown() {
            try {
                let drivers = [];

                // محاولة تحميل السائقين من قاعدة البيانات
                if (typeof db !== 'undefined' && db.getAllDistributors) {
                    drivers = db.getAllDistributors().filter(d => d.role === 'سائق');
                } else {
                    // بيانات تجريبية للسائقين
                    drivers = [
                        { id: 'd1', name: 'أحمد محمد', role: 'سائق' },
                        { id: 'd2', name: 'محمد علي', role: 'سائق' },
                        { id: 'd3', name: 'سالم أحمد', role: 'سائق' },
                        { id: 'd4', name: 'علي حسن', role: 'سائق' }
                    ];
                }

                const driverSelect = document.getElementById('assignedDriver');
                driverSelect.innerHTML = '<option value="">اختر السائق</option>' +
                    drivers.map(driver => `<option value="${driver.name}">${driver.name}</option>`).join('');

                console.log('✅ تم تحميل السائقين للقائمة:', drivers.length);
            } catch (error) {
                console.error('خطأ في تحميل السائقين:', error);
            }
        }

        // عرض التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // تحميل محتوى التبويب
            loadTabContent(tabName);
        }

        // تحميل محتوى التبويب
        function loadTabContent(tabName) {
            switch(tabName) {
                case 'maintenance':
                    loadMaintenanceContent();
                    break;
                case 'costs':
                    loadCostsContent();
                    break;
                case 'rental':
                    loadRentalContent();
                    break;
                case 'installments':
                    loadInstallmentsContent();
                    break;
            }
        }

        // فتح نافذة إضافة سيارة
        function openAddVehicleModal() {
            document.getElementById('addVehicleModal').style.display = 'block';
            document.getElementById('purchaseDate').value = new Date().toISOString().split('T')[0];
        }

        // إغلاق نافذة إضافة سيارة
        function closeAddVehicleModal() {
            document.getElementById('addVehicleModal').style.display = 'none';
            document.getElementById('addVehicleForm').reset();
        }

        // معالجة إضافة سيارة جديدة
        function handleAddVehicle(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const newVehicle = {
                id: 'v' + Date.now(),
                type: document.getElementById('vehicleType').value,
                plateNumber: document.getElementById('plateNumber').value,
                brand: document.getElementById('brand').value,
                model: document.getElementById('model').value,
                year: parseInt(document.getElementById('year').value),
                ownershipType: document.getElementById('ownershipType').value,
                purchasePrice: parseFloat(document.getElementById('purchasePrice').value) || 0,
                purchaseDate: document.getElementById('purchaseDate').value,
                assignedDriver: document.getElementById('assignedDriver').value,
                status: 'نشطة',
                mileage: 0,
                lastMaintenance: null,
                nextMaintenance: null,
                notes: document.getElementById('notes').value
            };

            vehicles.push(newVehicle);
            saveVehicles(); // حفظ البيانات
            displayVehicles();
            updateStats();
            closeAddVehicleModal();

            alert('تم إضافة السيارة بنجاح!');
        }

        // البحث في السيارات
        function searchVehicles() {
            const searchTerm = document.getElementById('vehicleSearch').value.toLowerCase();

            if (!searchTerm) {
                displayVehicles();
                return;
            }

            const filteredVehicles = vehicles.filter(vehicle =>
                vehicle.plateNumber.toLowerCase().includes(searchTerm) ||
                vehicle.brand.toLowerCase().includes(searchTerm) ||
                vehicle.model.toLowerCase().includes(searchTerm) ||
                vehicle.type.toLowerCase().includes(searchTerm) ||
                (vehicle.assignedDriver && vehicle.assignedDriver.toLowerCase().includes(searchTerm))
            );

            // عرض النتائج المفلترة
            const grid = document.getElementById('vehiclesGrid');
            if (filteredVehicles.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #666;">
                        <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;">🔍</div>
                        <h3>لا توجد نتائج للبحث</h3>
                        <p>جرب كلمات بحث أخرى</p>
                    </div>
                `;
            } else {
                // استخدام نفس منطق العرض مع السيارات المفلترة
                const originalVehicles = vehicles;
                vehicles = filteredVehicles;
                displayVehicles();
                vehicles = originalVehicles;
            }
        }

        // عرض تفاصيل السيارة
        function viewVehicleDetails(vehicleId) {
            const vehicle = vehicles.find(v => v.id === vehicleId);
            if (vehicle) {
                alert(`
تفاصيل السيارة:
• النوع: ${vehicle.type}
• رقم اللوحة: ${vehicle.plateNumber}
• الماركة والموديل: ${vehicle.brand} ${vehicle.model}
• سنة الصنع: ${vehicle.year}
• نوع الملكية: ${vehicle.ownershipType}
• سعر الشراء: ${vehicle.purchasePrice?.toLocaleString()} ريال
• السائق المخصص: ${vehicle.assignedDriver || 'غير محدد'}
• المسافة المقطوعة: ${vehicle.mileage?.toLocaleString()} كم
• الحالة: ${vehicle.status}
• الملاحظات: ${vehicle.notes || 'لا توجد ملاحظات'}
                `);
            }
        }

        // تصدير بيانات السيارات
        function exportVehicles() {
            try {
                const dataStr = JSON.stringify(vehicles, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `vehicles_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                alert('تم تصدير بيانات السيارات بنجاح');
            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                alert('حدث خطأ أثناء تصدير البيانات');
            }
        }

        // وظيفة حذف السيارة
        function deleteVehicle(vehicleId, plateNumber) {
            if (confirm(`هل أنت متأكد من حذف السيارة رقم ${plateNumber}؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
                try {
                    // العثور على السيارة وحذفها
                    const vehicleIndex = vehicles.findIndex(v => v.id === vehicleId);
                    if (vehicleIndex !== -1) {
                        vehicles.splice(vehicleIndex, 1);
                        saveVehicles(); // حفظ البيانات

                        // تحديث العرض والإحصائيات
                        displayVehicles();
                        updateStats();

                        alert(`تم حذف السيارة رقم ${plateNumber} بنجاح!`);
                        console.log(`✅ تم حذف السيارة: ${vehicleId}`);
                    } else {
                        alert('لم يتم العثور على السيارة!');
                    }
                } catch (error) {
                    console.error('خطأ في حذف السيارة:', error);
                    alert('حدث خطأ أثناء حذف السيارة');
                }
            }
        }

        // وظيفة تعديل السيارة
        function editVehicle(vehicleId) {
            const vehicle = vehicles.find(v => v.id === vehicleId);
            if (!vehicle) {
                alert('لم يتم العثور على السيارة!');
                return;
            }

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 2000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">✏️ تعديل بيانات السيارة</h3>

                    <form id="editVehicleForm" style="display: grid; gap: 15px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">نوع السيارة:</label>
                                <select id="editVehicleType" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                                    <option value="شاحنة صغيرة" ${vehicle.type === 'شاحنة صغيرة' ? 'selected' : ''}>شاحنة صغيرة</option>
                                    <option value="شاحنة متوسطة" ${vehicle.type === 'شاحنة متوسطة' ? 'selected' : ''}>شاحنة متوسطة</option>
                                    <option value="شاحنة كبيرة" ${vehicle.type === 'شاحنة كبيرة' ? 'selected' : ''}>شاحنة كبيرة</option>
                                    <option value="فان" ${vehicle.type === 'فان' ? 'selected' : ''}>فان</option>
                                    <option value="دراجة نارية" ${vehicle.type === 'دراجة نارية' ? 'selected' : ''}>دراجة نارية</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">رقم اللوحة:</label>
                                <input type="text" id="editPlateNumber" value="${vehicle.plateNumber}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">الماركة:</label>
                                <input type="text" id="editBrand" value="${vehicle.brand}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">الموديل:</label>
                                <input type="text" id="editModel" value="${vehicle.model}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">سنة الصنع:</label>
                                <input type="number" id="editYear" value="${vehicle.year}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">نوع الملكية:</label>
                                <select id="editOwnershipType" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                                    <option value="مملوكة" ${vehicle.ownershipType === 'مملوكة' ? 'selected' : ''}>مملوكة</option>
                                    <option value="مؤجرة" ${vehicle.ownershipType === 'مؤجرة' ? 'selected' : ''}>مؤجرة</option>
                                    <option value="تقسيط" ${vehicle.ownershipType === 'تقسيط' ? 'selected' : ''}>تقسيط</option>
                                    <option value="ليزينج" ${vehicle.ownershipType === 'ليزينج' ? 'selected' : ''}>ليزينج</option>
                                </select>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">الحالة:</label>
                                <select id="editStatus" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                                    <option value="نشطة" ${vehicle.status === 'نشطة' ? 'selected' : ''}>نشطة</option>
                                    <option value="في الصيانة" ${vehicle.status === 'في الصيانة' ? 'selected' : ''}>في الصيانة</option>
                                    <option value="غير نشطة" ${vehicle.status === 'غير نشطة' ? 'selected' : ''}>غير نشطة</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">المسافة المقطوعة (كم):</label>
                                <input type="number" id="editMileage" value="${vehicle.mileage || 0}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">السائق المخصص:</label>
                            <select id="editAssignedDriver" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                                <option value="">اختر السائق</option>
                                <!-- سيتم ملؤها من قاعدة البيانات -->
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">ملاحظات:</label>
                            <textarea id="editNotes" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; height: 80px;">${vehicle.notes || ''}</textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" onclick="closeEditModal()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit" style="padding: 10px 20px; background: #ffc107; color: #333; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">حفظ التعديلات</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // تحميل السائقين للقائمة
            loadDriversForEditForm(vehicle.assignedDriver);

            // معالجة النموذج
            document.getElementById('editVehicleForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // تحديث بيانات السيارة
                vehicle.type = document.getElementById('editVehicleType').value;
                vehicle.plateNumber = document.getElementById('editPlateNumber').value;
                vehicle.brand = document.getElementById('editBrand').value;
                vehicle.model = document.getElementById('editModel').value;
                vehicle.year = parseInt(document.getElementById('editYear').value);
                vehicle.ownershipType = document.getElementById('editOwnershipType').value;
                vehicle.status = document.getElementById('editStatus').value;
                vehicle.mileage = parseInt(document.getElementById('editMileage').value) || 0;
                vehicle.assignedDriver = document.getElementById('editAssignedDriver').value;
                vehicle.notes = document.getElementById('editNotes').value;
                vehicle.updatedAt = new Date().toISOString();

                saveVehicles(); // حفظ البيانات
                displayVehicles();
                updateStats();
                closeEditModal();

                alert('تم تحديث بيانات السيارة بنجاح!');
            });

            window.closeEditModal = function() {
                document.body.removeChild(modal);
            };
        }

        // تحميل السائقين لنموذج التعديل
        function loadDriversForEditForm(selectedDriver) {
            try {
                let drivers = [];

                if (typeof db !== 'undefined' && db.getAllDistributors) {
                    drivers = db.getAllDistributors().filter(d => d.role === 'سائق');
                } else {
                    drivers = [
                        { id: 'd1', name: 'أحمد محمد', role: 'سائق' },
                        { id: 'd2', name: 'محمد علي', role: 'سائق' },
                        { id: 'd3', name: 'سالم أحمد', role: 'سائق' },
                        { id: 'd4', name: 'علي حسن', role: 'سائق' }
                    ];
                }

                const driverSelect = document.getElementById('editAssignedDriver');
                driverSelect.innerHTML = '<option value="">اختر السائق</option>' +
                    drivers.map(driver =>
                        `<option value="${driver.name}" ${driver.name === selectedDriver ? 'selected' : ''}>${driver.name}</option>`
                    ).join('');
            } catch (error) {
                console.error('خطأ في تحميل السائقين:', error);
            }
        }

        // وظيفة إضافة صيانة
        function addMaintenance(vehicleId) {
            const vehicle = vehicles.find(v => v.id === vehicleId);
            if (!vehicle) {
                alert('لم يتم العثور على السيارة!');
                return;
            }

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 2000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">🔧 إضافة صيانة للسيارة</h3>
                    <p style="margin-bottom: 20px; color: #666;">السيارة: ${vehicle.brand} ${vehicle.model} - ${vehicle.plateNumber}</p>

                    <form id="addMaintenanceForm" style="display: grid; gap: 15px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">نوع الصيانة:</label>
                                <select id="maintenanceType" required style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                                    <option value="">اختر نوع الصيانة</option>
                                    <option value="صيانة دورية">صيانة دورية</option>
                                    <option value="تغيير زيت">تغيير زيت</option>
                                    <option value="فحص فرامل">فحص فرامل</option>
                                    <option value="تغيير إطارات">تغيير إطارات</option>
                                    <option value="إصلاح محرك">إصلاح محرك</option>
                                    <option value="صيانة تكييف">صيانة تكييف</option>
                                    <option value="فحص كهرباء">فحص كهرباء</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">تاريخ الصيانة:</label>
                                <input type="date" id="maintenanceDate" required style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">التكلفة (ريال):</label>
                                <input type="number" id="maintenanceCost" required style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="0">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">المسافة عند الصيانة (كم):</label>
                                <input type="number" id="maintenanceMileage" value="${vehicle.mileage || 0}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">مركز الصيانة:</label>
                            <input type="text" id="maintenanceCenter" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="اسم مركز الصيانة">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">تاريخ الصيانة القادمة:</label>
                            <input type="date" id="nextMaintenanceDate" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">ملاحظات الصيانة:</label>
                            <textarea id="maintenanceNotes" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; height: 80px;" placeholder="تفاصيل الصيانة والملاحظات..."></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" onclick="closeMaintenanceModal()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">حفظ الصيانة</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // تعيين التاريخ الحالي
            document.getElementById('maintenanceDate').value = new Date().toISOString().split('T')[0];

            // معالجة النموذج
            document.getElementById('addMaintenanceForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const maintenanceRecord = {
                    id: 'M' + Date.now(),
                    vehicleId: vehicleId,
                    type: document.getElementById('maintenanceType').value,
                    date: document.getElementById('maintenanceDate').value,
                    cost: parseFloat(document.getElementById('maintenanceCost').value) || 0,
                    mileage: parseInt(document.getElementById('maintenanceMileage').value) || 0,
                    center: document.getElementById('maintenanceCenter').value,
                    nextDate: document.getElementById('nextMaintenanceDate').value,
                    notes: document.getElementById('maintenanceNotes').value,
                    createdAt: new Date().toISOString()
                };

                // حفظ سجل الصيانة
                if (!maintenanceRecords) maintenanceRecords = [];
                maintenanceRecords.push(maintenanceRecord);

                // تحديث بيانات السيارة
                vehicle.lastMaintenance = maintenanceRecord.date;
                vehicle.nextMaintenance = maintenanceRecord.nextDate;
                vehicle.mileage = maintenanceRecord.mileage;

                saveVehicles(); // حفظ البيانات
                displayVehicles();
                updateStats();
                closeMaintenanceModal();

                alert('تم إضافة سجل الصيانة بنجاح!');
            });

            window.closeMaintenanceModal = function() {
                document.body.removeChild(modal);
            };
        }

        function loadMaintenanceContent() {
            const maintenanceContent = `
                <div style="padding: 20px;">
                    <!-- إحصائيات الصيانة -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">🔧</div>
                                <div>
                                    <div style="font-size: 2rem; font-weight: 700; color: #333;" id="total-maintenance">${maintenanceRecords ? maintenanceRecords.length : 0}</div>
                                    <div style="color: #666; font-size: 0.9rem;">إجمالي الصيانات</div>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">⏰</div>
                                <div>
                                    <div style="font-size: 2rem; font-weight: 700; color: #333;" id="pending-maintenance">${vehicles ? vehicles.filter(v => v.nextMaintenance && new Date(v.nextMaintenance) <= new Date()).length : 0}</div>
                                    <div style="color: #666; font-size: 0.9rem;">صيانات مستحقة</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div style="display: flex; gap: 15px; margin-bottom: 30px; flex-wrap: wrap;">
                        <button onclick="scheduleMaintenanceReminder()" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer;">
                            ⏰ جدولة تذكير صيانة
                        </button>
                        <button onclick="viewMaintenanceHistory()" style="background: linear-gradient(135deg, #6f42c1 0%, #6610f2 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer;">
                            📋 تاريخ الصيانة
                        </button>
                        <button onclick="exportMaintenanceReport()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer;">
                            📊 تصدير تقرير
                        </button>
                    </div>

                    <!-- قائمة الصيانات الأخيرة -->
                    <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <h3 style="color: #333; margin-bottom: 20px;">🔧 سجلات الصيانة الأخيرة</h3>
                        <div id="maintenance-records-list">
                            ${displayMaintenanceRecords()}
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('maintenanceContent').innerHTML = maintenanceContent;
        }

        function displayMaintenanceRecords() {
            if (!maintenanceRecords || maintenanceRecords.length === 0) {
                return '<p style="text-align: center; color: #666; padding: 40px;">لا توجد سجلات صيانة</p>';
            }

            return maintenanceRecords.slice(-10).reverse().map(record => {
                const vehicle = vehicles.find(v => v.id === record.vehicleId);
                return `
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 15px; border-left: 4px solid #28a745;">
                        <div style="display: flex; justify-content: space-between; align-items: start;">
                            <div>
                                <h4 style="margin: 0 0 10px 0; color: #333;">${record.type}</h4>
                                <p style="margin: 0 0 5px 0; color: #666;">🚗 ${vehicle ? `${vehicle.brand} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة محذوفة'}</p>
                                <p style="margin: 0 0 5px 0; color: #666;">📅 ${new Date(record.date).toLocaleDateString('ar-SA')}</p>
                                <p style="margin: 0 0 5px 0; color: #666;">💰 ${record.cost.toLocaleString()} ريال</p>
                                ${record.center ? `<p style="margin: 0 0 5px 0; color: #666;">🏪 ${record.center}</p>` : ''}
                                ${record.notes ? `<p style="margin: 10px 0 0 0; color: #555; font-style: italic;">${record.notes}</p>` : ''}
                            </div>
                            <div style="text-align: center;">
                                ${record.nextDate ? `
                                    <div style="background: #e3f2fd; padding: 8px 12px; border-radius: 8px; margin-bottom: 10px;">
                                        <div style="font-size: 0.8rem; color: #1976d2;">الصيانة القادمة</div>
                                        <div style="font-weight: 600; color: #1976d2;">${new Date(record.nextDate).toLocaleDateString('ar-SA')}</div>
                                    </div>
                                ` : ''}
                                <button onclick="deleteMaintenanceRecord('${record.id}')" style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; font-size: 0.8rem;">
                                    🗑️ حذف
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // وظيفة إضافة تكلفة
        function addCost(vehicleId) {
            const vehicle = vehicles.find(v => v.id === vehicleId);
            if (!vehicle) {
                alert('لم يتم العثور على السيارة!');
                return;
            }

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 2000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">💰 إضافة تكلفة للسيارة</h3>
                    <p style="margin-bottom: 20px; color: #666;">السيارة: ${vehicle.brand} ${vehicle.model} - ${vehicle.plateNumber}</p>

                    <form id="addCostForm" style="display: grid; gap: 15px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">نوع التكلفة:</label>
                                <select id="costType" required style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                                    <option value="">اختر نوع التكلفة</option>
                                    <option value="وقود">وقود</option>
                                    <option value="صيانة">صيانة</option>
                                    <option value="تأمين">تأمين</option>
                                    <option value="ترخيص">ترخيص</option>
                                    <option value="غسيل">غسيل</option>
                                    <option value="مخالفات">مخالفات</option>
                                    <option value="إطارات">إطارات</option>
                                    <option value="قطع غيار">قطع غيار</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">تاريخ التكلفة:</label>
                                <input type="date" id="costDate" required style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">المبلغ (ريال):</label>
                                <input type="number" id="costAmount" required step="0.01" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="0.00">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">طريقة الدفع:</label>
                                <select id="paymentMethod" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;">
                                    <option value="نقدي">نقدي</option>
                                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                    <option value="تحويل بنكي">تحويل بنكي</option>
                                    <option value="شيك">شيك</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">المورد/المحل:</label>
                            <input type="text" id="supplier" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="اسم المورد أو المحل">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">رقم الفاتورة:</label>
                            <input type="text" id="invoiceNumber" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="رقم الفاتورة (اختياري)">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">ملاحظات:</label>
                            <textarea id="costNotes" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; height: 80px;" placeholder="تفاصيل إضافية عن التكلفة..."></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" onclick="closeCostModal()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">حفظ التكلفة</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // تعيين التاريخ الحالي
            document.getElementById('costDate').value = new Date().toISOString().split('T')[0];

            // معالجة النموذج
            document.getElementById('addCostForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const costRecord = {
                    id: 'C' + Date.now(),
                    vehicleId: vehicleId,
                    type: document.getElementById('costType').value,
                    date: document.getElementById('costDate').value,
                    amount: parseFloat(document.getElementById('costAmount').value) || 0,
                    paymentMethod: document.getElementById('paymentMethod').value,
                    supplier: document.getElementById('supplier').value,
                    invoiceNumber: document.getElementById('invoiceNumber').value,
                    notes: document.getElementById('costNotes').value,
                    createdAt: new Date().toISOString()
                };

                // حفظ سجل التكلفة
                if (!costRecords) costRecords = [];
                costRecords.push(costRecord);

                saveVehicles(); // حفظ البيانات
                updateStats();
                closeCostModal();

                alert('تم إضافة سجل التكلفة بنجاح!');
            });

            window.closeCostModal = function() {
                document.body.removeChild(modal);
            };
        }

        function loadCostsContent() {
            const totalCosts = costRecords ? costRecords.reduce((sum, cost) => sum + cost.amount, 0) : 0;
            const monthlyAverage = costRecords ? totalCosts / Math.max(1, new Set(costRecords.map(c => c.date.substring(0, 7))).size) : 0;

            const costsContent = `
                <div style="padding: 20px;">
                    <!-- إحصائيات التكاليف -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">💰</div>
                                <div>
                                    <div style="font-size: 2rem; font-weight: 700; color: #333;">${totalCosts.toLocaleString()}</div>
                                    <div style="color: #666; font-size: 0.9rem;">إجمالي التكاليف (ريال)</div>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">📊</div>
                                <div>
                                    <div style="font-size: 2rem; font-weight: 700; color: #333;">${monthlyAverage.toLocaleString()}</div>
                                    <div style="color: #666; font-size: 0.9rem;">متوسط شهري (ريال)</div>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">📋</div>
                                <div>
                                    <div style="font-size: 2rem; font-weight: 700; color: #333;">${costRecords ? costRecords.length : 0}</div>
                                    <div style="color: #666; font-size: 0.9rem;">عدد السجلات</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div style="display: flex; gap: 15px; margin-bottom: 30px; flex-wrap: wrap;">
                        <button onclick="generateCostReport()" style="background: linear-gradient(135deg, #6f42c1 0%, #6610f2 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer;">
                            📊 تقرير التكاليف
                        </button>
                        <button onclick="exportCosts()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer;">
                            📤 تصدير البيانات
                        </button>
                        <button onclick="filterCostsByType()" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer;">
                            🔍 فلترة حسب النوع
                        </button>
                    </div>

                    <!-- قائمة التكاليف الأخيرة -->
                    <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <h3 style="color: #333; margin-bottom: 20px;">💰 سجلات التكاليف الأخيرة</h3>
                        <div id="cost-records-list">
                            ${displayCostRecords()}
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('costsContent').innerHTML = costsContent;
        }

        function displayCostRecords() {
            if (!costRecords || costRecords.length === 0) {
                return '<p style="text-align: center; color: #666; padding: 40px;">لا توجد سجلات تكاليف</p>';
            }

            return costRecords.slice(-10).reverse().map(record => {
                const vehicle = vehicles.find(v => v.id === record.vehicleId);
                return `
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 15px; border-left: 4px solid #007bff;">
                        <div style="display: flex; justify-content: space-between; align-items: start;">
                            <div>
                                <h4 style="margin: 0 0 10px 0; color: #333;">${record.type}</h4>
                                <p style="margin: 0 0 5px 0; color: #666;">🚗 ${vehicle ? `${vehicle.brand} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة محذوفة'}</p>
                                <p style="margin: 0 0 5px 0; color: #666;">📅 ${new Date(record.date).toLocaleDateString('ar-SA')}</p>
                                <p style="margin: 0 0 5px 0; color: #666;">💳 ${record.paymentMethod}</p>
                                ${record.supplier ? `<p style="margin: 0 0 5px 0; color: #666;">🏪 ${record.supplier}</p>` : ''}
                                ${record.invoiceNumber ? `<p style="margin: 0 0 5px 0; color: #666;">📄 فاتورة: ${record.invoiceNumber}</p>` : ''}
                                ${record.notes ? `<p style="margin: 10px 0 0 0; color: #555; font-style: italic;">${record.notes}</p>` : ''}
                            </div>
                            <div style="text-align: center;">
                                <div style="background: #e3f2fd; padding: 8px 12px; border-radius: 8px; margin-bottom: 10px;">
                                    <div style="font-size: 1.2rem; font-weight: 700; color: #1976d2;">${record.amount.toLocaleString()} ريال</div>
                                </div>
                                <button onclick="deleteCostRecord('${record.id}')" style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; font-size: 0.8rem;">
                                    🗑️ حذف
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function loadRentalContent() {
            const rentalContracts = JSON.parse(localStorage.getItem('rentalContracts') || '[]');

            if (rentalContracts.length === 0) {
                document.getElementById('rentalContent').innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: #666;">
                        <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;">🏠</div>
                        <h3>إدارة الإيجار</h3>
                        <p>لا توجد عقود إيجار مسجلة</p>
                        <button onclick="addRentalContract()"
                                style="background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; margin-top: 20px;">
                            ➕ إضافة عقد إيجار جديد
                        </button>
                    </div>
                `;
                return;
            }

            const contractsList = rentalContracts.map(contract => {
                const vehicle = vehicles.find(v => v.id === contract.vehicleId);
                const vehicleName = vehicle ? `${vehicle.make} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة غير معروفة';
                const statusColor = contract.status === 'نشط' ? '#28a745' : '#6c757d';
                const isExpiringSoon = new Date(contract.endDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

                return `
                    <div style="border: 1px solid #ddd; border-radius: 12px; padding: 20px; margin-bottom: 15px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h4 style="margin: 0; color: #333; font-size: 18px;">${contract.renterName}</h4>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                ${isExpiringSoon ? '<span style="background: #ffc107; color: #000; padding: 4px 8px; border-radius: 4px; font-size: 12px;">⚠️ ينتهي قريباً</span>' : ''}
                                <span style="background: ${statusColor}; color: white; padding: 6px 12px; border-radius: 6px; font-size: 12px;">${contract.status}</span>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <p style="margin: 5px 0; color: #666;"><strong>السيارة:</strong> ${vehicleName}</p>
                                <p style="margin: 5px 0; color: #666;"><strong>الجوال:</strong> ${contract.renterPhone}</p>
                                <p style="margin: 5px 0; color: #666;"><strong>رقم الهوية:</strong> ${contract.renterID}</p>
                            </div>
                            <div>
                                <p style="margin: 5px 0; color: #666;"><strong>الإيجار الشهري:</strong> ${contract.monthlyRent} ريال</p>
                                <p style="margin: 5px 0; color: #666;"><strong>التأمين:</strong> ${contract.securityDeposit} ريال</p>
                                <p style="margin: 5px 0; color: #666;"><strong>ينتهي في:</strong> ${new Date(contract.endDate).toLocaleDateString('ar-SA')}</p>
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button onclick="renewRentalContract()"
                                    style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                🔄 تجديد
                            </button>
                            <button onclick="viewRentalHistory()"
                                    style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                📋 التفاصيل
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            document.getElementById('rentalContent').innerHTML = `
                <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; color: #333;">🏠 عقود الإيجار (${rentalContracts.length})</h3>
                    <button onclick="addRentalContract()"
                            style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                        ➕ إضافة عقد جديد
                    </button>
                </div>
                ${contractsList}
            `;
        }

        function loadInstallmentsContent() {
            const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans') || '[]');

            if (installmentPlans.length === 0) {
                document.getElementById('installmentsContent').innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: #666;">
                        <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;">📊</div>
                        <h3>إدارة التقسيط</h3>
                        <p>لا توجد خطط تقسيط مسجلة</p>
                        <button onclick="addInstallmentPlan()"
                                style="background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; margin-top: 20px;">
                            ➕ إضافة خطة تقسيط جديدة
                        </button>
                    </div>
                `;
                return;
            }

            const plansList = installmentPlans.map(plan => {
                const vehicle = vehicles.find(v => v.id === plan.vehicleId);
                const vehicleName = vehicle ? `${vehicle.make} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة غير معروفة';
                const statusColor = plan.status === 'نشط' ? '#28a745' : plan.status === 'مكتمل' ? '#007bff' : '#6c757d';
                const progress = (plan.paidInstallments / plan.installmentCount) * 100;
                const remaining = plan.installmentCount - plan.paidInstallments;
                const remainingAmount = remaining * plan.monthlyInstallment;

                return `
                    <div style="border: 1px solid #ddd; border-radius: 12px; padding: 20px; margin-bottom: 15px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h4 style="margin: 0; color: #333; font-size: 18px;">${plan.customerName}</h4>
                            <span style="background: ${statusColor}; color: white; padding: 6px 12px; border-radius: 6px; font-size: 12px;">${plan.status}</span>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <p style="margin: 5px 0; color: #666;"><strong>السيارة:</strong> ${vehicleName}</p>
                                <p style="margin: 5px 0; color: #666;"><strong>إجمالي المبلغ:</strong> ${plan.totalAmount} ريال</p>
                                <p style="margin: 5px 0; color: #666;"><strong>المقدم:</strong> ${plan.downPayment} ريال</p>
                            </div>
                            <div>
                                <p style="margin: 5px 0; color: #666;"><strong>قيمة القسط:</strong> ${plan.monthlyInstallment} ريال</p>
                                <p style="margin: 5px 0; color: #666;"><strong>الأقساط المدفوعة:</strong> ${plan.paidInstallments} من ${plan.installmentCount}</p>
                                <p style="margin: 5px 0; color: #666;"><strong>المبلغ المتبقي:</strong> ${remainingAmount.toFixed(2)} ريال</p>
                            </div>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                <span style="font-size: 14px; color: #666;">التقدم</span>
                                <span style="font-size: 14px; color: #666;">${progress.toFixed(1)}%</span>
                            </div>
                            <div style="background: #e9ecef; border-radius: 10px; height: 8px; overflow: hidden;">
                                <div style="background: ${statusColor}; height: 100%; width: ${progress}%; transition: width 0.3s;"></div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            ${plan.status === 'نشط' && remaining > 0 ? `
                                <button onclick="payInstallment()"
                                        style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                    💰 دفع قسط
                                </button>
                            ` : ''}
                            <button onclick="viewInstallmentSchedule()"
                                    style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                                📊 الجدول
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            document.getElementById('installmentsContent').innerHTML = `
                <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; color: #333;">📊 خطط التقسيط (${installmentPlans.length})</h3>
                    <button onclick="addInstallmentPlan()"
                            style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                        ➕ إضافة خطة جديدة
                    </button>
                </div>
                ${plansList}
            `;
        }

        // وظائف مساعدة إضافية
        function deleteMaintenanceRecord(recordId) {
            if (confirm('هل أنت متأكد من حذف سجل الصيانة؟')) {
                const index = maintenanceRecords.findIndex(r => r.id === recordId);
                if (index !== -1) {
                    maintenanceRecords.splice(index, 1);
                    saveVehicles(); // حفظ البيانات
                    loadMaintenanceContent();
                    alert('تم حذف سجل الصيانة بنجاح!');
                }
            }
        }

        function deleteCostRecord(recordId) {
            if (confirm('هل أنت متأكد من حذف سجل التكلفة؟')) {
                const index = costRecords.findIndex(r => r.id === recordId);
                if (index !== -1) {
                    costRecords.splice(index, 1);
                    saveVehicles(); // حفظ البيانات
                    loadCostsContent();
                    updateStats();
                    alert('تم حذف سجل التكلفة بنجاح!');
                }
            }
        }

        function scheduleMaintenanceReminder() {
            if (!vehicles || vehicles.length === 0) {
                alert('لا توجد سيارات مسجلة. يرجى إضافة سيارة أولاً.');
                return;
            }

            // إنشاء نافذة إضافة تذكير
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 500px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">⏰ إضافة تذكير صيانة</h3>

                    <form id="addReminderForm">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اختر السيارة:</label>
                            <select id="reminderVehicleId" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="">اختر السيارة</option>
                                ${vehicles.map(v => `<option value="${v.id}">${v.make} ${v.model} - ${v.plateNumber}</option>`).join('')}
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">نوع التذكير:</label>
                            <select id="reminderType" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="">اختر نوع التذكير</option>
                                <option value="تغيير زيت">تغيير زيت</option>
                                <option value="فحص دوري">فحص دوري</option>
                                <option value="تجديد تأمين">تجديد تأمين</option>
                                <option value="تجديد رخصة">تجديد رخصة</option>
                                <option value="فحص إطارات">فحص إطارات</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ التذكير:</label>
                            <input type="date" id="reminderDate"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الأولوية:</label>
                            <select id="reminderPriority" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="منخفضة">منخفضة</option>
                                <option value="متوسطة" selected>متوسطة</option>
                                <option value="عالية">عالية</option>
                                <option value="عاجلة">عاجلة</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                            <textarea id="reminderNotes" rows="3" placeholder="تفاصيل التذكير..."
                                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" onclick="closeReminderModal()"
                                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">إضافة التذكير</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // تعيين تاريخ افتراضي (بعد أسبوع من اليوم)
            const nextWeek = new Date();
            nextWeek.setDate(nextWeek.getDate() + 7);
            document.getElementById('reminderDate').value = nextWeek.toISOString().split('T')[0];

            // معالجة النموذج
            document.getElementById('addReminderForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newReminder = {
                    id: 'REM' + Date.now(),
                    vehicleId: document.getElementById('reminderVehicleId').value,
                    type: document.getElementById('reminderType').value,
                    date: document.getElementById('reminderDate').value,
                    priority: document.getElementById('reminderPriority').value,
                    notes: document.getElementById('reminderNotes').value,
                    status: 'نشط',
                    createdAt: new Date().toISOString()
                };

                // حفظ التذكير في localStorage
                let reminders = JSON.parse(localStorage.getItem('maintenanceReminders') || '[]');
                reminders.push(newReminder);
                localStorage.setItem('maintenanceReminders', JSON.stringify(reminders));

                alert('تم إضافة التذكير بنجاح!');
                closeReminderModal();

                // عرض التذكيرات المضافة
                showActiveReminders();
            });

            window.closeReminderModal = function() {
                document.body.removeChild(modal);
            };
        }

        // عرض التذكيرات النشطة
        function showActiveReminders() {
            const reminders = JSON.parse(localStorage.getItem('maintenanceReminders') || '[]');
            const activeReminders = reminders.filter(r => r.status === 'نشط');

            if (activeReminders.length === 0) {
                alert('لا توجد تذكيرات نشطة');
                return;
            }

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            const remindersList = activeReminders.map(reminder => {
                const vehicle = vehicles.find(v => v.id === reminder.vehicleId);
                const vehicleName = vehicle ? `${vehicle.make} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة غير معروفة';
                const priorityColor = {
                    'منخفضة': '#28a745',
                    'متوسطة': '#ffc107',
                    'عالية': '#fd7e14',
                    'عاجلة': '#dc3545'
                }[reminder.priority] || '#6c757d';

                return `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; background: #f8f9fa;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4 style="margin: 0; color: #333;">${reminder.type}</h4>
                            <span style="background: ${priorityColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${reminder.priority}</span>
                        </div>
                        <p style="margin: 5px 0;"><strong>السيارة:</strong> ${vehicleName}</p>
                        <p style="margin: 5px 0;"><strong>التاريخ:</strong> ${new Date(reminder.date).toLocaleDateString('ar-SA')}</p>
                        <p style="margin: 5px 0;"><strong>الملاحظات:</strong> ${reminder.notes || 'لا توجد ملاحظات'}</p>
                        <button onclick="markReminderComplete('${reminder.id}')"
                                style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            ✅ تم الإنجاز
                        </button>
                    </div>
                `;
            }).join('');

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">⏰ التذكيرات النشطة</h3>
                    ${remindersList}
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeRemindersModal()"
                                style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إغلاق</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closeRemindersModal = function() {
                document.body.removeChild(modal);
            };

            window.markReminderComplete = function(reminderId) {
                let reminders = JSON.parse(localStorage.getItem('maintenanceReminders') || '[]');
                const reminderIndex = reminders.findIndex(r => r.id === reminderId);
                if (reminderIndex !== -1) {
                    reminders[reminderIndex].status = 'مكتمل';
                    reminders[reminderIndex].completedAt = new Date().toISOString();
                    localStorage.setItem('maintenanceReminders', JSON.stringify(reminders));
                    alert('تم تحديث حالة التذكير إلى مكتمل');
                    closeRemindersModal();
                    showActiveReminders();
                }
            };
        }

        function viewMaintenanceHistory() {
            if (!maintenanceRecords || maintenanceRecords.length === 0) {
                alert('لا توجد سجلات صيانة');
                return;
            }

            const historyWindow = window.open('', '_blank', 'width=800,height=600');
            historyWindow.document.write(`
                <html>
                <head>
                    <title>تاريخ الصيانة</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <h2>تاريخ الصيانة الكامل</h2>
                    <table>
                        <tr>
                            <th>التاريخ</th>
                            <th>السيارة</th>
                            <th>نوع الصيانة</th>
                            <th>التكلفة</th>
                            <th>المركز</th>
                        </tr>
                        ${maintenanceRecords.map(record => {
                            const vehicle = vehicles.find(v => v.id === record.vehicleId);
                            return `
                                <tr>
                                    <td>${new Date(record.date).toLocaleDateString('ar-SA')}</td>
                                    <td>${vehicle ? `${vehicle.brand} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة محذوفة'}</td>
                                    <td>${record.type}</td>
                                    <td>${record.cost.toLocaleString()} ريال</td>
                                    <td>${record.center || '-'}</td>
                                </tr>
                            `;
                        }).join('')}
                    </table>
                </body>
                </html>
            `);
        }

        function exportMaintenanceReport() {
            if (!maintenanceRecords || maintenanceRecords.length === 0) {
                alert('لا توجد سجلات صيانة للتصدير. يرجى إضافة سجلات صيانة أولاً.');
                return;
            }

            const report = {
                title: 'تقرير الصيانة',
                generatedAt: new Date().toISOString(),
                totalRecords: maintenanceRecords.length,
                totalCost: maintenanceRecords.reduce((sum, r) => sum + r.cost, 0),
                records: maintenanceRecords
            };

            const dataStr = JSON.stringify(report, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `maintenance_report_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        function generateCostReport() {
            if (!costRecords || costRecords.length === 0) {
                alert('لا توجد سجلات تكاليف للعرض. يرجى إضافة سجلات تكاليف أولاً.');
                return;
            }

            const costsByType = {};
            costRecords.forEach(record => {
                if (!costsByType[record.type]) {
                    costsByType[record.type] = { count: 0, total: 0 };
                }
                costsByType[record.type].count++;
                costsByType[record.type].total += record.amount;
            });

            let reportHtml = '<h3>تقرير التكاليف حسب النوع</h3><ul>';
            Object.entries(costsByType).forEach(([type, data]) => {
                reportHtml += `<li><strong>${type}:</strong> ${data.count} سجل - ${data.total.toLocaleString()} ريال</li>`;
            });
            reportHtml += '</ul>';

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 2000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    ${reportHtml}
                    <button onclick="document.body.removeChild(this.closest('div').parentElement)" style="margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 8px; cursor: pointer;">إغلاق</button>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function exportCosts() {
            if (!costRecords || costRecords.length === 0) {
                alert('لا توجد سجلات تكاليف للتصدير. يرجى إضافة سجلات تكاليف أولاً.');
                return;
            }

            const report = {
                title: 'تقرير التكاليف',
                generatedAt: new Date().toISOString(),
                totalRecords: costRecords.length,
                totalAmount: costRecords.reduce((sum, r) => sum + r.amount, 0),
                records: costRecords
            };

            const dataStr = JSON.stringify(report, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `costs_report_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        function filterCostsByType() {
            if (!costRecords || costRecords.length === 0) {
                alert('لا توجد سجلات تكاليف للفلترة');
                return;
            }

            // إنشاء نافذة الفلترة
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            // الحصول على أنواع التكاليف المتاحة
            const costTypes = [...new Set(costRecords.map(record => record.type))];
            const vehicles = JSON.parse(localStorage.getItem('vehicles') || '[]');

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">🔍 فلترة التكاليف المتقدمة</h3>

                    <form id="filterForm">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">نوع التكلفة:</label>
                            <select id="filterCostType" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                                <option value="">جميع الأنواع</option>
                                ${costTypes.map(type => `<option value="${type}">${type}</option>`).join('')}
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">السيارة:</label>
                            <select id="filterVehicle" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                                <option value="">جميع السيارات</option>
                                ${vehicles.map(v => `<option value="${v.id}">${v.make} ${v.model} - ${v.plateNumber}</option>`).join('')}
                            </select>
                        </div>

                        <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">من تاريخ:</label>
                                <input type="date" id="filterFromDate"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">إلى تاريخ:</label>
                                <input type="date" id="filterToDate"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                            </div>
                        </div>

                        <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الحد الأدنى للمبلغ:</label>
                                <input type="number" id="filterMinAmount" placeholder="0.00" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الحد الأقصى للمبلغ:</label>
                                <input type="number" id="filterMaxAmount" placeholder="0.00" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" onclick="closeFilterModal()"
                                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="button" onclick="clearFilters()"
                                    style="padding: 10px 20px; background: #ffc107; color: #000; border: none; border-radius: 8px; cursor: pointer;">مسح الفلاتر</button>
                            <button type="submit"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">تطبيق الفلتر</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const filters = {
                    costType: document.getElementById('filterCostType').value,
                    vehicleId: document.getElementById('filterVehicle').value,
                    fromDate: document.getElementById('filterFromDate').value,
                    toDate: document.getElementById('filterToDate').value,
                    minAmount: parseFloat(document.getElementById('filterMinAmount').value) || 0,
                    maxAmount: parseFloat(document.getElementById('filterMaxAmount').value) || Infinity
                };

                applyFilters(filters);
                closeFilterModal();
            });

            window.closeFilterModal = function() {
                document.body.removeChild(modal);
            };

            window.clearFilters = function() {
                document.getElementById('filterCostType').value = '';
                document.getElementById('filterVehicle').value = '';
                document.getElementById('filterFromDate').value = '';
                document.getElementById('filterToDate').value = '';
                document.getElementById('filterMinAmount').value = '';
                document.getElementById('filterMaxAmount').value = '';
            };
        }

        function applyFilters(filters) {
            let filteredRecords = costRecords.filter(record => {
                // فلترة حسب نوع التكلفة
                if (filters.costType && record.type !== filters.costType) {
                    return false;
                }

                // فلترة حسب السيارة
                if (filters.vehicleId && record.vehicleId !== filters.vehicleId) {
                    return false;
                }

                // فلترة حسب التاريخ
                const recordDate = new Date(record.date);
                if (filters.fromDate && recordDate < new Date(filters.fromDate)) {
                    return false;
                }
                if (filters.toDate && recordDate > new Date(filters.toDate)) {
                    return false;
                }

                // فلترة حسب المبلغ
                if (record.amount < filters.minAmount || record.amount > filters.maxAmount) {
                    return false;
                }

                return true;
            });

            // عرض النتائج المفلترة
            showFilteredResults(filteredRecords, filters);
        }

        function showFilteredResults(filteredRecords, filters) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            if (filteredRecords.length === 0) {
                modal.innerHTML = `
                    <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 500px; text-align: center;">
                        <h3 style="margin-bottom: 20px; color: #333;">🔍 نتائج الفلترة</h3>
                        <div style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;">📭</div>
                        <p>لا توجد نتائج تطابق معايير البحث المحددة</p>
                        <button onclick="closeResultsModal()"
                                style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; margin-top: 20px;">إغلاق</button>
                    </div>
                `;
            } else {
                const totalAmount = filteredRecords.reduce((sum, record) => sum + record.amount, 0);
                const vehicles = JSON.parse(localStorage.getItem('vehicles') || '[]');

                const resultsList = filteredRecords.map(record => {
                    const vehicle = vehicles.find(v => v.id === record.vehicleId);
                    const vehicleName = vehicle ? `${vehicle.make} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة غير معروفة';

                    return `
                        <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; background: #f8f9fa;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <h4 style="margin: 0; color: #333;">${record.type}</h4>
                                <span style="background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: bold;">${record.amount} ريال</span>
                            </div>
                            <p style="margin: 5px 0; color: #666;"><strong>السيارة:</strong> ${vehicleName}</p>
                            <p style="margin: 5px 0; color: #666;"><strong>التاريخ:</strong> ${new Date(record.date).toLocaleDateString('ar-SA')}</p>
                            <p style="margin: 5px 0; color: #666;"><strong>المزود:</strong> ${record.provider}</p>
                            ${record.notes ? `<p style="margin: 5px 0; color: #666;"><strong>ملاحظات:</strong> ${record.notes}</p>` : ''}
                        </div>
                    `;
                }).join('');

                modal.innerHTML = `
                    <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 800px; max-height: 90vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px; color: #333;">🔍 نتائج الفلترة</h3>

                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p style="margin: 5px 0; font-weight: bold;">📊 ملخص النتائج:</p>
                            <p style="margin: 5px 0;">عدد السجلات: ${filteredRecords.length}</p>
                            <p style="margin: 5px 0;">إجمالي المبلغ: ${totalAmount.toFixed(2)} ريال</p>
                        </div>

                        ${resultsList}

                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="exportFilteredResults()"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer; margin-left: 10px;">📥 تصدير النتائج</button>
                            <button onclick="closeResultsModal()"
                                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إغلاق</button>
                        </div>
                    </div>
                `;
            }

            document.body.appendChild(modal);

            // حفظ النتائج المفلترة للتصدير
            window.currentFilteredResults = filteredRecords;

            window.closeResultsModal = function() {
                document.body.removeChild(modal);
            };

            window.exportFilteredResults = function() {
                if (filteredRecords.length === 0) {
                    alert('لا توجد بيانات للتصدير');
                    return;
                }

                const report = {
                    title: 'تقرير التكاليف المفلترة',
                    generatedAt: new Date().toISOString(),
                    filters: filters,
                    totalRecords: filteredRecords.length,
                    totalAmount: filteredRecords.reduce((sum, record) => sum + record.amount, 0),
                    records: filteredRecords
                };

                const dataStr = JSON.stringify(report, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `filtered_costs_report_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                alert('تم تصدير التقرير بنجاح!');
            };
        }

        // وظائف التبويبات الأخرى
        function openMaintenanceModal() {
            if (!vehicles || vehicles.length === 0) {
                alert('لا توجد سيارات مسجلة. يرجى إضافة سيارة أولاً.');
                return;
            }

            // إنشاء نافذة إضافة صيانة
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 500px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">🔧 إضافة صيانة جديدة</h3>

                    <form id="addMaintenanceForm">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اختر السيارة:</label>
                            <select id="maintenanceVehicleId" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="">اختر السيارة</option>
                                ${vehicles.map(v => `<option value="${v.id}">${v.make} ${v.model} - ${v.plateNumber}</option>`).join('')}
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">نوع الصيانة:</label>
                            <select id="maintenanceType" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="">اختر نوع الصيانة</option>
                                <option value="تغيير زيت">تغيير زيت</option>
                                <option value="فحص دوري">فحص دوري</option>
                                <option value="إصلاح">إصلاح</option>
                                <option value="تغيير إطارات">تغيير إطارات</option>
                                <option value="صيانة مكيف">صيانة مكيف</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">التكلفة:</label>
                            <input type="number" id="maintenanceCost" placeholder="0.00" step="0.01" min="0"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ الصيانة:</label>
                            <input type="date" id="maintenanceDate" value="${new Date().toISOString().split('T')[0]}"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الورشة/المزود:</label>
                            <input type="text" id="maintenanceProvider" placeholder="اسم الورشة أو المزود"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                            <textarea id="maintenanceNotes" rows="3" placeholder="تفاصيل إضافية..."
                                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" onclick="closeMaintenanceModal()"
                                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">إضافة الصيانة</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            document.getElementById('addMaintenanceForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newMaintenance = {
                    id: 'MAINT' + Date.now(),
                    vehicleId: document.getElementById('maintenanceVehicleId').value,
                    type: document.getElementById('maintenanceType').value,
                    cost: parseFloat(document.getElementById('maintenanceCost').value),
                    date: document.getElementById('maintenanceDate').value,
                    provider: document.getElementById('maintenanceProvider').value,
                    notes: document.getElementById('maintenanceNotes').value,
                    createdAt: new Date().toISOString()
                };

                maintenanceRecords.push(newMaintenance);
                saveVehicles();
                alert('تم إضافة سجل الصيانة بنجاح!');
                loadMaintenanceContent();
                updateStats();
                closeMaintenanceModal();
            });

            window.closeMaintenanceModal = function() {
                document.body.removeChild(modal);
            };
        }

        function addCostRecord() {
            if (!vehicles || vehicles.length === 0) {
                alert('لا توجد سيارات مسجلة. يرجى إضافة سيارة أولاً.');
                return;
            }

            // إنشاء نافذة إضافة تكلفة
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 500px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">💰 إضافة تكلفة جديدة</h3>

                    <form id="addCostForm">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اختر السيارة:</label>
                            <select id="costVehicleId" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="">اختر السيارة</option>
                                ${vehicles.map(v => `<option value="${v.id}">${v.make} ${v.model} - ${v.plateNumber}</option>`).join('')}
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">نوع التكلفة:</label>
                            <select id="costType" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="">اختر نوع التكلفة</option>
                                <option value="وقود">وقود</option>
                                <option value="تأمين">تأمين</option>
                                <option value="رسوم">رسوم حكومية</option>
                                <option value="غرامات">غرامات مرورية</option>
                                <option value="إيجار">إيجار</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">المبلغ:</label>
                            <input type="number" id="costAmount" placeholder="0.00" step="0.01" min="0"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ التكلفة:</label>
                            <input type="date" id="costDate" value="${new Date().toISOString().split('T')[0]}"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">المزود/الجهة:</label>
                            <input type="text" id="costProvider" placeholder="اسم المزود أو الجهة"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                            <textarea id="costNotes" rows="3" placeholder="تفاصيل إضافية..."
                                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" onclick="closeCostModal()"
                                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">إضافة التكلفة</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            document.getElementById('addCostForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newCost = {
                    id: 'COST' + Date.now(),
                    vehicleId: document.getElementById('costVehicleId').value,
                    type: document.getElementById('costType').value,
                    amount: parseFloat(document.getElementById('costAmount').value),
                    date: document.getElementById('costDate').value,
                    provider: document.getElementById('costProvider').value,
                    notes: document.getElementById('costNotes').value,
                    createdAt: new Date().toISOString()
                };

                costRecords.push(newCost);
                saveVehicles();
                alert('تم إضافة سجل التكلفة بنجاح!');
                loadCostsContent();
                updateStats();
                closeCostModal();
            });

            window.closeCostModal = function() {
                document.body.removeChild(modal);
            };
        }

        function addRentalContract() {
            if (!vehicles || vehicles.length === 0) {
                alert('لا توجد سيارات مسجلة. يرجى إضافة سيارة أولاً.');
                return;
            }

            // إنشاء نافذة إضافة عقد إيجار
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">🏠 إضافة عقد إيجار جديد</h3>

                    <form id="addRentalForm">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اختر السيارة:</label>
                            <select id="rentalVehicleId" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="">اختر السيارة</option>
                                ${vehicles.map(v => `<option value="${v.id}">${v.make} ${v.model} - ${v.plateNumber}</option>`).join('')}
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم المستأجر:</label>
                            <input type="text" id="renterName" placeholder="الاسم الكامل"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهوية/الإقامة:</label>
                            <input type="text" id="renterID" placeholder="رقم الهوية أو الإقامة"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الجوال:</label>
                            <input type="tel" id="renterPhone" placeholder="05xxxxxxxx"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ البداية:</label>
                                <input type="date" id="rentalStartDate" value="${new Date().toISOString().split('T')[0]}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ النهاية:</label>
                                <input type="date" id="rentalEndDate"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>
                        </div>

                        <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">قيمة الإيجار الشهري:</label>
                                <input type="number" id="monthlyRent" placeholder="0.00" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">مبلغ التأمين:</label>
                                <input type="number" id="securityDeposit" placeholder="0.00" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">شروط إضافية:</label>
                            <textarea id="rentalTerms" rows="3" placeholder="شروط وأحكام العقد..."
                                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" onclick="closeRentalModal()"
                                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">إضافة العقد</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // تعيين تاريخ نهاية افتراضي (بعد شهر)
            const nextMonth = new Date();
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            document.getElementById('rentalEndDate').value = nextMonth.toISOString().split('T')[0];

            // معالجة النموذج
            document.getElementById('addRentalForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newRental = {
                    id: 'RENT' + Date.now(),
                    vehicleId: document.getElementById('rentalVehicleId').value,
                    renterName: document.getElementById('renterName').value,
                    renterID: document.getElementById('renterID').value,
                    renterPhone: document.getElementById('renterPhone').value,
                    startDate: document.getElementById('rentalStartDate').value,
                    endDate: document.getElementById('rentalEndDate').value,
                    monthlyRent: parseFloat(document.getElementById('monthlyRent').value),
                    securityDeposit: parseFloat(document.getElementById('securityDeposit').value),
                    terms: document.getElementById('rentalTerms').value,
                    status: 'نشط',
                    createdAt: new Date().toISOString()
                };

                // حفظ العقد في localStorage
                let rentalContracts = JSON.parse(localStorage.getItem('rentalContracts') || '[]');
                rentalContracts.push(newRental);
                localStorage.setItem('rentalContracts', JSON.stringify(rentalContracts));

                alert('تم إضافة عقد الإيجار بنجاح!');
                closeRentalModal();
                loadRentalContent();
            });

            window.closeRentalModal = function() {
                document.body.removeChild(modal);
            };
        }

        function renewRentalContract() {
            const rentalContracts = JSON.parse(localStorage.getItem('rentalContracts') || '[]');
            const activeContracts = rentalContracts.filter(c => c.status === 'نشط');

            if (activeContracts.length === 0) {
                alert('لا توجد عقود إيجار نشطة للتجديد');
                return;
            }

            // إنشاء نافذة اختيار العقد للتجديد
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            const contractsList = activeContracts.map(contract => {
                const vehicle = vehicles.find(v => v.id === contract.vehicleId);
                const vehicleName = vehicle ? `${vehicle.make} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة غير معروفة';

                return `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; background: #f8f9fa;">
                        <h4 style="margin: 0 0 10px 0; color: #333;">${contract.renterName}</h4>
                        <p style="margin: 5px 0;"><strong>السيارة:</strong> ${vehicleName}</p>
                        <p style="margin: 5px 0;"><strong>الإيجار الشهري:</strong> ${contract.monthlyRent} ريال</p>
                        <p style="margin: 5px 0;"><strong>ينتهي في:</strong> ${new Date(contract.endDate).toLocaleDateString('ar-SA')}</p>
                        <button onclick="processRenewal('${contract.id}')"
                                style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            🔄 تجديد العقد
                        </button>
                    </div>
                `;
            }).join('');

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">🔄 تجديد عقود الإيجار</h3>
                    ${contractsList}
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeRenewalModal()"
                                style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إغلاق</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closeRenewalModal = function() {
                document.body.removeChild(modal);
            };

            window.processRenewal = function(contractId) {
                const contracts = JSON.parse(localStorage.getItem('rentalContracts') || '[]');
                const contractIndex = contracts.findIndex(c => c.id === contractId);

                if (contractIndex !== -1) {
                    const contract = contracts[contractIndex];
                    const newEndDate = new Date(contract.endDate);
                    newEndDate.setMonth(newEndDate.getMonth() + 1);

                    contracts[contractIndex].endDate = newEndDate.toISOString().split('T')[0];
                    contracts[contractIndex].renewedAt = new Date().toISOString();

                    localStorage.setItem('rentalContracts', JSON.stringify(contracts));
                    alert('تم تجديد العقد بنجاح لشهر إضافي!');
                    closeRenewalModal();
                }
            };
        }

        function viewRentalHistory() {
            const rentalContracts = JSON.parse(localStorage.getItem('rentalContracts') || '[]');

            if (rentalContracts.length === 0) {
                alert('لا توجد عقود إيجار مسجلة');
                return;
            }

            // إنشاء نافذة عرض تاريخ الإيجار
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            const contractsList = rentalContracts.map(contract => {
                const vehicle = vehicles.find(v => v.id === contract.vehicleId);
                const vehicleName = vehicle ? `${vehicle.make} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة غير معروفة';
                const statusColor = contract.status === 'نشط' ? '#28a745' : '#6c757d';

                return `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; background: #f8f9fa;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4 style="margin: 0; color: #333;">${contract.renterName}</h4>
                            <span style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${contract.status}</span>
                        </div>
                        <p style="margin: 5px 0;"><strong>السيارة:</strong> ${vehicleName}</p>
                        <p style="margin: 5px 0;"><strong>الفترة:</strong> ${new Date(contract.startDate).toLocaleDateString('ar-SA')} - ${new Date(contract.endDate).toLocaleDateString('ar-SA')}</p>
                        <p style="margin: 5px 0;"><strong>الإيجار الشهري:</strong> ${contract.monthlyRent} ريال</p>
                        <p style="margin: 5px 0;"><strong>التأمين:</strong> ${contract.securityDeposit} ريال</p>
                        <p style="margin: 5px 0;"><strong>الجوال:</strong> ${contract.renterPhone}</p>
                    </div>
                `;
            }).join('');

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 700px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">📋 تاريخ عقود الإيجار</h3>
                    ${contractsList}
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeHistoryModal()"
                                style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إغلاق</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closeHistoryModal = function() {
                document.body.removeChild(modal);
            };
        }
        function addInstallmentPlan() {
            if (!vehicles || vehicles.length === 0) {
                alert('لا توجد سيارات مسجلة. يرجى إضافة سيارة أولاً.');
                return;
            }

            // إنشاء نافذة إضافة خطة تقسيط
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">📊 إضافة خطة تقسيط جديدة</h3>

                    <form id="addInstallmentForm">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اختر السيارة:</label>
                            <select id="installmentVehicleId" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                <option value="">اختر السيارة</option>
                                ${vehicles.map(v => `<option value="${v.id}">${v.make} ${v.model} - ${v.plateNumber}</option>`).join('')}
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم العميل:</label>
                            <input type="text" id="customerName" placeholder="الاسم الكامل"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهوية:</label>
                            <input type="text" id="customerID" placeholder="رقم الهوية أو الإقامة"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">إجمالي المبلغ:</label>
                                <input type="number" id="totalAmount" placeholder="0.00" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">المقدم:</label>
                                <input type="number" id="downPayment" placeholder="0.00" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>
                        </div>

                        <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">عدد الأقساط:</label>
                                <select id="installmentCount" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                                    <option value="">اختر عدد الأقساط</option>
                                    <option value="6">6 أقساط</option>
                                    <option value="12">12 قسط</option>
                                    <option value="18">18 قسط</option>
                                    <option value="24">24 قسط</option>
                                    <option value="36">36 قسط</option>
                                </select>
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">قيمة القسط الشهري:</label>
                                <input type="number" id="monthlyInstallment" placeholder="0.00" step="0.01" min="0" readonly
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;">
                            </div>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ بداية الأقساط:</label>
                            <input type="date" id="startDate" value="${new Date().toISOString().split('T')[0]}"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                            <textarea id="installmentNotes" rows="3" placeholder="شروط وملاحظات إضافية..."
                                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" onclick="closeInstallmentModal()"
                                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">إضافة الخطة</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // حساب قيمة القسط الشهري تلقائياً
            function calculateMonthlyInstallment() {
                const total = parseFloat(document.getElementById('totalAmount').value) || 0;
                const downPayment = parseFloat(document.getElementById('downPayment').value) || 0;
                const count = parseInt(document.getElementById('installmentCount').value) || 1;

                const remaining = total - downPayment;
                const monthly = remaining / count;
                document.getElementById('monthlyInstallment').value = monthly.toFixed(2);
            }

            document.getElementById('totalAmount').addEventListener('input', calculateMonthlyInstallment);
            document.getElementById('downPayment').addEventListener('input', calculateMonthlyInstallment);
            document.getElementById('installmentCount').addEventListener('change', calculateMonthlyInstallment);

            // معالجة النموذج
            document.getElementById('addInstallmentForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newPlan = {
                    id: 'INST' + Date.now(),
                    vehicleId: document.getElementById('installmentVehicleId').value,
                    customerName: document.getElementById('customerName').value,
                    customerID: document.getElementById('customerID').value,
                    totalAmount: parseFloat(document.getElementById('totalAmount').value),
                    downPayment: parseFloat(document.getElementById('downPayment').value),
                    installmentCount: parseInt(document.getElementById('installmentCount').value),
                    monthlyInstallment: parseFloat(document.getElementById('monthlyInstallment').value),
                    startDate: document.getElementById('startDate').value,
                    notes: document.getElementById('installmentNotes').value,
                    status: 'نشط',
                    paidInstallments: 0,
                    createdAt: new Date().toISOString()
                };

                // حفظ الخطة في localStorage
                let installmentPlans = JSON.parse(localStorage.getItem('installmentPlans') || '[]');
                installmentPlans.push(newPlan);
                localStorage.setItem('installmentPlans', JSON.stringify(installmentPlans));

                alert('تم إضافة خطة التقسيط بنجاح!');
                closeInstallmentModal();
                loadInstallmentsContent();
            });

            window.closeInstallmentModal = function() {
                document.body.removeChild(modal);
            };
        }

        function payInstallment() {
            const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans') || '[]');
            const activePlans = installmentPlans.filter(p => p.status === 'نشط' && p.paidInstallments < p.installmentCount);

            if (activePlans.length === 0) {
                alert('لا توجد خطط تقسيط نشطة تحتاج لدفع أقساط');
                return;
            }

            // إنشاء نافذة دفع الأقساط
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            const plansList = activePlans.map(plan => {
                const vehicle = vehicles.find(v => v.id === plan.vehicleId);
                const vehicleName = vehicle ? `${vehicle.make} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة غير معروفة';
                const remaining = plan.installmentCount - plan.paidInstallments;

                return `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; background: #f8f9fa;">
                        <h4 style="margin: 0 0 10px 0; color: #333;">${plan.customerName}</h4>
                        <p style="margin: 5px 0;"><strong>السيارة:</strong> ${vehicleName}</p>
                        <p style="margin: 5px 0;"><strong>قيمة القسط:</strong> ${plan.monthlyInstallment} ريال</p>
                        <p style="margin: 5px 0;"><strong>الأقساط المدفوعة:</strong> ${plan.paidInstallments} من ${plan.installmentCount}</p>
                        <p style="margin: 5px 0;"><strong>الأقساط المتبقية:</strong> ${remaining}</p>
                        <button onclick="processPayment('${plan.id}')"
                                style="background: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            💰 دفع قسط (${plan.monthlyInstallment} ريال)
                        </button>
                    </div>
                `;
            }).join('');

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">💰 دفع الأقساط</h3>
                    ${plansList}
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closePaymentModal()"
                                style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إغلاق</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closePaymentModal = function() {
                document.body.removeChild(modal);
            };

            window.processPayment = function(planId) {
                let plans = JSON.parse(localStorage.getItem('installmentPlans') || '[]');
                const planIndex = plans.findIndex(p => p.id === planId);

                if (planIndex !== -1) {
                    plans[planIndex].paidInstallments += 1;

                    // إذا تم دفع جميع الأقساط، تغيير الحالة إلى مكتمل
                    if (plans[planIndex].paidInstallments >= plans[planIndex].installmentCount) {
                        plans[planIndex].status = 'مكتمل';
                        plans[planIndex].completedAt = new Date().toISOString();
                    }

                    // حفظ سجل الدفع
                    let payments = JSON.parse(localStorage.getItem('installmentPayments') || '[]');
                    payments.push({
                        id: 'PAY' + Date.now(),
                        planId: planId,
                        amount: plans[planIndex].monthlyInstallment,
                        paymentDate: new Date().toISOString(),
                        installmentNumber: plans[planIndex].paidInstallments
                    });

                    localStorage.setItem('installmentPlans', JSON.stringify(plans));
                    localStorage.setItem('installmentPayments', JSON.stringify(payments));

                    alert(`تم دفع القسط رقم ${plans[planIndex].paidInstallments} بنجاح!`);
                    closePaymentModal();
                    payInstallment(); // إعادة تحميل النافذة
                }
            };
        }

        function viewInstallmentSchedule() {
            const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans') || '[]');

            if (installmentPlans.length === 0) {
                alert('لا توجد خطط تقسيط مسجلة');
                return;
            }

            // إنشاء نافذة عرض جدول الأقساط
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            const plansList = installmentPlans.map(plan => {
                const vehicle = vehicles.find(v => v.id === plan.vehicleId);
                const vehicleName = vehicle ? `${vehicle.make} ${vehicle.model} - ${vehicle.plateNumber}` : 'سيارة غير معروفة';
                const statusColor = plan.status === 'نشط' ? '#28a745' : plan.status === 'مكتمل' ? '#007bff' : '#6c757d';
                const progress = (plan.paidInstallments / plan.installmentCount) * 100;

                return `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; background: #f8f9fa;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4 style="margin: 0; color: #333;">${plan.customerName}</h4>
                            <span style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${plan.status}</span>
                        </div>
                        <p style="margin: 5px 0;"><strong>السيارة:</strong> ${vehicleName}</p>
                        <p style="margin: 5px 0;"><strong>إجمالي المبلغ:</strong> ${plan.totalAmount} ريال</p>
                        <p style="margin: 5px 0;"><strong>المقدم:</strong> ${plan.downPayment} ريال</p>
                        <p style="margin: 5px 0;"><strong>قيمة القسط:</strong> ${plan.monthlyInstallment} ريال</p>
                        <p style="margin: 5px 0;"><strong>التقدم:</strong> ${plan.paidInstallments} من ${plan.installmentCount} أقساط</p>

                        <div style="background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; overflow: hidden;">
                            <div style="background: ${statusColor}; height: 100%; width: ${progress}%; transition: width 0.3s;"></div>
                        </div>

                        <p style="margin: 5px 0; font-size: 14px; color: #666;">
                            <strong>بدء الأقساط:</strong> ${new Date(plan.startDate).toLocaleDateString('ar-SA')}
                        </p>
                    </div>
                `;
            }).join('');

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 700px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">📊 جدول خطط التقسيط</h3>
                    ${plansList}
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeScheduleModal()"
                                style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إغلاق</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.closeScheduleModal = function() {
                document.body.removeChild(modal);
            };
        }
    </script>

    <!-- تطبيق خط SF Pro Arabic Display على جميع العناصر -->
    <style>
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, textarea, select, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }
    </style>
</body>
</html>
