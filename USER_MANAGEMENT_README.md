# 📋 دليل نظام إدارة المستخدمين والصلاحيات

## 🎯 نظرة عامة

تم تطوير نظام إدارة المستخدمين والصلاحيات المتقدم لبرنامج إدارة الشحنات ليوفر تحكماً شاملاً في صلاحيات المستخدمين وأمان النظام.

---

## 🔐 المستخدمين الافتراضيين

### 👨‍💼 مدير النظام
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `admin123`
- **الصلاحيات:** جميع الصلاحيات
- **الوصف:** صلاحيات كاملة لجميع أجزاء النظام

### 👩‍💼 المدير
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `manager123`
- **الصلاحيات:** صلاحيات إدارية محدودة
- **الوصف:** إدارة الشحنات والعملاء والتقارير

### 🚚 الموزع
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `distributor123`
- **الصلاحيات:** صلاحيات الموزعين والمناديب
- **الوصف:** إدارة الشحنات والعملاء والمدفوعات

### 👁️ المشاهد
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `viewer123`
- **الصلاحيات:** صلاحيات عرض فقط
- **الوصف:** عرض البيانات بدون تعديل

---

## 🏗️ هيكل الصلاحيات

### 📦 إدارة الشحنات
- `shipments_view` - عرض الشحنات
- `shipments_create` - إنشاء شحنة
- `shipments_edit` - تعديل الشحنات
- `shipments_delete` - حذف الشحنات
- `shipments_track` - تتبع الشحنات
- `shipments_print` - طباعة الشحنات

### 👥 إدارة العملاء
- `customers_view` - عرض العملاء
- `customers_create` - إضافة عملاء
- `customers_edit` - تعديل العملاء
- `customers_delete` - حذف العملاء
- `customers_export` - تصدير العملاء

### 💰 الإدارة المالية
- `financial_view` - عرض التقارير المالية
- `financial_payments` - إدارة المدفوعات
- `financial_invoices` - إدارة الفواتير
- `financial_commissions` - إدارة العمولات
- `financial_reports` - التقارير المالية

### 👤 إدارة المستخدمين
- `users_view` - عرض المستخدمين
- `users_create` - إضافة مستخدمين
- `users_edit` - تعديل المستخدمين
- `users_delete` - حذف المستخدمين
- `users_permissions` - إدارة الصلاحيات
- `users_roles` - إدارة الأدوار

### 📊 التقارير والإحصائيات
- `reports_view` - عرض التقارير
- `reports_create` - إنشاء التقارير
- `reports_export` - تصدير التقارير
- `reports_analytics` - التحليلات المتقدمة

### ⚙️ إدارة النظام
- `system_settings` - إعدادات النظام
- `system_backup` - النسخ الاحتياطي
- `system_logs` - سجلات النظام
- `system_maintenance` - صيانة النظام

---

## 🎭 الأدوار الافتراضية

### 🔴 مدير النظام (Admin)
- **الأولوية:** 1 (الأعلى)
- **الصلاحيات:** جميع الصلاحيات
- **الوصف:** تحكم كامل في النظام

### 🟠 مدير (Manager)
- **الأولوية:** 2
- **الصلاحيات:** إدارة الشحنات، العملاء، المالية، التقارير
- **الوصف:** إدارة العمليات اليومية

### 🟡 موظف (Employee)
- **الأولوية:** 3
- **الصلاحيات:** إنشاء وتعديل الشحنات والعملاء، عرض التقارير
- **الوصف:** تنفيذ المهام الأساسية

### 🟢 مشاهد (Viewer)
- **الأولوية:** 4 (الأدنى)
- **الصلاحيات:** عرض البيانات فقط
- **الوصف:** مراقبة ومتابعة

---

## 🛠️ الميزات المتقدمة

### 🔒 نظام المصادقة
- تسجيل دخول آمن بالبريد الإلكتروني وكلمة المرور
- جلسات محدودة الوقت (8 ساعات)
- خيار "تذكرني" للجلسات الطويلة
- تسجيل محاولات الدخول الفاشلة

### 🎯 إدارة الصلاحيات المتقدمة
- صلاحيات مبنية على الأدوار
- صلاحيات مخصصة للمستخدمين
- تطبيق الصلاحيات على واجهة المستخدم
- إخفاء/إظهار العناصر حسب الصلاحيات

### 📝 سجل النشاطات
- تسجيل جميع أنشطة المستخدمين
- تتبع تسجيل الدخول والخروج
- سجل العمليات المنفذة
- معلومات المتصفح وعنوان IP

### 🔄 إدارة الجلسات
- انتهاء صلاحية الجلسة تلقائياً
- تسجيل خروج آمن
- التحقق من صحة الجلسة
- حماية من الجلسات المنتهية

---

## 📱 واجهة المستخدم

### 🏠 صفحة تسجيل الدخول
- **الملف:** `login.html`
- **الميزات:**
  - تصميم عصري ومتجاوب
  - مستخدمين تجريبيين للاختبار
  - رسائل خطأ ونجاح واضحة
  - إظهار/إخفاء كلمة المرور

### 👥 صفحة إدارة المستخدمين
- **الملف:** `user-management.html`
- **الأقسام:**
  - إدارة المستخدمين
  - إدارة الأدوار والصلاحيات
  - إعدادات الصلاحيات العامة
  - سجل النشاطات

### 📊 الإحصائيات والتقارير
- إجمالي المستخدمين
- المستخدمين النشطين
- عدد المديرين
- المتصلين حالياً

---

## 🔧 الملفات التقنية

### 📄 الملفات الرئيسية
- `login.html` - صفحة تسجيل الدخول
- `user-management.html` - صفحة إدارة المستخدمين
- `clear-data.html` - أداة تنظيف البيانات
- `js/permissions.js` - نظام إدارة الصلاحيات
- `js/database.js` - قاعدة البيانات المحلية

### 🗃️ البيانات المحفوظة
- `users` - بيانات المستخدمين
- `roles` - الأدوار والصلاحيات
- `activityLog` - سجل النشاطات
- `currentUser` - المستخدم الحالي
- `sessionStart` - بداية الجلسة

---

## 🚀 كيفية الاستخدام

### 1️⃣ تسجيل الدخول
1. افتح `login.html`
2. استخدم أحد المستخدمين التجريبيين
3. أو أدخل بيانات الدخول يدوياً
4. اختر "تذكرني" للجلسات الطويلة

### 2️⃣ إدارة المستخدمين
1. انتقل إلى صفحة إدارة المستخدمين
2. اعرض قائمة المستخدمين الحاليين
3. أضف مستخدمين جدد أو عدل الموجودين
4. حدد الأدوار والصلاحيات

### 3️⃣ إدارة الأدوار
1. انتقل إلى تبويب "الأدوار والصلاحيات"
2. اعرض الأدوار الحالية
3. أضف أدوار جديدة أو عدل الموجودة
4. حدد الصلاحيات لكل دور

### 4️⃣ مراقبة النشاطات
1. انتقل إلى تبويب "سجل النشاطات"
2. اعرض آخر الأنشطة
3. راقب تسجيل الدخول والعمليات
4. صدر السجلات للمراجعة

---

## 🔒 الأمان والحماية

### 🛡️ ميزات الأمان
- تشفير كلمات المرور (محاكاة)
- جلسات محدودة الوقت
- تسجيل محاولات الدخول
- حماية من الوصول غير المصرح

### 🚨 التحقق من الصلاحيات
- فحص الصلاحيات قبل كل عملية
- إخفاء العناصر غير المصرح بها
- رسائل تحذير للوصول المرفوض
- تسجيل محاولات الوصول غير المصرح

### 📋 أفضل الممارسات
- تغيير كلمات المرور الافتراضية
- مراجعة الصلاحيات دورياً
- مراقبة سجل النشاطات
- تحديث الأدوار حسب الحاجة

---

## 🔄 التطوير والتحديث

### 📈 الميزات المستقبلية
- تشفير حقيقي لكلمات المرور
- مصادقة ثنائية العامل
- إدارة الجلسات المتقدمة
- تقارير أمان مفصلة

### 🛠️ التخصيص
- إضافة صلاحيات جديدة
- تعديل الأدوار الافتراضية
- تخصيص واجهة المستخدم
- إضافة ميزات أمان إضافية

---

## 📞 الدعم والمساعدة

### 🆘 في حالة المشاكل

#### 🔧 المشاكل الشائعة وحلولها:

**1. خطأ "قاعدة البيانات غير متاحة":**
- أعد تحميل الصفحة
- تأكد من تحميل ملف `js/database.js`
- استخدم أداة تنظيف البيانات (`clear-data.html`)

**2. عدم تطابق بيانات المستخدمين:**
- افتح `clear-data.html`
- اضغط "مسح بيانات المستخدمين فقط"
- أعد تسجيل الدخول

**3. مشاكل في الصلاحيات:**
- تأكد من تحميل ملف `js/permissions.js`
- امسح ذاكرة التخزين المحلي
- أعد تسجيل الدخول

**4. بيانات قديمة أو تالفة:**
- افتح `clear-data.html`
- اضغط "مسح جميع البيانات"
- سيتم إعادة تعيين النظام للحالة الافتراضية

#### 🛠️ خطوات حل المشاكل:
1. تحقق من صحة بيانات الدخول
2. استخدم أداة تنظيف البيانات
3. أعد تحميل الصفحة
4. تواصل مع مدير النظام

### 📧 معلومات الاتصال
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966501234567
- **ساعات العمل:** الأحد - الخميس، 8:00 ص - 5:00 م

---

## ✅ الخلاصة

تم تطوير نظام إدارة المستخدمين والصلاحيات ليوفر:

- **🔐 أمان متقدم** مع تحكم دقيق في الصلاحيات
- **👥 إدارة شاملة** للمستخدمين والأدوار
- **📊 مراقبة مستمرة** لأنشطة النظام
- **🎨 واجهة سهلة** ومتجاوبة
- **🔄 مرونة عالية** في التخصيص والتطوير

النظام جاهز للاستخدام ويمكن تطويره وتخصيصه حسب احتياجات المؤسسة.
