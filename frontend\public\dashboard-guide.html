<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل لوحة التحكم الرئيسية</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 1.2rem;
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border-right: 5px solid #667eea;
        }
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #667eea;
        }
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        .icon {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            font-weight: bold;
            transition: transform 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .stats-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-demo {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-demo .number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-demo .label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .troubleshooting {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .troubleshooting h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        .troubleshooting ul {
            color: #856404;
            line-height: 1.8;
        }
        .success-box {
            background: #d4edda;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .success-box h3 {
            color: #155724;
            margin-bottom: 15px;
        }
        .error-box {
            background: #f8d7da;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .error-box h3 {
            color: #721c24;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 دليل لوحة التحكم الرئيسية</h1>
            <p>شرح شامل لجميع مكونات وميزات لوحة التحكم</p>
        </div>

        <!-- نظرة عامة -->
        <div class="section">
            <h2>🎯 نظرة عامة</h2>
            <p>لوحة التحكم الرئيسية هي المركز الأساسي لإدارة نظام الشحنات. تعرض الإحصائيات المهمة وتوفر وصولاً سريعاً لجميع وظائف النظام.</p>
            
            <div class="stats-demo">
                <div class="stat-demo">
                    <div class="number">15</div>
                    <div class="label">📦 إجمالي الشحنات</div>
                </div>
                <div class="stat-demo">
                    <div class="number">8</div>
                    <div class="label">⏳ الشحنات المعلقة</div>
                </div>
                <div class="stat-demo">
                    <div class="number">6</div>
                    <div class="label">✅ الشحنات المسلمة</div>
                </div>
                <div class="stat-demo">
                    <div class="number">5</div>
                    <div class="label">👥 إجمالي العملاء</div>
                </div>
            </div>
        </div>

        <!-- المكونات الرئيسية -->
        <div class="section">
            <h2>🏗️ المكونات الرئيسية</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <span class="icon">📊</span>
                    <h3>بطاقات الإحصائيات</h3>
                    <p>تعرض الأرقام المهمة مثل عدد الشحنات الإجمالي، المعلقة، المسلمة، وعدد العملاء. تتحدث تلقائياً من قاعدة البيانات.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">⚡</span>
                    <h3>الشريط السريع</h3>
                    <p>يحتوي على أزرار للوصول السريع لجميع وظائف النظام مثل إضافة شحنة، إدارة العملاء، والتقارير.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">🔐</span>
                    <h3>إدارة المستخدمين والصلاحيات</h3>
                    <p>أزرار مخصصة لإدارة المستخدمين، تعديل الصلاحيات المتقدمة، ومصفوفة الصلاحيات الشاملة.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">🧪</span>
                    <h3>أدوات التشخيص</h3>
                    <p>أزرار لاختبار النظام، تشخيص المشاكل، ومسح البيانات عند الحاجة.</p>
                </div>
            </div>
        </div>

        <!-- الوظائف المتاحة -->
        <div class="section">
            <h2>🚀 الوظائف المتاحة</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <span class="icon">📦</span>
                    <h3>إدارة الشحنات</h3>
                    <p>إضافة، تعديل، حذف، وتتبع الشحنات. عرض تفاصيل كاملة لكل شحنة مع إمكانية الطباعة.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">👥</span>
                    <h3>إدارة العملاء</h3>
                    <p>إضافة عملاء جدد، تعديل بياناتهم، وعرض تاريخ الشحنات لكل عميل.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">👨‍💼</span>
                    <h3>إدارة المناديب</h3>
                    <p>إدارة المناديب والسائقين، تعيين المناطق، ومتابعة الأداء.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">🏢</span>
                    <h3>إدارة الفروع</h3>
                    <p>إنشاء فروع جديدة، إدارة التحويلات بين الفروع، ومتابعة المخزون.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">💰</span>
                    <h3>النظام المالي</h3>
                    <p>إدارة المدفوعات، التحصيل من الموزعين، ومتابعة الحسابات المالية.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">📊</span>
                    <h3>التقارير</h3>
                    <p>تقارير شاملة عن الشحنات، العملاء، الأرباح، والأداء العام للنظام.</p>
                </div>
            </div>
        </div>

        <!-- حل المشاكل -->
        <div class="section">
            <h2>🔧 حل المشاكل الشائعة</h2>
            
            <div class="error-box">
                <h3>❌ مشكلة: "قاعدة البيانات غير متاحة"</h3>
                <p><strong>الأسباب المحتملة:</strong></p>
                <ul>
                    <li>ملف قاعدة البيانات لم يتم تحميله بعد</li>
                    <li>خطأ في ملف JavaScript</li>
                    <li>بيانات تالفة في التخزين المحلي</li>
                </ul>
            </div>

            <div class="success-box">
                <h3>✅ الحلول:</h3>
                <ol>
                    <li><strong>إعادة تحميل الصفحة:</strong> اضغط F5 أو Ctrl+R</li>
                    <li><strong>مسح ذاكرة التخزين:</strong> اضغط Ctrl+Shift+R</li>
                    <li><strong>استخدام أداة التنظيف:</strong> افتح clear-data.html واضغط "مسح جميع البيانات"</li>
                    <li><strong>فحص وحدة التحكم:</strong> اضغط F12 وتحقق من الأخطاء</li>
                </ol>
            </div>

            <div class="troubleshooting">
                <h3>🛠️ خطوات التشخيص:</h3>
                <ol>
                    <li>افتح test-permissions.html للتحقق من حالة النظام</li>
                    <li>تحقق من وجود ملف js/database.js</li>
                    <li>راجع رسائل وحدة تحكم المتصفح</li>
                    <li>جرب فتح الصفحة في متصفح آخر</li>
                </ol>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div class="section">
            <h2>🔗 روابط سريعة</h2>
            <div style="text-align: center;">
                <a href="main-dashboard.html" class="btn">🏠 العودة للرئيسية</a>
                <a href="user-management.html" class="btn btn-secondary">👥 إدارة المستخدمين</a>
                <a href="user-permissions-advanced.html" class="btn btn-success">🔐 الصلاحيات المتقدمة</a>
                <a href="permissions-matrix.html" class="btn btn-warning">📊 مصفوفة الصلاحيات</a>
                <a href="test-permissions.html" class="btn btn-secondary">🧪 اختبار النظام</a>
                <a href="clear-data.html" class="btn" style="background: #e74c3c;">🗑️ مسح البيانات</a>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="section">
            <h2>ℹ️ معلومات إضافية</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <span class="icon">🔒</span>
                    <h3>الأمان</h3>
                    <p>النظام يستخدم التخزين المحلي للبيانات ويتطلب تسجيل دخول للوصول للوظائف المتقدمة.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">📱</span>
                    <h3>التوافق</h3>
                    <p>يعمل على جميع المتصفحات الحديثة والأجهزة المحمولة مع تصميم متجاوب.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">🔄</span>
                    <h3>التحديثات</h3>
                    <p>البيانات تتحدث تلقائياً عند إضافة أو تعديل الشحنات والعملاء.</p>
                </div>
                <div class="feature-card">
                    <span class="icon">💾</span>
                    <h3>النسخ الاحتياطية</h3>
                    <p>يمكن تصدير البيانات وإنشاء نسخ احتياطية من خلال أدوات النظام.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
