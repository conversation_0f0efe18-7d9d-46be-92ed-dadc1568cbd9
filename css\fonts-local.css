/* Apple SF Pro Arabic Fonts - Local Fallback */
/* خطوط آبل SF Pro العربية - بديل محلي */

/* استخدام خطوط النظام المحلية المشابهة لخطوط آبل */

:root {
    /* Arabic Fonts - أفضل الخطوط العربية المتاحة */
    --font-arabic-display: 
        'SF Pro Arabic Display',
        'SF Arabic', 
        'Segoe UI Historic',
        'Segoe UI',
        'Tahoma',
        'Microsoft Sans Serif',
        'Arial Unicode MS',
        'Lucida Grande',
        'DejaVu Sans',
        sans-serif;
    
    --font-arabic-text: 
        'SF Pro Arabic Text',
        'SF Arabic', 
        'Segoe UI',
        'Tahoma',
        'Microsoft Sans Serif',
        'Arial Unicode MS',
        'Lucida Grande',
        sans-serif;
    
    /* English Fonts - خطوط إنجليزية مشابهة لآبل */
    --font-english-display: 
        'SF Pro Display',
        'SF Pro Text',
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        sans-serif;
    
    --font-english-text: 
        'SF Pro Text',
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        sans-serif;
    
    /* Fallback Fonts */
    --font-fallback: 
        'Segoe UI',
        'Tahoma',
        'Arial',
        sans-serif;
    
    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-heavy: 800;
    
    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 1.75;
    
    /* Letter Spacing */
    --letter-spacing-tighter: -0.05em;
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    --letter-spacing-widest: 0.1em;
}

/* Base Font Styles */
body {
    font-family: var(--font-arabic-display);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
}

/* Arabic Text Styling */
[lang="ar"], 
[dir="rtl"], 
.arabic {
    font-family: var(--font-arabic-display);
    line-height: var(--line-height-relaxed);
    letter-spacing: var(--letter-spacing-wide);
    text-align: right;
    direction: rtl;
}

/* English Text Styling */
[lang="en"], 
[dir="ltr"], 
.english {
    font-family: var(--font-english-display);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-tight);
    text-align: left;
    direction: ltr;
}

/* Headings with Apple-style Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-arabic-display);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: inherit;
    margin: 0;
}

/* English Headings */
[lang="en"] h1, [lang="en"] h2, [lang="en"] h3, 
[lang="en"] h4, [lang="en"] h5, [lang="en"] h6,
.english h1, .english h2, .english h3,
.english h4, .english h5, .english h6 {
    font-family: var(--font-english-display);
    letter-spacing: var(--letter-spacing-tighter);
}

/* Specific Heading Sizes */
h1 {
    font-size: 2.25rem; /* 36px */
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
}

h2 {
    font-size: 1.875rem; /* 30px */
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
}

h3 {
    font-size: 1.5rem; /* 24px */
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-snug);
}

h4 {
    font-size: 1.25rem; /* 20px */
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-snug);
}

h5 {
    font-size: 1.125rem; /* 18px */
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
}

h6 {
    font-size: 1rem; /* 16px */
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
}

/* Paragraph and Text Styles */
p {
    font-family: var(--font-arabic-text);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-relaxed);
    margin: 0;
}

/* Button Typography */
button, .btn {
    font-family: var(--font-arabic-display);
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-normal);
}

/* Form Elements Typography */
input, textarea, select {
    font-family: var(--font-arabic-text);
    font-weight: var(--font-weight-regular);
}

/* Table Typography */
table {
    font-family: var(--font-arabic-text);
}

th {
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-wide);
}

/* Utility Classes */
.font-light { font-weight: var(--font-weight-light); }
.font-regular { font-weight: var(--font-weight-regular); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-heavy { font-weight: var(--font-weight-heavy); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

.tracking-tighter { letter-spacing: var(--letter-spacing-tighter); }
.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.tracking-wider { letter-spacing: var(--letter-spacing-wider); }
.tracking-widest { letter-spacing: var(--letter-spacing-widest); }

/* Special Typography Classes */
.display-font {
    font-family: var(--font-arabic-display);
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-tight);
}

.text-font {
    font-family: var(--font-arabic-text);
    font-weight: var(--font-weight-regular);
}

.number-font {
    font-family: var(--font-english-text);
    font-variant-numeric: tabular-nums;
    font-feature-settings: 'tnum' 1;
}

/* Responsive Typography */
@media (max-width: 768px) {
    h1 { font-size: 1.875rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.125rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.875rem; }
    
    body {
        font-size: 0.9375rem;
        line-height: var(--line-height-relaxed);
    }
}

@media (max-width: 480px) {
    h1 { font-size: 1.5rem; }
    h2 { font-size: 1.25rem; }
    h3 { font-size: 1.125rem; }
    
    body {
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    body {
        font-family: var(--font-fallback);
        color: black;
        background: white;
    }
    
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--font-fallback);
        color: black;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: subpixel-antialiased;
        -moz-osx-font-smoothing: auto;
    }
}
