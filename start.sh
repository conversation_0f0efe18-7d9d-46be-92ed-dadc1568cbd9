#!/bin/bash

echo ""
echo "========================================"
echo "    نظام إدارة الشحنات"
echo "    Shipment Management System"
echo "========================================"
echo ""

echo "جاري التحقق من المتطلبات..."
echo "Checking requirements..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت! يرجى تثبيت Node.js من https://nodejs.org/"
    echo "❌ Node.js is not installed! Please install from https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت!"
    echo "❌ npm is not installed!"
    exit 1
fi

echo "✅ Node.js و npm مثبتان"
echo "✅ Node.js and npm are installed"

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo ""
    echo "📦 تثبيت التبعيات الرئيسية..."
    echo "📦 Installing main dependencies..."
    npm install
fi

if [ ! -d "backend/node_modules" ]; then
    echo ""
    echo "📦 تثبيت تبعيات الخلفية..."
    echo "📦 Installing backend dependencies..."
    cd backend
    npm install
    cd ..
fi

if [ ! -d "frontend/node_modules" ]; then
    echo ""
    echo "📦 تثبيت تبعيات الواجهة الأمامية..."
    echo "📦 Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
fi

# Check if .env file exists
if [ ! -f "backend/.env" ]; then
    echo ""
    echo "⚠️  ملف .env غير موجود في مجلد backend"
    echo "⚠️  .env file not found in backend folder"
    echo ""
    echo "يرجى نسخ ملف .env.example إلى .env وتعديل البيانات:"
    echo "Please copy .env.example to .env and update the configuration:"
    echo ""
    echo "cd backend"
    echo "cp .env.example .env"
    echo ""
    echo "ثم تعديل DATABASE_URL و JWT_SECRET"
    echo "Then edit DATABASE_URL and JWT_SECRET"
    echo ""
    exit 1
fi

echo ""
echo "🚀 بدء تشغيل النظام..."
echo "🚀 Starting the system..."
echo ""
echo "الواجهة الأمامية: http://localhost:3000"
echo "Frontend: http://localhost:3000"
echo ""
echo "API الخلفي: http://localhost:3001"
echo "Backend API: http://localhost:3001"
echo ""
echo "توثيق API: http://localhost:3001/api-docs"
echo "API Documentation: http://localhost:3001/api-docs"
echo ""
echo "اضغط Ctrl+C لإيقاف النظام"
echo "Press Ctrl+C to stop the system"
echo ""

# Start both frontend and backend
npm run dev
