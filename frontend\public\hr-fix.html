<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح نظام الموارد البشرية</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .btn {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); opacity: 0.9; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: monospace; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح نظام الموارد البشرية</h1>
        <p>أدوات لإصلاح وتحسين وظائف الإضافة والتعديل</p>

        <div class="fix-section">
            <h3>🔗 روابط سريعة</h3>
            <a href="hr-management.html" class="btn btn-primary" target="_blank">🏢 نظام الموارد البشرية</a>
            <a href="hr-test.html" class="btn btn-warning" target="_blank">🧪 صفحة الاختبار</a>
            <a href="main-dashboard.html" class="btn btn-info">🏠 لوحة التحكم</a>
        </div>

        <div class="fix-section">
            <h3>🔧 إصلاحات سريعة</h3>
            <button class="btn btn-success" onclick="fixEmployeeData()">🔧 إصلاح بيانات الموظفين</button>
            <button class="btn btn-warning" onclick="resetEmployeeData()">🔄 إعادة تعيين البيانات</button>
            <button class="btn btn-info" onclick="addSampleEmployees()">👥 إضافة موظفين تجريبيين</button>
            <button class="btn btn-danger" onclick="clearAllData()">🗑️ مسح جميع البيانات</button>
            <div id="fixResults"></div>
        </div>

        <div class="fix-section">
            <h3>📊 حالة النظام</h3>
            <button class="btn btn-info" onclick="checkSystemStatus()">🔍 فحص حالة النظام</button>
            <button class="btn btn-primary" onclick="validateForms()">✅ التحقق من النماذج</button>
            <div id="statusResults"></div>
        </div>

        <div class="fix-section">
            <h3>📝 سجل الإصلاحات</h3>
            <button class="btn btn-info" onclick="clearLog()">🗑️ مسح السجل</button>
            <div id="fixLog" class="log"></div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        let logContainer = null;

        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('fixLog');
            log('✅ تم تحميل أدوات الإصلاح');
            checkSystemStatus();
        });

        function log(message) {
            if (logContainer) {
                const timestamp = new Date().toLocaleTimeString('ar-SA');
                logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            console.log(message);
        }

        function clearLog() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }

        function fixEmployeeData() {
            log('🔧 بدء إصلاح بيانات الموظفين...');
            
            try {
                let employees = JSON.parse(localStorage.getItem('employees') || '[]');
                let fixed = 0;

                employees.forEach(employee => {
                    // إصلاح البيانات المفقودة
                    if (!employee.id) {
                        employee.id = 'emp' + Date.now() + Math.random();
                        fixed++;
                    }
                    if (!employee.avatar) {
                        employee.avatar = employee.name ? employee.name.charAt(0).toUpperCase() : 'م';
                        fixed++;
                    }
                    if (!employee.status) {
                        employee.status = 'نشط';
                        fixed++;
                    }
                    if (!employee.createdAt) {
                        employee.createdAt = new Date().toISOString();
                        fixed++;
                    }
                    if (!employee.joinDate) {
                        employee.joinDate = new Date().toISOString().split('T')[0];
                        fixed++;
                    }
                });

                localStorage.setItem('employees', JSON.stringify(employees));
                
                showResult('fixResults', 'success', `✅ تم إصلاح ${fixed} حقل في بيانات الموظفين`);
                log(`✅ تم إصلاح ${fixed} حقل في بيانات الموظفين`);
            } catch (error) {
                showResult('fixResults', 'error', `❌ خطأ في الإصلاح: ${error.message}`);
                log(`❌ خطأ في الإصلاح: ${error.message}`);
            }
        }

        function resetEmployeeData() {
            log('🔄 إعادة تعيين بيانات الموظفين...');
            
            const defaultEmployees = [
                {
                    id: 'emp001',
                    name: 'أحمد محمد علي',
                    position: 'مطور برمجيات',
                    department: 'تقنية المعلومات',
                    phone: '0501234567',
                    email: '<EMAIL>',
                    salary: 8000,
                    status: 'نشط',
                    joinDate: '2023-01-15',
                    avatar: 'أ',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'emp002',
                    name: 'فاطمة سعد الدين',
                    position: 'محاسبة',
                    department: 'المحاسبة',
                    phone: '0507654321',
                    email: '<EMAIL>',
                    salary: 6500,
                    status: 'نشط',
                    joinDate: '2023-03-10',
                    avatar: 'ف',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'emp003',
                    name: 'محمد عبدالله',
                    position: 'مدير المبيعات',
                    department: 'المبيعات',
                    phone: '0509876543',
                    email: '<EMAIL>',
                    salary: 9500,
                    status: 'نشط',
                    joinDate: '2022-11-20',
                    avatar: 'م',
                    createdAt: new Date().toISOString()
                }
            ];

            localStorage.setItem('employees', JSON.stringify(defaultEmployees));
            
            showResult('fixResults', 'success', `✅ تم إعادة تعيين البيانات مع ${defaultEmployees.length} موظف افتراضي`);
            log(`✅ تم إعادة تعيين البيانات مع ${defaultEmployees.length} موظف افتراضي`);
        }

        function addSampleEmployees() {
            log('👥 إضافة موظفين تجريبيين...');
            
            try {
                let employees = JSON.parse(localStorage.getItem('employees') || '[]');
                
                const sampleEmployees = [
                    {
                        id: 'sample' + Date.now() + '1',
                        name: 'سارة أحمد الزهراني',
                        position: 'مصممة جرافيك',
                        department: 'التسويق',
                        phone: '0551234567',
                        email: '<EMAIL>',
                        salary: 5500,
                        status: 'نشط',
                        joinDate: new Date().toISOString().split('T')[0],
                        avatar: 'س',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'sample' + Date.now() + '2',
                        name: 'خالد عبدالرحمن',
                        position: 'مهندس شبكات',
                        department: 'تقنية المعلومات',
                        phone: '0557654321',
                        email: '<EMAIL>',
                        salary: 7500,
                        status: 'نشط',
                        joinDate: new Date().toISOString().split('T')[0],
                        avatar: 'خ',
                        createdAt: new Date().toISOString()
                    }
                ];

                employees.push(...sampleEmployees);
                localStorage.setItem('employees', JSON.stringify(employees));
                
                showResult('fixResults', 'success', `✅ تم إضافة ${sampleEmployees.length} موظف تجريبي`);
                log(`✅ تم إضافة ${sampleEmployees.length} موظف تجريبي`);
            } catch (error) {
                showResult('fixResults', 'error', `❌ خطأ في الإضافة: ${error.message}`);
                log(`❌ خطأ في الإضافة: ${error.message}`);
            }
        }

        function clearAllData() {
            log('🗑️ مسح جميع البيانات...');
            
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                localStorage.removeItem('employees');
                localStorage.removeItem('distributors');
                localStorage.removeItem('vehicles');
                localStorage.removeItem('departments');
                localStorage.removeItem('attendance');
                localStorage.removeItem('salaries');
                
                showResult('fixResults', 'success', '✅ تم مسح جميع البيانات');
                log('✅ تم مسح جميع البيانات');
            } else {
                log('❌ تم إلغاء عملية المسح');
            }
        }

        function checkSystemStatus() {
            log('🔍 فحص حالة النظام...');
            
            try {
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');
                const distributors = JSON.parse(localStorage.getItem('distributors') || '[]');
                const vehicles = JSON.parse(localStorage.getItem('vehicles') || '[]');
                
                let status = `
                    📊 <strong>إحصائيات النظام:</strong><br>
                    👥 الموظفين: ${employees.length}<br>
                    🚚 المناديب: ${distributors.length}<br>
                    🚗 السيارات: ${vehicles.length}<br><br>
                    
                    🔧 <strong>حالة البيانات:</strong><br>
                `;

                // فحص سلامة بيانات الموظفين
                let validEmployees = 0;
                employees.forEach(emp => {
                    if (emp.id && emp.name && emp.email) validEmployees++;
                });

                status += `✅ موظفين صالحين: ${validEmployees}/${employees.length}<br>`;
                
                if (employees.length > 0) {
                    status += `📧 آخر موظف: ${employees[employees.length - 1].name}<br>`;
                }

                showResult('statusResults', 'info', status);
                log(`📊 تم فحص النظام: ${employees.length} موظف، ${validEmployees} صالح`);
            } catch (error) {
                showResult('statusResults', 'error', `❌ خطأ في فحص النظام: ${error.message}`);
                log(`❌ خطأ في فحص النظام: ${error.message}`);
            }
        }

        function validateForms() {
            log('✅ التحقق من النماذج...');
            
            // فتح نافذة الموارد البشرية للتحقق
            const hrWindow = window.open('hr-management.html', 'hrValidation');
            
            setTimeout(() => {
                try {
                    let validation = '';
                    
                    // فحص نموذج الإضافة
                    const addModal = hrWindow.document.getElementById('addEmployeeModal');
                    const addForm = hrWindow.document.getElementById('addEmployeeForm');
                    
                    if (addModal && addForm) {
                        validation += '✅ نموذج الإضافة: موجود وجاهز<br>';
                    } else {
                        validation += '❌ نموذج الإضافة: غير موجود أو معطل<br>';
                    }
                    
                    // فحص نموذج التعديل
                    const editModal = hrWindow.document.getElementById('editEmployeeModal');
                    const editForm = hrWindow.document.getElementById('editEmployeeForm');
                    
                    if (editModal && editForm) {
                        validation += '✅ نموذج التعديل: موجود وجاهز<br>';
                    } else {
                        validation += '❌ نموذج التعديل: غير موجود أو معطل<br>';
                    }
                    
                    // فحص الوظائف
                    if (typeof hrWindow.addEmployee === 'function') {
                        validation += '✅ وظيفة addEmployee: متاحة<br>';
                    } else {
                        validation += '❌ وظيفة addEmployee: غير متاحة<br>';
                    }
                    
                    if (typeof hrWindow.editEmployee === 'function') {
                        validation += '✅ وظيفة editEmployee: متاحة<br>';
                    } else {
                        validation += '❌ وظيفة editEmployee: غير متاحة<br>';
                    }
                    
                    showResult('statusResults', 'info', validation);
                    log('✅ تم التحقق من النماذج والوظائف');
                    
                    // إغلاق النافذة بعد التحقق
                    setTimeout(() => hrWindow.close(), 1000);
                } catch (error) {
                    showResult('statusResults', 'error', `❌ خطأ في التحقق: ${error.message}`);
                    log(`❌ خطأ في التحقق: ${error.message}`);
                }
            }, 3000);
        }

        function showResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `<div class="status status-${type}">${message}</div>`;
            }
        }
    </script>
</body>
</html>
