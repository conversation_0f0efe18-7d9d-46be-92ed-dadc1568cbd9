# متغيرات البيئة لنظام إدارة الشحنات
# Environment Variables for Shipment Management System

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/shipment_management"
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="7d"

# API Configuration
API_PORT=3001
API_HOST="localhost"
CORS_ORIGIN="http://localhost:3000"

# Frontend Configuration
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"

# Currency Exchange API
EXCHANGE_API_KEY="your-exchange-api-key"
EXCHANGE_API_URL="https://api.exchangerate-api.com/v4/latest"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="نظام إدارة الشحنات"

# SMS Configuration (for notifications)
SMS_API_KEY="your-sms-api-key"
SMS_API_URL="https://api.sms-provider.com"

# Push Notifications
FIREBASE_PROJECT_ID="your-firebase-project"
FIREBASE_PRIVATE_KEY="your-firebase-private-key"
FIREBASE_CLIENT_EMAIL="your-firebase-client-email"

# File Upload
UPLOAD_MAX_SIZE="10MB"
ALLOWED_FILE_TYPES="jpg,jpeg,png,pdf,doc,docx"
UPLOAD_PATH="./uploads"

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW="15m"
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# Development
NODE_ENV="development"
DEBUG="shipment:*"

# Production
# NODE_ENV="production"
# REDIS_URL="redis://localhost:6379"
# SESSION_SECRET="your-session-secret"

# Mobile App Configuration
EXPO_PROJECT_ID="your-expo-project-id"
EXPO_ACCESS_TOKEN="your-expo-access-token"

# Analytics (optional)
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"

# Maps API (for tracking)
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *" # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH="./backups"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
HEALTH_CHECK_INTERVAL="30s"
