<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 إدارة صفحات الموقع</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header p {
            color: #6c757d;
            font-size: 1.1rem;
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .page-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .page-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .page-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
        }

        .page-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 1.3rem;
        }

        .page-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .page-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-draft {
            background: #fff3cd;
            color: #856404;
        }

        .page-description {
            color: #495057;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .page-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .pages-grid {
                grid-template-columns: 1fr;
            }

            .page-actions {
                justify-content: center;
            }

            .back-btn {
                position: static;
                margin-bottom: 20px;
                align-self: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                <span>🏠</span>
                <span>العودة للوحة التحكم</span>
                <span>←</span>
            </a>
            <h1>🌐 إدارة صفحات الموقع</h1>
            <p>إدارة وتحرير جميع صفحات الموقع الإلكتروني</p>
        </div>

        <!-- Statistics Section -->
        <div class="stats-section">
            <h2 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">📊 إحصائيات الصفحات</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">إجمالي الصفحات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">الصفحات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">0</div>
                    <div class="stat-label">المسودات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">معدل التفعيل</div>
                </div>
            </div>
        </div>

        <!-- Pages Grid -->
        <div class="pages-grid">
            <!-- الصفحة الرئيسية -->
            <div class="page-card">
                <div class="page-header">
                    <div class="page-icon">🏠</div>
                    <div class="page-info">
                        <h3>الصفحة الرئيسية</h3>
                        <p>index.html</p>
                    </div>
                </div>
                <span class="page-status status-active">نشط</span>
                <div class="page-description">
                    الصفحة الرئيسية للموقع تحتوي على معلومات الشركة والخدمات المقدمة مع نموذج تسجيل الدخول.
                </div>
                <div class="page-actions">
                    <a href="index.html" class="btn btn-primary" target="_blank">
                        <span>👁️</span> عرض الصفحة
                    </a>
                    <button class="btn btn-info" onclick="editPage('index')">
                        <span>✏️</span> تحرير
                    </button>
                </div>
            </div>

            <!-- صفحة من نحن -->
            <div class="page-card">
                <div class="page-header">
                    <div class="page-icon">ℹ️</div>
                    <div class="page-info">
                        <h3>من نحن</h3>
                        <p>about.html</p>
                    </div>
                </div>
                <span class="page-status status-active">نشط</span>
                <div class="page-description">
                    صفحة تعريفية بالشركة وتاريخها ورؤيتها ورسالتها وفريق العمل.
                </div>
                <div class="page-actions">
                    <a href="about.html" class="btn btn-primary" target="_blank">
                        <span>👁️</span> عرض الصفحة
                    </a>
                    <button class="btn btn-info" onclick="editPage('about')">
                        <span>✏️</span> تحرير
                    </button>
                </div>
            </div>

            <!-- صفحة الخدمات -->
            <div class="page-card">
                <div class="page-header">
                    <div class="page-icon">🛎️</div>
                    <div class="page-info">
                        <h3>خدماتنا</h3>
                        <p>services.html</p>
                    </div>
                </div>
                <span class="page-status status-active">نشط</span>
                <div class="page-description">
                    صفحة تعرض جميع الخدمات المقدمة من الشركة مع التفاصيل والأسعار.
                </div>
                <div class="page-actions">
                    <a href="services.html" class="btn btn-primary" target="_blank">
                        <span>👁️</span> عرض الصفحة
                    </a>
                    <button class="btn btn-info" onclick="editPage('services')">
                        <span>✏️</span> تحرير
                    </button>
                </div>
            </div>

            <!-- صفحة اتصل بنا -->
            <div class="page-card">
                <div class="page-header">
                    <div class="page-icon">📞</div>
                    <div class="page-info">
                        <h3>اتصل بنا</h3>
                        <p>contact.html</p>
                    </div>
                </div>
                <span class="page-status status-active">نشط</span>
                <div class="page-description">
                    صفحة معلومات الاتصال ونموذج التواصل مع الشركة.
                </div>
                <div class="page-actions">
                    <a href="contact.html" class="btn btn-primary" target="_blank">
                        <span>👁️</span> عرض الصفحة
                    </a>
                    <button class="btn btn-info" onclick="editPage('contact')">
                        <span>✏️</span> تحرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 تحميل صفحة إدارة صفحات الموقع...');
            initializePage();
        });

        // تهيئة الصفحة
        function initializePage() {
            // تطبيق خط SF Pro Arabic
            applyArabicFont();

            // تحديث الإحصائيات
            updateStatistics();

            console.log('✅ تم تحميل صفحة إدارة صفحات الموقع بنجاح');
        }

        // تطبيق الخط العربي
        function applyArabicFont() {
            const style = document.createElement('style');
            style.textContent = `
                * {
                    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
                    font-weight: 600 !important;
                }
            `;
            document.head.appendChild(style);
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalPages = document.querySelectorAll('.page-card').length;
            const activePages = document.querySelectorAll('.status-active').length;
            const draftPages = document.querySelectorAll('.status-draft').length;
            const activationRate = totalPages > 0 ? Math.round((activePages / totalPages) * 100) : 0;

            // تحديث الأرقام في الإحصائيات
            const statNumbers = document.querySelectorAll('.stat-number');
            if (statNumbers.length >= 4) {
                statNumbers[0].textContent = totalPages;
                statNumbers[1].textContent = activePages;
                statNumbers[2].textContent = draftPages;
                statNumbers[3].textContent = activationRate + '%';
            }
        }

        // تحرير صفحة
        function editPage(pageType) {
            const pageInfo = {
                'index': {
                    name: 'الصفحة الرئيسية',
                    file: 'index.html',
                    description: 'الصفحة الرئيسية للموقع'
                },
                'about': {
                    name: 'من نحن',
                    file: 'about.html',
                    description: 'صفحة تعريفية بالشركة'
                },
                'services': {
                    name: 'خدماتنا',
                    file: 'services.html',
                    description: 'صفحة الخدمات المقدمة'
                },
                'contact': {
                    name: 'اتصل بنا',
                    file: 'contact.html',
                    description: 'صفحة معلومات الاتصال'
                }
            };

            const page = pageInfo[pageType];
            if (!page) {
                alert('❌ صفحة غير موجودة');
                return;
            }

            alert('✏️ سيتم فتح محرر الصفحة قريباً\n\nالصفحة: ' + page.name + '\nالملف: ' + page.file);
            console.log(`✏️ تحرير صفحة: ${pageType}`);
        }

        console.log('🌐 تم تحميل نظام إدارة صفحات الموقع بنجاح!');
    </script>
</body>
</html>
