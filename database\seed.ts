// Database Seed Script
// سكريبت إدراج البيانات الأساسية

import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcrypt'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 بدء إدراج البيانات الأساسية...')
  console.log('🌱 Starting database seeding...')

  // إنشاء العملات الأساسية
  console.log('💱 إنشاء العملات...')
  const currencies = await Promise.all([
    prisma.currency.upsert({
      where: { code: 'USD' },
      update: {},
      create: {
        code: 'USD',
        name: 'US Dollar',
        symbol: '$',
        exchangeRate: 1.0,
        isBase: true,
        isActive: true,
      },
    }),
    prisma.currency.upsert({
      where: { code: 'SAR' },
      update: {},
      create: {
        code: 'SAR',
        name: 'Saudi Riyal',
        symbol: 'ر.س',
        exchangeRate: 3.75,
        isBase: false,
        isActive: true,
      },
    }),
    prisma.currency.upsert({
      where: { code: 'AED' },
      update: {},
      create: {
        code: 'AED',
        name: 'UAE Dirham',
        symbol: 'د.إ',
        exchangeRate: 3.67,
        isBase: false,
        isActive: true,
      },
    }),
    prisma.currency.upsert({
      where: { code: 'EUR' },
      update: {},
      create: {
        code: 'EUR',
        name: 'Euro',
        symbol: '€',
        exchangeRate: 0.85,
        isBase: false,
        isActive: true,
      },
    }),
  ])

  console.log(`✅ تم إنشاء ${currencies.length} عملة`)

  // إنشاء المستخدم الإداري
  console.log('👤 إنشاء المستخدم الإداري...')
  const adminPassword = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: adminPassword,
      firstName: 'مدير',
      lastName: 'النظام',
      phone: '+966501234567',
      role: 'ADMIN',
      language: 'ar',
      isActive: true,
    },
  })

  console.log('✅ تم إنشاء المستخدم الإداري')

  // إنشاء مستخدم مدير
  console.log('👤 إنشاء مستخدم مدير...')
  const managerPassword = await bcrypt.hash('manager123', 12)
  
  const managerUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: managerPassword,
      firstName: 'أحمد',
      lastName: 'المدير',
      phone: '+966502345678',
      role: 'MANAGER',
      language: 'ar',
      isActive: true,
    },
  })

  console.log('✅ تم إنشاء مستخدم المدير')

  // إنشاء موزع تجريبي
  console.log('🚚 إنشاء موزع تجريبي...')
  const distributorPassword = await bcrypt.hash('distributor123', 12)
  
  const distributorUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      passwordHash: distributorPassword,
      firstName: 'محمد',
      lastName: 'الموزع',
      phone: '+966503456789',
      role: 'DISTRIBUTOR',
      language: 'ar',
      isActive: true,
    },
  })

  const distributor = await prisma.distributor.create({
    data: {
      userId: distributorUser.id,
      name: 'محمد الموزع',
      email: '<EMAIL>',
      phone: '+966503456789',
      vehicleType: 'شاحنة صغيرة',
      vehicleNumber: 'ABC-123',
      licenseNumber: 'DL123456',
      area: 'الرياض',
      rating: 4.5,
      totalDeliveries: 0,
      isAvailable: true,
    },
  })

  console.log('✅ تم إنشاء الموزع التجريبي')

  // إنشاء عملاء تجريبيين
  console.log('👥 إنشاء عملاء تجريبيين...')
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'شركة التجارة المتقدمة',
        email: '<EMAIL>',
        phone: '+966504567890',
        address: 'شارع الملك فهد، حي العليا',
        city: 'الرياض',
        country: 'المملكة العربية السعودية',
        postalCode: '12345',
        createdBy: adminUser.id,
      },
    }),
    prisma.customer.create({
      data: {
        name: 'مؤسسة الخليج للتوريدات',
        email: '<EMAIL>',
        phone: '+971505678901',
        address: 'شارع الشيخ زايد، دبي',
        city: 'دبي',
        country: 'الإمارات العربية المتحدة',
        postalCode: '54321',
        createdBy: adminUser.id,
      },
    }),
    prisma.customer.create({
      data: {
        name: 'أحمد محمد العلي',
        email: '<EMAIL>',
        phone: '+966506789012',
        address: 'حي النخيل، شارع الأمير سلطان',
        city: 'جدة',
        country: 'المملكة العربية السعودية',
        postalCode: '67890',
        createdBy: managerUser.id,
      },
    }),
  ])

  console.log(`✅ تم إنشاء ${customers.length} عميل`)

  // إنشاء شحنات تجريبية
  console.log('📦 إنشاء شحنات تجريبية...')
  const usdCurrency = currencies.find(c => c.code === 'USD')!
  const sarCurrency = currencies.find(c => c.code === 'SAR')!

  const shipments = await Promise.all([
    prisma.shipment.create({
      data: {
        trackingNumber: 'SHP001',
        senderId: customers[0].id,
        receiverId: customers[1].id,
        distributorId: distributor.id,
        weight: 5.5,
        length: 30,
        width: 20,
        height: 15,
        contents: 'قطع غيار إلكترونية',
        pickupAddress: customers[0].address!,
        pickupCity: customers[0].city!,
        pickupCountry: customers[0].country!,
        deliveryAddress: customers[1].address!,
        deliveryCity: customers[1].city!,
        deliveryCountry: customers[1].country!,
        estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        cost: 150.00,
        currencyId: sarCurrency.id,
        status: 'IN_TRANSIT',
        notes: 'شحنة عاجلة - يرجى التعامل بحذر',
        createdBy: managerUser.id,
      },
    }),
    prisma.shipment.create({
      data: {
        trackingNumber: 'SHP002',
        senderId: customers[1].id,
        receiverId: customers[2].id,
        weight: 2.0,
        length: 25,
        width: 15,
        height: 10,
        contents: 'مستندات مهمة',
        pickupAddress: customers[1].address!,
        pickupCity: customers[1].city!,
        pickupCountry: customers[1].country!,
        deliveryAddress: customers[2].address!,
        deliveryCity: customers[2].city!,
        deliveryCountry: customers[2].country!,
        estimatedDelivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        cost: 75.00,
        currencyId: sarCurrency.id,
        status: 'PENDING',
        specialInstructions: 'تسليم يدوي فقط',
        createdBy: adminUser.id,
      },
    }),
  ])

  console.log(`✅ تم إنشاء ${shipments.length} شحنة`)

  // إنشاء تتبع للشحنات
  console.log('📍 إنشاء بيانات التتبع...')
  await prisma.shipmentTracking.createMany({
    data: [
      {
        shipmentId: shipments[0].id,
        status: 'PICKED_UP',
        location: 'مركز التوزيع - الرياض',
        latitude: 24.7136,
        longitude: 46.6753,
        notes: 'تم استلام الشحنة من المرسل',
        createdBy: distributorUser.id,
      },
      {
        shipmentId: shipments[0].id,
        status: 'IN_TRANSIT',
        location: 'في الطريق إلى دبي',
        latitude: 25.2048,
        longitude: 55.2708,
        notes: 'الشحنة في الطريق',
        createdBy: distributorUser.id,
      },
    ],
  })

  console.log('✅ تم إنشاء بيانات التتبع')

  // إنشاء إشعارات تجريبية
  console.log('🔔 إنشاء إشعارات تجريبية...')
  await prisma.notification.createMany({
    data: [
      {
        userId: adminUser.id,
        title: 'مرحباً بك في نظام إدارة الشحنات',
        message: 'تم إعداد النظام بنجاح وهو جاهز للاستخدام',
        type: 'SUCCESS',
        isRead: false,
      },
      {
        userId: managerUser.id,
        title: 'شحنة جديدة تحتاج موافقة',
        message: 'يوجد شحنة جديدة تحتاج إلى موافقتك',
        type: 'INFO',
        isRead: false,
        shipmentId: shipments[1].id,
      },
      {
        userId: distributorUser.id,
        title: 'مهمة توصيل جديدة',
        message: 'تم تعيين شحنة جديدة لك للتوصيل',
        type: 'INFO',
        isRead: false,
        shipmentId: shipments[0].id,
      },
    ],
  })

  console.log('✅ تم إنشاء الإشعارات')

  console.log('🎉 تم إكمال إدراج البيانات الأساسية بنجاح!')
  console.log('🎉 Database seeding completed successfully!')
  
  console.log('\n📊 ملخص البيانات المُدرجة:')
  console.log(`- العملات: ${currencies.length}`)
  console.log(`- المستخدمين: 3`)
  console.log(`- الموزعين: 1`)
  console.log(`- العملاء: ${customers.length}`)
  console.log(`- الشحنات: ${shipments.length}`)
  console.log(`- بيانات التتبع: 2`)
  console.log(`- الإشعارات: 3`)

  console.log('\n🔐 بيانات تسجيل الدخول:')
  console.log('المدير: <EMAIL> / admin123')
  console.log('المدير: <EMAIL> / manager123')
  console.log('الموزع: <EMAIL> / distributor123')
}

main()
  .catch((e) => {
    console.error('❌ خطأ في إدراج البيانات:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
