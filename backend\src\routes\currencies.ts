// Currency Routes
// مسارات العملات

import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { asyncHandler, AppError } from '../middleware/errorHandler'
import { authenticate, requireAdmin } from '../middleware/auth'
import { config } from '../config/config'
import axios from 'axios'

const router = Router()
const prisma = new PrismaClient()

// Validation schemas
const createCurrencySchema = z.object({
  code: z.string().length(3, 'Currency code must be 3 characters').toUpperCase(),
  name: z.string().min(1, 'Name is required'),
  symbol: z.string().min(1, 'Symbol is required'),
  exchangeRate: z.number().positive('Exchange rate must be positive').default(1.0),
  isBase: z.boolean().default(false),
  isActive: z.boolean().default(true),
})

const updateCurrencySchema = createCurrencySchema.partial()

/**
 * @swagger
 * /api/currencies:
 *   get:
 *     summary: Get currencies list
 *     tags: [Currencies]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Currencies retrieved successfully
 */
router.get('/', authenticate, asyncHandler(async (req, res) => {
  const activeOnly = req.query.active === 'true'
  
  const where: any = {}
  if (activeOnly) {
    where.isActive = true
  }

  const currencies = await prisma.currency.findMany({
    where,
    orderBy: [
      { isBase: 'desc' },
      { code: 'asc' },
    ],
  })

  res.json({
    success: true,
    data: { currencies },
  })
}))

/**
 * @swagger
 * /api/currencies:
 *   post:
 *     summary: Create new currency
 *     tags: [Currencies]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: Currency created successfully
 */
router.post('/', authenticate, requireAdmin, asyncHandler(async (req, res) => {
  const currencyData = createCurrencySchema.parse(req.body)
  
  // If setting as base currency, unset other base currencies
  if (currencyData.isBase) {
    await prisma.currency.updateMany({
      where: { isBase: true },
      data: { isBase: false },
    })
  }
  
  const currency = await prisma.currency.create({
    data: currencyData,
  })
  
  res.status(201).json({
    success: true,
    data: { currency },
    message: 'Currency created successfully',
  })
}))

/**
 * @swagger
 * /api/currencies/update-rates:
 *   post:
 *     summary: Update exchange rates
 *     tags: [Currencies]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Exchange rates updated successfully
 */
router.post('/update-rates', authenticate, requireAdmin, asyncHandler(async (req, res) => {
  if (!config.exchange.apiKey) {
    throw new AppError('Exchange API key not configured', 500, 'EXCHANGE_API_NOT_CONFIGURED')
  }
  
  try {
    // Get base currency
    const baseCurrency = await prisma.currency.findFirst({
      where: { isBase: true },
    })
    
    if (!baseCurrency) {
      throw new AppError('No base currency configured', 400, 'NO_BASE_CURRENCY')
    }
    
    // Fetch exchange rates
    const response = await axios.get(`${config.exchange.apiUrl}/${baseCurrency.code}`, {
      params: {
        access_key: config.exchange.apiKey,
      },
      timeout: 10000,
    })
    
    if (!response.data || !response.data.rates) {
      throw new AppError('Invalid response from exchange API', 500, 'INVALID_EXCHANGE_RESPONSE')
    }
    
    const rates = response.data.rates
    const updatedCurrencies = []
    
    // Update exchange rates for all currencies
    const currencies = await prisma.currency.findMany({
      where: { isActive: true },
    })
    
    for (const currency of currencies) {
      if (currency.isBase) {
        // Base currency rate is always 1.0
        if (currency.exchangeRate !== 1.0) {
          await prisma.currency.update({
            where: { id: currency.id },
            data: { exchangeRate: 1.0 },
          })
          updatedCurrencies.push({ ...currency, exchangeRate: 1.0 })
        }
      } else if (rates[currency.code]) {
        const newRate = parseFloat(rates[currency.code])
        if (newRate && newRate !== currency.exchangeRate) {
          await prisma.currency.update({
            where: { id: currency.id },
            data: { exchangeRate: newRate },
          })
          updatedCurrencies.push({ ...currency, exchangeRate: newRate })
        }
      }
    }
    
    res.json({
      success: true,
      data: {
        updatedCurrencies,
        baseCurrency: baseCurrency.code,
        updatedAt: new Date().toISOString(),
      },
      message: `Updated ${updatedCurrencies.length} exchange rates`,
    })
    
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new AppError(
        'Failed to fetch exchange rates',
        500,
        'EXCHANGE_API_ERROR'
      )
    }
    throw error
  }
}))

/**
 * @swagger
 * /api/currencies/{id}:
 *   put:
 *     summary: Update currency
 *     tags: [Currencies]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Currency updated successfully
 */
router.put('/:id', authenticate, requireAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params
  const updateData = updateCurrencySchema.parse(req.body)
  
  // If setting as base currency, unset other base currencies
  if (updateData.isBase) {
    await prisma.currency.updateMany({
      where: { 
        isBase: true,
        id: { not: id },
      },
      data: { isBase: false },
    })
  }
  
  const currency = await prisma.currency.update({
    where: { id },
    data: updateData,
  })
  
  res.json({
    success: true,
    data: { currency },
    message: 'Currency updated successfully',
  })
}))

/**
 * @swagger
 * /api/currencies/convert:
 *   post:
 *     summary: Convert amount between currencies
 *     tags: [Currencies]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - fromCurrency
 *               - toCurrency
 *             properties:
 *               amount:
 *                 type: number
 *               fromCurrency:
 *                 type: string
 *               toCurrency:
 *                 type: string
 *     responses:
 *       200:
 *         description: Amount converted successfully
 */
router.post('/convert', authenticate, asyncHandler(async (req, res) => {
  const { amount, fromCurrency, toCurrency } = z.object({
    amount: z.number().positive('Amount must be positive'),
    fromCurrency: z.string().length(3, 'Invalid currency code'),
    toCurrency: z.string().length(3, 'Invalid currency code'),
  }).parse(req.body)
  
  if (fromCurrency === toCurrency) {
    return res.json({
      success: true,
      data: {
        originalAmount: amount,
        convertedAmount: amount,
        fromCurrency,
        toCurrency,
        exchangeRate: 1.0,
      },
    })
  }
  
  const [fromCurrencyData, toCurrencyData] = await Promise.all([
    prisma.currency.findUnique({ where: { code: fromCurrency } }),
    prisma.currency.findUnique({ where: { code: toCurrency } }),
  ])
  
  if (!fromCurrencyData) {
    throw new AppError(`Currency ${fromCurrency} not found`, 404, 'FROM_CURRENCY_NOT_FOUND')
  }
  
  if (!toCurrencyData) {
    throw new AppError(`Currency ${toCurrency} not found`, 404, 'TO_CURRENCY_NOT_FOUND')
  }
  
  // Convert to base currency first, then to target currency
  const baseAmount = amount / fromCurrencyData.exchangeRate
  const convertedAmount = baseAmount * toCurrencyData.exchangeRate
  const exchangeRate = toCurrencyData.exchangeRate / fromCurrencyData.exchangeRate
  
  res.json({
    success: true,
    data: {
      originalAmount: amount,
      convertedAmount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
      fromCurrency,
      toCurrency,
      exchangeRate: Math.round(exchangeRate * 10000) / 10000, // Round to 4 decimal places
    },
  })
}))

export default router
