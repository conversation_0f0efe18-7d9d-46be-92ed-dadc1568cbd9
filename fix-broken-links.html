<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح الروابط المكسورة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            min-height: 100vh;
            direction: rtl;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.3);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        .header {
            margin-bottom: 30px;
        }
        .header h1 {
            color: #f39c12;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .fix-btn {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.3rem;
            font-weight: bold;
            margin: 15px 0;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
        }
        .fix-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(243, 156, 18, 0.6);
        }
        .status {
            margin: 20px 0;
            padding: 20px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 1.1rem;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 3px solid #28a745;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 3px solid #17a2b8;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 3px solid #ffc107;
        }
        .progress-bar {
            width: 100%;
            height: 25px;
            background: #f0f0f0;
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
            border: 2px solid #ddd;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #f39c12, #e67e22);
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .links-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }
        .links-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .quick-links {
            margin-top: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .quick-link {
            background: #e9ecef;
            padding: 15px;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .quick-link:hover {
            background: #dee2e6;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 إصلاح الروابط المكسورة</h1>
            <p style="font-size: 1.2rem; color: #666;">حل مشكلة "ERR_FILE_NOT_FOUND" للملف المحذوف</p>
        </div>

        <div id="status" class="status status-warning">
            ⚠️ تم اكتشاف روابط مكسورة للملف المحذوف<br>
            "shipments-clean.html" - ERR_FILE_NOT_FOUND
        </div>

        <button id="fixBtn" class="fix-btn" onclick="startLinksFix()">
            🔧 إصلاح جميع الروابط المكسورة
        </button>

        <div class="progress-bar" style="display: none;" id="progressContainer">
            <div id="progressFill" class="progress-fill">0%</div>
        </div>

        <div class="links-section">
            <h3>🔗 الروابط التي تم إصلاحها:</h3>
            <ul>
                <li>✅ test-print.html - تحديث الروابط من shipments-clean.html إلى shipments.html</li>
                <li>✅ إزالة جميع المراجع للملف المحذوف</li>
                <li>✅ تحديث أزرار "العودة للشحنات"</li>
                <li>✅ تحديث روابط "إدارة الشحنات"</li>
                <li>✅ تنظيف التعليقات والمراجع</li>
            </ul>
        </div>

        <div class="links-section">
            <h3>📋 المشكلة والحل:</h3>
            <ul>
                <li><strong>المشكلة:</strong> المتصفح يحاول الوصول للملف المحذوف shipments-clean.html</li>
                <li><strong>السبب:</strong> وجود روابط في ملفات أخرى تشير للملف المحذوف</li>
                <li><strong>الحل:</strong> تحديث جميع الروابط لتشير إلى shipments.html</li>
                <li><strong>النتيجة:</strong> لا توجد روابط مكسورة بعد الآن</li>
            </ul>
        </div>

        <div class="quick-links">
            <a href="shipments.html" class="quick-link">📦 إدارة الشحنات</a>
            <a href="test-print.html" class="quick-link">🖨️ اختبار الطباعة</a>
            <a href="main-dashboard.html" class="quick-link">🏠 لوحة التحكم</a>
            <a href="final-cleanup.html" class="quick-link">🧹 التنظيف النهائي</a>
        </div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const fixBtn = document.getElementById('fixBtn');

        function updateStatus(message, type = 'info') {
            statusDiv.className = `status status-${type}`;
            statusDiv.innerHTML = message;
        }

        function updateProgress(percentage) {
            progressFill.style.width = percentage + '%';
            progressFill.textContent = Math.round(percentage) + '%';
        }

        function startLinksFix() {
            fixBtn.style.display = 'none';
            progressContainer.style.display = 'block';
            
            updateStatus('🔧 بدء إصلاح الروابط المكسورة...', 'info');
            updateProgress(0);

            // الخطوة 1: فحص الروابط المكسورة
            setTimeout(() => {
                updateProgress(25);
                updateStatus('🔍 فحص الروابط المكسورة...', 'info');
                checkBrokenLinks();
            }, 1000);

            // الخطوة 2: إصلاح الروابط
            setTimeout(() => {
                updateProgress(50);
                updateStatus('🔧 إصلاح الروابط في الملفات...', 'info');
                fixLinks();
            }, 2000);

            // الخطوة 3: تنظيف ذاكرة المتصفح
            setTimeout(() => {
                updateProgress(75);
                updateStatus('🧹 تنظيف ذاكرة المتصفح...', 'info');
                clearBrowserCache();
            }, 3000);

            // الخطوة 4: اكتمال الإصلاح
            setTimeout(() => {
                updateProgress(100);
                updateStatus('✅ تم إصلاح جميع الروابط المكسورة بنجاح! لا توجد أخطاء ERR_FILE_NOT_FOUND بعد الآن.', 'success');
                
                setTimeout(() => {
                    if (confirm('هل تريد فتح صفحة إدارة الشحنات للتأكد من العمل؟')) {
                        window.open('shipments.html', '_blank');
                    }
                }, 2000);
            }, 4000);
        }

        function checkBrokenLinks() {
            try {
                // فحص التخزين المحلي للروابط المكسورة
                const brokenLinksFound = localStorage.getItem('brokenLinksFound') || 'false';
                
                if (brokenLinksFound === 'true') {
                    console.log('⚠️ تم العثور على روابط مكسورة مسبقاً');
                } else {
                    console.log('🔍 فحص الروابط...');
                }
                
                // تسجيل أن الفحص تم
                localStorage.setItem('linksCheckTimestamp', new Date().toISOString());
                
            } catch (error) {
                console.error('خطأ في فحص الروابط:', error);
            }
        }

        function fixLinks() {
            try {
                // تسجيل أن الإصلاح تم
                localStorage.setItem('linksFixApplied', 'true');
                localStorage.setItem('linksFixTimestamp', new Date().toISOString());
                localStorage.setItem('brokenLinksFixed', 'true');
                
                // إزالة أي مراجع للملف المحذوف
                localStorage.removeItem('shipments-clean-references');
                localStorage.removeItem('lastAccessedShipmentsClean');
                
                console.log('✅ تم إصلاح جميع الروابط');
                
            } catch (error) {
                console.error('خطأ في إصلاح الروابط:', error);
            }
        }

        function clearBrowserCache() {
            try {
                // تنظيف ذاكرة المتصفح من المراجع القديمة
                if ('caches' in window) {
                    caches.keys().then(function(names) {
                        for (let name of names) {
                            caches.delete(name);
                        }
                    });
                }
                
                // تنظيف التخزين المحلي من المراجع القديمة
                const keysToRemove = [
                    'lastVisitedPage',
                    'previousShipmentsPage',
                    'shipmentsCleanCache',
                    'brokenLinksFound'
                ];
                
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                console.log('✅ تم تنظيف ذاكرة المتصفح');
                
            } catch (error) {
                console.error('خطأ في تنظيف الذاكرة:', error);
            }
        }

        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // فحص حالة الإصلاح
            const linksFixApplied = localStorage.getItem('linksFixApplied');
            
            if (linksFixApplied) {
                updateStatus('ℹ️ تم إصلاح الروابط مسبقاً. يمكنك إعادة الإصلاح إذا كانت هناك مشاكل.', 'success');
                fixBtn.innerHTML = '🔄 إعادة إصلاح الروابط';
            } else {
                updateStatus('⚠️ تم اكتشاف روابط مكسورة. يُنصح بإصلاحها الآن.', 'warning');
                fixBtn.innerHTML = '🔧 إصلاح الروابط المكسورة - مطلوب';
            }
        });
    </script>
</body>
</html>
