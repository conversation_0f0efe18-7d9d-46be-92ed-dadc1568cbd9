# 🚚 نظام إدارة الشحنات المتكامل - الإصدار النهائي

## 📋 **نظرة عامة**

نظام شامل ومتطور لإدارة الشحنات والتوصيل مع النظام المالي المتكامل ودعم اللغة العربية الكامل.

---

## ✨ **المزايا الرئيسية**

### 📦 **إدارة الشحنات**
- ✅ إضافة وتعديل وحذف الشحنات
- ✅ تتبع حالة الشحنات في الوقت الفعلي
- ✅ طباعة بوليصات شحن احترافية مع QR Code
- ✅ دعم العملات المتعددة (SAR, KWD, USD, EUR)
- ✅ نظام تسعير متقدم
- ✅ إدارة أسباب الإلغاء

### 💰 **النظام المالي المتكامل**
- ✅ إدارة التحصيل من المناديب
- ✅ تحصيل الشحنات المؤجلة
- ✅ إدارة الدفع عند الاستلام (COD)
- ✅ حساب وسداد عمولات المناديب
- ✅ إنشاء فواتير الشحنات الآجلة مع بيانات الشركة
- ✅ تحويل العملات التلقائي

### 👥 **إدارة المستخدمين والصلاحيات**
- ✅ نظام صلاحيات متقدم ومفصل
- ✅ إدارة المستخدمين والأدوار
- ✅ إدارة المناديب والسائقين
- ✅ لوحة تحكم للعملاء
- ✅ تسجيل دخول موحد وآمن
- ✅ **تسجيل الدخول عبر الشبكات الاجتماعية (جديد!)**
  - 📧 Google OAuth
  - 🍎 Apple Sign In
  - 📘 Facebook Login

### 🏢 **إدارة الفروع**
- ✅ إنشاء وإدارة الفروع
- ✅ تحويلات بين الفروع
- ✅ استقبال الشحنات من الفروع الأخرى
- ✅ تتبع حركة الشحنات بين الفروع

### 🌍 **دعم جغرافي واسع**
- ✅ جميع مناطق المملكة العربية السعودية
- ✅ جميع مناطق دولة الكويت
- ✅ بحث ذكي في المناطق الجغرافية
- ✅ حاسبة الشحن التلقائية

### 📊 **التقارير والإحصائيات**
- ✅ تقارير مالية شاملة
- ✅ تقارير الشحنات والأداء
- ✅ إحصائيات الفروع والمناديب
- ✅ تقارير الإلغاءات والأسباب
- ✅ لوحة تحكم تفاعلية ثلاثية الأبعاد

### 🎨 **إدارة المحتوى والصفحات**
- ✅ محرر بصري متقدم للصفحات
- ✅ إدارة صفحات الموقع (الرئيسية، من نحن، شركاء النجاح)
- ✅ رفع الصور والفيديوهات
- ✅ نسخ وتحرير الصفحات بالسحب والإفلات
- ✅ روابط خارجية وإدارة المحتوى

---

## 🚀 **كيفية التشغيل**

### **الطريقة السريعة**
1. **افتح ملف `index.html`** في المتصفح
2. **استخدم بيانات الدخول الافتراضية:**
   - **اسم المستخدم:** `admin`
   - **كلمة المرور:** `123456`
3. **أو استخدم:** `unified-login.html` للدخول الموحد

### **الطريقة المتقدمة**
1. **شغل الخادم المحلي:**
   ```bash
   # Windows
   start.bat
   
   # Linux/Mac
   ./start.sh
   ```
2. **افتح المتصفح على:** `http://localhost:3000`

---

## 📁 **هيكل الملفات**

### **🏠 الصفحات الرئيسية**
- `index.html` - الصفحة الرئيسية
- `main-dashboard.html` - لوحة التحكم الرئيسية
- `unified-login.html` - تسجيل الدخول الموحد

### **📦 إدارة الشحنات**
- `shipments.html` - إدارة الشحنات
- `shipment-tracking.html` - تتبع الشحنات
- `test-print.html` - طباعة بوليصات الشحن
- `pricing-management.html` - إدارة التسعير
- `cancellation-management.html` - إدارة الإلغاءات

### **💰 النظام المالي**
- `financial-system.html` - النظام المالي الرئيسي
- `invoice-management.html` - إدارة الفواتير
- `payment-management.html` - إدارة المدفوعات
- `collection-management.html` - إدارة التحصيل
- `commission-management.html` - إدارة العمولات
- `cod-management.html` - إدارة الدفع عند الاستلام

### **👥 إدارة المستخدمين**
- `user-management.html` - إدارة المستخدمين
- `user-permissions-advanced.html` - الصلاحيات المتقدمة
- `distributors-management.html` - إدارة المناديب
- `customer-dashboard.html` - لوحة تحكم العملاء

### **🏢 إدارة الفروع**
- `branches-management.html` - إدارة الفروع
- `branch-transfers.html` - تحويلات الفروع

### **📊 التقارير**
- `reports.html` - التقارير العامة
- `advanced-reports.html` - التقارير المتقدمة
- `cancellation-reports.html` - تقارير الإلغاءات

### **🎨 إدارة المحتوى**
- `pages-management.html` - إدارة الصفحات
- `visual-page-editor.html` - المحرر البصري
- `about-us-editor.html` - محرر صفحة من نحن
- `partners-editor.html` - محرر صفحة شركاء النجاح

### **⚙️ الإعدادات والأدوات**
- `settings.html` - الإعدادات العامة
- `currency-converter.html` - محول العملات
- `shipping-calculator.html` - حاسبة الشحن

### **🗂️ الملفات التقنية**
- `js/database-simple.js` - قاعدة البيانات الرئيسية
- `js/financial-database.js` - قاعدة البيانات المالية
- `js/permissions.js` - نظام الصلاحيات
- `css/style.css` - التصميم الرئيسي
- `css/fonts.css` - الخطوط العربية

---

## 🔧 **الإصلاحات والميزات الأخيرة**

### **🆕 الميزات الجديدة:**
1. **🔐 تسجيل الدخول عبر الشبكات الاجتماعية** - Google, Apple, Facebook
2. **📄 نظام فواتير متكامل** - مع بيانات الشركة الكاملة
3. **🗑️ أدوات إدارة البيانات** - حذف البيانات القديمة في الإعدادات
4. **🖨️ بوليصة شحن محسنة** - مع شعار الشركة وQR Code محسن

### **✅ تم إصلاحها مؤخراً:**
1. **خطأ "db is not defined"** - تم إصلاحه بالكامل
2. **مشكلة تسجيل الدخول التلقائي** - تم تحسينها
3. **إضافة وظيفة حذف البيانات القديمة** - متاحة في الإعدادات
4. **إضافة بيانات الشركة للفواتير** - تم تطبيقها
5. **تحسين بوليصة الشحن** - مع شعار الشركة

### **🛡️ الحماية والأمان:**
- ✅ قاعدة بيانات احتياطية موثوقة
- ✅ معالجة شاملة للأخطاء
- ✅ تشفير كلمات المرور
- ✅ جلسات آمنة للمستخدمين

---

## 📚 **دليل الاستخدام السريع**

### **🔐 تسجيل الدخول:**

#### **الطريقة التقليدية:**
1. افتح `unified-login.html`
2. اختر نوع المستخدم (مدير النظام/موظف/عميل)
3. أدخل البيانات:
   - **مدير النظام:** admin / 123456
   - **موظف:** employee / 123456
   - **عميل:** customer / 123456

#### **🆕 تسجيل الدخول الاجتماعي (جديد!):**
1. افتح `unified-login.html`
2. اختر نوع المستخدم أولاً
3. اضغط على أحد أزرار الشبكات الاجتماعية:
   - **📧 Google** - تسجيل الدخول عبر Gmail
   - **🍎 Apple** - تسجيل الدخول عبر Apple ID
   - **📘 Facebook** - تسجيل الدخول عبر Facebook
4. انتظر التحويل التلقائي للوحة التحكم

### **📦 إضافة شحنة جديدة:**
1. اذهب إلى "الشحنات" من القائمة الرئيسية
2. اضغط "إضافة شحنة جديدة"
3. املأ بيانات المرسل والمستقبل
4. اختر نوع الدفع والخدمة
5. احفظ الشحنة

### **💰 إنشاء فاتورة:**
1. اذهب إلى "النظام المالي" ← "إدارة الفواتير"
2. اختر العميل من القائمة
3. حدد الشحنات المؤجلة
4. أدخل تاريخ الاستحقاق
5. اضغط "إنشاء الفاتورة"

### **🖨️ طباعة بوليصة شحن:**
1. اذهب إلى "اختبار الطباعة"
2. اختر الشحنة المطلوبة
3. اضغط "طباعة بوليصة الشحن"
4. ستفتح نافذة جديدة للطباعة

---

## 🎯 **الميزات المتقدمة**

### **📊 لوحة التحكم التفاعلية:**
- إحصائيات مباشرة للشحنات
- رسوم بيانية تفاعلية
- مراقبة الأداء في الوقت الفعلي
- تنبيهات ذكية

### **🔍 البحث الذكي:**
- بحث في الشحنات بالرقم أو الاسم
- فلترة متقدمة بالحالة والتاريخ
- بحث جغرافي في المناطق
- حفظ عمليات البحث المفضلة

### **📱 التوافق مع الأجهزة:**
- تصميم متجاوب لجميع الشاشات
- دعم اللمس للأجهزة اللوحية
- تحسين للهواتف الذكية
- عمل بدون اتصال إنترنت

---

## 🔧 **متطلبات النظام**

### **الحد الأدنى:**
- متصفح حديث (Chrome, Firefox, Safari, Edge)
- ذاكرة 4 جيجابايت RAM
- مساحة 100 ميجابايت على القرص الصلب

### **الموصى به:**
- متصفح Chrome أو Firefox أحدث إصدار
- ذاكرة 8 جيجابايت RAM
- مساحة 500 ميجابايت على القرص الصلب
- اتصال إنترنت للخرائط والتحديثات

---

## 📞 **الدعم والمساعدة**

### **📋 الملفات المرجعية:**
- `QUICK_START.md` - دليل البدء السريع
- `SOCIAL_LOGIN_README.md` - دليل تسجيل الدخول الاجتماعي (جديد!)
- `TROUBLESHOOTING.md` - حل المشاكل الشائعة
- `DATABASE_ERROR_FIX.md` - إصلاح أخطاء قاعدة البيانات
- `SYSTEM_FIXES_COMPLETE.md` - جميع الإصلاحات

### **🔧 أدوات التشخيص:**
- `system-check.html` - فحص النظام
- `test-database.html` - اختبار قاعدة البيانات
- `page-diagnostics.html` - تشخيص الصفحات
- `test-social-login.html` - اختبار تسجيل الدخول الاجتماعي (جديد!)

---

## 🎉 **الخلاصة**

**نظام إدارة الشحنات المتكامل** هو حل شامل ومتطور لإدارة عمليات الشحن والتوصيل مع:

- ✅ **واجهة عربية كاملة** وسهلة الاستخدام
- ✅ **نظام مالي متكامل** مع الفواتير والتحصيل
- ✅ **إدارة متقدمة للمستخدمين** والصلاحيات
- ✅ **تقارير شاملة** وإحصائيات مفصلة
- ✅ **دعم كامل للأجهزة المختلفة**
- ✅ **أمان وحماية عالية** للبيانات

**جاهز للاستخدام الفوري والإنتاج!** 🚀✨
