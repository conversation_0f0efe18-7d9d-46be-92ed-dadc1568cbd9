'use client'

import React, { useState } from 'react'
import { toast } from 'react-hot-toast'
import { useRouter } from 'next/navigation'
import { authApi } from '@/lib/api/auth'
import { LoginCredentials, LoginWith2FACredentials } from '@/types/auth'

interface LoginFormProps {
  onSuccess?: () => void
}

export default function LoginForm({ onSuccess }: LoginFormProps) {
  const router = useRouter()
  const [step, setStep] = useState<'login' | '2fa'>('login')
  const [loading, setLoading] = useState(false)
  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: '',
    password: '',
  })
  const [twoFactorToken, setTwoFactorToken] = useState('')
  const [userId, setUserId] = useState('')

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!credentials.email || !credentials.password) {
      toast.error('يرجى إدخال البريد الإلكتروني وكلمة المرور')
      return
    }

    try {
      setLoading(true)
      const response = await authApi.login(credentials)
      
      if (response.requiresTwoFactor) {
        setUserId(response.userId!)
        setStep('2fa')
        toast.success('يرجى إدخال رمز المصادقة الثنائية')
      } else {
        // Store token and redirect
        localStorage.setItem('token', response.token!)
        toast.success('تم تسجيل الدخول بنجاح')
        
        if (onSuccess) {
          onSuccess()
        } else {
          router.push('/dashboard')
        }
      }
    } catch (error: any) {
      toast.error(error.message || 'فشل في تسجيل الدخول')
    } finally {
      setLoading(false)
    }
  }

  const handleTwoFactorLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!twoFactorToken) {
      toast.error('يرجى إدخال رمز المصادقة الثنائية')
      return
    }

    try {
      setLoading(true)
      const loginData: LoginWith2FACredentials = {
        email: credentials.email,
        password: credentials.password,
        token: twoFactorToken,
      }
      
      const response = await authApi.loginWith2FA(loginData)
      
      // Store token and redirect
      localStorage.setItem('token', response.token!)
      
      if (response.usedBackupCode) {
        toast.success(`تم تسجيل الدخول بنجاح باستخدام رمز احتياطي. الرموز المتبقية: ${response.remainingBackupCodes}`)
      } else {
        toast.success('تم تسجيل الدخول بنجاح')
      }
      
      if (onSuccess) {
        onSuccess()
      } else {
        router.push('/dashboard')
      }
    } catch (error: any) {
      toast.error(error.message || 'رمز المصادقة الثنائية غير صحيح')
    } finally {
      setLoading(false)
    }
  }

  const handleBackToLogin = () => {
    setStep('login')
    setTwoFactorToken('')
    setUserId('')
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6" dir="rtl">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {step === 'login' ? 'تسجيل الدخول' : 'المصادقة الثنائية'}
        </h2>
        <p className="text-gray-600 mt-2">
          {step === 'login' 
            ? 'أدخل بياناتك للدخول إلى النظام' 
            : 'أدخل رمز المصادقة الثنائية من تطبيق المصادقة'
          }
        </p>
      </div>

      {step === 'login' ? (
        <form onSubmit={handleLogin} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              البريد الإلكتروني
            </label>
            <input
              type="email"
              value={credentials.email}
              onChange={(e) => setCredentials({ ...credentials, email: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              كلمة المرور
            </label>
            <input
              type="password"
              value={credentials.password}
              onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="••••••••"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
          </button>
        </form>
      ) : (
        <form onSubmit={handleTwoFactorLogin} className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-blue-800 text-sm">
              أدخل الرمز المكون من 6 أرقام من تطبيق المصادقة، أو استخدم أحد الرموز الاحتياطية.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رمز المصادقة الثنائية
            </label>
            <input
              type="text"
              value={twoFactorToken}
              onChange={(e) => setTwoFactorToken(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-center text-lg tracking-widest"
              placeholder="123456"
              maxLength={8}
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              أدخل الرمز من تطبيق Google Authenticator أو رمز احتياطي
            </p>
          </div>

          <div className="flex space-x-4 space-x-reverse">
            <button
              type="button"
              onClick={handleBackToLogin}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700"
            >
              رجوع
            </button>
            <button
              type="submit"
              disabled={loading || !twoFactorToken}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري التحقق...' : 'تأكيد'}
            </button>
          </div>
        </form>
      )}

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-600">
          ليس لديك حساب؟{' '}
          <a href="/register" className="text-blue-600 hover:text-blue-700 font-medium">
            إنشاء حساب جديد
          </a>
        </p>
      </div>

      {step === '2fa' && (
        <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <h4 className="font-semibold text-yellow-900 mb-2">فقدت هاتفك؟</h4>
          <p className="text-yellow-800 text-sm">
            يمكنك استخدام أحد الرموز الاحتياطية التي حفظتها عند تفعيل المصادقة الثنائية.
          </p>
        </div>
      )}
    </div>
  )
}
