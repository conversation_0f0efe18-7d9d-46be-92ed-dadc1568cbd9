<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الشحنات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-color: #ecf0f1;
            --font-arabic: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic);
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
            color: var(--secondary-color);
        }

        .login-title {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #666;
            font-size: 1rem;
        }

        .login-form {
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .form-control {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            font-size: 1rem;
            font-family: var(--font-arabic);
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            background: white;
        }

        .form-control.error {
            border-color: var(--danger-color);
            background: #fff5f5;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 1.2rem;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-arabic);
            position: relative;
            overflow: hidden;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-btn .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            font-size: 0.9rem;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .remember-me input[type="checkbox"] {
            transform: scale(1.2);
        }

        .forgot-password {
            color: var(--secondary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--primary-color);
        }

        .error-message {
            background: #fff5f5;
            color: var(--danger-color);
            padding: 12px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #fecaca;
            font-size: 0.9rem;
            display: none;
        }

        .success-message {
            background: #f0fff4;
            color: var(--success-color);
            padding: 12px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #bbf7d0;
            font-size: 0.9rem;
            display: none;
        }

        .demo-users {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-top: 30px;
            border: 2px dashed #dee2e6;
        }

        .demo-users h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            text-align: center;
        }

        .demo-user {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .demo-user:hover {
            background: var(--light-color);
            transform: translateX(-5px);
        }

        .demo-user-info {
            flex: 1;
        }

        .demo-user-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .demo-user-role {
            font-size: 0.8rem;
            color: #666;
        }

        .demo-user-btn {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9rem;
        }

        .footer a {
            color: var(--secondary-color);
            text-decoration: none;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 20px;
            }

            .login-title {
                font-size: 1.5rem;
            }

            .logo {
                font-size: 3rem;
            }

            .remember-forgot {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">🚚</div>
            <h1 class="login-title">نظام إدارة الشحنات</h1>
            <p class="login-subtitle">مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
        </div>

        <div class="error-message" id="errorMessage">
            <!-- رسائل الخطأ ستظهر هنا -->
        </div>

        <div class="success-message" id="successMessage">
            <!-- رسائل النجاح ستظهر هنا -->
        </div>

        <form class="login-form" id="loginForm">
            <div class="form-group">
                <label class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-control" id="email" required 
                       placeholder="أدخل البريد الإلكتروني">
            </div>

            <div class="form-group">
                <label class="form-label">كلمة المرور</label>
                <div style="position: relative;">
                    <input type="password" class="form-control" id="password" required 
                           placeholder="أدخل كلمة المرور">
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        👁️
                    </button>
                </div>
            </div>

            <div class="remember-forgot">
                <label class="remember-me">
                    <input type="checkbox" id="rememberMe">
                    <span>تذكرني</span>
                </label>
                <a href="#" class="forgot-password" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <span id="loginBtnText">تسجيل الدخول</span>
                <div class="spinner" id="loginSpinner"></div>
            </button>
        </form>

        <!-- مستخدمين تجريبيين -->
        <div class="demo-users">
            <h4>🧪 مستخدمين تجريبيين</h4>
            
            <div class="demo-user" onclick="fillDemoUser('<EMAIL>', 'admin123')">
                <div class="demo-user-info">
                    <div class="demo-user-name">أحمد محمد</div>
                    <div class="demo-user-role">مدير النظام</div>
                </div>
                <button class="demo-user-btn">تجربة</button>
            </div>

            <div class="demo-user" onclick="fillDemoUser('<EMAIL>', 'manager123')">
                <div class="demo-user-info">
                    <div class="demo-user-name">فاطمة أحمد</div>
                    <div class="demo-user-role">مدير</div>
                </div>
                <button class="demo-user-btn">تجربة</button>
            </div>

            <div class="demo-user" onclick="fillDemoUser('<EMAIL>', 'distributor123')">
                <div class="demo-user-info">
                    <div class="demo-user-name">محمد علي</div>
                    <div class="demo-user-role">موزع</div>
                </div>
                <button class="demo-user-btn">تجربة</button>
            </div>

            <div class="demo-user" onclick="fillDemoUser('<EMAIL>', 'viewer123')">
                <div class="demo-user-info">
                    <div class="demo-user-name">سارة خالد</div>
                    <div class="demo-user-role">مشاهد</div>
                </div>
                <button class="demo-user-btn">تجربة</button>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 نظام إدارة الشحنات. جميع الحقوق محفوظة.</p>
            <p><a href="#" onclick="showHelp()">المساعدة</a> | <a href="#" onclick="showContact()">اتصل بنا</a></p>
        </div>
    </div>

    <script src="js/database-simple.js"></script>
    <script>
        // بيانات المستخدمين للمصادقة
        const authUsers = {
            '<EMAIL>': {
                password: 'admin123',
                user: {
                    id: 'user1',
                    name: 'أحمد محمد',
                    email: '<EMAIL>',
                    phone: '+966501234567',
                    role: 'admin',
                    status: 'active',
                    branch: 'main',
                    avatar: 'أ',
                    customPermissions: []
                }
            },
            '<EMAIL>': {
                password: 'manager123',
                user: {
                    id: 'user2',
                    name: 'فاطمة أحمد',
                    email: '<EMAIL>',
                    phone: '+966507654321',
                    role: 'manager',
                    status: 'active',
                    branch: 'riyadh',
                    avatar: 'ف',
                    customPermissions: []
                }
            },
            '<EMAIL>': {
                password: 'distributor123',
                user: {
                    id: 'user3',
                    name: 'محمد علي',
                    email: '<EMAIL>',
                    phone: '+966509876543',
                    role: 'distributor',
                    status: 'active',
                    branch: 'jeddah',
                    avatar: 'م',
                    customPermissions: []
                }
            },
            '<EMAIL>': {
                password: 'viewer123',
                user: {
                    id: 'user4',
                    name: 'سارة خالد',
                    email: '<EMAIL>',
                    phone: '+966502468135',
                    role: 'viewer',
                    status: 'active',
                    branch: 'dammam',
                    avatar: 'س',
                    customPermissions: []
                }
            }
        };

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📍 تحميل صفحة تسجيل الدخول...');

            // مسح البيانات القديمة إذا كانت تحتوي على عناوين بريد قديمة
            const users = localStorage.getItem('users');
            if (users && users.includes('@company.com')) {
                console.log('🔄 مسح البيانات القديمة...');
                localStorage.removeItem('users');
                localStorage.removeItem('roles');
                localStorage.removeItem('activityLog');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('sessionStart');
            }

            // التحقق من وجود جلسة نشطة
            checkExistingSession();

            // إعداد النموذج
            setupLoginForm();

            console.log('✅ تم تحميل صفحة تسجيل الدخول بنجاح');
        });

        // التحقق من وجود جلسة نشطة
        function checkExistingSession() {
            const currentUser = localStorage.getItem('currentUser');
            const sessionStart = localStorage.getItem('sessionStart');
            
            if (currentUser && sessionStart) {
                const sessionDuration = 8 * 60 * 60 * 1000; // 8 ساعات
                
                if ((Date.now() - parseInt(sessionStart)) < sessionDuration) {
                    // الجلسة لا تزال نشطة
                    showSuccessMessage('مرحباً بعودتك! جاري تحويلك...');
                    setTimeout(() => {
                        window.location.href = 'main-dashboard.html';
                    }, 1500);
                    return;
                }
            }
            
            // مسح الجلسة المنتهية الصلاحية
            localStorage.removeItem('currentUser');
            localStorage.removeItem('sessionStart');
        }

        // إعداد نموذج تسجيل الدخول
        function setupLoginForm() {
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                handleLogin();
            });
        }

        // معالجة تسجيل الدخول
        function handleLogin() {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // التحقق من صحة البيانات
            if (!validateLoginData(email, password)) {
                return;
            }

            // إظهار حالة التحميل
            showLoadingState(true);

            // محاكاة تأخير الشبكة
            setTimeout(() => {
                authenticateUser(email, password, rememberMe);
            }, 1500);
        }

        // التحقق من صحة بيانات تسجيل الدخول
        function validateLoginData(email, password) {
            hideMessages();

            if (!email) {
                showErrorMessage('يرجى إدخال البريد الإلكتروني');
                document.getElementById('email').classList.add('error');
                return false;
            }

            if (!isValidEmail(email)) {
                showErrorMessage('يرجى إدخال بريد إلكتروني صحيح');
                document.getElementById('email').classList.add('error');
                return false;
            }

            if (!password) {
                showErrorMessage('يرجى إدخال كلمة المرور');
                document.getElementById('password').classList.add('error');
                return false;
            }

            if (password.length < 6) {
                showErrorMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                document.getElementById('password').classList.add('error');
                return false;
            }

            // إزالة فئة الخطأ
            document.getElementById('email').classList.remove('error');
            document.getElementById('password').classList.remove('error');

            return true;
        }

        // التحقق من صحة البريد الإلكتروني
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // مصادقة المستخدم
        function authenticateUser(email, password, rememberMe) {
            const authData = authUsers[email];

            if (!authData || authData.password !== password) {
                showLoadingState(false);
                showErrorMessage('البريد الإلكتروني أو كلمة المرور غير صحيحة');

                // تسجيل محاولة دخول فاشلة
                logFailedLogin(email);
                return;
            }

            // التحقق من حالة المستخدم
            if (authData.user.status !== 'active') {
                showLoadingState(false);
                showErrorMessage('حسابك غير نشط. يرجى التواصل مع المدير');
                return;
            }

            // تسجيل دخول ناجح
            loginSuccess(authData.user, rememberMe);
        }

        // تسجيل دخول ناجح
        function loginSuccess(user, rememberMe) {
            // حفظ بيانات المستخدم والجلسة
            localStorage.setItem('currentUser', JSON.stringify(user));
            localStorage.setItem('sessionStart', Date.now().toString());

            if (rememberMe) {
                localStorage.setItem('rememberUser', user.email);
            }

            // تحديث آخر دخول
            updateLastLogin(user.id);

            // تسجيل النشاط
            logSuccessfulLogin(user);

            // إظهار رسالة نجاح
            showSuccessMessage('تم تسجيل الدخول بنجاح! مرحباً ' + user.name);

            // إعادة توجيه للوحة التحكم
            setTimeout(() => {
                window.location.href = 'main-dashboard.html';
            }, 2000);
        }

        // تحديث آخر دخول للمستخدم
        function updateLastLogin(userId) {
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const userIndex = users.findIndex(u => u.id === userId);

            if (userIndex !== -1) {
                users[userIndex].lastLogin = new Date().toISOString();
                localStorage.setItem('users', JSON.stringify(users));
            }
        }

        // تسجيل محاولة دخول ناجحة
        function logSuccessfulLogin(user) {
            const activity = {
                id: 'act' + Date.now(),
                userId: user.id,
                userName: user.name,
                action: 'تسجيل دخول ناجح',
                details: 'تسجيل دخول ناجح للنظام',
                timestamp: new Date().toISOString(),
                ipAddress: '192.168.1.' + Math.floor(Math.random() * 255),
                browser: getBrowserInfo()
            };

            const activities = JSON.parse(localStorage.getItem('activityLog') || '[]');
            activities.unshift(activity);

            if (activities.length > 500) {
                activities.splice(500);
            }

            localStorage.setItem('activityLog', JSON.stringify(activities));
        }

        // تسجيل محاولة دخول فاشلة
        function logFailedLogin(email) {
            const activity = {
                id: 'act' + Date.now(),
                userId: 'unknown',
                userName: 'مستخدم غير معروف',
                action: 'محاولة دخول فاشلة',
                details: 'محاولة دخول فاشلة للبريد: ' + email,
                timestamp: new Date().toISOString(),
                ipAddress: '192.168.1.' + Math.floor(Math.random() * 255),
                browser: getBrowserInfo()
            };

            const activities = JSON.parse(localStorage.getItem('activityLog') || '[]');
            activities.unshift(activity);

            if (activities.length > 500) {
                activities.splice(500);
            }

            localStorage.setItem('activityLog', JSON.stringify(activities));
        }

        // الحصول على معلومات المتصفح
        function getBrowserInfo() {
            const userAgent = navigator.userAgent;
            let browser = 'Unknown';

            if (userAgent.includes('Chrome')) {
                browser = 'Chrome';
            } else if (userAgent.includes('Firefox')) {
                browser = 'Firefox';
            } else if (userAgent.includes('Safari')) {
                browser = 'Safari';
            } else if (userAgent.includes('Edge')) {
                browser = 'Edge';
            }

            return browser + ' ' + Math.floor(Math.random() * 10 + 115) + '.0';
        }

        // إظهار/إخفاء حالة التحميل
        function showLoadingState(loading) {
            const btn = document.getElementById('loginBtn');
            const btnText = document.getElementById('loginBtnText');
            const spinner = document.getElementById('loginSpinner');

            if (loading) {
                btn.disabled = true;
                btnText.textContent = 'جاري تسجيل الدخول...';
                spinner.style.display = 'inline-block';
            } else {
                btn.disabled = false;
                btnText.textContent = 'تسجيل الدخول';
                spinner.style.display = 'none';
            }
        }

        // إظهار رسالة خطأ
        function showErrorMessage(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // إظهار رسالة نجاح
        function showSuccessMessage(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // إخفاء جميع الرسائل
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // تبديل إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.password-toggle');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        // ملء بيانات مستخدم تجريبي
        function fillDemoUser(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
            document.getElementById('rememberMe').checked = true;

            // إزالة أي أخطاء سابقة
            document.getElementById('email').classList.remove('error');
            document.getElementById('password').classList.remove('error');
            hideMessages();
        }

        // إظهار نافذة نسيان كلمة المرور
        function showForgotPassword() {
            alert('لاستعادة كلمة المرور، يرجى التواصل مع مدير النظام على:\n\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +966501234567\n\nأو استخدم أحد المستخدمين التجريبيين أدناه.');
        }

        // إظهار المساعدة
        function showHelp() {
            alert('مساعدة تسجيل الدخول:\n\n1. أدخل البريد الإلكتروني وكلمة المرور\n2. يمكنك استخدام المستخدمين التجريبيين\n3. اختر "تذكرني" للبقاء مسجلاً\n4. في حالة نسيان كلمة المرور، تواصل مع المدير\n\nالمستخدمين التجريبيين:\n- مدير النظام: <EMAIL> / admin123\n- مدير: <EMAIL> / manager123\n- موزع: <EMAIL> / distributor123\n- مشاهد: <EMAIL> / viewer123');
        }

        // إظهار معلومات الاتصال
        function showContact() {
            alert('معلومات الاتصال:\n\nالشركة: نظام إدارة الشحنات\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +966501234567\nالعنوان: الرياض، المملكة العربية السعودية\n\nساعات العمل: من الأحد إلى الخميس، 8:00 ص - 5:00 م');
        }
    </script>
</body>
</html>
