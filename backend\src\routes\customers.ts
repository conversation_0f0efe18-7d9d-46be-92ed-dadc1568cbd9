// Customer Routes
// مسارات العملاء

import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { asyncHandler, AppError } from '../middleware/errorHandler'
import { authenticate, requireManagerOrAdmin } from '../middleware/auth'
import { config } from '../config/config'

const router = Router()
const prisma = new PrismaClient()

// Validation schemas
const createCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(config.constants.maxNameLength),
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().optional(),
  address: z.string().max(config.constants.maxAddressLength).optional(),
  city: z.string().max(config.constants.maxNameLength).optional(),
  country: z.string().max(config.constants.maxNameLength).optional(),
  postalCode: z.string().optional(),
})

const updateCustomerSchema = createCustomerSchema.partial()

/**
 * @swagger
 * /api/customers:
 *   get:
 *     summary: Get customers list
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Customers retrieved successfully
 */
router.get('/', authenticate, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1
  const limit = Math.min(parseInt(req.query.limit as string) || config.constants.defaultPageSize, config.constants.maxPageSize)
  const search = req.query.search as string
  const skip = (page - 1) * limit

  const where: any = {}
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search, mode: 'insensitive' } },
    ]
  }

  const [customers, total] = await Promise.all([
    prisma.customer.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        _count: {
          select: {
            sentShipments: true,
            receivedShipments: true,
          },
        },
      },
    }),
    prisma.customer.count({ where }),
  ])

  const totalPages = Math.ceil(total / limit)

  res.json({
    success: true,
    data: {
      customers,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    },
  })
}))

/**
 * @swagger
 * /api/customers:
 *   post:
 *     summary: Create new customer
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: Customer created successfully
 */
router.post('/', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const customerData = createCustomerSchema.parse(req.body)
  
  const customer = await prisma.customer.create({
    data: {
      ...customerData,
      createdBy: req.user!.id,
    },
  })
  
  res.status(201).json({
    success: true,
    data: { customer },
    message: 'Customer created successfully',
  })
}))

/**
 * @swagger
 * /api/customers/{id}:
 *   get:
 *     summary: Get customer by ID
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer retrieved successfully
 *       404:
 *         description: Customer not found
 */
router.get('/:id', authenticate, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const customer = await prisma.customer.findUnique({
    where: { id },
    include: {
      sentShipments: {
        select: {
          id: true,
          trackingNumber: true,
          status: true,
          cost: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      receivedShipments: {
        select: {
          id: true,
          trackingNumber: true,
          status: true,
          cost: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      _count: {
        select: {
          sentShipments: true,
          receivedShipments: true,
        },
      },
    },
  })
  
  if (!customer) {
    throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND')
  }
  
  res.json({
    success: true,
    data: { customer },
  })
}))

/**
 * @swagger
 * /api/customers/{id}:
 *   put:
 *     summary: Update customer
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer updated successfully
 *       404:
 *         description: Customer not found
 */
router.put('/:id', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params
  const updateData = updateCustomerSchema.parse(req.body)
  
  const customer = await prisma.customer.update({
    where: { id },
    data: updateData,
  })
  
  res.json({
    success: true,
    data: { customer },
    message: 'Customer updated successfully',
  })
}))

/**
 * @swagger
 * /api/customers/{id}:
 *   delete:
 *     summary: Delete customer
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer deleted successfully
 *       404:
 *         description: Customer not found
 */
router.delete('/:id', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  // Check if customer has shipments
  const shipmentCount = await prisma.shipment.count({
    where: {
      OR: [
        { senderId: id },
        { receiverId: id },
      ],
    },
  })
  
  if (shipmentCount > 0) {
    throw new AppError('Cannot delete customer with existing shipments', 400, 'CUSTOMER_HAS_SHIPMENTS')
  }
  
  await prisma.customer.delete({
    where: { id },
  })
  
  res.json({
    success: true,
    message: 'Customer deleted successfully',
  })
}))

export default router
