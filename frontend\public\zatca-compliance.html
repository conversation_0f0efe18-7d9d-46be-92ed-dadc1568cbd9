<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الامتثال لهيئة الزكاة والضريبة | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 25px 35px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #333;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .zatca-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .compliance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .compliance-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .compliance-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
        }

        .compliance-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
        }

        .card-subtitle {
            color: #666;
            font-size: 0.9rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #555;
        }

        .feature-list li::before {
            content: "✅";
            font-size: 1rem;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 1rem;
            margin-top: 15px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 126, 52, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 126, 52, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 15px;
        }

        .status-compliant {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .status-pending {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
        }

        .status-required {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .requirements-section {
            background: white;
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.6rem;
            color: #333;
            font-weight: 700;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-title i {
            color: #28a745;
        }

        .requirement-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
        }

        .requirement-title {
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .requirement-description {
            color: #666;
            line-height: 1.6;
        }

        .vat-calculator {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }

        .calculator-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #1976d2;
            margin-bottom: 15px;
            text-align: center;
        }

        .calculator-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .form-control {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .result-display {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
        }

        .result-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #28a745;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #666;
            text-decoration: none;
            margin-bottom: 20px;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.8);
            color: #333;
        }

        @media (max-width: 768px) {
            .compliance-grid {
                grid-template-columns: 1fr;
            }
            
            .calculator-form {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="financial-system.html" class="back-link">
            <i class="fas fa-arrow-right"></i>
            العودة للنظام المالي
        </a>

        <div class="header">
            <div class="zatca-logo">
                🏛️
            </div>
            <h1>نظام الامتثال لهيئة الزكاة والضريبة والجمارك</h1>
            <p>نظام متكامل للامتثال لمتطلبات هيئة الزكاة والضريبة والجمارك السعودية (ZATCA)</p>
        </div>

        <!-- بطاقات الامتثال -->
        <div class="compliance-grid">
            <!-- الفوترة الإلكترونية -->
            <div class="compliance-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div>
                        <div class="card-title">الفوترة الإلكترونية</div>
                        <div class="card-subtitle">نظام الفواتير الإلكترونية المعتمد</div>
                    </div>
                </div>
                <ul class="feature-list">
                    <li>إنشاء فواتير إلكترونية معتمدة</li>
                    <li>ختم زمني وتوقيع رقمي</li>
                    <li>رمز QR للتحقق من الفاتورة</li>
                    <li>حفظ تلقائي في النظام</li>
                    <li>تصدير بصيغة XML و PDF</li>
                </ul>
                <div class="status-indicator status-compliant">
                    <i class="fas fa-check-circle"></i>
                    متوافق
                </div>
                <button class="btn btn-primary" onclick="openEInvoicing()">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير الإلكترونية
                </button>
            </div>

            <!-- ضريبة القيمة المضافة -->
            <div class="compliance-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div>
                        <div class="card-title">ضريبة القيمة المضافة</div>
                        <div class="card-subtitle">حساب وإدارة ضريبة القيمة المضافة 15%</div>
                    </div>
                </div>
                <ul class="feature-list">
                    <li>حساب تلقائي للضريبة 15%</li>
                    <li>تقارير ضريبية شهرية وربعية</li>
                    <li>فصل المبيعات الخاضعة والمعفاة</li>
                    <li>حساب الضريبة المستردة</li>
                    <li>إقرارات ضريبية جاهزة</li>
                </ul>
                <div class="status-indicator status-compliant">
                    <i class="fas fa-check-circle"></i>
                    متوافق
                </div>
                <button class="btn btn-primary" onclick="openVATManagement()">
                    <i class="fas fa-calculator"></i>
                    إدارة ضريبة القيمة المضافة
                </button>
            </div>

            <!-- التقارير الضريبية -->
            <div class="compliance-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div>
                        <div class="card-title">التقارير الضريبية</div>
                        <div class="card-subtitle">تقارير مفصلة للامتثال الضريبي</div>
                    </div>
                </div>
                <ul class="feature-list">
                    <li>تقرير المبيعات الشهري</li>
                    <li>تقرير ضريبة القيمة المضافة</li>
                    <li>تقرير الفواتير الإلكترونية</li>
                    <li>تقرير العملاء والموردين</li>
                    <li>تصدير للجهات الحكومية</li>
                </ul>
                <div class="status-indicator status-compliant">
                    <i class="fas fa-check-circle"></i>
                    متوافق
                </div>
                <button class="btn btn-primary" onclick="openTaxReports()">
                    <i class="fas fa-file-alt"></i>
                    عرض التقارير الضريبية
                </button>
            </div>

            <!-- الربط مع منصة فاتورة -->
            <div class="compliance-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div>
                        <div class="card-title">منصة فاتورة</div>
                        <div class="card-subtitle">الربط مع منصة فاتورة الحكومية</div>
                    </div>
                </div>
                <ul class="feature-list">
                    <li>تسجيل النظام في منصة فاتورة</li>
                    <li>إرسال الفواتير تلقائياً</li>
                    <li>استقبال الموافقات والرفض</li>
                    <li>مزامنة البيانات</li>
                    <li>تحديثات تلقائية</li>
                </ul>
                <div class="status-indicator status-pending">
                    <i class="fas fa-clock"></i>
                    قيد التطوير
                </div>
                <button class="btn btn-secondary" onclick="setupFatoorahIntegration()">
                    <i class="fas fa-cog"></i>
                    إعداد الربط
                </button>
            </div>
        </div>

        <!-- متطلبات الامتثال -->
        <div class="requirements-section">
            <h2 class="section-title">
                <i class="fas fa-clipboard-check"></i>
                متطلبات الامتثال الأساسية
            </h2>

            <div class="requirement-item">
                <div class="requirement-title">1. تسجيل الرقم الضريبي</div>
                <div class="requirement-description">
                    يجب تسجيل الرقم الضريبي للشركة في النظام وإظهاره في جميع الفواتير والمستندات المالية.
                    الرقم الضريبي الحالي: <strong id="tax-number">300000000000003</strong>
                </div>
            </div>

            <div class="requirement-item">
                <div class="requirement-title">2. الفوترة الإلكترونية الإلزامية</div>
                <div class="requirement-description">
                    جميع الفواتير يجب أن تكون إلكترونية ومطابقة لمعايير هيئة الزكاة والضريبة، مع رمز QR وختم زمني.
                </div>
            </div>

            <div class="requirement-item">
                <div class="requirement-title">3. حفظ السجلات</div>
                <div class="requirement-description">
                    يجب حفظ جميع الفواتير والمستندات المالية لمدة لا تقل عن 6 سنوات في شكل إلكتروني قابل للاسترجاع.
                </div>
            </div>

            <div class="requirement-item">
                <div class="requirement-title">4. التقارير الدورية</div>
                <div class="requirement-description">
                    تقديم الإقرارات الضريبية في المواعيد المحددة (شهرياً أو ربعياً حسب حجم النشاط).
                </div>
            </div>
        </div>

        <!-- حاسبة ضريبة القيمة المضافة -->
        <div class="vat-calculator">
            <div class="calculator-title">
                <i class="fas fa-calculator"></i>
                حاسبة ضريبة القيمة المضافة
            </div>
            
            <div class="calculator-form">
                <div class="form-group">
                    <label class="form-label">المبلغ الأساسي (ريال)</label>
                    <input type="number" id="base-amount" class="form-control" placeholder="1000" step="0.01" oninput="calculateVAT()">
                </div>
                
                <div class="form-group">
                    <label class="form-label">نسبة الضريبة (%)</label>
                    <select id="vat-rate" class="form-control" onchange="calculateVAT()">
                        <option value="15">15% - المعدل الأساسي</option>
                        <option value="0">0% - معفى من الضريبة</option>
                        <option value="5">5% - معدل مخفض (بعض السلع)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="button" class="btn btn-primary" onclick="calculateVAT()">
                        <i class="fas fa-calculator"></i>
                        احسب
                    </button>
                </div>
            </div>
            
            <div class="result-display" id="vat-result" style="display: none;">
                <div>المبلغ الأساسي: <span id="result-base">0</span> ريال</div>
                <div>ضريبة القيمة المضافة: <span id="result-vat">0</span> ريال</div>
                <div class="result-amount">المبلغ الإجمالي: <span id="result-total">0</span> ريال</div>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏛️ تحميل نظام الامتثال لهيئة الزكاة والضريبة...');
            loadCompanyTaxNumber();
            initializeZATCACompliance();
        });

        // تحميل الرقم الضريبي للشركة
        function loadCompanyTaxNumber() {
            const companySettings = JSON.parse(localStorage.getItem('companySettings') || '{}');
            const taxNumber = companySettings.taxNumber || '300000000000003';
            document.getElementById('tax-number').textContent = taxNumber;
        }

        // تهيئة نظام الامتثال
        function initializeZATCACompliance() {
            // تحقق من وجود البيانات المطلوبة
            checkComplianceStatus();

            // تهيئة إعدادات الضريبة
            initializeTaxSettings();
        }

        // فحص حالة الامتثال
        function checkComplianceStatus() {
            const companySettings = JSON.parse(localStorage.getItem('companySettings') || '{}');

            // فحص الرقم الضريبي
            if (!companySettings.taxNumber) {
                console.warn('⚠️ الرقم الضريبي غير مسجل');
                // يمكن إضافة تنبيه للمستخدم
            }

            // فحص إعدادات الفوترة الإلكترونية
            if (!companySettings.eInvoicingEnabled) {
                console.warn('⚠️ الفوترة الإلكترونية غير مفعلة');
            }
        }

        // تهيئة إعدادات الضريبة
        function initializeTaxSettings() {
            if (!localStorage.getItem('taxSettings')) {
                const defaultTaxSettings = {
                    vatRate: 15, // ضريبة القيمة المضافة 15%
                    taxYear: new Date().getFullYear(),
                    reportingPeriod: 'monthly', // شهري أو ربعي
                    exemptCategories: ['medical', 'education', 'basic-food'],
                    lastReportDate: null,
                    nextReportDue: null
                };
                localStorage.setItem('taxSettings', JSON.stringify(defaultTaxSettings));
            }
        }

        // حساب ضريبة القيمة المضافة
        function calculateVAT() {
            const baseAmount = parseFloat(document.getElementById('base-amount').value) || 0;
            const vatRate = parseFloat(document.getElementById('vat-rate').value) || 15;

            const vatAmount = baseAmount * (vatRate / 100);
            const totalAmount = baseAmount + vatAmount;

            // عرض النتائج
            document.getElementById('result-base').textContent = baseAmount.toFixed(2);
            document.getElementById('result-vat').textContent = vatAmount.toFixed(2);
            document.getElementById('result-total').textContent = totalAmount.toFixed(2);
            document.getElementById('vat-result').style.display = 'block';
        }

        // فتح إدارة الفواتير الإلكترونية
        function openEInvoicing() {
            // التحقق من وجود صفحة إدارة الفواتير
            if (document.querySelector('a[href="invoice-management.html"]')) {
                window.location.href = 'invoice-management.html';
            } else {
                // إنشاء فاتورة إلكترونية جديدة
                createElectronicInvoice();
            }
        }

        // إنشاء فاتورة إلكترونية
        function createElectronicInvoice() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #333; font-size: 1.4rem;">إنشاء فاتورة إلكترونية</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>

                    <form id="e-invoice-form">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">اسم العميل</label>
                            <input type="text" id="customer-name" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">الرقم الضريبي للعميل</label>
                            <input type="text" id="customer-tax-number" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;" placeholder="اختياري">
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">المبلغ الأساسي</label>
                                <input type="number" id="invoice-amount" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;" step="0.01" required>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">نوع الخدمة</label>
                                <select id="service-type" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                                    <option value="shipping">خدمات الشحن</option>
                                    <option value="delivery">خدمات التوصيل</option>
                                    <option value="storage">خدمات التخزين</option>
                                    <option value="other">خدمات أخرى</option>
                                </select>
                            </div>
                        </div>

                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>المبلغ الأساسي:</span>
                                <span id="modal-base-amount">0.00 ريال</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>ضريبة القيمة المضافة (15%):</span>
                                <span id="modal-vat-amount">0.00 ريال</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; font-weight: 700; font-size: 1.1rem; border-top: 1px solid #dee2e6; padding-top: 10px;">
                                <span>المبلغ الإجمالي:</span>
                                <span id="modal-total-amount">0.00 ريال</span>
                            </div>
                        </div>

                        <div style="display: flex; gap: 15px; justify-content: center;">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-invoice"></i>
                                إنشاء الفاتورة
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);

            // إضافة مستمع للحساب التلقائي
            document.getElementById('invoice-amount').addEventListener('input', function() {
                const amount = parseFloat(this.value) || 0;
                const vat = amount * 0.15;
                const total = amount + vat;

                document.getElementById('modal-base-amount').textContent = amount.toFixed(2) + ' ريال';
                document.getElementById('modal-vat-amount').textContent = vat.toFixed(2) + ' ريال';
                document.getElementById('modal-total-amount').textContent = total.toFixed(2) + ' ريال';
            });

            // معالجة إرسال النموذج
            document.getElementById('e-invoice-form').addEventListener('submit', function(e) {
                e.preventDefault();
                generateElectronicInvoice();
            });
        }

        // إنشاء الفاتورة الإلكترونية
        function generateElectronicInvoice() {
            const customerName = document.getElementById('customer-name').value;
            const customerTaxNumber = document.getElementById('customer-tax-number').value;
            const amount = parseFloat(document.getElementById('invoice-amount').value);
            const serviceType = document.getElementById('service-type').value;

            const vat = amount * 0.15;
            const total = amount + vat;

            // إنشاء رقم الفاتورة
            const invoiceNumber = 'EI-' + Date.now();

            // بيانات الفاتورة
            const invoice = {
                id: invoiceNumber,
                type: 'electronic',
                customerName: customerName,
                customerTaxNumber: customerTaxNumber,
                serviceType: serviceType,
                baseAmount: amount,
                vatAmount: vat,
                totalAmount: total,
                vatRate: 15,
                issueDate: new Date().toISOString(),
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 يوم
                status: 'issued',
                qrCode: generateQRCode(invoiceNumber, total),
                digitalSignature: generateDigitalSignature(invoiceNumber),
                zatcaCompliant: true
            };

            // حفظ الفاتورة
            saveElectronicInvoice(invoice);

            // إغلاق النافذة المنبثقة
            document.querySelector('.modal').remove();

            // عرض رسالة نجاح
            showSuccessMessage('تم إنشاء الفاتورة الإلكترونية بنجاح!', invoiceNumber);
        }

        // حفظ الفاتورة الإلكترونية
        function saveElectronicInvoice(invoice) {
            try {
                const invoices = JSON.parse(localStorage.getItem('electronicInvoices') || '[]');
                invoices.push(invoice);
                localStorage.setItem('electronicInvoices', JSON.stringify(invoices));

                // تحديث إحصائيات الامتثال
                updateComplianceStats();

                console.log('✅ تم حفظ الفاتورة الإلكترونية:', invoice.id);
            } catch (error) {
                console.error('❌ خطأ في حفظ الفاتورة:', error);
            }
        }

        // إنشاء رمز QR
        function generateQRCode(invoiceNumber, amount) {
            // محاكاة إنشاء رمز QR (في التطبيق الحقيقي يتم استخدام مكتبة QR)
            const qrData = {
                invoiceNumber: invoiceNumber,
                amount: amount,
                timestamp: new Date().toISOString(),
                companyTaxNumber: '300000000000003'
            };
            return btoa(JSON.stringify(qrData)); // ترميز base64
        }

        // إنشاء التوقيع الرقمي
        function generateDigitalSignature(invoiceNumber) {
            // محاكاة التوقيع الرقمي (في التطبيق الحقيقي يتم استخدام شهادات رقمية)
            const timestamp = Date.now();
            const signature = btoa(invoiceNumber + timestamp + 'digital_signature_key');
            return signature;
        }

        // تحديث إحصائيات الامتثال
        function updateComplianceStats() {
            const invoices = JSON.parse(localStorage.getItem('electronicInvoices') || '[]');
            const stats = {
                totalInvoices: invoices.length,
                thisMonth: invoices.filter(inv => {
                    const invoiceDate = new Date(inv.issueDate);
                    const now = new Date();
                    return invoiceDate.getMonth() === now.getMonth() &&
                           invoiceDate.getFullYear() === now.getFullYear();
                }).length,
                totalVAT: invoices.reduce((sum, inv) => sum + inv.vatAmount, 0),
                complianceRate: 100 // جميع الفواتير متوافقة
            };

            localStorage.setItem('complianceStats', JSON.stringify(stats));
        }

        // عرض رسالة نجاح
        function showSuccessMessage(message, invoiceNumber) {
            const alert = document.createElement('div');
            alert.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                color: #155724;
                padding: 20px;
                border-radius: 12px;
                border: 1px solid #c3e6cb;
                z-index: 2000;
                max-width: 400px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            `;

            alert.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                    <i class="fas fa-check-circle" style="font-size: 1.2rem;"></i>
                    <strong>نجح الإنشاء!</strong>
                </div>
                <div>${message}</div>
                <div style="margin-top: 10px; font-weight: 600;">رقم الفاتورة: ${invoiceNumber}</div>
                <button onclick="this.parentElement.remove()" style="position: absolute; top: 10px; left: 10px; background: none; border: none; font-size: 1.2rem; cursor: pointer;">×</button>
            `;

            document.body.appendChild(alert);

            // إزالة تلقائية بعد 5 ثوان
            setTimeout(() => {
                if (alert.parentElement) {
                    alert.remove();
                }
            }, 5000);
        }

        // فتح إدارة ضريبة القيمة المضافة
        function openVATManagement() {
            window.location.href = '#vat-management';
            // يمكن إضافة صفحة منفصلة لإدارة ضريبة القيمة المضافة
        }

        // فتح التقارير الضريبية
        function openTaxReports() {
            generateTaxReport();
        }

        // إنشاء تقرير ضريبي
        function generateTaxReport() {
            const invoices = JSON.parse(localStorage.getItem('electronicInvoices') || '[]');
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();

            const monthlyInvoices = invoices.filter(inv => {
                const invoiceDate = new Date(inv.issueDate);
                return invoiceDate.getMonth() === currentMonth &&
                       invoiceDate.getFullYear() === currentYear;
            });

            const report = {
                period: `${currentYear}-${(currentMonth + 1).toString().padStart(2, '0')}`,
                totalInvoices: monthlyInvoices.length,
                totalSales: monthlyInvoices.reduce((sum, inv) => sum + inv.baseAmount, 0),
                totalVAT: monthlyInvoices.reduce((sum, inv) => sum + inv.vatAmount, 0),
                totalAmount: monthlyInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0),
                generatedAt: new Date().toISOString()
            };

            // عرض التقرير
            showTaxReport(report);
        }

        // عرض التقرير الضريبي
        function showTaxReport(report) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 700px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #333; font-size: 1.4rem;">التقرير الضريبي الشهري</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #333; margin-bottom: 15px;">فترة التقرير: ${report.period}</h4>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #28a745;">${report.totalInvoices}</div>
                                <div style="color: #666;">عدد الفواتير</div>
                            </div>

                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #007bff;">${report.totalSales.toFixed(2)}</div>
                                <div style="color: #666;">إجمالي المبيعات (ريال)</div>
                            </div>

                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #ffc107;">${report.totalVAT.toFixed(2)}</div>
                                <div style="color: #666;">ضريبة القيمة المضافة (ريال)</div>
                            </div>

                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #dc3545;">${report.totalAmount.toFixed(2)}</div>
                                <div style="color: #666;">المبلغ الإجمالي (ريال)</div>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button class="btn btn-primary" onclick="downloadTaxReport()">
                            <i class="fas fa-download"></i>
                            تحميل التقرير
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            إغلاق
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // تحميل التقرير الضريبي
        function downloadTaxReport() {
            alert('سيتم تحميل التقرير الضريبي كملف PDF\n\n(هذه ميزة تحتاج لتطوير إضافي مع مكتبة PDF)');
        }

        // إعداد الربط مع منصة فاتورة
        function setupFatoorahIntegration() {
            alert('إعداد الربط مع منصة فاتورة\n\nهذه الميزة قيد التطوير وستكون متاحة في التحديث القادم.');
        }
    </script>
</body>
</html>
