<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قسم الفروع</title>
    <style>
        body {
            font-family: '<PERSON>', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .test-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .result {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #28a745;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            margin-bottom: 20px;
            padding: 12px 20px;
            border-radius: 25px;
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s;
            font-weight: 500;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(5px);
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html#branches" class="back-link">
        <span>🔙</span>
        العودة لقسم الفروع
    </a>

    <div class="container">
        <div class="header">
            <h1>🧪 اختبار قسم الفروع</h1>
            <p>اختبار وظائف إدارة الفروع والتحويلات</p>
        </div>

        <div style="text-align: center; margin-bottom: 30px;">
            <button class="test-btn" onclick="testBranchesData()">
                🏢 اختبار بيانات الفروع
            </button>
            
            <button class="test-btn" onclick="testTransfersData()">
                🔄 اختبار بيانات التحويلات
            </button>
            
            <button class="test-btn" onclick="testAddBranch()">
                ➕ اختبار إضافة فرع
            </button>
            
            <button class="test-btn" onclick="testAddTransfer()">
                🚚 اختبار إضافة تحويل
            </button>
            
            <button class="test-btn" onclick="clearAllData()">
                🗑️ مسح جميع البيانات
            </button>
        </div>

        <div id="test-results"></div>
    </div>

    <script>
        function showResult(title, content, type = 'success') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.style.borderLeftColor = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#ffc107';
            
            resultDiv.innerHTML = `
                <h3 style="color: #333; margin-bottom: 15px;">${title}</h3>
                <div style="color: #666;">${content}</div>
            `;
            
            resultsDiv.appendChild(resultDiv);
        }

        function testBranchesData() {
            const branches = JSON.parse(localStorage.getItem('branches') || '[]');
            
            if (branches.length === 0) {
                // إنشاء بيانات تجريبية
                const defaultBranches = [
                    {
                        id: 'BR001',
                        name: 'الفرع الرئيسي - الرياض',
                        address: 'شارع الملك فهد، الرياض',
                        phone: '+966501234567',
                        manager: 'أحمد محمد',
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        shipmentsCount: 150,
                        revenue: 45000
                    },
                    {
                        id: 'BR002',
                        name: 'فرع جدة',
                        address: 'شارع التحلية، جدة',
                        phone: '+966502345678',
                        manager: 'سارة أحمد',
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        shipmentsCount: 120,
                        revenue: 38000
                    },
                    {
                        id: 'BR003',
                        name: 'فرع الدمام',
                        address: 'شارع الملك عبدالعزيز، الدمام',
                        phone: '+966503456789',
                        manager: 'محمد علي',
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        shipmentsCount: 95,
                        revenue: 28500
                    }
                ];
                localStorage.setItem('branches', JSON.stringify(defaultBranches));
                showResult('✅ تم إنشاء بيانات الفروع', `تم إنشاء ${defaultBranches.length} فروع تجريبية`);
            } else {
                const activeBranches = branches.filter(b => b.isActive).length;
                const totalRevenue = branches.reduce((sum, b) => sum + (b.revenue || 0), 0);
                
                showResult('📊 بيانات الفروع الحالية', `
                    <strong>إجمالي الفروع:</strong> ${branches.length}<br>
                    <strong>فروع نشطة:</strong> ${activeBranches}<br>
                    <strong>إجمالي الإيرادات:</strong> ${totalRevenue.toLocaleString()} ريال<br>
                    <strong>أسماء الفروع:</strong> ${branches.map(b => b.name).join(', ')}
                `);
            }
        }

        function testTransfersData() {
            const transfers = JSON.parse(localStorage.getItem('branchTransfers') || '[]');
            
            if (transfers.length === 0) {
                const defaultTransfers = [
                    {
                        id: 'TR001',
                        fromBranch: 'BR001',
                        toBranch: 'BR002',
                        shipmentId: 'SH123456',
                        transferDate: new Date().toISOString(),
                        status: 'مكتمل',
                        notes: 'تحويل عادي'
                    },
                    {
                        id: 'TR002',
                        fromBranch: 'BR002',
                        toBranch: 'BR003',
                        shipmentId: 'SH123457',
                        transferDate: new Date().toISOString(),
                        status: 'قيد النقل',
                        notes: 'تحويل سريع'
                    }
                ];
                localStorage.setItem('branchTransfers', JSON.stringify(defaultTransfers));
                showResult('✅ تم إنشاء بيانات التحويلات', `تم إنشاء ${defaultTransfers.length} تحويلات تجريبية`);
            } else {
                const completedTransfers = transfers.filter(t => t.status === 'مكتمل').length;
                const pendingTransfers = transfers.filter(t => t.status === 'قيد النقل' || t.status === 'معلق').length;
                
                showResult('📊 بيانات التحويلات الحالية', `
                    <strong>إجمالي التحويلات:</strong> ${transfers.length}<br>
                    <strong>تحويلات مكتملة:</strong> ${completedTransfers}<br>
                    <strong>تحويلات معلقة:</strong> ${pendingTransfers}<br>
                    <strong>آخر تحويل:</strong> ${transfers.length > 0 ? transfers[transfers.length - 1].id : 'لا يوجد'}
                `);
            }
        }

        function testAddBranch() {
            const branches = JSON.parse(localStorage.getItem('branches') || '[]');
            const newBranch = {
                id: 'BR' + String(Date.now()).slice(-3),
                name: 'فرع اختبار - ' + new Date().toLocaleTimeString('ar-SA'),
                address: 'عنوان تجريبي',
                phone: '+966500000000',
                manager: 'مدير تجريبي',
                isActive: true,
                createdAt: new Date().toISOString(),
                shipmentsCount: 0,
                revenue: 0
            };
            
            branches.push(newBranch);
            localStorage.setItem('branches', JSON.stringify(branches));
            
            showResult('✅ تم إضافة فرع جديد', `
                <strong>رقم الفرع:</strong> ${newBranch.id}<br>
                <strong>اسم الفرع:</strong> ${newBranch.name}<br>
                <strong>إجمالي الفروع الآن:</strong> ${branches.length}
            `);
        }

        function testAddTransfer() {
            const branches = JSON.parse(localStorage.getItem('branches') || '[]');
            const transfers = JSON.parse(localStorage.getItem('branchTransfers') || '[]');
            
            if (branches.length < 2) {
                showResult('❌ خطأ في الاختبار', 'يجب وجود فرعين على الأقل لإجراء التحويل', 'error');
                return;
            }
            
            const newTransfer = {
                id: 'TR' + String(Date.now()).slice(-3),
                fromBranch: branches[0].id,
                toBranch: branches[1].id,
                shipmentId: 'SH' + String(Date.now()).slice(-6),
                transferDate: new Date().toISOString(),
                status: 'قيد النقل',
                notes: 'تحويل تجريبي'
            };
            
            transfers.push(newTransfer);
            localStorage.setItem('branchTransfers', JSON.stringify(transfers));
            
            showResult('✅ تم إضافة تحويل جديد', `
                <strong>رقم التحويل:</strong> ${newTransfer.id}<br>
                <strong>من:</strong> ${branches[0].name}<br>
                <strong>إلى:</strong> ${branches[1].name}<br>
                <strong>رقم الشحنة:</strong> ${newTransfer.shipmentId}<br>
                <strong>إجمالي التحويلات الآن:</strong> ${transfers.length}
            `);
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات الفروع والتحويلات؟')) {
                localStorage.removeItem('branches');
                localStorage.removeItem('branchTransfers');
                
                showResult('🗑️ تم مسح البيانات', 'تم مسح جميع بيانات الفروع والتحويلات بنجاح', 'warning');
                
                // مسح النتائج السابقة
                setTimeout(() => {
                    document.getElementById('test-results').innerHTML = '';
                }, 2000);
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 مرحباً بك في اختبار قسم الفروع', 'يمكنك الآن اختبار جميع وظائف إدارة الفروع والتحويلات');
        });
    </script>
</body>
</html>
