<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف نظام الموارد البشرية</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }
        
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار وظائف نظام الموارد البشرية</h1>
        <p>اختبار شامل لجميع الوظائف والنماذج في النظام</p>
        
        <div style="margin: 20px 0;">
            <a href="hr-management.html" class="btn btn-primary" target="_blank">🏢 فتح نظام الموارد البشرية</a>
            <button class="btn btn-success" onclick="testAllFunctions()">🚀 اختبار جميع الوظائف</button>
            <button class="btn btn-info" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
        
        <div class="test-section">
            <h3>👨‍💼 اختبار وظائف الموظفين</h3>
            <button class="btn btn-primary" onclick="testEmployeeFunctions()">اختبار الموظفين</button>
            <button class="btn btn-warning" onclick="testEmployeeModals()">اختبار النماذج</button>
            <div id="employeeResults"></div>
        </div>
        
        <div class="test-section">
            <h3>🚚 اختبار وظائف المناديب</h3>
            <button class="btn btn-primary" onclick="testDistributorFunctions()">اختبار المناديب</button>
            <button class="btn btn-warning" onclick="testDistributorModals()">اختبار النماذج</button>
            <div id="distributorResults"></div>
        </div>
        
        <div class="test-section">
            <h3>🏢 اختبار وظائف الأقسام</h3>
            <button class="btn btn-primary" onclick="testDepartmentFunctions()">اختبار الأقسام</button>
            <button class="btn btn-warning" onclick="testDepartmentModals()">اختبار النماذج</button>
            <div id="departmentResults"></div>
        </div>
        
        <div class="test-section">
            <h3>🚗 اختبار وظائف السيارات</h3>
            <button class="btn btn-primary" onclick="testVehicleFunctions()">اختبار السيارات</button>
            <button class="btn btn-warning" onclick="testVehicleModals()">اختبار النماذج</button>
            <div id="vehicleResults"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 نتائج الاختبار الشامل</h3>
            <div id="overallResults"></div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        let testResults = {};

        function showResult(containerId, testName, passed, message) {
            const container = document.getElementById(containerId);
            if (container) {
                const resultClass = passed ? 'success' : 'error';
                const icon = passed ? '✅' : '❌';
                container.innerHTML += `<div class="result ${resultClass}">${icon} ${testName}: ${message}</div>`;
            }
            testResults[testName] = { passed, message };
        }

        function clearResults() {
            const containers = ['employeeResults', 'distributorResults', 'departmentResults', 'vehicleResults', 'overallResults'];
            containers.forEach(id => {
                const container = document.getElementById(id);
                if (container) container.innerHTML = '';
            });
            testResults = {};
        }

        function testEmployeeFunctions() {
            // فتح نافذة النظام
            const hrWindow = window.open('hr-management.html', 'hrTest');
            
            setTimeout(() => {
                try {
                    // اختبار وجود الوظائف
                    const functions = ['addEmployee', 'editEmployee', 'deleteEmployee', 'viewEmployee', 'exportEmployees', 'searchEmployees'];
                    let passedTests = 0;
                    
                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('employeeResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('employeeResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });
                    
                    showResult('employeeResults', 'إجمالي وظائف الموظفين', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);
                    
                    // اختبار البيانات
                    if (hrWindow.employees && hrWindow.employees.length > 0) {
                        showResult('employeeResults', 'بيانات الموظفين', true, `تم العثور على ${hrWindow.employees.length} موظف`);
                    } else {
                        showResult('employeeResults', 'بيانات الموظفين', false, 'لا توجد بيانات موظفين');
                    }
                    
                    hrWindow.close();
                } catch (error) {
                    showResult('employeeResults', 'خطأ في الاختبار', false, error.message);
                }
            }, 3000);
        }

        function testEmployeeModals() {
            const hrWindow = window.open('hr-management.html', 'hrTest');
            
            setTimeout(() => {
                try {
                    // اختبار وجود النماذج
                    const modals = ['addEmployeeModal', 'editEmployeeModal'];
                    let passedTests = 0;
                    
                    modals.forEach(modalId => {
                        const modal = hrWindow.document.getElementById(modalId);
                        if (modal) {
                            showResult('employeeResults', `نموذج ${modalId}`, true, 'موجود في الصفحة');
                            passedTests++;
                        } else {
                            showResult('employeeResults', `نموذج ${modalId}`, false, 'غير موجود');
                        }
                    });
                    
                    // اختبار وظائف النماذج
                    if (typeof hrWindow.openModal === 'function' && typeof hrWindow.closeModal === 'function') {
                        showResult('employeeResults', 'وظائف النماذج', true, 'openModal و closeModal متاحتان');
                    } else {
                        showResult('employeeResults', 'وظائف النماذج', false, 'وظائف النماذج غير متاحة');
                    }
                    
                    showResult('employeeResults', 'إجمالي نماذج الموظفين', passedTests === modals.length, `${passedTests}/${modals.length} نموذج موجود`);
                    
                    hrWindow.close();
                } catch (error) {
                    showResult('employeeResults', 'خطأ في اختبار النماذج', false, error.message);
                }
            }, 3000);
        }

        function testDistributorFunctions() {
            const hrWindow = window.open('hr-management.html', 'hrTest');
            
            setTimeout(() => {
                try {
                    const functions = ['addDistributor', 'editDistributor', 'deleteDistributor', 'viewDistributor', 'exportDistributors', 'searchDistributors'];
                    let passedTests = 0;
                    
                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('distributorResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('distributorResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });
                    
                    showResult('distributorResults', 'إجمالي وظائف المناديب', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);
                    
                    if (hrWindow.distributors && hrWindow.distributors.length > 0) {
                        showResult('distributorResults', 'بيانات المناديب', true, `تم العثور على ${hrWindow.distributors.length} مندوب`);
                    } else {
                        showResult('distributorResults', 'بيانات المناديب', false, 'لا توجد بيانات مناديب');
                    }
                    
                    hrWindow.close();
                } catch (error) {
                    showResult('distributorResults', 'خطأ في الاختبار', false, error.message);
                }
            }, 3000);
        }

        function testDistributorModals() {
            const hrWindow = window.open('hr-management.html', 'hrTest');
            
            setTimeout(() => {
                try {
                    const modals = ['addDistributorModal', 'editDistributorModal'];
                    let passedTests = 0;
                    
                    modals.forEach(modalId => {
                        const modal = hrWindow.document.getElementById(modalId);
                        if (modal) {
                            showResult('distributorResults', `نموذج ${modalId}`, true, 'موجود في الصفحة');
                            passedTests++;
                        } else {
                            showResult('distributorResults', `نموذج ${modalId}`, false, 'غير موجود');
                        }
                    });
                    
                    showResult('distributorResults', 'إجمالي نماذج المناديب', passedTests === modals.length, `${passedTests}/${modals.length} نموذج موجود`);
                    
                    hrWindow.close();
                } catch (error) {
                    showResult('distributorResults', 'خطأ في اختبار النماذج', false, error.message);
                }
            }, 3000);
        }

        function testDepartmentFunctions() {
            const hrWindow = window.open('hr-management.html', 'hrTest');
            
            setTimeout(() => {
                try {
                    const functions = ['addDepartment', 'editDepartment', 'deleteDepartment', 'viewDepartment', 'exportDepartments', 'searchDepartments'];
                    let passedTests = 0;
                    
                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('departmentResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('departmentResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });
                    
                    showResult('departmentResults', 'إجمالي وظائف الأقسام', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);
                    
                    if (hrWindow.departments && hrWindow.departments.length > 0) {
                        showResult('departmentResults', 'بيانات الأقسام', true, `تم العثور على ${hrWindow.departments.length} قسم`);
                    } else {
                        showResult('departmentResults', 'بيانات الأقسام', false, 'لا توجد بيانات أقسام');
                    }
                    
                    hrWindow.close();
                } catch (error) {
                    showResult('departmentResults', 'خطأ في الاختبار', false, error.message);
                }
            }, 3000);
        }

        function testDepartmentModals() {
            const hrWindow = window.open('hr-management.html', 'hrTest');
            
            setTimeout(() => {
                try {
                    const modals = ['addDepartmentModal'];
                    let passedTests = 0;
                    
                    modals.forEach(modalId => {
                        const modal = hrWindow.document.getElementById(modalId);
                        if (modal) {
                            showResult('departmentResults', `نموذج ${modalId}`, true, 'موجود في الصفحة');
                            passedTests++;
                        } else {
                            showResult('departmentResults', `نموذج ${modalId}`, false, 'غير موجود');
                        }
                    });
                    
                    showResult('departmentResults', 'إجمالي نماذج الأقسام', passedTests === modals.length, `${passedTests}/${modals.length} نموذج موجود`);
                    
                    hrWindow.close();
                } catch (error) {
                    showResult('departmentResults', 'خطأ في اختبار النماذج', false, error.message);
                }
            }, 3000);
        }

        function testVehicleFunctions() {
            const hrWindow = window.open('hr-management.html', 'hrTest');
            
            setTimeout(() => {
                try {
                    const functions = ['addVehicle', 'editVehicle', 'deleteVehicle', 'viewVehicle', 'exportVehicles', 'searchVehicles'];
                    let passedTests = 0;
                    
                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('vehicleResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('vehicleResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });
                    
                    showResult('vehicleResults', 'إجمالي وظائف السيارات', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);
                    
                    if (hrWindow.vehicles && hrWindow.vehicles.length > 0) {
                        showResult('vehicleResults', 'بيانات السيارات', true, `تم العثور على ${hrWindow.vehicles.length} سيارة`);
                    } else {
                        showResult('vehicleResults', 'بيانات السيارات', false, 'لا توجد بيانات سيارات');
                    }
                    
                    hrWindow.close();
                } catch (error) {
                    showResult('vehicleResults', 'خطأ في الاختبار', false, error.message);
                }
            }, 3000);
        }

        function testVehicleModals() {
            const hrWindow = window.open('hr-management.html', 'hrTest');
            
            setTimeout(() => {
                try {
                    const modals = ['addVehicleModal'];
                    let passedTests = 0;
                    
                    modals.forEach(modalId => {
                        const modal = hrWindow.document.getElementById(modalId);
                        if (modal) {
                            showResult('vehicleResults', `نموذج ${modalId}`, true, 'موجود في الصفحة');
                            passedTests++;
                        } else {
                            showResult('vehicleResults', `نموذج ${modalId}`, false, 'غير موجود');
                        }
                    });
                    
                    showResult('vehicleResults', 'إجمالي نماذج السيارات', passedTests === modals.length, `${passedTests}/${modals.length} نموذج موجود`);
                    
                    hrWindow.close();
                } catch (error) {
                    showResult('vehicleResults', 'خطأ في اختبار النماذج', false, error.message);
                }
            }, 3000);
        }

        function testAllFunctions() {
            clearResults();
            
            testEmployeeFunctions();
            setTimeout(() => testEmployeeModals(), 1000);
            setTimeout(() => testDistributorFunctions(), 2000);
            setTimeout(() => testDistributorModals(), 3000);
            setTimeout(() => testDepartmentFunctions(), 4000);
            setTimeout(() => testDepartmentModals(), 5000);
            setTimeout(() => testVehicleFunctions(), 6000);
            setTimeout(() => testVehicleModals(), 7000);
            
            setTimeout(() => {
                const totalTests = Object.keys(testResults).length;
                const passedTests = Object.values(testResults).filter(result => result.passed).length;
                const percentage = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
                
                const overallContainer = document.getElementById('overallResults');
                if (overallContainer) {
                    overallContainer.innerHTML = `
                        <div class="result info">
                            <h4>📊 نتائج الاختبار الشامل</h4>
                            <p><strong>إجمالي الاختبارات:</strong> ${totalTests}</p>
                            <p><strong>الاختبارات الناجحة:</strong> ${passedTests}</p>
                            <p><strong>الاختبارات الفاشلة:</strong> ${totalTests - passedTests}</p>
                            <p><strong>نسبة النجاح:</strong> ${percentage}%</p>
                            <p><strong>الحالة العامة:</strong> ${percentage >= 90 ? '✅ ممتاز' : percentage >= 70 ? '⚠️ جيد' : '❌ يحتاج تحسين'}</p>
                        </div>
                    `;
                }
            }, 10000);
        }
    </script>
</body>
</html>
