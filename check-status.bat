@echo off
chcp 65001 >nul
echo.
echo ========================================
echo      فحص حالة نظام إدارة الشحنات
echo      Shipment Management Status Check
echo ========================================
echo.

:: Check Node.js
echo 🔍 فحص Node.js...
echo 🔍 Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo ❌ Node.js not installed
    set nodejs_ok=0
) else (
    for /f "tokens=*" %%i in ('node --version') do set node_version=%%i
    echo ✅ Node.js مثبت: %node_version%
    echo ✅ Node.js installed: %node_version%
    set nodejs_ok=1
)

:: Check npm
echo.
echo 🔍 فحص npm...
echo 🔍 Checking npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير مثبت
    echo ❌ npm not installed
    set npm_ok=0
) else (
    for /f "tokens=*" %%i in ('npm --version') do set npm_version=%%i
    echo ✅ npm مثبت: %npm_version%
    echo ✅ npm installed: %npm_version%
    set npm_ok=1
)

:: Check main dependencies
echo.
echo 🔍 فحص التبعيات الرئيسية...
echo 🔍 Checking main dependencies...
if exist "node_modules" (
    echo ✅ التبعيات الرئيسية مثبتة
    echo ✅ Main dependencies installed
    set main_deps=1
) else (
    echo ❌ التبعيات الرئيسية غير مثبتة
    echo ❌ Main dependencies not installed
    set main_deps=0
)

:: Check backend dependencies
echo.
echo 🔍 فحص تبعيات الخلفية...
echo 🔍 Checking backend dependencies...
if exist "backend\node_modules" (
    echo ✅ تبعيات الخلفية مثبتة
    echo ✅ Backend dependencies installed
    set backend_deps=1
) else (
    echo ❌ تبعيات الخلفية غير مثبتة
    echo ❌ Backend dependencies not installed
    set backend_deps=0
)

:: Check frontend dependencies
echo.
echo 🔍 فحص تبعيات الواجهة الأمامية...
echo 🔍 Checking frontend dependencies...
if exist "frontend\node_modules" (
    echo ✅ تبعيات الواجهة الأمامية مثبتة
    echo ✅ Frontend dependencies installed
    set frontend_deps=1
) else (
    echo ❌ تبعيات الواجهة الأمامية غير مثبتة
    echo ❌ Frontend dependencies not installed
    set frontend_deps=0
)

:: Check .env file
echo.
echo 🔍 فحص ملف البيئة...
echo 🔍 Checking environment file...
if exist "backend\.env" (
    echo ✅ ملف .env موجود
    echo ✅ .env file exists
    
    :: Check if DATABASE_URL is set
    findstr /C:"DATABASE_URL=" backend\.env >nul
    if %errorlevel% equ 0 (
        echo ✅ DATABASE_URL محدد
        echo ✅ DATABASE_URL is set
        set db_url=1
    ) else (
        echo ❌ DATABASE_URL غير محدد
        echo ❌ DATABASE_URL not set
        set db_url=0
    )
    
    :: Check if JWT_SECRET is set
    findstr /C:"JWT_SECRET=" backend\.env >nul
    if %errorlevel% equ 0 (
        echo ✅ JWT_SECRET محدد
        echo ✅ JWT_SECRET is set
        set jwt_secret=1
    ) else (
        echo ❌ JWT_SECRET غير محدد
        echo ❌ JWT_SECRET not set
        set jwt_secret=0
    )
    
    set env_file=1
) else (
    echo ❌ ملف .env غير موجود
    echo ❌ .env file not found
    set env_file=0
    set db_url=0
    set jwt_secret=0
)

:: Summary
echo.
echo ========================================
echo              ملخص الحالة
echo              Status Summary
echo ========================================
echo.

if %nodejs_ok%==1 if %npm_ok%==1 if %main_deps%==1 if %backend_deps%==1 if %frontend_deps%==1 if %env_file%==1 if %db_url%==1 if %jwt_secret%==1 (
    echo 🎉 النظام جاهز للتشغيل!
    echo 🎉 System ready to run!
    echo.
    echo يمكنك الآن تشغيل:
    echo You can now run:
    echo.
    echo 1. npm run setup:db  ^(إعداد قاعدة البيانات^)
    echo 2. start.bat         ^(تشغيل النظام^)
    echo.
    set system_ready=1
) else (
    echo ⚠️ النظام غير جاهز للتشغيل
    echo ⚠️ System not ready to run
    echo.
    echo المشاكل المكتشفة:
    echo Issues found:
    echo.
    
    if %nodejs_ok%==0 echo - Node.js غير مثبت ^| Node.js not installed
    if %npm_ok%==0 echo - npm غير مثبت ^| npm not installed
    if %main_deps%==0 echo - التبعيات الرئيسية غير مثبتة ^| Main dependencies not installed
    if %backend_deps%==0 echo - تبعيات الخلفية غير مثبتة ^| Backend dependencies not installed
    if %frontend_deps%==0 echo - تبعيات الواجهة الأمامية غير مثبتة ^| Frontend dependencies not installed
    if %env_file%==0 echo - ملف .env غير موجود ^| .env file missing
    if %db_url%==0 echo - DATABASE_URL غير محدد ^| DATABASE_URL not set
    if %jwt_secret%==0 echo - JWT_SECRET غير محدد ^| JWT_SECRET not set
    
    echo.
    echo الحلول المقترحة:
    echo Suggested solutions:
    echo.
    
    if %nodejs_ok%==0 (
        echo 1. ثبت Node.js من: https://nodejs.org/
        echo    Install Node.js from: https://nodejs.org/
    )
    
    if %main_deps%==0 (
        echo 2. شغل: quick-setup.bat
        echo    Run: quick-setup.bat
    )
    
    if %env_file%==0 (
        echo 3. شغل: quick-setup.bat لإنشاء ملف .env
        echo    Run: quick-setup.bat to create .env file
    )
    
    if %db_url%==0 (
        echo 4. عدل backend\.env وأضف DATABASE_URL
        echo    Edit backend\.env and add DATABASE_URL
    )
    
    if %jwt_secret%==0 (
        echo 5. عدل backend\.env وأضف JWT_SECRET
        echo    Edit backend\.env and add JWT_SECRET
    )
    
    set system_ready=0
)

echo.
echo ========================================
echo.

if %system_ready%==1 (
    echo هل تريد تشغيل النظام الآن؟
    echo Do you want to start the system now?
    echo.
    set /p start_now="(y/n): "
    
    if /i "!start_now!"=="y" (
        echo.
        echo 🚀 بدء تشغيل النظام...
        echo 🚀 Starting system...
        start.bat
    )
) else (
    echo هل تريد تشغيل الإعداد السريع؟
    echo Do you want to run quick setup?
    echo.
    set /p run_setup="(y/n): "
    
    if /i "!run_setup!"=="y" (
        echo.
        echo 🔧 تشغيل الإعداد السريع...
        echo 🔧 Running quick setup...
        quick-setup.bat
    )
)

echo.
pause
