<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المناديب والسائقين - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: var(--font-arabic-display);
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-purple {
            background: #6f42c1;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total {
            border-left-color: #6f42c1;
        }

        .stat-card.active {
            border-left-color: #28a745;
        }

        .stat-card.inactive {
            border-left-color: #dc3545;
        }

        .stat-card.performance {
            border-left-color: #ffc107;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input,
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            min-width: 200px;
        }

        .search-input:focus,
        .filter-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .distributors-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .status-inactive {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .status-busy {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .rating {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stars {
            color: #ffc107;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
        }

        .form-input {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-data h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input,
            .filter-select {
                min-width: auto;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                justify-content: center;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>👥</span>
                <span>إدارة المناديب والسائقين</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="distributors-management.html" class="active">المناديب</a>
                <a href="commission-management.html">العمولات</a>
                <a href="financial-system.html">النظام المالي</a>
                <a href="shipments.html">الشحنات</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">👥 إدارة المناديب والسائقين</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="openAddModal()">
                    ➕ إضافة مندوب جديد
                </button>
                <button class="btn" onclick="loadDistributors()">
                    🔄 تحديث القائمة
                </button>
                <a href="commission-management.html" class="btn btn-purple">
                    🤝 إدارة العمولات
                </a>
            </div>
        </div>

        <!-- إحصائيات المناديب -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="card-icon">👥</div>
                <div class="card-amount" id="totalDistributors">0</div>
                <div class="card-label">إجمالي المناديب</div>
            </div>

            <div class="stat-card active">
                <div class="card-icon">✅</div>
                <div class="card-amount" id="activeDistributors">0</div>
                <div class="card-label">مناديب نشطين</div>
            </div>

            <div class="stat-card inactive">
                <div class="card-icon">❌</div>
                <div class="card-amount" id="inactiveDistributors">0</div>
                <div class="card-label">مناديب غير نشطين</div>
            </div>

            <div class="stat-card performance">
                <div class="card-icon">⭐</div>
                <div class="card-amount" id="averageRating">0.0</div>
                <div class="card-label">متوسط التقييم</div>
            </div>
        </div>

        <!-- أدوات التحكم والبحث -->
        <div class="controls">
            <input type="text" id="searchInput" class="search-input" placeholder="🔍 البحث في المناديب...">

            <select id="statusFilter" class="filter-select">
                <option value="">جميع الحالات</option>
                <option value="متاح">متاح</option>
                <option value="مشغول">مشغول</option>
                <option value="غير متاح">غير متاح</option>
            </select>

            <select id="areaFilter" class="filter-select">
                <option value="">جميع المناطق</option>
                <option value="الرياض">الرياض</option>
                <option value="جدة">جدة</option>
                <option value="الدمام">الدمام</option>
                <option value="الكويت">الكويت</option>
            </select>

            <button class="btn" onclick="clearFilters()">مسح الفلاتر</button>
        </div>

        <!-- جدول المناديب -->
        <div class="distributors-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>رقم الجوال</th>
                        <th>المنطقة</th>
                        <th>نوع المركبة</th>
                        <th>التقييم</th>
                        <th>الحالة</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="distributorsTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>

            <!-- رسالة عدم وجود بيانات -->
            <div id="noDataMessage" class="no-data" style="display: none;">
                <h3>👥 لا توجد مناديب</h3>
                <p>لم يتم العثور على أي مناديب. ابدأ بإضافة مندوب جديد.</p>
                <button class="btn btn-success" onclick="openAddModal()" style="margin-top: 15px;">
                    ➕ إضافة مندوب جديد
                </button>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل المندوب -->
    <div id="distributorModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة مندوب جديد</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>

            <div class="modal-body">
                <form id="distributorForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-input" id="distributorName" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">رقم الجوال *</label>
                            <input type="tel" class="form-input" id="distributorPhone" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-input" id="distributorEmail">
                        </div>

                        <div class="form-group">
                            <label class="form-label">المنطقة *</label>
                            <select class="form-input" id="distributorArea" required>
                                <option value="">اختر المنطقة</option>
                                <option value="الرياض">الرياض</option>
                                <option value="جدة">جدة</option>
                                <option value="الدمام">الدمام</option>
                                <option value="مكة المكرمة">مكة المكرمة</option>
                                <option value="المدينة المنورة">المدينة المنورة</option>
                                <option value="الكويت">الكويت</option>
                                <option value="حولي">حولي</option>
                                <option value="الفروانية">الفروانية</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">نوع المركبة</label>
                            <select class="form-input" id="vehicleType">
                                <option value="">اختر نوع المركبة</option>
                                <option value="سيارة صغيرة">سيارة صغيرة</option>
                                <option value="سيارة متوسطة">سيارة متوسطة</option>
                                <option value="شاحنة صغيرة">شاحنة صغيرة</option>
                                <option value="شاحنة كبيرة">شاحنة كبيرة</option>
                                <option value="دراجة نارية">دراجة نارية</option>
                                <option value="دراجة هوائية">دراجة هوائية</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">رقم لوحة المركبة</label>
                            <input type="text" class="form-input" id="vehiclePlate">
                        </div>

                        <div class="form-group">
                            <label class="form-label">الحالة</label>
                            <select class="form-input" id="distributorStatus">
                                <option value="متاح">متاح</option>
                                <option value="مشغول">مشغول</option>
                                <option value="غير متاح">غير متاح</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">التقييم (من 5)</label>
                            <input type="number" class="form-input" id="distributorRating" min="0" max="5" step="0.1" value="5.0">
                        </div>

                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-input" id="distributorNotes" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn" onclick="closeModal()">إلغاء</button>
                        <button type="submit" class="btn btn-success">💾 حفظ المندوب</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/database-simple.js"></script>
    <script>
        let currentEditId = null;

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('👥 تحميل صفحة إدارة المناديب...');

            try {
                loadDistributors();
                setupEventListeners();
                loadStats();

                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('distributorForm').addEventListener('submit', saveDistributor);
            document.getElementById('searchInput').addEventListener('input', filterDistributors);
            document.getElementById('statusFilter').addEventListener('change', filterDistributors);
            document.getElementById('areaFilter').addEventListener('change', filterDistributors);
        }

        // تحميل وعرض المناديب
        function loadDistributors() {
            try {
                const distributors = db.getAllDistributors();
                displayDistributors(distributors);
                console.log('👥 تم تحميل', distributors.length, 'مندوب');
            } catch (error) {
                console.error('❌ خطأ في تحميل المناديب:', error);
                alert('خطأ في تحميل المناديب: ' + error.message);
            }
        }

        // عرض المناديب في الجدول
        function displayDistributors(distributors) {
            const tbody = document.getElementById('distributorsTableBody');
            const noDataMessage = document.getElementById('noDataMessage');

            if (distributors.length === 0) {
                tbody.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }

            noDataMessage.style.display = 'none';

            tbody.innerHTML = distributors.map(distributor =>
                `<tr>
                    <td><strong>${distributor.name}</strong></td>
                    <td>${distributor.phone}</td>
                    <td>${distributor.area || 'غير محدد'}</td>
                    <td>${distributor.vehicleType || 'غير محدد'}</td>
                    <td>
                        <div class="rating">
                            <span class="stars">${generateStars(distributor.rating || 5)}</span>
                            <span>${(distributor.rating || 5).toFixed(1)}</span>
                        </div>
                    </td>
                    <td><span class="status-badge status-${getStatusClass(distributor.isAvailable)}">${getStatusText(distributor.isAvailable)}</span></td>
                    <td>${formatDate(distributor.joinDate)}</td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-small" onclick="viewDistributor('${distributor.id}')">عرض</button>
                            <button class="btn btn-small btn-warning" onclick="editDistributor('${distributor.id}')">تعديل</button>
                            <button class="btn btn-small btn-info" onclick="toggleStatus('${distributor.id}')">تغيير الحالة</button>
                            <button class="btn btn-small btn-danger" onclick="deleteDistributor('${distributor.id}')">حذف</button>
                        </div>
                    </td>
                </tr>`
            ).join('');
        }

        // تحميل الإحصائيات
        function loadStats() {
            try {
                const distributors = db.getAllDistributors();

                const totalCount = distributors.length;
                const activeCount = distributors.filter(d => d.isAvailable).length;
                const inactiveCount = totalCount - activeCount;
                const averageRating = distributors.length > 0 ?
                    distributors.reduce((sum, d) => sum + (d.rating || 5), 0) / distributors.length : 0;

                document.getElementById('totalDistributors').textContent = totalCount;
                document.getElementById('activeDistributors').textContent = activeCount;
                document.getElementById('inactiveDistributors').textContent = inactiveCount;
                document.getElementById('averageRating').textContent = averageRating.toFixed(1);

            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات:', error);
            }
        }

        // فلترة المناديب
        function filterDistributors() {
            try {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;
                const areaFilter = document.getElementById('areaFilter').value;

                const allDistributors = db.getAllDistributors();

                const filteredDistributors = allDistributors.filter(distributor => {
                    const matchesSearch = !searchTerm ||
                        distributor.name.toLowerCase().includes(searchTerm) ||
                        distributor.phone.includes(searchTerm) ||
                        (distributor.email && distributor.email.toLowerCase().includes(searchTerm));

                    const matchesStatus = !statusFilter || getStatusText(distributor.isAvailable) === statusFilter;
                    const matchesArea = !areaFilter || distributor.area === areaFilter;

                    return matchesSearch && matchesStatus && matchesArea;
                });

                displayDistributors(filteredDistributors);
            } catch (error) {
                console.error('❌ خطأ في فلترة المناديب:', error);
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('areaFilter').value = '';
            loadDistributors();
        }

        // فتح نافذة إضافة مندوب
        function openAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = 'إضافة مندوب جديد';
            document.getElementById('distributorForm').reset();
            document.getElementById('distributorRating').value = '5.0';
            document.getElementById('distributorModal').style.display = 'block';
        }

        // تعديل مندوب
        function editDistributor(id) {
            console.log('✏️ تعديل المندوب:', id);

            try {
                const distributor = db.getDistributorById(id);
                if (!distributor) {
                    alert('لم يتم العثور على المندوب');
                    return;
                }

                currentEditId = id;
                document.getElementById('modalTitle').textContent = 'تعديل المندوب - ' + distributor.name;

                // ملء النموذج بالبيانات الحالية
                document.getElementById('distributorName').value = distributor.name || '';
                document.getElementById('distributorPhone').value = distributor.phone || '';
                document.getElementById('distributorEmail').value = distributor.email || '';
                document.getElementById('distributorArea').value = distributor.area || '';
                document.getElementById('vehicleType').value = distributor.vehicleType || '';
                document.getElementById('vehiclePlate').value = distributor.vehiclePlate || '';
                document.getElementById('distributorStatus').value = getStatusText(distributor.isAvailable);
                document.getElementById('distributorRating').value = distributor.rating || 5;
                document.getElementById('distributorNotes').value = distributor.notes || '';

                document.getElementById('distributorModal').style.display = 'block';

                console.log('✅ تم ملء النموذج للتعديل');
            } catch (error) {
                console.error('❌ خطأ في تعديل المندوب:', error);
                alert('خطأ في تعديل المندوب: ' + error.message);
            }
        }

        // عرض تفاصيل المندوب
        function viewDistributor(id) {
            try {
                const distributor = db.getDistributorById(id);
                if (!distributor) {
                    alert('لم يتم العثور على المندوب');
                    return;
                }

                alert(`تفاصيل المندوب ${distributor.name}:\n\n` +
                      `الاسم: ${distributor.name}\n` +
                      `رقم الجوال: ${distributor.phone}\n` +
                      `البريد الإلكتروني: ${distributor.email || 'غير محدد'}\n` +
                      `المنطقة: ${distributor.area || 'غير محدد'}\n` +
                      `نوع المركبة: ${distributor.vehicleType || 'غير محدد'}\n` +
                      `رقم اللوحة: ${distributor.vehiclePlate || 'غير محدد'}\n` +
                      `التقييم: ${(distributor.rating || 5).toFixed(1)} من 5\n` +
                      `الحالة: ${getStatusText(distributor.isAvailable)}\n` +
                      `تاريخ التسجيل: ${formatDate(distributor.joinDate)}\n` +
                      `ملاحظات: ${distributor.notes || 'لا توجد ملاحظات'}`);
            } catch (error) {
                console.error('❌ خطأ في عرض المندوب:', error);
                alert('خطأ في عرض المندوب: ' + error.message);
            }
        }

        // تغيير حالة المندوب
        function toggleStatus(id) {
            try {
                const distributor = db.getDistributorById(id);
                if (!distributor) {
                    alert('لم يتم العثور على المندوب');
                    return;
                }

                const newStatus = !distributor.isAvailable;
                const statusText = newStatus ? 'متاح' : 'غير متاح';

                if (confirm(`هل تريد تغيير حالة المندوب ${distributor.name} إلى "${statusText}"؟`)) {
                    const updatedDistributor = db.updateDistributor(id, { isAvailable: newStatus });

                    if (updatedDistributor) {
                        alert(`تم تغيير حالة المندوب إلى "${statusText}" بنجاح`);
                        loadDistributors();
                        loadStats();
                    } else {
                        alert('خطأ في تغيير حالة المندوب');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في تغيير حالة المندوب:', error);
                alert('خطأ في تغيير حالة المندوب: ' + error.message);
            }
        }

        // حذف مندوب
        function deleteDistributor(id) {
            try {
                const distributor = db.getDistributorById(id);
                if (!distributor) {
                    alert('لم يتم العثور على المندوب');
                    return;
                }

                if (confirm(`هل أنت متأكد من حذف المندوب ${distributor.name}؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
                    const result = db.deleteDistributor(id);
                    if (result) {
                        alert('تم حذف المندوب بنجاح');
                        loadDistributors();
                        loadStats();
                    } else {
                        alert('خطأ في حذف المندوب');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في حذف المندوب:', error);
                alert('خطأ في حذف المندوب: ' + error.message);
            }
        }

        // حفظ المندوب
        function saveDistributor(e) {
            e.preventDefault();

            console.log('💾 حفظ المندوب - currentEditId:', currentEditId);

            try {
                const formData = {
                    name: document.getElementById('distributorName').value.trim(),
                    phone: document.getElementById('distributorPhone').value.trim(),
                    email: document.getElementById('distributorEmail').value.trim(),
                    area: document.getElementById('distributorArea').value,
                    vehicleType: document.getElementById('vehicleType').value,
                    vehiclePlate: document.getElementById('vehiclePlate').value.trim(),
                    isAvailable: document.getElementById('distributorStatus').value === 'متاح',
                    rating: parseFloat(document.getElementById('distributorRating').value) || 5,
                    notes: document.getElementById('distributorNotes').value.trim()
                };

                // التحقق من البيانات المطلوبة
                if (!formData.name || !formData.phone || !formData.area) {
                    alert('يرجى ملء الحقول المطلوبة (الاسم، رقم الجوال، المنطقة)');
                    return;
                }

                let savedDistributor;
                if (currentEditId) {
                    // تحديث مندوب موجود
                    savedDistributor = db.updateDistributor(currentEditId, formData);
                    if (savedDistributor) {
                        alert('تم تحديث بيانات المندوب بنجاح');
                    } else {
                        alert('خطأ في تحديث بيانات المندوب');
                        return;
                    }
                } else {
                    // إضافة مندوب جديد
                    savedDistributor = db.addDistributor(formData);
                    if (savedDistributor) {
                        alert(`تم إضافة المندوب بنجاح\nرقم المندوب: ${savedDistributor.id}`);
                    } else {
                        alert('خطأ في إضافة المندوب');
                        return;
                    }
                }

                // إعادة تحميل البيانات
                loadDistributors();
                loadStats();
                closeModal();

                console.log('✅ تم حفظ المندوب بنجاح');

            } catch (error) {
                console.error('❌ خطأ في حفظ المندوب:', error);
                alert('خطأ في حفظ المندوب: ' + error.message);
            }
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('distributorModal').style.display = 'none';
            currentEditId = null;
        }

        // دوال مساعدة
        function getStatusClass(isAvailable) {
            return isAvailable ? 'active' : 'inactive';
        }

        function getStatusText(isAvailable) {
            return isAvailable ? 'متاح' : 'غير متاح';
        }

        function generateStars(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 >= 0.5;
            let stars = '';

            for (let i = 0; i < fullStars; i++) {
                stars += '★';
            }

            if (hasHalfStar) {
                stars += '☆';
            }

            const emptyStars = 5 - Math.ceil(rating);
            for (let i = 0; i < emptyStars; i++) {
                stars += '☆';
            }

            return stars;
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('distributorModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
