<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظرة عامة على الإعدادات والصلاحيات - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .overview-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
        }
        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .card-icon {
            font-size: 2.5rem;
            margin-left: 15px;
        }
        .card-title {
            font-size: 1.5rem;
            color: #2c3e50;
            margin: 0;
        }
        .card-description {
            color: #6c757d;
            margin: 10px 0 20px 0;
            line-height: 1.6;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-icon {
            margin-left: 10px;
            color: #28a745;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 8px 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .btn-info { background: #17a2b8; }
        .btn-info:hover { background: #138496; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
        .stats-section {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .quick-actions {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-top: 30px;
        }
        .quick-actions h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ نظرة عامة على الإعدادات والصلاحيات</h1>
            <p>نظام شامل ومتكامل لإدارة جميع جوانب النظام</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-section">
            <h2>📊 إحصائيات النظام</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalPages">18+</div>
                    <div class="stat-label">صفحات النظام</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div class="stat-label">أدوار المستخدمين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">34</div>
                    <div class="stat-label">صلاحية متاحة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">SF Pro AR Display</div>
                </div>
            </div>
        </div>

        <!-- بطاقات النظرة العامة -->
        <div class="overview-grid">
            <!-- الإعدادات المتقدمة -->
            <div class="overview-card">
                <div class="card-header">
                    <span class="card-icon">🔐</span>
                    <h3 class="card-title">الإعدادات المتقدمة</h3>
                </div>
                <p class="card-description">نظام شامل لإدارة الصلاحيات والأدوار مع واجهة متقدمة</p>
                <ul class="feature-list">
                    <li><span class="feature-icon">✅</span> إدارة 6 أدوار مختلفة</li>
                    <li><span class="feature-icon">✅</span> 34 صلاحية تفصيلية</li>
                    <li><span class="feature-icon">✅</span> مصفوفة صلاحيات تفاعلية</li>
                    <li><span class="feature-icon">✅</span> نسخ احتياطي واستعادة</li>
                </ul>
                <div style="margin-top: 20px;">
                    <a href="advanced-settings.html" class="btn">🔧 الإعدادات المتقدمة</a>
                    <a href="test-advanced-permissions.html" class="btn btn-secondary">🧪 اختبار النظام</a>
                </div>
            </div>

            <!-- إدارة المستخدمين -->
            <div class="overview-card">
                <div class="card-header">
                    <span class="card-icon">👥</span>
                    <h3 class="card-title">إدارة المستخدمين</h3>
                </div>
                <p class="card-description">إدارة كاملة للمستخدمين مع إمكانيات التعديل والإضافة</p>
                <ul class="feature-list">
                    <li><span class="feature-icon">✅</span> إضافة مستخدمين جدد</li>
                    <li><span class="feature-icon">✅</span> تعديل البيانات والصلاحيات</li>
                    <li><span class="feature-icon">✅</span> إدارة الأدوار والحالات</li>
                    <li><span class="feature-icon">✅</span> تصدير واستيراد البيانات</li>
                </ul>
                <div style="margin-top: 20px;">
                    <a href="user-management.html" class="btn btn-success">👥 قائمة المستخدمين</a>
                    <a href="user-permissions-advanced.html" class="btn btn-info">🔐 الصلاحيات المتقدمة</a>
                </div>
            </div>

            <!-- الموارد البشرية -->
            <div class="overview-card">
                <div class="card-header">
                    <span class="card-icon">🏢</span>
                    <h3 class="card-title">الموارد البشرية</h3>
                </div>
                <p class="card-description">نظام متكامل لإدارة الموظفين والمناديب والسائقين</p>
                <ul class="feature-list">
                    <li><span class="feature-icon">✅</span> إدارة الموظفين والتعديل</li>
                    <li><span class="feature-icon">✅</span> إدارة المناديب والسائقين</li>
                    <li><span class="feature-icon">✅</span> تتبع الحضور والانصراف</li>
                    <li><span class="feature-icon">✅</span> إدارة الرواتب والمكافآت</li>
                </ul>
                <div style="margin-top: 20px;">
                    <a href="hr-management.html" class="btn btn-warning">👨‍💼 إدارة الموظفين</a>
                    <a href="distributors-management.html" class="btn btn-info">🚚 إدارة المناديب</a>
                </div>
            </div>

            <!-- إدارة الفروع -->
            <div class="overview-card">
                <div class="card-header">
                    <span class="card-icon">🏪</span>
                    <h3 class="card-title">إدارة الفروع</h3>
                </div>
                <p class="card-description">إدارة شاملة للفروع والتحويلات بين الفروع</p>
                <ul class="feature-list">
                    <li><span class="feature-icon">✅</span> إدارة الفروع والمواقع</li>
                    <li><span class="feature-icon">✅</span> تحويلات بين الفروع</li>
                    <li><span class="feature-icon">✅</span> تتبع المخزون</li>
                    <li><span class="feature-icon">✅</span> تقارير الأداء</li>
                </ul>
                <div style="margin-top: 20px;">
                    <a href="branches-management.html" class="btn btn-secondary">🏢 إدارة الفروع</a>
                    <a href="branch-transfers.html" class="btn">🔄 التحويلات</a>
                </div>
            </div>

            <!-- النظام المالي -->
            <div class="overview-card">
                <div class="card-header">
                    <span class="card-icon">💰</span>
                    <h3 class="card-title">النظام المالي</h3>
                </div>
                <p class="card-description">إدارة متكاملة للنظام المالي والتسعير</p>
                <ul class="feature-list">
                    <li><span class="feature-icon">✅</span> إدارة التسعير والعمولات</li>
                    <li><span class="feature-icon">✅</span> محول العملات</li>
                    <li><span class="feature-icon">✅</span> التقارير المالية</li>
                    <li><span class="feature-icon">✅</span> إدارة المدفوعات</li>
                </ul>
                <div style="margin-top: 20px;">
                    <a href="financial-system.html" class="btn btn-warning">💳 النظام المالي</a>
                    <a href="pricing-management.html" class="btn btn-info">💲 التسعير</a>
                </div>
            </div>

            <!-- أدوات النظام -->
            <div class="overview-card">
                <div class="card-header">
                    <span class="card-icon">🛠️</span>
                    <h3 class="card-title">أدوات النظام</h3>
                </div>
                <p class="card-description">أدوات متقدمة لصيانة وإدارة النظام</p>
                <ul class="feature-list">
                    <li><span class="feature-icon">✅</span> حالة النظام والمراقبة</li>
                    <li><span class="feature-icon">✅</span> إدارة الخطوط والتصميم</li>
                    <li><span class="feature-icon">✅</span> النسخ الاحتياطي</li>
                    <li><span class="feature-icon">✅</span> سجلات النظام</li>
                </ul>
                <div style="margin-top: 20px;">
                    <a href="system-status.html" class="btn btn-success">📊 حالة النظام</a>
                    <a href="update-all-fonts.html" class="btn btn-info">🔤 إدارة الخطوط</a>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="quick-actions">
            <h3>🚀 إجراءات سريعة</h3>
            <div class="actions-grid">
                <a href="main-dashboard.html" class="btn">🏠 العودة للوحة التحكم</a>
                <a href="advanced-settings.html" class="btn">⚙️ الإعدادات المتقدمة</a>
                <a href="user-management.html" class="btn btn-success">👥 إدارة المستخدمين</a>
                <a href="hr-management.html" class="btn btn-warning">🏢 الموارد البشرية</a>
                <a href="test-advanced-permissions.html" class="btn btn-info">🧪 اختبار الصلاحيات</a>
                <a href="system-status.html" class="btn btn-secondary">📊 حالة النظام</a>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم تحميل صفحة نظرة عامة على الإعدادات');
            
            // تحديث الإحصائيات
            updateStats();
        });

        function updateStats() {
            // محاكاة تحديث الإحصائيات
            const totalPagesElement = document.getElementById('totalPages');
            if (totalPagesElement) {
                totalPagesElement.textContent = '18+';
            }
        }
    </script>
</body>
</html>
