/* Apple SF Pro Arabic Fonts */
/* خطوط آبل SF Pro العربية */

/* استيراد خطوط Google كبديل */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* SF Pro Arabic Display - Primary Font */
@font-face {
    font-family: 'SF Pro Arabic Display';
    src: url('https://developer.apple.com/fonts/SF-Pro-Arabic-Display-Semibold.woff2') format('woff2'),
         url('https://developer.apple.com/fonts/SF-Pro-Arabic-Display-Semibold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SF Pro Arabic Display';
    src: url('https://developer.apple.com/fonts/SF-Pro-Arabic-Display-Bold.woff2') format('woff2'),
         url('https://developer.apple.com/fonts/SF-Pro-Arabic-Display-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SF Pro Arabic Display';
    src: url('https://developer.apple.com/fonts/SF-Pro-Arabic-Display-Regular.woff2') format('woff2'),
         url('https://developer.apple.com/fonts/SF-Pro-Arabic-Display-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SF Pro Arabic Display';
    src: url('https://developer.apple.com/fonts/SF-Pro-Arabic-Display-Medium.woff2') format('woff2'),
         url('https://developer.apple.com/fonts/SF-Pro-Arabic-Display-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

/* SF Pro Text for English */
@font-face {
    font-family: 'SF Pro Text';
    src: url('https://developer.apple.com/fonts/SF-Pro-Text-Semibold.woff2') format('woff2'),
         url('https://developer.apple.com/fonts/SF-Pro-Text-Semibold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SF Pro Text';
    src: url('https://developer.apple.com/fonts/SF-Pro-Text-Bold.woff2') format('woff2'),
         url('https://developer.apple.com/fonts/SF-Pro-Text-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SF Pro Text';
    src: url('https://developer.apple.com/fonts/SF-Pro-Text-Regular.woff2') format('woff2'),
         url('https://developer.apple.com/fonts/SF-Pro-Text-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SF Pro Text';
    src: url('https://developer.apple.com/fonts/SF-Pro-Text-Medium.woff2') format('woff2'),
         url('https://developer.apple.com/fonts/SF-Pro-Text-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

/* Font Variables */
:root {
    /* Arabic Fonts - أفضل الخطوط العربية المتاحة */
    --font-arabic-display:
        'SF Pro Arabic Display',
        'Cairo',
        'Tajawal',
        'SF Arabic',
        'Segoe UI Historic',
        'Segoe UI',
        'Tahoma',
        'Microsoft Sans Serif',
        'Arial Unicode MS',
        'Lucida Grande',
        sans-serif;

    --font-arabic-text:
        'SF Pro Arabic Text',
        'Cairo',
        'Tajawal',
        'SF Arabic',
        'Segoe UI',
        'Tahoma',
        'Microsoft Sans Serif',
        'Arial Unicode MS',
        sans-serif;

    /* English Fonts - خطوط إنجليزية مشابهة لآبل */
    --font-english-display:
        'SF Pro Display',
        'SF Pro Text',
        'Inter',
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        sans-serif;

    --font-english-text:
        'SF Pro Text',
        'Inter',
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        sans-serif;

    /* Fallback Fonts */
    --font-fallback: 'Inter', 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    
    /* Font Weights */
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.7;
    
    /* Letter Spacing */
    --letter-spacing-tight: -0.02em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.02em;
}

/* Base Font Styles */
body {
    font-family: var(--font-arabic-display);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Arabic Text Styling */
[lang="ar"], [dir="rtl"], .arabic {
    font-family: var(--font-arabic-display);
    line-height: var(--line-height-relaxed);
    letter-spacing: var(--letter-spacing-wide);
}

/* English Text Styling */
[lang="en"], [dir="ltr"], .english {
    font-family: var(--font-english-display);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-tight);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-arabic-display);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
}

[lang="en"] h1, [lang="en"] h2, [lang="en"] h3, 
[lang="en"] h4, [lang="en"] h5, [lang="en"] h6 {
    font-family: var(--font-english-display);
}

/* Font Size Classes */
.text-xs {
    font-size: 0.75rem; /* 12px */
    line-height: 1rem; /* 16px */
}

.text-sm {
    font-size: 0.875rem; /* 14px */
    line-height: 1.25rem; /* 20px */
}

.text-base {
    font-size: 1rem; /* 16px */
    line-height: 1.5rem; /* 24px */
}

.text-lg {
    font-size: 1.125rem; /* 18px */
    line-height: 1.75rem; /* 28px */
}

.text-xl {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
}

.text-2xl {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
}

.text-3xl {
    font-size: 1.875rem; /* 30px */
    line-height: 2.25rem; /* 36px */
}

.text-4xl {
    font-size: 2.25rem; /* 36px */
    line-height: 2.5rem; /* 40px */
}

/* Font Weight Classes */
.font-regular {
    font-weight: var(--font-weight-regular);
}

.font-medium {
    font-weight: var(--font-weight-medium);
}

.font-semibold {
    font-weight: var(--font-weight-semibold);
}

.font-bold {
    font-weight: var(--font-weight-bold);
}

/* Special Typography Classes */
.display-font {
    font-family: var(--font-arabic-display);
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-tight);
}

.text-font {
    font-family: var(--font-arabic-text);
    font-weight: var(--font-weight-regular);
}

.number-font {
    font-family: var(--font-english-text);
    font-variant-numeric: tabular-nums;
}

/* Responsive Typography */
@media (max-width: 768px) {
    body {
        font-size: 0.9rem;
    }
    
    h1 {
        font-size: 1.75rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.25rem;
    }
}

/* Print Styles */
@media print {
    body {
        font-family: var(--font-fallback);
        color: black;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: subpixel-antialiased;
    }
}
