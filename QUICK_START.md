# 🚀 دليل التشغيل السريع | Quick Start Guide

## المتطلبات الأساسية | Prerequisites

### 1. تثبيت Node.js
- اذهب إلى: https://nodejs.org/
- حمل النسخة LTS (الموصى بها)
- قم بالتثبيت وأعد تشغيل Command Prompt

### 2. إعداد قاعدة البيانات (اختر واحدة)

#### الخيار أ: Supabase (سهل ومجاني) ⭐ موصى به
1. اذهب إلى: https://supabase.com/
2. أنشئ حساب مجاني
3. أنشئ مشروع جديد
4. من Settings > Database، انسخ Connection String

#### الخيار ب: PostgreSQL محلي
1. حمل من: https://www.postgresql.org/download/
2. ثبت PostgreSQL
3. أنشئ قاعدة بيانات: `shipment_management`

## 🏃‍♂️ خطوات التشغيل السريع

### 1. تثبيت التبعيات
```bash
# في مجلد المشروع
npm run setup:quick
```

### 2. إعداد البيئة
```bash
# انتقل لمجلد backend
cd backend

# انسخ ملف البيئة
copy .env.example .env

# افتح ملف .env وعدل هذه القيم:
```

**ملف .env:**
```env
# قاعدة البيانات - استخدم واحدة من هذه:

# للـ Supabase (انسخ من لوحة التحكم):
DATABASE_URL="postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres"

# أو للـ PostgreSQL المحلي:
DATABASE_URL="postgresql://postgres:password@localhost:5432/shipment_management"

# مفتاح JWT (مطلوب):
JWT_SECRET="your-super-secret-jwt-key-here-make-it-at-least-32-characters-long-for-security"

# باقي الإعدادات (اختيارية):
NODE_ENV=development
PORT=3001
CORS_ORIGIN="http://localhost:3000"
```

### 3. إعداد قاعدة البيانات
```bash
# في مجلد backend
npm run db:setup
```

### 4. تشغيل البرنامج
```bash
# ارجع للمجلد الرئيسي
cd ..

# شغل البرنامج
npm run dev
```

## 🌐 الوصول للتطبيق

بعد التشغيل الناجح:

- **الواجهة الرئيسية:** http://localhost:3000
- **API الخلفي:** http://localhost:3001  
- **توثيق API:** http://localhost:3001/api-docs

## 🔐 بيانات تسجيل الدخول

```
المدير | Admin:
📧 <EMAIL>
🔑 admin123

المدير | Manager:
📧 <EMAIL>  
🔑 manager123

الموزع | Distributor:
📧 <EMAIL>
🔑 distributor123
```

## ❗ حل المشاكل الشائعة

### مشكلة: "node is not recognized"
**الحل:** تأكد من تثبيت Node.js وأعد تشغيل Command Prompt

### مشكلة: "Database connection failed"
**الحل:** تأكد من صحة DATABASE_URL في ملف .env

### مشكلة: "Port 3000 is already in use"
**الحل:** أغلق أي تطبيق يستخدم المنفذ أو غير المنفذ في ملف التكوين

### مشكلة: "JWT_SECRET is required"
**الحل:** تأكد من وجود JWT_SECRET في ملف .env (32 حرف على الأقل)

## 📞 الدعم

إذا واجهت أي مشكلة:
1. تأكد من تثبيت Node.js بشكل صحيح
2. تأكد من صحة ملف .env
3. تأكد من اتصال قاعدة البيانات
4. راجع رسائل الخطأ في Terminal

---

**نصيحة:** استخدم Supabase للبداية السريعة، يمكنك التبديل لـ PostgreSQL محلي لاحقاً إذا أردت.
