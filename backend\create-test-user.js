// Create Test User Script
// سكريبت إنشاء مستخدم تجريبي

const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcrypt')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    // Hash password
    const passwordHash = await bcrypt.hash('test123', 12)
    
    // Create test user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        passwordHash,
        firstName: 'مستخدم',
        lastName: 'تجريبي',
        phone: '+966501234567',
        role: 'USER',
        language: 'ar',
        isActive: true,
        twoFactorEnabled: false,
      },
    })
    
    console.log('✅ تم إنشاء المستخدم التجريبي بنجاح!')
    console.log('📧 البريد الإلكتروني: <EMAIL>')
    console.log('🔑 كلمة المرور: test123')
    console.log('👤 المستخدم:', user)
    
  } catch (error) {
    if (error.code === 'P2002') {
      console.log('⚠️ المستخدم موجود بالفعل!')
    } else {
      console.error('❌ خطأ في إنشاء المستخدم:', error)
    }
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
