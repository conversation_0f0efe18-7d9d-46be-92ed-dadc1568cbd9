// نظام إدارة الصلاحيات المتقدم
class PermissionManager {
    constructor() {
        this.currentUser = null;
        this.userPermissions = [];
        this.rolePermissions = [];
        this.init();
    }

    // تهيئة النظام
    init() {
        this.loadCurrentUser();
        this.loadUserPermissions();
        this.checkPagePermissions();
    }

    // تحميل المستخدم الحالي
    loadCurrentUser() {
        // محاكاة تحميل المستخدم الحالي من الجلسة
        this.currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
        
        // إذا لم يكن هناك مستخدم، استخدم المدير الافتراضي
        if (!this.currentUser) {
            this.currentUser = {
                id: 'user1',
                name: 'أحم<PERSON> محمد',
                email: '<EMAIL>',
                role: 'admin',
                status: 'active',
                customPermissions: []
            };
            localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
        }
    }

    // تحميل صلاحيات المستخدم
    loadUserPermissions() {
        const roles = JSON.parse(localStorage.getItem('roles') || '[]');
        const userRole = roles.find(r => r.id === this.currentUser.role);
        
        // صلاحيات الدور
        this.rolePermissions = userRole ? userRole.permissions : [];
        
        // الصلاحيات المخصصة للمستخدم
        const customPermissions = this.currentUser.customPermissions || [];
        
        // دمج الصلاحيات
        this.userPermissions = [...new Set([...this.rolePermissions, ...customPermissions])];
    }

    // التحقق من صلاحية معينة
    hasPermission(permission) {
        // المدير له جميع الصلاحيات
        if (this.currentUser.role === 'admin') {
            return true;
        }
        
        return this.userPermissions.includes(permission);
    }

    // التحقق من صلاحيات متعددة (يجب أن تكون جميعها متاحة)
    hasAllPermissions(permissions) {
        return permissions.every(permission => this.hasPermission(permission));
    }

    // التحقق من صلاحيات متعددة (يكفي أن تكون واحدة متاحة)
    hasAnyPermission(permissions) {
        return permissions.some(permission => this.hasPermission(permission));
    }

    // التحقق من صلاحيات الصفحة الحالية
    checkPagePermissions() {
        const currentPage = window.location.pathname.split('/').pop();
        const pagePermissions = this.getPagePermissions(currentPage);
        
        if (pagePermissions.length > 0 && !this.hasAnyPermission(pagePermissions)) {
            this.redirectToUnauthorized();
        }
    }

    // الحصول على صلاحيات الصفحة
    getPagePermissions(page) {
        const pagePermissionMap = {
            'shipments.html': ['shipments_view'],
            'customers.html': ['customers_view'],
            'user-management.html': ['users_view'],
            'reports.html': ['reports_view'],
            'financial.html': ['financial_view']
        };
        
        return pagePermissionMap[page] || [];
    }

    // إعادة توجيه للصفحة غير المصرح بها
    redirectToUnauthorized() {
        alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
        window.location.href = 'main-dashboard.html';
    }

    // إخفاء/إظهار العناصر حسب الصلاحيات
    applyPermissionBasedUI() {
        // إخفاء الأزرار والعناصر حسب الصلاحيات
        this.hideElementsByPermission();
        
        // تعطيل الوظائف حسب الصلاحيات
        this.disableFunctionsByPermission();
        
        // تخصيص القوائم حسب الصلاحيات
        this.customizeMenusByPermission();
    }

    // إخفاء العناصر حسب الصلاحيات
    hideElementsByPermission() {
        const permissionElements = document.querySelectorAll('[data-permission]');
        
        permissionElements.forEach(element => {
            const requiredPermission = element.getAttribute('data-permission');
            const requiredPermissions = requiredPermission.split(',').map(p => p.trim());
            
            if (!this.hasAnyPermission(requiredPermissions)) {
                element.style.display = 'none';
            }
        });
    }

    // تعطيل الوظائف حسب الصلاحيات
    disableFunctionsByPermission() {
        const permissionButtons = document.querySelectorAll('button[data-permission]');
        
        permissionButtons.forEach(button => {
            const requiredPermission = button.getAttribute('data-permission');
            const requiredPermissions = requiredPermission.split(',').map(p => p.trim());
            
            if (!this.hasAnyPermission(requiredPermissions)) {
                button.disabled = true;
                button.title = 'ليس لديك صلاحية لهذا الإجراء';
                button.style.opacity = '0.5';
                button.style.cursor = 'not-allowed';
            }
        });
    }

    // تخصيص القوائم حسب الصلاحيات
    customizeMenusByPermission() {
        const menuItems = document.querySelectorAll('.nav-links a[data-permission]');
        
        menuItems.forEach(item => {
            const requiredPermission = item.getAttribute('data-permission');
            const requiredPermissions = requiredPermission.split(',').map(p => p.trim());
            
            if (!this.hasAnyPermission(requiredPermissions)) {
                item.style.display = 'none';
            }
        });
    }

    // تسجيل نشاط المستخدم
    logUserActivity(action, details = '') {
        const activity = {
            id: 'act' + Date.now(),
            userId: this.currentUser.id,
            userName: this.currentUser.name,
            action: action,
            details: details,
            timestamp: new Date().toISOString(),
            ipAddress: this.getClientIP(),
            browser: this.getBrowserInfo(),
            page: window.location.pathname.split('/').pop()
        };

        const activities = JSON.parse(localStorage.getItem('activityLog') || '[]');
        activities.unshift(activity);
        
        // الاحتفاظ بآخر 500 نشاط
        if (activities.length > 500) {
            activities.splice(500);
        }

        localStorage.setItem('activityLog', JSON.stringify(activities));
    }

    // الحصول على عنوان IP العميل (محاكاة)
    getClientIP() {
        return '192.168.1.' + Math.floor(Math.random() * 255);
    }

    // الحصول على معلومات المتصفح
    getBrowserInfo() {
        const userAgent = navigator.userAgent;
        let browser = 'Unknown';
        
        if (userAgent.includes('Chrome')) {
            browser = 'Chrome';
        } else if (userAgent.includes('Firefox')) {
            browser = 'Firefox';
        } else if (userAgent.includes('Safari')) {
            browser = 'Safari';
        } else if (userAgent.includes('Edge')) {
            browser = 'Edge';
        }
        
        return browser + ' ' + Math.floor(Math.random() * 10 + 115) + '.0';
    }

    // التحقق من انتهاء صلاحية الجلسة
    checkSessionExpiry() {
        const sessionStart = localStorage.getItem('sessionStart');
        const sessionDuration = 8 * 60 * 60 * 1000; // 8 ساعات
        
        if (sessionStart && (Date.now() - parseInt(sessionStart)) > sessionDuration) {
            this.logout();
            return false;
        }
        
        return true;
    }

    // تسجيل الخروج
    logout() {
        this.logUserActivity('تسجيل خروج', 'تسجيل خروج من النظام');
        localStorage.removeItem('currentUser');
        localStorage.removeItem('sessionStart');
        window.location.href = 'login.html';
    }

    // تحديث صلاحيات المستخدم
    updateUserPermissions() {
        this.loadUserPermissions();
        this.applyPermissionBasedUI();
    }

    // التحقق من الصلاحيات قبل تنفيذ إجراء
    checkPermissionBeforeAction(permission, action, errorMessage = null) {
        if (!this.hasPermission(permission)) {
            const message = errorMessage || 'ليس لديك صلاحية لتنفيذ هذا الإجراء';
            alert(message);
            this.logUserActivity('محاولة وصول غير مصرح', 'محاولة تنفيذ إجراء: ' + permission);
            return false;
        }
        
        // تسجيل النشاط
        this.logUserActivity(action, 'تنفيذ إجراء: ' + permission);
        return true;
    }

    // الحصول على قائمة الصلاحيات المتاحة للمستخدم
    getAvailablePermissions() {
        return this.userPermissions;
    }

    // الحصول على معلومات المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // تحديث معلومات المستخدم الحالي
    updateCurrentUser(userData) {
        this.currentUser = { ...this.currentUser, ...userData };
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
        this.updateUserPermissions();
    }
}

// إنشاء مثيل عام لمدير الصلاحيات
const permissionManager = new PermissionManager();

// تطبيق الصلاحيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من انتهاء الجلسة
    if (!permissionManager.checkSessionExpiry()) {
        return;
    }
    
    // تطبيق واجهة المستخدم حسب الصلاحيات
    setTimeout(() => {
        permissionManager.applyPermissionBasedUI();
    }, 100);
    
    // تسجيل دخول الصفحة
    permissionManager.logUserActivity('دخول صفحة', 'دخول إلى صفحة: ' + document.title);
});

// وظائف مساعدة عامة للتحقق من الصلاحيات
function hasPermission(permission) {
    return permissionManager.hasPermission(permission);
}

function checkPermission(permission, errorMessage = null) {
    return permissionManager.checkPermissionBeforeAction(permission, 'تحقق من صلاحية', errorMessage);
}

function logActivity(action, details = '') {
    permissionManager.logUserActivity(action, details);
}

// تصدير المدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PermissionManager;
}
