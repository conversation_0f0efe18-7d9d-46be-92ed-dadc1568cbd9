# 📁 قائمة ملفات نظام إدارة الشحنات المتكامل

## 🏠 **الملفات الرئيسية**

### **صفحات البداية:**
- `index.html` - الصفحة الرئيسية للنظام
- `unified-login.html` - صفحة تسجيل الدخول الموحدة
- `main-dashboard.html` - لوحة التحكم الرئيسية
- `home.html` - الصفحة الرئيسية للموقع

---

## 📦 **إدارة الشحنات**

### **الشحنات الأساسية:**
- `shipments.html` - إدارة الشحنات الرئيسية
- `shipment-tracking.html` - تتبع الشحنات
- `test-print.html` - طباعة بوليصات الشحن
- `shipping-calculator.html` - حاسبة تكلفة الشحن

### **التسعير والإلغاءات:**
- `pricing-management.html` - إدارة أسعار الشحن
- `cancellation-management.html` - إدارة أسباب الإلغاء
- `cancellation-reports.html` - تقارير الإلغاءات

---

## 💰 **النظام المالي**

### **النظام المالي الرئيسي:**
- `financial-system.html` - النظام المالي المتكامل
- `invoice-management.html` - إدارة الفواتير (جديد)
- `currency-converter.html` - محول العملات

### **إدارة المدفوعات:**
- `payment-management.html` - إدارة المدفوعات المؤجلة
- `collection-management.html` - إدارة التحصيل من المناديب
- `cod-management.html` - إدارة الدفع عند الاستلام
- `commission-management.html` - إدارة عمولات المناديب

---

## 👥 **إدارة المستخدمين**

### **المستخدمون والصلاحيات:**
- `user-management.html` - إدارة المستخدمين
- `user-permissions-advanced.html` - الصلاحيات المتقدمة
- `permissions-matrix.html` - مصفوفة الصلاحيات
- `test-permissions.html` - اختبار الصلاحيات

### **المناديب والعملاء:**
- `distributors-management.html` - إدارة المناديب والسائقين
- `customer-dashboard.html` - لوحة تحكم العملاء
- `customer-register.html` - تسجيل العملاء الجدد
- `customers.html` - إدارة العملاء

---

## 🏢 **إدارة الفروع**

### **الفروع والتحويلات:**
- `branches-management.html` - إدارة الفروع
- `branch-transfers.html` - تحويلات بين الفروع

---

## 📊 **التقارير والإحصائيات**

### **التقارير:**
- `reports.html` - التقارير العامة
- `advanced-reports.html` - التقارير المتقدمة
- `dashboard-3d.html` - لوحة تحكم ثلاثية الأبعاد

---

## 🎨 **إدارة المحتوى والصفحات**

### **إدارة الصفحات:**
- `pages-management.html` - إدارة صفحات الموقع
- `visual-page-editor.html` - المحرر البصري للصفحات
- `advanced-visual-editor.html` - المحرر البصري المتقدم
- `page-editor.html` - محرر الصفحات

### **صفحات الموقع:**
- `about-us.html` - صفحة من نحن
- `about-us-editor.html` - محرر صفحة من نحن
- `success-partners.html` - صفحة شركاء النجاح
- `partners-editor.html` - محرر صفحة شركاء النجاح

---

## ⚙️ **الإعدادات والأدوات**

### **الإعدادات:**
- `settings.html` - الإعدادات العامة للنظام

### **أدوات التشخيص:**
- `system-check.html` - فحص حالة النظام
- `test-database.html` - اختبار قاعدة البيانات
- `test-database-simple.html` - اختبار قاعدة البيانات المبسطة
- `page-diagnostics.html` - تشخيص الصفحات

### **أدوات الصيانة:**
- `fix-all-database-issues.html` - إصلاح مشاكل قاعدة البيانات
- `fix-database.html` - إصلاح قاعدة البيانات
- `fix-dashboard-issue.html` - إصلاح مشاكل لوحة التحكم
- `fix-broken-links.html` - إصلاح الروابط المعطلة
- `fix-print-issue.html` - إصلاح مشاكل الطباعة
- `emergency-fix.html` - الإصلاحات الطارئة
- `final-cleanup.html` - التنظيف النهائي
- `clear-data.html` - مسح البيانات
- `update-all-pages.html` - تحديث جميع الصفحات

### **أدوات الاختبار:**
- `test-kuwait.html` - اختبار مناطق الكويت
- `test-print.html` - اختبار الطباعة
- `test-social-login.html` - اختبار تسجيل الدخول الاجتماعي (جديد!)
- `dashboard-guide.html` - دليل لوحة التحكم

---

## 🗂️ **الملفات التقنية**

### **📁 مجلد JavaScript (js/)**
- `js/database-simple.js` - قاعدة البيانات الرئيسية
- `js/financial-database.js` - قاعدة البيانات المالية
- `js/permissions.js` - نظام الصلاحيات
- `js/database.js` - قاعدة البيانات الأصلية

### **📁 مجلد CSS (css/)**
- `css/style.css` - التصميم الرئيسي
- `css/fonts.css` - الخطوط العربية
- `css/fonts-local.css` - الخطوط المحلية

### **📁 مجلد Shared (shared/)**
- `shared/design-system.css` - نظام التصميم الموحد
- `shared/fonts.css` - الخطوط المشتركة
- `shared/i18n.json` - ملف الترجمة

---

## 🗄️ **قاعدة البيانات**

### **📁 مجلد Database (database/)**
- `database/schema.sql` - هيكل قاعدة البيانات SQL
- `database/schema.prisma` - هيكل قاعدة البيانات Prisma
- `database/seed.ts` - بيانات البذر الأولية

---

## 🖥️ **الخادم والواجهة الأمامية**

### **📁 مجلد Backend (backend/)**
- `backend/package.json` - إعدادات الخادم الخلفي
- `backend/tsconfig.json` - إعدادات TypeScript
- `backend/src/` - مجلد الكود المصدري للخادم

### **📁 مجلد Frontend (frontend/)**
- `frontend/package.json` - إعدادات الواجهة الأمامية
- `frontend/next.config.js` - إعدادات Next.js
- `frontend/postcss.config.js` - إعدادات PostCSS
- `frontend/tailwind.config.js` - إعدادات Tailwind CSS
- `frontend/src/` - مجلد الكود المصدري للواجهة
- `frontend/app/` - مجلد تطبيق Next.js

### **📁 مجلد Mobile (mobile/)**
- `mobile/` - ملفات التطبيق المحمول

---

## 📚 **الوثائق والأدلة**

### **الأدلة الرئيسية:**
- `README.md` - الدليل الأساسي
- `README_COMPLETE.md` - الدليل الشامل (جديد)
- `QUICK_START.md` - دليل البدء السريع
- `QUICK_SETUP_GUIDE.md` - دليل التشغيل السريع (جديد)
- `TROUBLESHOOTING.md` - حل المشاكل الشائعة

### **أدلة الميزات:**
- `DASHBOARD_README.md` - دليل لوحة التحكم
- `USER_MANAGEMENT_README.md` - دليل إدارة المستخدمين
- `CUSTOMER_SYSTEM_README.md` - دليل نظام العملاء
- `PAGES_MANAGEMENT_README.md` - دليل إدارة الصفحات
- `VISUAL_EDITOR_README.md` - دليل المحرر البصري
- `WEBSITE_PAGES_README.md` - دليل صفحات الموقع
- `ADVANCED_PERMISSIONS_README.md` - دليل الصلاحيات المتقدمة
- `SOCIAL_LOGIN_README.md` - دليل تسجيل الدخول الاجتماعي (جديد!)

### **أدلة الإصلاحات:**
- `DATABASE_FIX_README.md` - إصلاح قاعدة البيانات
- `DASHBOARD_FIX_README.md` - إصلاح لوحة التحكم
- `LOGIN_PAGE_UPDATE_README.md` - تحديث صفحة تسجيل الدخول
- `PRINT_FIX_README.md` - إصلاح مشاكل الطباعة
- `HOME_PAGE_FIXES_README.md` - إصلاحات الصفحة الرئيسية
- `BROKEN_LINKS_FIX_README.md` - إصلاح الروابط المعطلة
- `EMERGENCY_FIX_README.md` - الإصلاحات الطارئة
- `FINAL_FIX_README.md` - الإصلاحات النهائية
- `FINAL_SOLUTION_README.md` - الحل النهائي
- `COMPLETE_FIX_GUIDE.md` - دليل الإصلاحات الشامل
- `COMPANY_SETTINGS_FIX.md` - إصلاح إعدادات الشركة
- `WEBSITE_FIELD_FIX.md` - إصلاح حقل الموقع الإلكتروني
- `DASHBOARD_PAGES_INTEGRATION_README.md` - دمج صفحات لوحة التحكم

### **الإصلاحات الأخيرة:**
- `DATABASE_ERROR_FIX.md` - إصلاح خطأ قاعدة البيانات (جديد)
- `SYSTEM_FIXES_COMPLETE.md` - الإصلاحات الشاملة (جديد)
- `FILES_LIST.md` - قائمة الملفات (هذا الملف)

### **أدلة الوصول السريع:**
- `QUICK_ACCESS_GUIDE.md` - دليل الوصول السريع

---

## 🔧 **ملفات التشغيل**

### **ملفات Windows:**
- `start.bat` - تشغيل النظام على Windows
- `setup.bat` - إعداد النظام على Windows
- `quick-setup.bat` - الإعداد السريع
- `check-status.bat` - فحص حالة النظام
- `run-with-node.bat` - تشغيل مع Node.js
- `test-node.bat` - اختبار Node.js

### **ملفات Linux/Mac:**
- `start.sh` - تشغيل النظام على Linux/Mac

### **ملفات الإعداد:**
- `package.json` - إعدادات Node.js الرئيسية

### **ملفات أخرى:**
- `redirect.html` - صفحة إعادة التوجيه
- `start-here.html` - نقطة البداية
- `تشغيل البرنامج.txt` - تعليمات التشغيل بالعربية

---

## 📊 **إحصائيات الملفات**

### **📈 العدد الإجمالي:**
- **ملفات HTML:** 60+ ملف
- **ملفات JavaScript:** 4 ملفات رئيسية
- **ملفات CSS:** 5 ملفات
- **ملفات التوثيق:** 25+ ملف
- **ملفات الإعداد:** 10+ ملفات

### **📁 المجلدات الرئيسية:**
- `js/` - ملفات JavaScript
- `css/` - ملفات التصميم
- `shared/` - الملفات المشتركة
- `database/` - قاعدة البيانات
- `backend/` - الخادم الخلفي
- `frontend/` - الواجهة الأمامية
- `mobile/` - التطبيق المحمول
- `docs/` - الوثائق التقنية

---

## 🎯 **الملفات الأساسية للتشغيل**

### **الحد الأدنى للتشغيل:**
1. `index.html` أو `unified-login.html`
2. `main-dashboard.html`
3. `js/database-simple.js`
4. `css/style.css`
5. `css/fonts.css`

### **للاستخدام الكامل:**
- جميع ملفات HTML
- جميع ملفات JavaScript
- جميع ملفات CSS
- ملفات التوثيق الأساسية

---

## 🎉 **خلاصة**

**النظام يحتوي على أكثر من 100 ملف** يغطي جميع جوانب إدارة الشحنات:

- ✅ **واجهات مستخدم شاملة**
- ✅ **نظام مالي متكامل**
- ✅ **إدارة متقدمة للمستخدمين**
- ✅ **أدوات تشخيص وصيانة**
- ✅ **وثائق شاملة ومفصلة**
- ✅ **دعم متعدد المنصات**

**جميع الملفات جاهزة للاستخدام الفوري!** 🚀✨
