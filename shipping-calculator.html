<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة تكلفة الشحن | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .calculator-form {
            padding: 40px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        input, select, textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .weight-size-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
        }

        .calculate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 30px 0;
            transition: transform 0.3s;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
        }

        .result-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-top: 20px;
            display: none;
        }

        .result-section.show {
            display: block;
        }

        .price-display {
            text-align: center;
            margin-bottom: 20px;
        }

        .price-amount {
            font-size: 3rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .price-label {
            font-size: 1.2rem;
            color: #666;
        }

        .price-breakdown {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .breakdown-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .breakdown-item:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 1.1rem;
            color: #667eea;
        }

        .service-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .service-option {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .service-option:hover {
            border-color: #667eea;
        }

        .service-option.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .service-option h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .service-option p {
            color: #666;
            font-size: 0.9rem;
        }

        .back-link {
            display: inline-block;
            margin: 20px 0;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .weight-size-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .service-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 حاسبة تكلفة الشحن</h1>
            <p>احسب تكلفة شحنتك بدقة واحصل على أفضل الأسعار</p>
        </div>

        <div class="calculator-form">
            <a href="home.html" class="back-link">← العودة للصفحة الرئيسية</a>
            
            <form id="shippingCalculator">
                <div class="form-row">
                    <div class="form-group">
                        <label for="fromCity">من (المدينة)</label>
                        <select id="fromCity" required>
                            <option value="">اختر المدينة</option>
                            <option value="riyadh">الرياض</option>
                            <option value="jeddah">جدة</option>
                            <option value="dammam">الدمام</option>
                            <option value="mecca">مكة المكرمة</option>
                            <option value="medina">المدينة المنورة</option>
                            <option value="khobar">الخبر</option>
                            <option value="taif">الطائف</option>
                            <option value="abha">أبها</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="toCity">إلى (المدينة)</label>
                        <select id="toCity" required>
                            <option value="">اختر المدينة</option>
                            <option value="riyadh">الرياض</option>
                            <option value="jeddah">جدة</option>
                            <option value="dammam">الدمام</option>
                            <option value="mecca">مكة المكرمة</option>
                            <option value="medina">المدينة المنورة</option>
                            <option value="khobar">الخبر</option>
                            <option value="taif">الطائف</option>
                            <option value="abha">أبها</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>نوع الخدمة</label>
                    <div class="service-options">
                        <div class="service-option" data-service="standard">
                            <h4>🚚 شحن عادي</h4>
                            <p>3-5 أيام عمل</p>
                        </div>
                        <div class="service-option selected" data-service="express">
                            <h4>⚡ شحن سريع</h4>
                            <p>1-2 أيام عمل</p>
                        </div>
                        <div class="service-option" data-service="same-day">
                            <h4>🏃 نفس اليوم</h4>
                            <p>خلال 24 ساعة</p>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>الوزن والأبعاد</label>
                    <div class="weight-size-grid">
                        <div>
                            <label for="weight">الوزن (كجم)</label>
                            <input type="number" id="weight" min="0.1" step="0.1" value="1" required>
                        </div>
                        <div>
                            <label for="length">الطول (سم)</label>
                            <input type="number" id="length" min="1" value="20" required>
                        </div>
                        <div>
                            <label for="width">العرض (سم)</label>
                            <input type="number" id="width" min="1" value="15" required>
                        </div>
                        <div>
                            <label for="height">الارتفاع (سم)</label>
                            <input type="number" id="height" min="1" value="10" required>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="packageType">نوع الطرد</label>
                        <select id="packageType">
                            <option value="documents">مستندات</option>
                            <option value="electronics">إلكترونيات</option>
                            <option value="clothing">ملابس</option>
                            <option value="fragile">قابل للكسر</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="value">قيمة الشحنة (ريال)</label>
                        <input type="number" id="value" min="0" value="100">
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="insurance" style="width: auto; margin-left: 10px;">
                        تأمين الشحنة (+5% من قيمة الشحنة)
                    </label>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="cod" style="width: auto; margin-left: 10px;">
                        دفع عند الاستلام (+10 ريال)
                    </label>
                </div>

                <button type="submit" class="calculate-btn">احسب التكلفة</button>
            </form>

            <div id="resultSection" class="result-section">
                <div class="price-display">
                    <div class="price-amount" id="totalPrice">0 ريال</div>
                    <div class="price-label">إجمالي تكلفة الشحن</div>
                </div>
                
                <div class="price-breakdown">
                    <h4 style="margin-bottom: 15px;">تفاصيل التكلفة:</h4>
                    <div class="breakdown-item">
                        <span>تكلفة الشحن الأساسية:</span>
                        <span id="basePrice">0 ريال</span>
                    </div>
                    <div class="breakdown-item">
                        <span>رسوم الخدمة:</span>
                        <span id="servicePrice">0 ريال</span>
                    </div>
                    <div class="breakdown-item">
                        <span>التأمين:</span>
                        <span id="insurancePrice">0 ريال</span>
                    </div>
                    <div class="breakdown-item">
                        <span>دفع عند الاستلام:</span>
                        <span id="codPrice">0 ريال</span>
                    </div>
                    <div class="breakdown-item">
                        <span>الإجمالي:</span>
                        <span id="finalPrice">0 ريال</span>
                    </div>
                </div>
                
                <button class="calculate-btn" onclick="window.location.href='index.html'">
                    احجز الشحنة الآن
                </button>
            </div>
        </div>
    </div>

    <script>
        // Service option selection
        document.querySelectorAll('.service-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.service-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // Calculate shipping cost
        document.getElementById('shippingCalculator').addEventListener('submit', function(e) {
            e.preventDefault();
            calculateShipping();
        });

        function calculateShipping() {
            const fromCity = document.getElementById('fromCity').value;
            const toCity = document.getElementById('toCity').value;
            const weight = parseFloat(document.getElementById('weight').value);
            const length = parseFloat(document.getElementById('length').value);
            const width = parseFloat(document.getElementById('width').value);
            const height = parseFloat(document.getElementById('height').value);
            const packageValue = parseFloat(document.getElementById('value').value) || 0;
            const insurance = document.getElementById('insurance').checked;
            const cod = document.getElementById('cod').checked;
            const selectedService = document.querySelector('.service-option.selected').dataset.service;

            if (!fromCity || !toCity) {
                alert('يرجى اختيار مدينة المرسل والمستقبل');
                return;
            }

            // Calculate volumetric weight
            const volumetricWeight = (length * width * height) / 5000;
            const chargeableWeight = Math.max(weight, volumetricWeight);

            // Base price calculation
            let basePrice = 15; // Base price
            
            // Distance factor
            const sameCity = fromCity === toCity;
            if (sameCity) {
                basePrice = 10;
            } else {
                basePrice = 20;
            }

            // Weight factor
            basePrice += chargeableWeight * 5;

            // Service type factor
            let serviceMultiplier = 1;
            let serviceName = 'شحن عادي';
            
            switch(selectedService) {
                case 'standard':
                    serviceMultiplier = 1;
                    serviceName = 'شحن عادي';
                    break;
                case 'express':
                    serviceMultiplier = 1.5;
                    serviceName = 'شحن سريع';
                    break;
                case 'same-day':
                    serviceMultiplier = 2.5;
                    serviceName = 'نفس اليوم';
                    break;
            }

            const servicePrice = basePrice * (serviceMultiplier - 1);
            basePrice = basePrice * serviceMultiplier;

            // Insurance
            const insurancePrice = insurance ? packageValue * 0.05 : 0;

            // COD fee
            const codPrice = cod ? 10 : 0;

            // Total
            const totalPrice = basePrice + insurancePrice + codPrice;

            // Display results
            document.getElementById('basePrice').textContent = Math.round(basePrice - servicePrice) + ' ريال';
            document.getElementById('servicePrice').textContent = Math.round(servicePrice) + ' ريال (' + serviceName + ')';
            document.getElementById('insurancePrice').textContent = Math.round(insurancePrice) + ' ريال';
            document.getElementById('codPrice').textContent = codPrice + ' ريال';
            document.getElementById('finalPrice').textContent = Math.round(totalPrice) + ' ريال';
            document.getElementById('totalPrice').textContent = Math.round(totalPrice) + ' ريال';

            // Show result section
            document.getElementById('resultSection').classList.add('show');
            document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
