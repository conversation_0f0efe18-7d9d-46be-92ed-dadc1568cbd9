<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفروع - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display);
            font-weight: 600;
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total {
            border-left-color: #28a745;
        }

        .stat-card.active {
            border-left-color: #20c997;
        }

        .stat-card.saudi {
            border-left-color: #007bff;
        }

        .stat-card.kuwait {
            border-left-color: #6f42c1;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input,
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            min-width: 200px;
        }

        .search-input:focus,
        .filter-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .branches-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
        }

        .branch-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .branch-card:hover {
            transform: translateY(-5px);
        }

        .branch-header {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 3px solid;
            position: relative;
        }

        .branch-header.main {
            border-bottom-color: #28a745;
        }

        .branch-header.regular {
            border-bottom-color: #17a2b8;
        }

        .branch-header.inactive {
            border-bottom-color: #dc3545;
        }

        .branch-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0 0 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .branch-code {
            background: rgba(0, 0, 0, 0.1);
            color: #495057;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: bold;
        }

        .branch-status {
            position: absolute;
            top: 15px;
            left: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .status-active {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .status-inactive {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .status-main {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .branch-info {
            padding: 20px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 12px;
            font-size: 0.9rem;
        }

        .info-icon {
            width: 20px;
            text-align: center;
            color: #6c757d;
        }

        .branch-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 2px;
        }

        .branch-actions {
            padding: 15px 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        /* تحسينات النافذة المنبثقة */
        .form-input:focus {
            border-color: var(--color-primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .form-input:invalid {
            border-color: #dc3545;
        }

        .form-input:valid {
            border-color: #28a745;
        }

        /* تحسين مظهر الـ checkboxes */
        input[type="checkbox"] {
            accent-color: #28a745;
        }

        /* تحسين مظهر النافذة على الشاشات الصغيرة */
        @media (max-width: 768px) {
            .modal-content {
                width: 98% !important;
                margin: 1% auto !important;
                max-height: 98vh !important;
            }

            .modal-body {
                padding: 20px !important;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input,
            .filter-select {
                min-width: auto;
            }

            .branches-grid {
                grid-template-columns: 1fr;
            }

            .branch-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🏢</span>
                <span>إدارة الفروع</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="branches-management.html" class="active">الفروع</a>
                <a href="branch-transfers.html">التحويلات</a>
                <a href="shipments.html">الشحنات</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">🏢 إدارة الفروع</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="openAddBranchModal()">
                    ➕ إضافة فرع جديد
                </button>
                <a href="branch-transfers.html" class="btn btn-info">
                    🔄 إدارة التحويلات
                </a>
                <button class="btn" onclick="loadBranches()">
                    🔄 تحديث القائمة
                </button>
            </div>
        </div>

        <!-- إحصائيات الفروع -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="card-icon">🏢</div>
                <div class="card-amount" id="totalBranches">0</div>
                <div class="card-label">إجمالي الفروع</div>
            </div>
            
            <div class="stat-card active">
                <div class="card-icon">✅</div>
                <div class="card-amount" id="activeBranches">0</div>
                <div class="card-label">فروع نشطة</div>
            </div>
            
            <div class="stat-card saudi">
                <div class="card-icon">🇸🇦</div>
                <div class="card-amount" id="saudiBranches">0</div>
                <div class="card-label">فروع السعودية</div>
            </div>
            
            <div class="stat-card kuwait">
                <div class="card-icon">🇰🇼</div>
                <div class="card-amount" id="kuwaitBranches">0</div>
                <div class="card-label">فروع الكويت</div>
            </div>
        </div>

        <!-- أدوات التحكم والبحث -->
        <div class="controls">
            <input type="text" id="searchInput" class="search-input" placeholder="🔍 البحث في الفروع...">
            
            <select id="countryFilter" class="filter-select">
                <option value="">جميع البلدان</option>
                <option value="السعودية">السعودية</option>
                <option value="الكويت">الكويت</option>
            </select>
            
            <select id="statusFilter" class="filter-select">
                <option value="">جميع الحالات</option>
                <option value="true">نشط</option>
                <option value="false">غير نشط</option>
            </select>
            
            <button class="btn" onclick="clearFilters()">مسح الفلاتر</button>
        </div>

        <!-- شبكة الفروع -->
        <div class="branches-grid" id="branchesContainer">
            <!-- سيتم ملء البيانات هنا -->
        </div>
    </main>

    <!-- نافذة إضافة فرع جديد -->
    <div id="addBranchModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); backdrop-filter: blur(5px);">
        <div class="modal-content" style="background-color: white; margin: 2% auto; padding: 0; border-radius: 12px; width: 95%; max-width: 800px; max-height: 95vh; overflow-y: auto; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">إضافة فرع جديد</h2>
                <span class="close" onclick="closeAddBranchModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer; line-height: 1;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="addBranchForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <!-- معلومات أساسية -->
                        <div style="grid-column: 1 / -1;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📋 المعلومات الأساسية</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم الفرع *</label>
                            <input type="text" id="branchName" required placeholder="مثال: فرع الرياض الشمالي" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">كود الفرع *</label>
                            <input type="text" id="branchCode" required placeholder="مثال: RYD-02" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">البلد *</label>
                            <select id="branchCountry" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر البلد</option>
                                <option value="السعودية">السعودية</option>
                                <option value="الكويت">الكويت</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">المدينة *</label>
                            <input type="text" id="branchCity" required placeholder="مثال: الرياض" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">المنطقة *</label>
                            <input type="text" id="branchRegion" required placeholder="مثال: منطقة الرياض" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">السعة القصوى *</label>
                            <input type="number" id="branchCapacity" required min="1" placeholder="1000" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <!-- معلومات الاتصال -->
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📞 معلومات الاتصال</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">رقم الهاتف *</label>
                            <input type="tel" id="branchPhone" required placeholder="+966112345678" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">البريد الإلكتروني *</label>
                            <input type="email" id="branchEmail" required placeholder="<EMAIL>" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم المدير *</label>
                            <input type="text" id="branchManager" required placeholder="أحمد محمد السعد" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px; grid-column: 1 / -1;">
                            <label style="font-weight: 600; color: #495057;">العنوان الكامل *</label>
                            <textarea id="branchAddress" required rows="2" placeholder="شارع الملك فهد، حي العليا، الرياض" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical; transition: border-color 0.3s ease;"></textarea>
                        </div>

                        <!-- ساعات العمل -->
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">🕒 ساعات العمل</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">بداية العمل</label>
                            <input type="time" id="workStart" value="08:00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">نهاية العمل</label>
                            <input type="time" id="workEnd" value="18:00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <!-- الخدمات -->
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">🛠️ الخدمات المتاحة</h3>
                        </div>

                        <div style="grid-column: 1 / -1;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                    <input type="checkbox" id="serviceReceive" checked style="transform: scale(1.2);">
                                    <span>استقبال الشحنات</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                    <input type="checkbox" id="serviceDistribute" checked style="transform: scale(1.2);">
                                    <span>توزيع محلي</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                    <input type="checkbox" id="serviceTransfer" checked style="transform: scale(1.2);">
                                    <span>تحويل بين الفروع</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                    <input type="checkbox" id="serviceStorage" style="transform: scale(1.2);">
                                    <span>تخزين</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeAddBranchModal()" style="background: #6c757d;">إلغاء</button>
                        <button type="submit" class="btn btn-success">🏢 إضافة الفرع</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏢 تحميل صفحة إدارة الفروع...');

            try {
                loadBranches();
                setupEventListeners();
                loadStats();

                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterBranches);
            document.getElementById('countryFilter').addEventListener('change', filterBranches);
            document.getElementById('statusFilter').addEventListener('change', filterBranches);
        }

        // تحميل وعرض الفروع
        function loadBranches() {
            try {
                const branches = db.getAllBranches();
                displayBranches(branches);
                console.log('🏢 تم تحميل', branches.length, 'فرع');
            } catch (error) {
                console.error('❌ خطأ في تحميل الفروع:', error);
                alert('خطأ في تحميل الفروع: ' + error.message);
            }
        }

        // عرض الفروع في الشبكة
        function displayBranches(branches) {
            const container = document.getElementById('branchesContainer');

            if (branches.length === 0) {
                container.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #6c757d;">
                        <h3>🏢 لا توجد فروع</h3>
                        <p>لم يتم العثور على أي فروع. ابدأ بإضافة فرع جديد.</p>
                        <button class="btn btn-success" onclick="openAddBranchModal()" style="margin-top: 15px;">
                            ➕ إضافة فرع جديد
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = branches.map(branch => {
                const stats = db.getBranchStats(branch.id);
                const headerClass = branch.isMainBranch ? 'main' : (branch.isActive ? 'regular' : 'inactive');
                const statusClass = branch.isMainBranch ? 'status-main' : (branch.isActive ? 'status-active' : 'status-inactive');
                const statusText = branch.isMainBranch ? 'فرع رئيسي' : (branch.isActive ? 'نشط' : 'غير نشط');

                return `
                    <div class="branch-card">
                        <div class="branch-header ${headerClass}">
                            <div class="branch-status ${statusClass}">${statusText}</div>
                            <h3 class="branch-title">
                                <span>${branch.country === 'السعودية' ? '🇸🇦' : '🇰🇼'}</span>
                                ${branch.name}
                            </h3>
                            <span class="branch-code">${branch.code}</span>
                        </div>

                        <div class="branch-info">
                            <div class="info-item">
                                <span class="info-icon">📍</span>
                                <span>${branch.city}, ${branch.region}</span>
                            </div>

                            <div class="info-item">
                                <span class="info-icon">📧</span>
                                <span>${branch.email}</span>
                            </div>

                            <div class="info-item">
                                <span class="info-icon">📞</span>
                                <span>${branch.phone}</span>
                            </div>

                            <div class="info-item">
                                <span class="info-icon">👤</span>
                                <span>${branch.manager}</span>
                            </div>

                            <div class="info-item">
                                <span class="info-icon">🕒</span>
                                <span>${branch.workingHours.start} - ${branch.workingHours.end}</span>
                            </div>

                            <div class="branch-stats">
                                <div class="stat-item">
                                    <div class="stat-number">${stats.totalShipments}</div>
                                    <div class="stat-label">شحنات</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${stats.pendingReceives}</div>
                                    <div class="stat-label">تحويلات واردة</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${stats.outgoingTransfers}</div>
                                    <div class="stat-label">تحويلات صادرة</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${branch.capacity}</div>
                                    <div class="stat-label">السعة</div>
                                </div>
                            </div>
                        </div>

                        <div class="branch-actions">
                            <button class="btn btn-small" onclick="viewBranch('${branch.id}')">عرض</button>
                            <button class="btn btn-small btn-warning" onclick="editBranch('${branch.id}')">تعديل</button>
                            <button class="btn btn-small btn-info" onclick="viewTransfers('${branch.id}')">التحويلات</button>
                            ${!branch.isMainBranch ?
                                `<button class="btn btn-small ${branch.isActive ? 'btn-danger' : 'btn-success'}" onclick="toggleBranchStatus('${branch.id}')">
                                    ${branch.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                                </button>` :
                                ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // تحميل الإحصائيات
        function loadStats() {
            try {
                const branches = db.getAllBranches();

                const totalCount = branches.length;
                const activeCount = branches.filter(b => b.isActive).length;
                const saudiCount = branches.filter(b => b.country === 'السعودية').length;
                const kuwaitCount = branches.filter(b => b.country === 'الكويت').length;

                document.getElementById('totalBranches').textContent = totalCount;
                document.getElementById('activeBranches').textContent = activeCount;
                document.getElementById('saudiBranches').textContent = saudiCount;
                document.getElementById('kuwaitBranches').textContent = kuwaitCount;

            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات:', error);
            }
        }

        // فلترة الفروع
        function filterBranches() {
            try {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const countryFilter = document.getElementById('countryFilter').value;
                const statusFilter = document.getElementById('statusFilter').value;

                const allBranches = db.getAllBranches();

                const filteredBranches = allBranches.filter(branch => {
                    const matchesSearch = !searchTerm ||
                        branch.name.toLowerCase().includes(searchTerm) ||
                        branch.code.toLowerCase().includes(searchTerm) ||
                        branch.city.toLowerCase().includes(searchTerm) ||
                        branch.manager.toLowerCase().includes(searchTerm);

                    const matchesCountry = !countryFilter || branch.country === countryFilter;
                    const matchesStatus = !statusFilter || branch.isActive.toString() === statusFilter;

                    return matchesSearch && matchesCountry && matchesStatus;
                });

                displayBranches(filteredBranches);
            } catch (error) {
                console.error('❌ خطأ في فلترة الفروع:', error);
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('countryFilter').value = '';
            document.getElementById('statusFilter').value = '';
            loadBranches();
        }

        // عرض تفاصيل الفرع
        function viewBranch(id) {
            try {
                const branch = db.getBranchById(id);
                if (!branch) {
                    alert('لم يتم العثور على الفرع');
                    return;
                }

                const stats = db.getBranchStats(id);

                alert(`تفاصيل الفرع ${branch.name}:\n\n` +
                      `الكود: ${branch.code}\n` +
                      `المدينة: ${branch.city}, ${branch.region}\n` +
                      `البلد: ${branch.country}\n` +
                      `العنوان: ${branch.address}\n` +
                      `الهاتف: ${branch.phone}\n` +
                      `البريد الإلكتروني: ${branch.email}\n` +
                      `المدير: ${branch.manager}\n` +
                      `ساعات العمل: ${branch.workingHours.start} - ${branch.workingHours.end}\n` +
                      `السعة: ${branch.capacity}\n` +
                      `الحالة: ${branch.isActive ? 'نشط' : 'غير نشط'}\n` +
                      `فرع رئيسي: ${branch.isMainBranch ? 'نعم' : 'لا'}\n\n` +
                      `--- الإحصائيات ---\n` +
                      `إجمالي الشحنات: ${stats.totalShipments}\n` +
                      `الشحنات المعلقة: ${stats.pendingShipments}\n` +
                      `التحويلات الواردة: ${stats.incomingTransfers}\n` +
                      `التحويلات الصادرة: ${stats.outgoingTransfers}\n` +
                      `تاريخ الإنشاء: ${branch.createdDate}`);
            } catch (error) {
                console.error('❌ خطأ في عرض الفرع:', error);
                alert('خطأ في عرض الفرع: ' + error.message);
            }
        }

        // تعديل فرع
        function editBranch(id) {
            try {
                const branch = db.getBranchById(id);
                if (!branch) {
                    alert('لم يتم العثور على الفرع');
                    return;
                }

                // ملء النموذج ببيانات الفرع الحالية
                document.getElementById('branchName').value = branch.name;
                document.getElementById('branchCode').value = branch.code;
                document.getElementById('branchCountry').value = branch.country;
                document.getElementById('branchCity').value = branch.city;
                document.getElementById('branchRegion').value = branch.region;
                document.getElementById('branchAddress').value = branch.address;
                document.getElementById('branchPhone').value = branch.phone;
                document.getElementById('branchEmail').value = branch.email;
                document.getElementById('branchManager').value = branch.manager;
                document.getElementById('branchCapacity').value = branch.capacity;
                document.getElementById('workStart').value = branch.workingHours.start;
                document.getElementById('workEnd').value = branch.workingHours.end;

                // تحديد الخدمات
                document.getElementById('serviceReceive').checked = branch.services.includes('استقبال الشحنات');
                document.getElementById('serviceDistribute').checked = branch.services.includes('توزيع محلي');
                document.getElementById('serviceTransfer').checked = branch.services.includes('تحويل بين الفروع');
                document.getElementById('serviceStorage').checked = branch.services.includes('تخزين');

                // تغيير عنوان النافذة
                document.querySelector('#addBranchModal .modal-header h2').textContent = 'تعديل الفرع';
                document.querySelector('#addBranchModal .btn-success').textContent = '💾 حفظ التعديلات';

                // حفظ معرف الفرع للتعديل
                document.getElementById('addBranchForm').dataset.editId = id;

                // فتح النافذة
                document.getElementById('addBranchModal').style.display = 'block';

            } catch (error) {
                console.error('❌ خطأ في تعديل الفرع:', error);
                alert('خطأ في تعديل الفرع: ' + error.message);
            }
        }

        // عرض تحويلات الفرع
        function viewTransfers(id) {
            window.location.href = `branch-transfers.html?branch=${id}`;
        }

        // تغيير حالة الفرع
        function toggleBranchStatus(id) {
            try {
                const branch = db.getBranchById(id);
                if (!branch) {
                    alert('لم يتم العثور على الفرع');
                    return;
                }

                const newStatus = !branch.isActive;
                const statusText = newStatus ? 'تفعيل' : 'إلغاء تفعيل';

                if (confirm(`هل تريد ${statusText} الفرع ${branch.name}؟`)) {
                    const updatedBranch = db.updateBranch(id, { isActive: newStatus });

                    if (updatedBranch) {
                        alert(`تم ${statusText} الفرع بنجاح`);
                        loadBranches();
                        loadStats();
                    } else {
                        alert('خطأ في تغيير حالة الفرع');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في تغيير حالة الفرع:', error);
                alert('خطأ في تغيير حالة الفرع: ' + error.message);
            }
        }

        // فتح نافذة إضافة فرع
        function openAddBranchModal() {
            try {
                // إعداد مستمع الأحداث لنموذج إضافة الفرع
                document.getElementById('addBranchForm').addEventListener('submit', submitAddBranch);

                // إظهار النافذة
                document.getElementById('addBranchModal').style.display = 'block';

                // التركيز على أول حقل
                document.getElementById('branchName').focus();

            } catch (error) {
                console.error('❌ خطأ في فتح نافذة إضافة الفرع:', error);
                alert('خطأ في فتح نافذة إضافة الفرع: ' + error.message);
            }
        }

        // إغلاق نافذة إضافة فرع
        function closeAddBranchModal() {
            document.getElementById('addBranchModal').style.display = 'none';
            document.getElementById('addBranchForm').reset();

            // إعادة تعيين القيم الافتراضية
            document.getElementById('workStart').value = '08:00';
            document.getElementById('workEnd').value = '18:00';
            document.getElementById('serviceReceive').checked = true;
            document.getElementById('serviceDistribute').checked = true;
            document.getElementById('serviceTransfer').checked = true;
            document.getElementById('serviceStorage').checked = false;

            // إعادة تعيين حالة التعديل
            document.querySelector('#addBranchModal .modal-header h2').textContent = 'إضافة فرع جديد';
            document.querySelector('#addBranchModal .btn-success').textContent = '🏢 إضافة الفرع';
            delete document.getElementById('addBranchForm').dataset.editId;
        }

        // تنفيذ إضافة أو تعديل فرع
        function submitAddBranch(e) {
            e.preventDefault();

            try {
                const isEditing = document.getElementById('addBranchForm').dataset.editId;
                const editId = isEditing;
                // جمع البيانات من النموذج
                const branchData = {
                    name: document.getElementById('branchName').value.trim(),
                    code: document.getElementById('branchCode').value.trim().toUpperCase(),
                    country: document.getElementById('branchCountry').value,
                    city: document.getElementById('branchCity').value.trim(),
                    region: document.getElementById('branchRegion').value.trim(),
                    address: document.getElementById('branchAddress').value.trim(),
                    phone: document.getElementById('branchPhone').value.trim(),
                    email: document.getElementById('branchEmail').value.trim(),
                    manager: document.getElementById('branchManager').value.trim(),
                    capacity: parseInt(document.getElementById('branchCapacity').value),
                    workingHours: {
                        start: document.getElementById('workStart').value,
                        end: document.getElementById('workEnd').value,
                        workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس']
                    },
                    services: [],
                    isMainBranch: false,
                    coordinates: { lat: 0, lng: 0 } // سيتم تحديثها لاحقاً
                };

                // جمع الخدمات المحددة
                if (document.getElementById('serviceReceive').checked) {
                    branchData.services.push('استقبال الشحنات');
                }
                if (document.getElementById('serviceDistribute').checked) {
                    branchData.services.push('توزيع محلي');
                }
                if (document.getElementById('serviceTransfer').checked) {
                    branchData.services.push('تحويل بين الفروع');
                }
                if (document.getElementById('serviceStorage').checked) {
                    branchData.services.push('تخزين');
                }

                // التحقق من صحة البيانات
                if (!branchData.name || !branchData.code || !branchData.country ||
                    !branchData.city || !branchData.region || !branchData.address ||
                    !branchData.phone || !branchData.email || !branchData.manager ||
                    !branchData.capacity) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                // التحقق من صحة البريد الإلكتروني
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(branchData.email)) {
                    alert('يرجى إدخال بريد إلكتروني صحيح');
                    return;
                }

                // التحقق من عدم تكرار كود الفرع (إلا إذا كان نفس الفرع في حالة التعديل)
                const existingBranches = db.getAllBranches();
                const codeExists = existingBranches.some(branch =>
                    branch.code.toUpperCase() === branchData.code && branch.id !== editId
                );

                if (codeExists) {
                    alert('كود الفرع موجود مسبقاً. يرجى اختيار كود آخر.');
                    document.getElementById('branchCode').focus();
                    return;
                }

                // التحقق من عدم تكرار اسم الفرع (إلا إذا كان نفس الفرع في حالة التعديل)
                const nameExists = existingBranches.some(branch =>
                    branch.name.toLowerCase() === branchData.name.toLowerCase() && branch.id !== editId
                );

                if (nameExists) {
                    alert('اسم الفرع موجود مسبقاً. يرجى اختيار اسم آخر.');
                    document.getElementById('branchName').focus();
                    return;
                }

                // التحقق من السعة
                if (branchData.capacity < 1 || branchData.capacity > 10000) {
                    alert('السعة يجب أن تكون بين 1 و 10000');
                    return;
                }

                // تأكيد العملية
                const actionText = isEditing ? 'تعديل' : 'إضافة';
                const confirmText = `هل أنت متأكد من ${actionText} الفرع؟\n\nاسم الفرع: ${branchData.name}\nكود الفرع: ${branchData.code}\nالمدينة: ${branchData.city}\nالمدير: ${branchData.manager}`;

                if (confirm(confirmText)) {
                    let result;

                    if (isEditing) {
                        // تعديل الفرع
                        result = db.updateBranch(editId, branchData);
                    } else {
                        // إضافة فرع جديد
                        result = db.addBranch(branchData);
                    }

                    if (result) {
                        const successText = isEditing ? 'تعديل' : 'إضافة';
                        alert(`✅ تم ${successText} الفرع بنجاح!\n\n🏢 اسم الفرع: ${result.name}\n🔖 كود الفرع: ${result.code}\n🆔 المعرف: ${result.id}\n📍 المدينة: ${result.city}\n👤 المدير: ${result.manager}\n\nيمكنك الآن استخدام هذا الفرع في عمليات التحويل والشحن.`);
                        closeAddBranchModal();
                        loadBranches();
                        loadStats();

                        // التمرير إلى الفرع (إذا أمكن)
                        if (!isEditing) {
                            setTimeout(() => {
                                const branchCards = document.querySelectorAll('.branch-card');
                                if (branchCards.length > 0) {
                                    branchCards[branchCards.length - 1].scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'center'
                                    });
                                }
                            }, 500);
                        }

                    } else {
                        alert(`❌ خطأ في ${actionText} الفرع. يرجى المحاولة مرة أخرى.`);
                    }
                }

            } catch (error) {
                console.error('❌ خطأ في إضافة الفرع:', error);
                alert('خطأ في إضافة الفرع: ' + error.message);
            }
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const addBranchModal = document.getElementById('addBranchModal');

            if (event.target === addBranchModal) {
                closeAddBranchModal();
            }
        }

        // تحسين تجربة المستخدم - إضافة مستمعي الأحداث للتنسيق التلقائي
        document.addEventListener('DOMContentLoaded', function() {
            // تنسيق كود الفرع تلقائياً
            setTimeout(() => {
                const branchCodeInput = document.getElementById('branchCode');
                if (branchCodeInput) {
                    branchCodeInput.addEventListener('input', function() {
                        this.value = this.value.toUpperCase().replace(/[^A-Z0-9-]/g, '');
                    });
                }

                // تنسيق رقم الهاتف
                const phoneInput = document.getElementById('branchPhone');
                if (phoneInput) {
                    phoneInput.addEventListener('input', function() {
                        let value = this.value.replace(/[^0-9+]/g, '');
                        if (value && !value.startsWith('+')) {
                            value = '+' + value;
                        }
                        this.value = value;
                    });
                }

                // تحديد البلد تلقائياً بناءً على رقم الهاتف
                const phoneInputForCountry = document.getElementById('branchPhone');
                const countrySelect = document.getElementById('branchCountry');
                if (phoneInputForCountry && countrySelect) {
                    phoneInputForCountry.addEventListener('input', function() {
                        const phone = this.value;
                        if (phone.startsWith('+966')) {
                            countrySelect.value = 'السعودية';
                        } else if (phone.startsWith('+965')) {
                            countrySelect.value = 'الكويت';
                        }
                    });
                }
            }, 100);
        });
    </script>
</body>
</html>
