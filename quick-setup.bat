@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    إعداد سريع لنظام إدارة الشحنات
echo    Quick Setup - Shipment Management
echo ========================================
echo.

:: Check if Node.js is installed
echo 🔍 التحقق من Node.js...
echo 🔍 Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Node.js غير مثبت!
    echo ❌ Node.js is not installed!
    echo.
    echo يرجى تثبيت Node.js من:
    echo Please install Node.js from:
    echo 🔗 https://nodejs.org/
    echo.
    echo بعد التثبيت، أعد تشغيل هذا الملف
    echo After installation, run this file again
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
echo ✅ Node.js is installed

:: Install dependencies
echo.
echo 📦 تثبيت التبعيات...
echo 📦 Installing dependencies...
echo.

echo تثبيت التبعيات الرئيسية...
echo Installing main dependencies...
npm install

echo.
echo تثبيت تبعيات الخلفية...
echo Installing backend dependencies...
cd backend
npm install

echo.
echo تثبيت تبعيات الواجهة الأمامية...
echo Installing frontend dependencies...
cd ..\frontend
npm install
cd ..

:: Setup .env file
echo.
echo ⚙️ إعداد ملف البيئة...
echo ⚙️ Setting up environment file...

if not exist "backend\.env" (
    cd backend
    copy .env.example .env >nul
    cd ..
    echo ✅ تم إنشاء ملف .env
    echo ✅ .env file created
) else (
    echo ✅ ملف .env موجود بالفعل
    echo ✅ .env file already exists
)

echo.
echo ========================================
echo           إعداد قاعدة البيانات
echo           Database Setup
echo ========================================
echo.
echo اختر نوع قاعدة البيانات:
echo Choose database type:
echo.
echo 1. Supabase (مجاني وسهل) - Recommended
echo 2. PostgreSQL محلي (Local PostgreSQL)
echo 3. تخطي الآن (Skip for now)
echo.
set /p choice="اختر (1/2/3): "

if "%choice%"=="1" (
    echo.
    echo 🌐 إعداد Supabase:
    echo 🌐 Supabase Setup:
    echo.
    echo 1. اذهب إلى: https://supabase.com/
    echo    Go to: https://supabase.com/
    echo.
    echo 2. أنشئ حساب مجاني
    echo    Create a free account
    echo.
    echo 3. أنشئ مشروع جديد
    echo    Create a new project
    echo.
    echo 4. من Settings ^> Database، انسخ Connection String
    echo    From Settings ^> Database, copy Connection String
    echo.
    echo 5. افتح backend\.env وضع الرابط في DATABASE_URL
    echo    Open backend\.env and put the URL in DATABASE_URL
    echo.
    echo مثال:
    echo Example:
    echo DATABASE_URL="postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres"
    echo.
) else if "%choice%"=="2" (
    echo.
    echo 🗄️ إعداد PostgreSQL محلي:
    echo 🗄️ Local PostgreSQL Setup:
    echo.
    echo 1. حمل PostgreSQL من: https://www.postgresql.org/download/
    echo    Download PostgreSQL from: https://www.postgresql.org/download/
    echo.
    echo 2. ثبت PostgreSQL
    echo    Install PostgreSQL
    echo.
    echo 3. أنشئ قاعدة بيانات: shipment_management
    echo    Create database: shipment_management
    echo.
    echo 4. افتح backend\.env وعدل DATABASE_URL
    echo    Open backend\.env and edit DATABASE_URL
    echo.
    echo مثال:
    echo Example:
    echo DATABASE_URL="postgresql://postgres:password@localhost:5432/shipment_management"
    echo.
) else (
    echo.
    echo ⏭️ تم تخطي إعداد قاعدة البيانات
    echo ⏭️ Database setup skipped
    echo.
    echo يمكنك إعدادها لاحقاً من ملف backend\.env
    echo You can set it up later in backend\.env
    echo.
)

echo.
echo ========================================
echo              الخطوات التالية
echo              Next Steps
echo ========================================
echo.
echo 1. ✏️ عدل ملف backend\.env:
echo    📝 Edit backend\.env file:
echo.
echo    DATABASE_URL="your-database-url-here"
echo    JWT_SECRET="your-32-character-secret-key-here"
echo.
echo 2. 🗄️ إعداد قاعدة البيانات:
echo    🗄️ Setup database:
echo    npm run setup:db
echo.
echo 3. 🚀 تشغيل البرنامج:
echo    🚀 Start the application:
echo    start.bat
echo.
echo 4. 🌐 افتح المتصفح على:
echo    🌐 Open browser at:
echo    http://localhost:3000
echo.
echo ========================================
echo.
echo هل تريد فتح ملف .env للتعديل الآن؟
echo Do you want to open .env file for editing now?
echo.
set /p edit="(y/n): "

if /i "%edit%"=="y" (
    echo فتح ملف .env...
    echo Opening .env file...
    notepad backend\.env
)

echo.
echo ✅ الإعداد مكتمل!
echo ✅ Setup completed!
echo.
pause
