// قاعدة البيانات المحلية - Local Database
class LocalDatabase {
    constructor() {
        this.initializeData();
    }

    // تهيئة البيانات الأساسية
    initializeData() {
        // تهيئة الفروع
        if (!localStorage.getItem('branches')) {
            const branches = [
                {
                    id: 'BR001',
                    name: 'الفرع الرئيسي - الرياض',
                    code: 'RYD-MAIN',
                    city: 'الرياض',
                    region: 'منطقة الرياض',
                    country: 'السعودية',
                    address: 'شارع الملك فهد، حي العليا، الرياض',
                    phone: '+966112345678',
                    email: '<EMAIL>',
                    manager: 'أحمد محمد السعد',
                    isActive: true,
                    isMainBranch: true,
                    capacity: 1000,
                    currentLoad: 0,
                    coordinates: { lat: 24.7136, lng: 46.6753 },
                    workingHours: {
                        start: '08:00',
                        end: '18:00',
                        workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس']
                    },
                    services: ['استقبال الشحنات', 'توزيع محلي', 'تحويل بين الفروع', 'تخزين'],
                    createdDate: '2024-01-01'
                },
                {
                    id: 'BR002',
                    name: 'فرع جدة',
                    code: 'JED-01',
                    city: 'جدة',
                    region: 'منطقة مكة المكرمة',
                    country: 'السعودية',
                    address: 'شارع الأمير سلطان، حي الروضة، جدة',
                    phone: '+966126789012',
                    email: '<EMAIL>',
                    manager: 'فاطمة أحمد الزهراني',
                    isActive: true,
                    isMainBranch: false,
                    capacity: 800,
                    currentLoad: 0,
                    coordinates: { lat: 21.4858, lng: 39.1925 },
                    workingHours: {
                        start: '08:00',
                        end: '18:00',
                        workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس']
                    },
                    services: ['استقبال الشحنات', 'توزيع محلي', 'تحويل بين الفروع'],
                    createdDate: '2024-01-15'
                },
                {
                    id: 'BR003',
                    name: 'فرع الدمام',
                    code: 'DMM-01',
                    city: 'الدمام',
                    region: 'المنطقة الشرقية',
                    country: 'السعودية',
                    address: 'شارع الملك عبدالعزيز، حي الفيصلية، الدمام',
                    phone: '+966138901234',
                    email: '<EMAIL>',
                    manager: 'خالد عبدالله العتيبي',
                    isActive: true,
                    isMainBranch: false,
                    capacity: 600,
                    currentLoad: 0,
                    coordinates: { lat: 26.4207, lng: 50.0888 },
                    workingHours: {
                        start: '08:00',
                        end: '18:00',
                        workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس']
                    },
                    services: ['استقبال الشحنات', 'توزيع محلي', 'تحويل بين الفروع'],
                    createdDate: '2024-02-01'
                },
                {
                    id: 'BR004',
                    name: 'فرع الكويت الرئيسي',
                    code: 'KWT-MAIN',
                    city: 'الكويت',
                    region: 'محافظة العاصمة',
                    country: 'الكويت',
                    address: 'شارع الخليج العربي، منطقة الشرق، الكويت',
                    phone: '+96522345678',
                    email: '<EMAIL>',
                    manager: 'سارة محمد الصباح',
                    isActive: true,
                    isMainBranch: false,
                    capacity: 500,
                    currentLoad: 0,
                    coordinates: { lat: 29.3759, lng: 47.9774 },
                    workingHours: {
                        start: '08:00',
                        end: '17:00',
                        workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس']
                    },
                    services: ['استقبال الشحنات', 'توزيع محلي', 'تحويل دولي'],
                    createdDate: '2024-02-15'
                },
                {
                    id: 'BR005',
                    name: 'فرع حولي',
                    code: 'HWL-01',
                    city: 'حولي',
                    region: 'محافظة حولي',
                    country: 'الكويت',
                    address: 'شارع سالم المبارك، منطقة السالمية، حولي',
                    phone: '+96523456789',
                    email: '<EMAIL>',
                    manager: 'عبدالله أحمد الرشيد',
                    isActive: true,
                    isMainBranch: false,
                    capacity: 300,
                    currentLoad: 0,
                    coordinates: { lat: 29.3375, lng: 48.0758 },
                    workingHours: {
                        start: '08:30',
                        end: '17:30',
                        workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس']
                    },
                    services: ['استقبال الشحنات', 'توزيع محلي'],
                    createdDate: '2024-03-01'
                }
            ];
            localStorage.setItem('branches', JSON.stringify(branches));
        }

        // تهيئة تحويلات الفروع
        if (!localStorage.getItem('branchTransfers')) {
            const branchTransfers = [];
            localStorage.setItem('branchTransfers', JSON.stringify(branchTransfers));
        }

        // تهيئة أسباب الإلغاء
        if (!localStorage.getItem('cancellationReasons')) {
            const cancellationReasons = [
                // أسباب عدم التوصيل
                { id: 1, reason: 'العميل غير متواجد بالمتجر', category: 'اسباب عدم التوصيل' },
                { id: 2, reason: 'العميل طلب تغيير الميعاد', category: 'اسباب عدم التوصيل' },
                { id: 3, reason: 'العميل طلب تغيير العنوان', category: 'اسباب عدم التوصيل' },
                { id: 4, reason: 'العميل لم يرد علي الهاتف', category: 'اسباب عدم التوصيل' },
                { id: 5, reason: 'العميل طلب تأجيل الاستلام', category: 'اسباب عدم التوصيل' },
                { id: 6, reason: 'العميل رفض الاستلام', category: 'اسباب عدم التوصيل' },
                { id: 7, reason: 'العميل يتهرب من الاستلام', category: 'اسباب عدم التوصيل' },
                { id: 8, reason: 'الهاتف لا يخص العميل', category: 'اسباب عدم التوصيل' },
                { id: 9, reason: 'الهاتف ناقص', category: 'اسباب عدم التوصيل' },
                { id: 10, reason: 'الهاتف مغلق', category: 'اسباب عدم التوصيل' },
                { id: 11, reason: 'الهاتف مشغول', category: 'اسباب عدم التوصيل' },
                { id: 12, reason: 'الهاتف لا يجمع', category: 'اسباب عدم التوصيل' },
                { id: 13, reason: 'العميل يرد الاستلام مساءا', category: 'اسباب عدم التوصيل' },
                { id: 14, reason: 'الهاتف بريد صوتي', category: 'اسباب عدم التوصيل' },
                { id: 20, reason: 'الشحنه ملغية', category: 'اسباب عدم التوصيل' },
                { id: 26, reason: 'طلب الغاء من التاجر', category: 'اسباب عدم التوصيل' },
                { id: 30, reason: 'يتهرب', category: 'اسباب عدم التوصيل' },

                // أسباب رفض الشحنات
                { id: 15, reason: 'شحنه مكرره', category: 'اسباب رفض الشحنات' },
                { id: 16, reason: 'لم يتم استلامها من العميل', category: 'اسباب رفض الشحنات' },
                { id: 17, reason: 'شحنه مرفوضه لصعوبه نقلها', category: 'اسباب رفض الشحنات' },
                { id: 18, reason: 'سبب اخر', category: 'اسباب رفض الشحنات' },
                { id: 19, reason: 'تم الغها من العميل', category: 'اسباب رفض الشحنات' },
                { id: 69, reason: 'الزبون غير لطيف', category: 'اسباب رفض الشحنات' },
                { id: 123, reason: 'غير جاد', category: 'اسباب رفض الشحنات' }
            ];
            localStorage.setItem('cancellationReasons', JSON.stringify(cancellationReasons));
        }

        if (!localStorage.getItem('shipments')) {
            const initialShipments = [
                {
                    id: 'SHP001',
                    trackingNumber: 'SHP001',
                    senderName: 'شركة التجارة المتقدمة',
                    senderPhone: '+966501234567',
                    senderAddress: 'شارع الملك فهد، الرياض',
                    receiverName: 'مؤسسة الخليج للتوريدات',
                    receiverPhone: '+971505678901',
                    receiverAddress: 'شارع الشيخ زايد، دبي',
                    weight: 5.5,
                    contents: 'قطع غيار إلكترونية',
                    cost: 150.00,
                    currency: 'SAR',
                    status: 'في الطريق',
                    distributorName: 'محمد أحمد',
                    createdDate: '2024-01-15',
                    estimatedDelivery: '2024-01-18',
                    notes: 'شحنة عاجلة - يرجى التعامل بحذر'
                },
                {
                    id: 'SHP002',
                    trackingNumber: 'SHP002',
                    senderName: 'أحمد محمد العلي',
                    senderPhone: '+966506789012',
                    senderAddress: 'حي النخيل، جدة',
                    receiverName: 'فاطمة سالم',
                    receiverPhone: '+966507890123',
                    receiverAddress: 'شارع الأمير سلطان، الدمام',
                    weight: 2.0,
                    contents: 'مستندات مهمة',
                    cost: 75.00,
                    currency: 'SAR',
                    status: 'معلق',
                    distributorName: 'سارة علي',
                    createdDate: '2024-01-16',
                    estimatedDelivery: '2024-01-19',
                    notes: 'تسليم يدوي فقط'
                },
                {
                    id: 'SHP003',
                    trackingNumber: 'SHP003',
                    senderName: 'شركة النور للتجارة',
                    senderPhone: '+965501234567',
                    senderAddress: 'السالمية - حولي - الكويت',
                    receiverName: 'مركز التوزيع الرئيسي',
                    receiverPhone: '+966508901234',
                    receiverAddress: 'المنطقة الصناعية، الرياض',
                    weight: 15.0,
                    contents: 'معدات طبية',
                    cost: 300.00,
                    currency: 'SAR',
                    status: 'مسلم',
                    distributorName: 'خالد محمود',
                    createdDate: '2024-01-14',
                    estimatedDelivery: '2024-01-17',
                    actualDelivery: '2024-01-17',
                    notes: 'تم التسليم بنجاح'
                },
                {
                    id: 'SHP004',
                    trackingNumber: 'KWT001',
                    senderName: 'خالد أحمد الصباح',
                    senderPhone: '+965502345678',
                    senderAddress: 'الجابرية - حولي - الكويت',
                    receiverName: 'شركة الخليج للاستيراد',
                    receiverPhone: '+965504567890',
                    receiverAddress: 'الشرق - مدينة الكويت - الكويت',
                    weight: 5.5,
                    contents: 'وثائق ومستندات',
                    cost: 15.750,
                    currency: 'KWD',
                    status: 'في الطريق',
                    distributorName: 'أحمد الكويتي',
                    createdDate: '2024-01-16',
                    estimatedDelivery: '2024-01-18',
                    notes: 'شحنة داخلية في الكويت - دينار كويتي'
                },
                {
                    id: 'SHP005',
                    trackingNumber: 'KWT002',
                    senderName: 'نورا محمد العتيبي',
                    senderPhone: '+965503456789',
                    senderAddress: 'الفروانية - الفروانية - الكويت',
                    receiverName: 'سارة أحمد',
                    receiverPhone: '+966507654321',
                    receiverAddress: 'العليا - الرياض - السعودية',
                    weight: 3.2,
                    contents: 'هدايا شخصية',
                    cost: 25.500,
                    currency: 'KWD',
                    status: 'معلق',
                    distributorName: 'محمد الشمري',
                    createdDate: '2024-01-17',
                    estimatedDelivery: '2024-01-20',
                    notes: 'شحنة دولية من الكويت للسعودية'
                }
            ];
            localStorage.setItem('shipments', JSON.stringify(initialShipments));
        }

        if (!localStorage.getItem('customers')) {
            const initialCustomers = [
                {
                    id: 'CUST001',
                    name: 'شركة التجارة المتقدمة',
                    email: '<EMAIL>',
                    phone: '+966501234567',
                    address: 'العليا - الرياض - السعودية',
                    city: 'الرياض',
                    country: 'السعودية'
                },
                {
                    id: 'CUST002',
                    name: 'مؤسسة الخليج للتوريدات',
                    email: '<EMAIL>',
                    phone: '+971505678901',
                    address: 'الخليج التجاري - دبي - الإمارات',
                    city: 'دبي',
                    country: 'الإمارات'
                },
                {
                    id: 'CUST003',
                    name: 'أحمد محمد العلي',
                    email: '<EMAIL>',
                    phone: '+966506789012',
                    address: 'الحمراء - جدة - السعودية',
                    city: 'جدة',
                    country: 'السعودية'
                },
                {
                    id: 'CUST004',
                    name: 'فاطمة سالم',
                    email: '<EMAIL>',
                    phone: '+966507890123',
                    address: 'الفيصلية - الدمام - السعودية',
                    city: 'الدمام',
                    country: 'السعودية'
                },
                {
                    id: 'CUST005',
                    name: 'شركة النور للتجارة',
                    email: '<EMAIL>',
                    phone: '+965501234567',
                    address: 'السالمية - حولي - الكويت',
                    city: 'حولي',
                    country: 'الكويت'
                },
                {
                    id: 'CUST006',
                    name: 'خالد أحمد الصباح',
                    email: '<EMAIL>',
                    phone: '+965502345678',
                    address: 'الجابرية - حولي - الكويت',
                    city: 'حولي',
                    country: 'الكويت'
                },
                {
                    id: 'CUST007',
                    name: 'نورا محمد العتيبي',
                    email: '<EMAIL>',
                    phone: '+965503456789',
                    address: 'الفروانية - الفروانية - الكويت',
                    city: 'الفروانية',
                    country: 'الكويت'
                },
                {
                    id: 'CUST008',
                    name: 'شركة الخليج للاستيراد',
                    email: '<EMAIL>',
                    phone: '+965504567890',
                    address: 'الشرق - مدينة الكويت - الكويت',
                    city: 'مدينة الكويت',
                    country: 'الكويت'
                },
                {
                    id: 'CUST006',
                    name: 'محمد خالد الأحمد',
                    email: '<EMAIL>',
                    phone: '+971506789012',
                    address: 'مارينا - دبي - الإمارات',
                    city: 'دبي',
                    country: 'الإمارات'
                },
                {
                    id: 'CUST007',
                    name: 'سارة عبدالله',
                    email: '<EMAIL>',
                    phone: '+966508901234',
                    address: 'الروضة - جدة - السعودية',
                    city: 'جدة',
                    country: 'السعودية'
                },
                {
                    id: 'CUST008',
                    name: 'مركز التوزيع الرئيسي',
                    email: '<EMAIL>',
                    phone: '+966509012345',
                    address: 'الملز - الرياض - السعودية',
                    city: 'الرياض',
                    country: 'السعودية'
                }
            ];
            localStorage.setItem('customers', JSON.stringify(initialCustomers));
        }

        if (!localStorage.getItem('distributors')) {
            const initialDistributors = [
                {
                    id: 'DIST001',
                    name: 'محمد أحمد',
                    phone: '+966509876543',
                    vehicleType: 'شاحنة صغيرة',
                    vehicleNumber: 'ABC-123',
                    area: 'الرياض',
                    rating: 4.8,
                    totalDeliveries: 45,
                    isAvailable: true
                },
                {
                    id: 'DIST002',
                    name: 'سارة علي',
                    phone: '+966508765432',
                    vehicleType: 'سيارة نقل',
                    vehicleNumber: 'DEF-456',
                    area: 'جدة',
                    rating: 4.7,
                    totalDeliveries: 38,
                    isAvailable: true
                },
                {
                    id: 'DIST003',
                    name: 'خالد محمود',
                    phone: '+966507654321',
                    vehicleType: 'دراجة نارية',
                    vehicleNumber: 'GHI-789',
                    area: 'الدمام',
                    rating: 4.6,
                    totalDeliveries: 32,
                    isAvailable: false
                }
            ];
            localStorage.setItem('distributors', JSON.stringify(initialDistributors));
        }
    }

    // الحصول على جميع الشحنات
    getAllShipments() {
        return JSON.parse(localStorage.getItem('shipments') || '[]');
    }

    // الحصول على شحنة بالمعرف
    getShipmentById(id) {
        const shipments = this.getAllShipments();
        return shipments.find(shipment => shipment.id === id);
    }

    // إضافة شحنة جديدة
    addShipment(shipmentData) {
        const shipments = this.getAllShipments();
        const newId = 'SHP' + String(shipments.length + 1).padStart(3, '0');
        
        const newShipment = {
            id: newId,
            trackingNumber: newId,
            ...shipmentData,
            createdDate: new Date().toISOString().split('T')[0],
            status: shipmentData.status || 'معلق'
        };
        
        shipments.push(newShipment);
        localStorage.setItem('shipments', JSON.stringify(shipments));
        return newShipment;
    }

    // تحديث شحنة
    updateShipment(id, updatedData) {
        console.log('تحديث الشحنة في قاعدة البيانات:', id, updatedData);

        const shipments = this.getAllShipments();
        const index = shipments.findIndex(shipment => shipment.id === id);

        if (index !== -1) {
            // الاحتفاظ بالبيانات الأساسية مثل ID ورقم التتبع وتاريخ الإنشاء
            const originalShipment = shipments[index];
            shipments[index] = {
                ...originalShipment,
                ...updatedData,
                id: originalShipment.id, // التأكد من عدم تغيير المعرف
                trackingNumber: originalShipment.trackingNumber, // التأكد من عدم تغيير رقم التتبع
                createdDate: originalShipment.createdDate, // التأكد من عدم تغيير تاريخ الإنشاء
                updatedDate: new Date().toISOString().split('T')[0] // إضافة تاريخ التحديث
            };

            localStorage.setItem('shipments', JSON.stringify(shipments));
            console.log('تم تحديث الشحنة بنجاح:', shipments[index]);
            return shipments[index];
        }

        console.error('لم يتم العثور على الشحنة للتحديث:', id);
        return null;
    }

    // حذف شحنة
    deleteShipment(id) {
        const shipments = this.getAllShipments();
        const filteredShipments = shipments.filter(shipment => shipment.id !== id);
        localStorage.setItem('shipments', JSON.stringify(filteredShipments));
        return true;
    }

    // إلغاء شحنة مع سبب الإلغاء
    cancelShipment(id, cancellationReasonId, notes = '') {
        try {
            const shipments = this.getAllShipments();
            const shipmentIndex = shipments.findIndex(s => s.id === id);

            if (shipmentIndex === -1) {
                return false;
            }

            const cancellationReason = this.getCancellationReasonById(cancellationReasonId);

            shipments[shipmentIndex] = {
                ...shipments[shipmentIndex],
                status: 'ملغي',
                cancellationReasonId: cancellationReasonId,
                cancellationReason: cancellationReason ? cancellationReason.reason : 'سبب غير محدد',
                cancellationCategory: cancellationReason ? cancellationReason.category : 'غير محدد',
                cancellationDate: new Date().toISOString().split('T')[0],
                cancellationNotes: notes,
                updatedDate: new Date().toISOString()
            };

            localStorage.setItem('shipments', JSON.stringify(shipments));
            return shipments[shipmentIndex];
        } catch (error) {
            console.error('خطأ في إلغاء الشحنة:', error);
            return false;
        }
    }

    // الحصول على جميع أسباب الإلغاء
    getAllCancellationReasons() {
        try {
            const reasons = localStorage.getItem('cancellationReasons');
            return reasons ? JSON.parse(reasons) : [];
        } catch (error) {
            console.error('خطأ في استرجاع أسباب الإلغاء:', error);
            return [];
        }
    }

    // الحصول على أسباب الإلغاء حسب الفئة
    getCancellationReasonsByCategory(category) {
        try {
            const allReasons = this.getAllCancellationReasons();
            return allReasons.filter(reason => reason.category === category);
        } catch (error) {
            console.error('خطأ في استرجاع أسباب الإلغاء حسب الفئة:', error);
            return [];
        }
    }

    // الحصول على سبب إلغاء محدد
    getCancellationReasonById(id) {
        try {
            const allReasons = this.getAllCancellationReasons();
            return allReasons.find(reason => reason.id === parseInt(id));
        } catch (error) {
            console.error('خطأ في استرجاع سبب الإلغاء:', error);
            return null;
        }
    }

    // الحصول على فئات أسباب الإلغاء
    getCancellationCategories() {
        try {
            const allReasons = this.getAllCancellationReasons();
            const categories = [...new Set(allReasons.map(reason => reason.category))];
            return categories;
        } catch (error) {
            console.error('خطأ في استرجاع فئات أسباب الإلغاء:', error);
            return [];
        }
    }

    // إحصائيات الإلغاء
    getCancellationStats() {
        try {
            const shipments = this.getAllShipments();
            const cancelledShipments = shipments.filter(s => s.status === 'ملغي');

            const stats = {
                totalCancelled: cancelledShipments.length,
                byCategory: {},
                byReason: {},
                recentCancellations: []
            };

            // إحصائيات حسب الفئة
            cancelledShipments.forEach(shipment => {
                const category = shipment.cancellationCategory || 'غير محدد';
                stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
            });

            // إحصائيات حسب السبب
            cancelledShipments.forEach(shipment => {
                const reason = shipment.cancellationReason || 'غير محدد';
                stats.byReason[reason] = (stats.byReason[reason] || 0) + 1;
            });

            // الإلغاءات الحديثة (آخر 30 يوم)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            stats.recentCancellations = cancelledShipments.filter(shipment => {
                if (!shipment.cancellationDate) return false;
                const cancellationDate = new Date(shipment.cancellationDate);
                return cancellationDate >= thirtyDaysAgo;
            });

            return stats;
        } catch (error) {
            console.error('خطأ في حساب إحصائيات الإلغاء:', error);
            return {
                totalCancelled: 0,
                byCategory: {},
                byReason: {},
                recentCancellations: []
            };
        }
    }

    // === وظائف إدارة الفروع ===

    // الحصول على جميع الفروع
    getAllBranches() {
        try {
            const branches = localStorage.getItem('branches');
            return branches ? JSON.parse(branches) : [];
        } catch (error) {
            console.error('خطأ في استرجاع الفروع:', error);
            return [];
        }
    }

    // الحصول على الفروع النشطة فقط
    getActiveBranches() {
        try {
            const allBranches = this.getAllBranches();
            return allBranches.filter(branch => branch.isActive);
        } catch (error) {
            console.error('خطأ في استرجاع الفروع النشطة:', error);
            return [];
        }
    }

    // الحصول على فرع محدد
    getBranchById(id) {
        try {
            const branches = this.getAllBranches();
            return branches.find(branch => branch.id === id);
        } catch (error) {
            console.error('خطأ في استرجاع الفرع:', error);
            return null;
        }
    }

    // الحصول على الفرع الرئيسي
    getMainBranch() {
        try {
            const branches = this.getAllBranches();
            return branches.find(branch => branch.isMainBranch);
        } catch (error) {
            console.error('خطأ في استرجاع الفرع الرئيسي:', error);
            return null;
        }
    }

    // الحصول على الفروع حسب المدينة
    getBranchesByCity(city) {
        try {
            const branches = this.getAllBranches();
            return branches.filter(branch => branch.city === city && branch.isActive);
        } catch (error) {
            console.error('خطأ في استرجاع فروع المدينة:', error);
            return [];
        }
    }

    // الحصول على الفروع حسب البلد
    getBranchesByCountry(country) {
        try {
            const branches = this.getAllBranches();
            return branches.filter(branch => branch.country === country && branch.isActive);
        } catch (error) {
            console.error('خطأ في استرجاع فروع البلد:', error);
            return [];
        }
    }

    // إضافة فرع جديد
    addBranch(branchData) {
        try {
            const branches = this.getAllBranches();

            // إنشاء معرف فريد للفرع
            const newId = 'BR' + String(branches.length + 1).padStart(3, '0');

            const newBranch = {
                id: newId,
                ...branchData,
                currentLoad: 0,
                isActive: true,
                createdDate: new Date().toISOString().split('T')[0],
                updatedDate: new Date().toISOString()
            };

            branches.push(newBranch);
            localStorage.setItem('branches', JSON.stringify(branches));

            return newBranch;
        } catch (error) {
            console.error('خطأ في إضافة الفرع:', error);
            return false;
        }
    }

    // تحديث فرع
    updateBranch(id, updateData) {
        try {
            const branches = this.getAllBranches();
            const branchIndex = branches.findIndex(branch => branch.id === id);

            if (branchIndex === -1) {
                return false;
            }

            branches[branchIndex] = {
                ...branches[branchIndex],
                ...updateData,
                updatedDate: new Date().toISOString()
            };

            localStorage.setItem('branches', JSON.stringify(branches));
            return branches[branchIndex];
        } catch (error) {
            console.error('خطأ في تحديث الفرع:', error);
            return false;
        }
    }

    // حذف فرع (إلغاء تفعيل)
    deleteBranch(id) {
        try {
            return this.updateBranch(id, { isActive: false });
        } catch (error) {
            console.error('خطأ في حذف الفرع:', error);
            return false;
        }
    }

    // === وظائف تحويل الشحنات بين الفروع ===

    // الحصول على جميع التحويلات
    getAllBranchTransfers() {
        try {
            const transfers = localStorage.getItem('branchTransfers');
            return transfers ? JSON.parse(transfers) : [];
        } catch (error) {
            console.error('خطأ في استرجاع التحويلات:', error);
            return [];
        }
    }

    // إنشاء تحويل جديد بين الفروع
    createBranchTransfer(transferData) {
        try {
            const transfers = this.getAllBranchTransfers();

            // إنشاء معرف فريد للتحويل
            const newId = 'TRF' + String(transfers.length + 1).padStart(6, '0');

            const newTransfer = {
                id: newId,
                shipmentId: transferData.shipmentId,
                fromBranchId: transferData.fromBranchId,
                toBranchId: transferData.toBranchId,
                fromBranchName: transferData.fromBranchName,
                toBranchName: transferData.toBranchName,
                transferReason: transferData.transferReason || 'تحويل عادي',
                status: 'معلق', // معلق، في الطريق، مستلم، ملغي
                requestedBy: transferData.requestedBy || 'النظام',
                requestDate: new Date().toISOString(),
                estimatedArrival: transferData.estimatedArrival,
                actualArrival: null,
                receivedBy: null,
                notes: transferData.notes || '',
                priority: transferData.priority || 'عادي', // عادي، عاجل، طارئ
                trackingNumber: 'TRK' + newId,
                createdDate: new Date().toISOString()
            };

            transfers.push(newTransfer);
            localStorage.setItem('branchTransfers', JSON.stringify(transfers));

            // تحديث حالة الشحنة
            this.updateShipment(transferData.shipmentId, {
                status: 'في التحويل',
                currentBranch: transferData.fromBranchId,
                destinationBranch: transferData.toBranchId,
                transferId: newId
            });

            return newTransfer;
        } catch (error) {
            console.error('خطأ في إنشاء التحويل:', error);
            return false;
        }
    }

    // تحديث حالة التحويل
    updateTransferStatus(transferId, status, additionalData = {}) {
        try {
            const transfers = this.getAllBranchTransfers();
            const transferIndex = transfers.findIndex(t => t.id === transferId);

            if (transferIndex === -1) {
                return false;
            }

            transfers[transferIndex] = {
                ...transfers[transferIndex],
                status: status,
                ...additionalData,
                updatedDate: new Date().toISOString()
            };

            localStorage.setItem('branchTransfers', JSON.stringify(transfers));

            // تحديث حالة الشحنة حسب حالة التحويل
            const transfer = transfers[transferIndex];
            let shipmentStatus = 'في التحويل';
            let currentBranch = transfer.fromBranchId;

            if (status === 'مستلم') {
                shipmentStatus = 'في الفرع';
                currentBranch = transfer.toBranchId;
            } else if (status === 'في الطريق') {
                shipmentStatus = 'في الطريق للفرع';
            } else if (status === 'ملغي') {
                shipmentStatus = 'في الفرع';
                currentBranch = transfer.fromBranchId;
            }

            this.updateShipment(transfer.shipmentId, {
                status: shipmentStatus,
                currentBranch: currentBranch
            });

            return transfers[transferIndex];
        } catch (error) {
            console.error('خطأ في تحديث حالة التحويل:', error);
            return false;
        }
    }

    // استلام شحنة من فرع آخر
    receiveTransfer(transferId, receivedBy, notes = '') {
        try {
            const additionalData = {
                actualArrival: new Date().toISOString(),
                receivedBy: receivedBy,
                notes: notes
            };

            return this.updateTransferStatus(transferId, 'مستلم', additionalData);
        } catch (error) {
            console.error('خطأ في استلام التحويل:', error);
            return false;
        }
    }

    // الحصول على التحويلات حسب الفرع
    getTransfersByBranch(branchId, type = 'all') {
        try {
            const transfers = this.getAllBranchTransfers();

            if (type === 'incoming') {
                return transfers.filter(t => t.toBranchId === branchId);
            } else if (type === 'outgoing') {
                return transfers.filter(t => t.fromBranchId === branchId);
            } else {
                return transfers.filter(t => t.fromBranchId === branchId || t.toBranchId === branchId);
            }
        } catch (error) {
            console.error('خطأ في استرجاع تحويلات الفرع:', error);
            return [];
        }
    }

    // الحصول على التحويلات المعلقة للفرع
    getPendingTransfersForBranch(branchId) {
        try {
            const transfers = this.getAllBranchTransfers();
            return transfers.filter(t =>
                t.toBranchId === branchId &&
                (t.status === 'معلق' || t.status === 'في الطريق')
            );
        } catch (error) {
            console.error('خطأ في استرجاع التحويلات المعلقة:', error);
            return [];
        }
    }

    // الحصول على إحصائيات الفروع
    getBranchStats(branchId = null) {
        try {
            const shipments = this.getAllShipments();
            const transfers = this.getAllBranchTransfers();

            if (branchId) {
                // إحصائيات فرع محدد
                const branchShipments = shipments.filter(s => s.currentBranch === branchId);
                const incomingTransfers = transfers.filter(t => t.toBranchId === branchId);
                const outgoingTransfers = transfers.filter(t => t.fromBranchId === branchId);
                const pendingReceives = transfers.filter(t =>
                    t.toBranchId === branchId &&
                    (t.status === 'معلق' || t.status === 'في الطريق')
                );

                return {
                    totalShipments: branchShipments.length,
                    pendingShipments: branchShipments.filter(s => s.status === 'معلق').length,
                    inTransitShipments: branchShipments.filter(s => s.status === 'في الطريق').length,
                    deliveredShipments: branchShipments.filter(s => s.status === 'مسلم').length,
                    incomingTransfers: incomingTransfers.length,
                    outgoingTransfers: outgoingTransfers.length,
                    pendingReceives: pendingReceives.length,
                    totalTransfers: incomingTransfers.length + outgoingTransfers.length
                };
            } else {
                // إحصائيات عامة لجميع الفروع
                const branches = this.getAllBranches();
                const stats = {};

                branches.forEach(branch => {
                    stats[branch.id] = this.getBranchStats(branch.id);
                });

                return stats;
            }
        } catch (error) {
            console.error('خطأ في حساب إحصائيات الفروع:', error);
            return {};
        }
    }

    // البحث في الفروع
    searchBranches(query) {
        try {
            const branches = this.getAllBranches();
            const lowerQuery = query.toLowerCase();

            return branches.filter(branch =>
                branch.name.toLowerCase().includes(lowerQuery) ||
                branch.code.toLowerCase().includes(lowerQuery) ||
                branch.city.toLowerCase().includes(lowerQuery) ||
                branch.manager.toLowerCase().includes(lowerQuery) ||
                branch.address.toLowerCase().includes(lowerQuery)
            );
        } catch (error) {
            console.error('خطأ في البحث في الفروع:', error);
            return [];
        }
    }

    // الحصول على شحنة بالمعرف
    getShipmentById(id) {
        const shipments = this.getAllShipments();
        return shipments.find(shipment => shipment.id === id);
    }

    // الحصول على جميع العملاء
    getAllCustomers() {
        return JSON.parse(localStorage.getItem('customers') || '[]');
    }

    // إضافة عميل جديد
    addCustomer(customerData) {
        const customers = this.getAllCustomers();
        const newId = 'CUST' + String(customers.length + 1).padStart(3, '0');
        
        const newCustomer = {
            id: newId,
            ...customerData
        };
        
        customers.push(newCustomer);
        localStorage.setItem('customers', JSON.stringify(customers));
        return newCustomer;
    }

    // الحصول على جميع الموزعين
    getAllDistributors() {
        return JSON.parse(localStorage.getItem('distributors') || '[]');
    }

    // إضافة موزع جديد
    addDistributor(distributorData) {
        const distributors = this.getAllDistributors();
        const newId = 'DIST' + String(distributors.length + 1).padStart(3, '0');
        
        const newDistributor = {
            id: newId,
            ...distributorData,
            rating: 0,
            totalDeliveries: 0,
            isAvailable: true
        };
        
        distributors.push(newDistributor);
        localStorage.setItem('distributors', JSON.stringify(distributors));
        return newDistributor;
    }

    // البحث في الشحنات
    searchShipments(query) {
        const shipments = this.getAllShipments();
        const lowerQuery = query.toLowerCase();

        return shipments.filter(shipment =>
            shipment.trackingNumber.toLowerCase().includes(lowerQuery) ||
            shipment.senderName.toLowerCase().includes(lowerQuery) ||
            shipment.receiverName.toLowerCase().includes(lowerQuery) ||
            shipment.contents.toLowerCase().includes(lowerQuery)
        );
    }

    // البحث عن عميل بالجوال
    findCustomerByPhone(phone) {
        const customers = this.getAllCustomers();
        return customers.find(customer => customer.phone === phone);
    }

    // البحث عن عميل بالاسم
    searchCustomersByName(name) {
        const customers = this.getAllCustomers();
        const lowerName = name.toLowerCase();
        return customers.filter(customer =>
            customer.name.toLowerCase().includes(lowerName)
        );
    }

    // الحصول على جميع المناطق الجغرافية
    getAllAreas() {
        return [
            // منطقة الرياض
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'العليا', district: 'حي العليا', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'الملز', district: 'حي الملز', zipCode: '11566' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'النخيل', district: 'حي النخيل', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'الورود', district: 'حي الورود', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'الياسمين', district: 'حي الياسمين', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'الملك فهد', district: 'حي الملك فهد', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'الصحافة', district: 'حي الصحافة', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'النرجس', district: 'حي النرجس', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'الربوة', district: 'حي الربوة', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'المروج', district: 'حي المروج', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'الحمراء', district: 'حي الحمراء', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'الديرة', district: 'حي الديرة', zipCode: '11564' },
            { country: 'السعودية', region: 'منطقة الرياض', city: 'الرياض', area: 'البطحاء', district: 'حي البطحاء', zipCode: '11564' },

            // منطقة مكة المكرمة
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'جدة', area: 'الحمراء', district: 'حي الحمراء', zipCode: '21577' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'جدة', area: 'الروضة', district: 'حي الروضة', zipCode: '21577' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'جدة', area: 'الزهراء', district: 'حي الزهراء', zipCode: '21577' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'جدة', area: 'الصفا', district: 'حي الصفا', zipCode: '21577' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'جدة', area: 'النزهة', district: 'حي النزهة', zipCode: '21577' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'جدة', area: 'الشاطئ', district: 'حي الشاطئ', zipCode: '21577' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'جدة', area: 'الكورنيش', district: 'حي الكورنيش', zipCode: '21577' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'جدة', area: 'الفيصلية', district: 'حي الفيصلية', zipCode: '21577' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'مكة المكرمة', area: 'العزيزية', district: 'حي العزيزية', zipCode: '21955' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'مكة المكرمة', area: 'الشوقية', district: 'حي الشوقية', zipCode: '21955' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'الطائف', area: 'الحوية', district: 'حي الحوية', zipCode: '26571' },
            { country: 'السعودية', region: 'منطقة مكة المكرمة', city: 'الطائف', area: 'الشهداء', district: 'حي الشهداء', zipCode: '26571' },

            // المنطقة الشرقية
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الدمام', area: 'الفيصلية', district: 'حي الفيصلية', zipCode: '32241' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الدمام', area: 'الشاطئ', district: 'حي الشاطئ', zipCode: '32241' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الدمام', area: 'الجلوية', district: 'حي الجلوية', zipCode: '32241' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الخبر', area: 'العقربية', district: 'حي العقربية', zipCode: '34428' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الخبر', area: 'الثقبة', district: 'حي الثقبة', zipCode: '34428' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الخبر', area: 'الكورنيش', district: 'حي الكورنيش', zipCode: '34428' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الظهران', area: 'الدوحة', district: 'حي الدوحة', zipCode: '34465' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'القطيف', area: 'الناصرة', district: 'حي الناصرة', zipCode: '32654' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الجبيل', area: 'الصناعية', district: 'المدينة الصناعية', zipCode: '35718' },
            { country: 'السعودية', region: 'المنطقة الشرقية', city: 'الأحساء', area: 'الهفوف', district: 'حي الهفوف', zipCode: '36441' },

            // منطقة المدينة المنورة
            { country: 'السعودية', region: 'منطقة المدينة المنورة', city: 'المدينة المنورة', area: 'العوالي', district: 'حي العوالي', zipCode: '42311' },
            { country: 'السعودية', region: 'منطقة المدينة المنورة', city: 'المدينة المنورة', area: 'الحرم', district: 'منطقة الحرم', zipCode: '42311' },
            { country: 'السعودية', region: 'منطقة المدينة المنورة', city: 'ينبع', area: 'الصناعية', district: 'ينبع الصناعية', zipCode: '46455' },

            // منطقة القصيم
            { country: 'السعودية', region: 'منطقة القصيم', city: 'بريدة', area: 'الإسكان', district: 'حي الإسكان', zipCode: '51431' },
            { country: 'السعودية', region: 'منطقة القصيم', city: 'عنيزة', area: 'المنتزه', district: 'حي المنتزه', zipCode: '56219' },

            // منطقة عسير
            { country: 'السعودية', region: 'منطقة عسير', city: 'أبها', area: 'الموظفين', district: 'حي الموظفين', zipCode: '62521' },
            { country: 'السعودية', region: 'منطقة عسير', city: 'خميس مشيط', area: 'أم سرار', district: 'حي أم سرار', zipCode: '62411' },

            // منطقة تبوك
            { country: 'السعودية', region: 'منطقة تبوك', city: 'تبوك', area: 'السليمانية', district: 'حي السليمانية', zipCode: '71491' },

            // منطقة حائل
            { country: 'السعودية', region: 'منطقة حائل', city: 'حائل', area: 'الصناعية', district: 'المنطقة الصناعية', zipCode: '81411' },

            // منطقة الحدود الشمالية
            { country: 'السعودية', region: 'منطقة الحدود الشمالية', city: 'عرعر', area: 'الناصرية', district: 'حي الناصرية', zipCode: '91431' },

            // منطقة جازان
            { country: 'السعودية', region: 'منطقة جازان', city: 'جازان', area: 'الروضة', district: 'حي الروضة', zipCode: '82611' },

            // منطقة نجران
            { country: 'السعودية', region: 'منطقة نجران', city: 'نجران', area: 'الفهد', district: 'حي الفهد', zipCode: '66262' },

            // منطقة الباحة
            { country: 'السعودية', region: 'منطقة الباحة', city: 'الباحة', area: 'الظفير', district: 'حي الظفير', zipCode: '65311' },

            // منطقة الجوف
            { country: 'السعودية', region: 'منطقة الجوف', city: 'سكاكا', area: 'الملك فهد', district: 'حي الملك فهد', zipCode: '72341' },

            // دولة الكويت - محافظة العاصمة
            { country: 'الكويت', region: 'محافظة العاصمة', city: 'مدينة الكويت', area: 'الشرق', district: 'منطقة الشرق', zipCode: '15300' },
            { country: 'الكويت', region: 'محافظة العاصمة', city: 'مدينة الكويت', area: 'المرقاب', district: 'منطقة المرقاب', zipCode: '15301' },
            { country: 'الكويت', region: 'محافظة العاصمة', city: 'مدينة الكويت', area: 'القبلة', district: 'منطقة القبلة', zipCode: '15302' },
            { country: 'الكويت', region: 'محافظة العاصمة', city: 'مدينة الكويت', area: 'جبلة', district: 'منطقة جبلة', zipCode: '15303' },
            { country: 'الكويت', region: 'محافظة العاصمة', city: 'مدينة الكويت', area: 'دسمان', district: 'منطقة دسمان', zipCode: '15304' },
            { country: 'الكويت', region: 'محافظة العاصمة', city: 'مدينة الكويت', area: 'الصوابر', district: 'منطقة الصوابر', zipCode: '15305' },
            { country: 'الكويت', region: 'محافظة العاصمة', city: 'مدينة الكويت', area: 'الدعية', district: 'منطقة الدعية', zipCode: '15306' },
            { country: 'الكويت', region: 'محافظة العاصمة', city: 'مدينة الكويت', area: 'كيفان', district: 'منطقة كيفان', zipCode: '15307' },

            // محافظة حولي
            { country: 'الكويت', region: 'محافظة حولي', city: 'حولي', area: 'حولي', district: 'منطقة حولي', zipCode: '22060' },
            { country: 'الكويت', region: 'محافظة حولي', city: 'حولي', area: 'السالمية', district: 'منطقة السالمية', zipCode: '22061' },
            { country: 'الكويت', region: 'محافظة حولي', city: 'حولي', area: 'الجابرية', district: 'منطقة الجابرية', zipCode: '22062' },
            { country: 'الكويت', region: 'محافظة حولي', city: 'حولي', area: 'ميدان حولي', district: 'منطقة ميدان حولي', zipCode: '22063' },
            { country: 'الكويت', region: 'محافظة حولي', city: 'حولي', area: 'الشعب', district: 'منطقة الشعب', zipCode: '22064' },
            { country: 'الكويت', region: 'محافظة حولي', city: 'حولي', area: 'الرميثية', district: 'منطقة الرميثية', zipCode: '22065' },
            { country: 'الكويت', region: 'محافظة حولي', city: 'حولي', area: 'بيان', district: 'منطقة بيان', zipCode: '22066' },
            { country: 'الكويت', region: 'محافظة حولي', city: 'حولي', area: 'مشرف', district: 'منطقة مشرف', zipCode: '22067' },

            // محافظة الفروانية
            { country: 'الكويت', region: 'محافظة الفروانية', city: 'الفروانية', area: 'الفروانية', district: 'منطقة الفروانية', zipCode: '81006' },
            { country: 'الكويت', region: 'محافظة الفروانية', city: 'الفروانية', area: 'الرابية', district: 'منطقة الرابية', zipCode: '81007' },
            { country: 'الكويت', region: 'محافظة الفروانية', city: 'الفروانية', area: 'الأندلس', district: 'منطقة الأندلس', zipCode: '81008' },
            { country: 'الكويت', region: 'محافظة الفروانية', city: 'الفروانية', area: 'الرقة', district: 'منطقة الرقة', zipCode: '81009' },
            { country: 'الكويت', region: 'محافظة الفروانية', city: 'الفروانية', area: 'إشبيلية', district: 'منطقة إشبيلية', zipCode: '81010' },
            { country: 'الكويت', region: 'محافظة الفروانية', city: 'الفروانية', area: 'الضجيج', district: 'منطقة الضجيج', zipCode: '81011' },
            { country: 'الكويت', region: 'محافظة الفروانية', city: 'الفروانية', area: 'العمرية', district: 'منطقة العمرية', zipCode: '81012' },
            { country: 'الكويت', region: 'محافظة الفروانية', city: 'الفروانية', area: 'جليب الشيوخ', district: 'منطقة جليب الشيوخ', zipCode: '81013' },

            // محافظة الأحمدي
            { country: 'الكويت', region: 'محافظة الأحمدي', city: 'الأحمدي', area: 'الأحمدي', district: 'منطقة الأحمدي', zipCode: '61008' },
            { country: 'الكويت', region: 'محافظة الأحمدي', city: 'الأحمدي', area: 'الفحيحيل', district: 'منطقة الفحيحيل', zipCode: '61009' },
            { country: 'الكويت', region: 'محافظة الأحمدي', city: 'الأحمدي', area: 'المنقف', district: 'منطقة المنقف', zipCode: '61010' },
            { country: 'الكويت', region: 'محافظة الأحمدي', city: 'الأحمدي', area: 'أبو حليفة', district: 'منطقة أبو حليفة', zipCode: '61011' },
            { country: 'الكويت', region: 'محافظة الأحمدي', city: 'الأحمدي', area: 'الفنطاس', district: 'منطقة الفنطاس', zipCode: '61012' },
            { country: 'الكويت', region: 'محافظة الأحمدي', city: 'الأحمدي', area: 'الرقة', district: 'منطقة الرقة', zipCode: '61013' },
            { country: 'الكويت', region: 'محافظة الأحمدي', city: 'الأحمدي', area: 'الصباحية', district: 'منطقة الصباحية', zipCode: '61014' },
            { country: 'الكويت', region: 'محافظة الأحمدي', city: 'الأحمدي', area: 'الخيران', district: 'منطقة الخيران', zipCode: '61015' },

            // محافظة الجهراء
            { country: 'الكويت', region: 'محافظة الجهراء', city: 'الجهراء', area: 'الجهراء', district: 'منطقة الجهراء', zipCode: '01001' },
            { country: 'الكويت', region: 'محافظة الجهراء', city: 'الجهراء', area: 'القصر', district: 'منطقة القصر', zipCode: '01002' },
            { country: 'الكويت', region: 'محافظة الجهراء', city: 'الجهراء', area: 'تيماء', district: 'منطقة تيماء', zipCode: '01003' },
            { country: 'الكويت', region: 'محافظة الجهراء', city: 'الجهراء', area: 'النعيم', district: 'منطقة النعيم', zipCode: '01004' },
            { country: 'الكويت', region: 'محافظة الجهراء', city: 'الجهراء', area: 'الواحة', district: 'منطقة الواحة', zipCode: '01005' },
            { country: 'الكويت', region: 'محافظة الجهراء', city: 'الجهراء', area: 'كاظمة', district: 'منطقة كاظمة', zipCode: '01006' },

            // محافظة مبارك الكبير
            { country: 'الكويت', region: 'محافظة مبارك الكبير', city: 'مبارك الكبير', area: 'القرين', district: 'منطقة القرين', zipCode: '54541' },
            { country: 'الكويت', region: 'محافظة مبارك الكبير', city: 'مبارك الكبير', area: 'أبو فطيرة', district: 'منطقة أبو فطيرة', zipCode: '54542' },
            { country: 'الكويت', region: 'محافظة مبارك الكبير', city: 'مبارك الكبير', area: 'القصور', district: 'منطقة القصور', zipCode: '54543' },
            { country: 'الكويت', region: 'محافظة مبارك الكبير', city: 'مبارك الكبير', area: 'المسيلة', district: 'منطقة المسيلة', zipCode: '54544' },
            { country: 'الكويت', region: 'محافظة مبارك الكبير', city: 'مبارك الكبير', area: 'صباح السالم', district: 'منطقة صباح السالم', zipCode: '54545' },
            { country: 'الكويت', region: 'محافظة مبارك الكبير', city: 'مبارك الكبير', area: 'العدان', district: 'منطقة العدان', zipCode: '54546' }
        ];
    }

    // البحث في المناطق مع دعم الكلمات المتقطعة
    searchAreas(query) {
        const areas = this.getAllAreas();
        const lowerQuery = query.toLowerCase().trim();

        if (!lowerQuery) return [];

        // تقسيم الاستعلام إلى كلمات منفصلة
        const queryWords = lowerQuery.split(/\s+/);

        return areas.filter(area => {
            // إنشاء نص شامل للبحث
            const searchText = [
                area.country,
                area.region,
                area.city,
                area.area,
                area.district,
                area.zipCode
            ].join(' ').toLowerCase();

            // التحقق من وجود جميع الكلمات في النص
            return queryWords.every(word => searchText.includes(word));
        }).sort((a, b) => {
            // ترتيب النتائج حسب الأولوية
            const aText = `${a.city} ${a.area}`.toLowerCase();
            const bText = `${b.city} ${b.area}`.toLowerCase();

            // إعطاء أولوية للمطابقات التي تبدأ بالاستعلام
            const aStartsWith = aText.startsWith(lowerQuery);
            const bStartsWith = bText.startsWith(lowerQuery);

            if (aStartsWith && !bStartsWith) return -1;
            if (!aStartsWith && bStartsWith) return 1;

            // ترتيب أبجدي
            return aText.localeCompare(bText, 'ar');
        });
    }

    // البحث السريع في المناطق بالمدينة
    searchAreasByCity(cityName) {
        const areas = this.getAllAreas();
        return areas.filter(area =>
            area.city.toLowerCase().includes(cityName.toLowerCase())
        );
    }

    // البحث بالمنطقة الإدارية
    searchAreasByRegion(regionName) {
        const areas = this.getAllAreas();
        return areas.filter(area =>
            area.region.toLowerCase().includes(regionName.toLowerCase())
        );
    }

    // الحصول على جميع المدن
    getAllCities() {
        const areas = this.getAllAreas();
        const cities = [...new Set(areas.map(area => area.city))];
        return cities.sort((a, b) => a.localeCompare(b, 'ar'));
    }

    // الحصول على جميع المناطق الإدارية
    getAllRegions() {
        const areas = this.getAllAreas();
        const regions = [...new Set(areas.map(area => area.region))];
        return regions.sort((a, b) => a.localeCompare(b, 'ar'));
    }

    // فلترة الشحنات حسب الحالة
    filterShipmentsByStatus(status) {
        const shipments = this.getAllShipments();
        return shipments.filter(shipment => shipment.status === status);
    }

    // إحصائيات الشحنات
    getShipmentStats() {
        const shipments = this.getAllShipments();
        
        return {
            total: shipments.length,
            pending: shipments.filter(s => s.status === 'معلق').length,
            inTransit: shipments.filter(s => s.status === 'في الطريق').length,
            delivered: shipments.filter(s => s.status === 'مسلم').length,
            cancelled: shipments.filter(s => s.status === 'ملغي').length
        };
    }

    // مسح جميع البيانات (للاختبار)
    clearAllData() {
        localStorage.removeItem('shipments');
        localStorage.removeItem('customers');
        localStorage.removeItem('distributors');
        this.initializeData();
    }

    // إعادة تهيئة البيانات (لإصلاح أي مشاكل)
    reinitializeData() {
        // إعادة تهيئة العملاء مع البيانات المحدثة
        const customers = this.getAllCustomers();
        if (customers.length < 8) {
            localStorage.removeItem('customers');
            this.initializeData();
        }

        // إعادة تهيئة المناطق إذا لم تكن محدثة
        const areas = this.getAllAreas();
        if (areas.length < 30) {
            // البيانات قديمة، لا حاجة لإعادة تهيئة المناطق لأنها في الكود
        }
    }
    // === وظائف إدارة التسعير ===

    // الحصول على جميع أنواع التسعير
    getAllPricing() {
        try {
            const pricing = localStorage.getItem('pricing');
            if (pricing) {
                return JSON.parse(pricing);
            } else {
                // إنشاء التسعير الافتراضي وحفظه
                const defaultPricing = this.getDefaultPricing();
                this.savePricing(defaultPricing);
                return defaultPricing;
            }
        } catch (error) {
            console.error('خطأ في استرجاع التسعير:', error);
            return this.getDefaultPricing();
        }
    }

    // الحصول على التسعير الافتراضي
    getDefaultPricing() {
        return {
            weightPricing: [
                { id: 1, name: 'مستندات', minWeight: 0, maxWeight: 0.5, basePrice: 10, perKgPrice: 2, active: true },
                { id: 2, name: 'طرد صغير', minWeight: 0.5, maxWeight: 2, basePrice: 15, perKgPrice: 5, active: true },
                { id: 3, name: 'طرد متوسط', minWeight: 2, maxWeight: 10, basePrice: 25, perKgPrice: 8, active: true },
                { id: 4, name: 'طرد كبير', minWeight: 10, maxWeight: 30, basePrice: 50, perKgPrice: 12, active: true },
                { id: 5, name: 'شحنة ثقيلة', minWeight: 30, maxWeight: 100, basePrice: 100, perKgPrice: 15, active: true }
            ],
            zonePricing: [
                { id: 1, name: 'الرياض', type: 'محلي', basePrice: 15, extraPrice: 0, deliveryTime: '1-2 أيام', active: true },
                { id: 2, name: 'جدة', type: 'محلي', basePrice: 18, extraPrice: 3, deliveryTime: '2-3 أيام', active: true },
                { id: 3, name: 'الدمام', type: 'محلي', basePrice: 20, extraPrice: 5, deliveryTime: '2-3 أيام', active: true },
                { id: 4, name: 'مكة المكرمة', type: 'محلي', basePrice: 22, extraPrice: 7, deliveryTime: '2-4 أيام', active: true },
                { id: 5, name: 'المدينة المنورة', type: 'محلي', basePrice: 25, extraPrice: 10, deliveryTime: '3-4 أيام', active: true },
                { id: 6, name: 'الكويت', type: 'دولي', basePrice: 50, extraPrice: 35, deliveryTime: '5-7 أيام', active: true },
                { id: 7, name: 'حولي', type: 'دولي', basePrice: 55, extraPrice: 40, deliveryTime: '5-7 أيام', active: true }
            ],
            servicePricing: [
                { id: 1, name: 'التوصيل العادي', description: 'خدمة التوصيل الاعتيادية', price: 0, type: 'مجاني', active: true },
                { id: 2, name: 'التوصيل السريع', description: 'توصيل في نفس اليوم', price: 20, type: 'إضافي', active: true },
                { id: 3, name: 'التوصيل الطارئ', description: 'توصيل خلال ساعتين', price: 50, type: 'إضافي', active: true },
                { id: 4, name: 'التأمين على الشحنة', description: 'تأمين ضد الفقدان والتلف', price: 15, type: 'اختياري', active: true },
                { id: 5, name: 'التغليف الخاص', description: 'تغليف احترافي للشحنات الحساسة', price: 25, type: 'اختياري', active: true },
                { id: 6, name: 'التتبع المتقدم', description: 'تتبع مباشر مع إشعارات SMS', price: 10, type: 'اختياري', active: true }
            ],
            specialPricing: [
                { id: 1, name: 'عملاء VIP', description: 'خصم للعملاء المميزين', discount: 15, type: 'نسبة مئوية', minOrders: 50, active: true },
                { id: 2, name: 'الشحنات الكبيرة', description: 'خصم للشحنات أكثر من 20 كيلو', discount: 10, type: 'نسبة مئوية', minWeight: 20, active: true },
                { id: 3, name: 'العملاء الجدد', description: 'خصم ترحيبي للعملاء الجدد', discount: 20, type: 'مبلغ ثابت', maxUses: 1, active: true },
                { id: 4, name: 'الطلبات الشهرية', description: 'خصم للعملاء مع أكثر من 10 شحنات شهرياً', discount: 12, type: 'نسبة مئوية', minMonthlyOrders: 10, active: true },
                { id: 5, name: 'المناسبات الخاصة', description: 'خصم في المناسبات والأعياد', discount: 25, type: 'نسبة مئوية', seasonal: true, active: false }
            ]
        };
    }

    // حفظ التسعير
    savePricing(pricingData) {
        try {
            localStorage.setItem('pricing', JSON.stringify(pricingData));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ التسعير:', error);
            return false;
        }
    }

    // حساب تكلفة الشحنة
    calculateShipmentCost(shipmentData) {
        try {
            const pricing = this.getAllPricing();
            let totalCost = 0;

            // التسعير حسب الوزن
            const weight = parseFloat(shipmentData.weight) || 0;
            if (weight > 0) {
                const weightPricing = pricing.weightPricing.find(p =>
                    p.active && weight >= p.minWeight && weight <= p.maxWeight
                );

                if (weightPricing) {
                    totalCost += weightPricing.basePrice + (weight * weightPricing.perKgPrice);
                } else {
                    // إذا لم يتم العثور على تسعير مناسب، استخدم التسعير الافتراضي
                    totalCost += 15 + (weight * 5); // تسعير افتراضي
                }
            }

            // التسعير حسب المنطقة
            const city = shipmentData.receiverCity;
            if (city) {
                const zonePricing = pricing.zonePricing.find(p =>
                    p.active && p.name === city
                );

                if (zonePricing) {
                    totalCost += zonePricing.basePrice + zonePricing.extraPrice;
                } else {
                    // إذا لم يتم العثور على تسعير للمدينة، أضف رسوم افتراضية
                    totalCost += 5; // رسوم افتراضية للمناطق غير المحددة
                }
            }

            // إضافة تكلفة الخدمات الإضافية
            if (shipmentData.services && Array.isArray(shipmentData.services)) {
                shipmentData.services.forEach(serviceId => {
                    const service = pricing.servicePricing.find(s =>
                        s.active && s.id === serviceId
                    );
                    if (service) {
                        totalCost += service.price;
                    }
                });
            }

            // تطبيق الخصومات الخاصة
            if (shipmentData.specialDiscounts && Array.isArray(shipmentData.specialDiscounts)) {
                shipmentData.specialDiscounts.forEach(discountId => {
                    const discount = pricing.specialPricing.find(d =>
                        d.active && d.id === discountId
                    );
                    if (discount) {
                        if (discount.type === 'نسبة مئوية') {
                            totalCost -= (totalCost * discount.discount / 100);
                        } else {
                            totalCost -= discount.discount;
                        }
                    }
                });
            }

            return Math.max(totalCost, 5); // الحد الأدنى 5 ريال
        } catch (error) {
            console.error('خطأ في حساب تكلفة الشحنة:', error);
            return 15; // تكلفة افتراضية في حالة الخطأ
        }
    }

    // الحصول على تسعير حسب النوع
    getPricingByType(type) {
        try {
            const pricing = this.getAllPricing();
            return pricing[type] || [];
        } catch (error) {
            console.error('خطأ في استرجاع التسعير حسب النوع:', error);
            return [];
        }
    }

    // تحديث عنصر تسعير
    updatePricingItem(type, id, updateData) {
        try {
            const pricing = this.getAllPricing();
            const items = pricing[type];
            const itemIndex = items.findIndex(item => item.id === id);

            if (itemIndex !== -1) {
                items[itemIndex] = { ...items[itemIndex], ...updateData };
                this.savePricing(pricing);
                return items[itemIndex];
            }

            return false;
        } catch (error) {
            console.error('خطأ في تحديث عنصر التسعير:', error);
            return false;
        }
    }

    // إضافة عنصر تسعير جديد
    addPricingItem(type, itemData) {
        try {
            const pricing = this.getAllPricing();
            const items = pricing[type];
            const newId = Math.max(...items.map(item => item.id), 0) + 1;

            const newItem = {
                id: newId,
                ...itemData,
                active: true
            };

            items.push(newItem);
            this.savePricing(pricing);
            return newItem;
        } catch (error) {
            console.error('خطأ في إضافة عنصر التسعير:', error);
            return false;
        }
    }

    // حذف عنصر تسعير (إلغاء تفعيل)
    deletePricingItem(type, id) {
        try {
            return this.updatePricingItem(type, id, { active: false });
        } catch (error) {
            console.error('خطأ في حذف عنصر التسعير:', error);
            return false;
        }
    }
}

    // === وظائف إدارة الشحنات ===

    // الحصول على جميع الشحنات
    getAllShipments() {
        try {
            const shipments = localStorage.getItem('shipments');
            if (shipments) {
                return JSON.parse(shipments);
            } else {
                // إنشاء الشحنات الافتراضية
                const defaultShipments = this.getDefaultShipments();
                this.saveShipments(defaultShipments);
                return defaultShipments;
            }
        } catch (error) {
            console.error('خطأ في استرجاع الشحنات:', error);
            return this.getDefaultShipments();
        }
    }

    // الحصول على الشحنات الافتراضية
    getDefaultShipments() {
        return [
            {
                id: 'SHP001',
                trackingNumber: 'SHP001',
                senderName: 'أحمد محمد',
                senderPhone: '+966501234567',
                senderAddress: 'الرياض، حي النخيل',
                receiverName: 'فاطمة أحمد',
                receiverPhone: '+966507654321',
                receiverAddress: 'جدة، حي الصفا',
                contents: 'ملابس وإكسسوارات',
                weight: 2.5,
                cost: 150,
                currency: 'ريال سعودي',
                status: 'في الطريق',
                createdDate: '2024-01-10',
                estimatedDelivery: '2024-01-15',
                actualDelivery: null,
                notes: 'شحنة عادية'
            },
            {
                id: 'SHP002',
                trackingNumber: 'SHP002',
                senderName: 'محمد علي',
                senderPhone: '+966509876543',
                senderAddress: 'الدمام، حي الشاطئ',
                receiverName: 'سارة خالد',
                receiverPhone: '+966502468135',
                receiverAddress: 'الرياض، حي العليا',
                contents: 'كتب ومجلات',
                weight: 1.8,
                cost: 120,
                currency: 'ريال سعودي',
                status: 'مسلم',
                createdDate: '2024-01-08',
                estimatedDelivery: '2024-01-12',
                actualDelivery: '2024-01-12T14:30:00',
                notes: 'تم التسليم بنجاح'
            },
            {
                id: 'SHP003',
                trackingNumber: 'SHP003',
                senderName: 'عبدالله سالم',
                senderPhone: '+966505555555',
                senderAddress: 'مكة المكرمة، العزيزية',
                receiverName: 'نورا أحمد',
                receiverPhone: '+966506666666',
                receiverAddress: 'المدينة المنورة، قباء',
                contents: 'هدايا ومستحضرات',
                weight: 3.2,
                cost: 200,
                currency: 'ريال سعودي',
                status: 'معلق',
                createdDate: '2024-01-12',
                estimatedDelivery: '2024-01-16',
                actualDelivery: null,
                notes: 'في انتظار التأكيد'
            },
            {
                id: 'KWT001',
                trackingNumber: 'KWT001',
                senderName: 'خالد الكويتي',
                senderPhone: '+96599887766',
                senderAddress: 'الكويت، السالمية',
                receiverName: 'عبدالرحمن السعودي',
                receiverPhone: '+966501111111',
                receiverAddress: 'الرياض، حي الملك فهد',
                contents: 'منتجات كويتية',
                weight: 4.0,
                cost: 85,
                currency: 'دينار كويتي',
                status: 'في التحويل',
                createdDate: '2024-01-09',
                estimatedDelivery: '2024-01-14',
                actualDelivery: null,
                notes: 'شحنة دولية من الكويت'
            },
            {
                id: 'KWT002',
                trackingNumber: 'KWT002',
                senderName: 'مريم الكويتية',
                senderPhone: '+96599112233',
                senderAddress: 'الكويت، حولي',
                receiverName: 'ليلى السعودية',
                receiverPhone: '+966502222222',
                receiverAddress: 'جدة، حي الروضة',
                contents: 'مجوهرات وذهب',
                weight: 0.5,
                cost: 120,
                currency: 'دينار كويتي',
                status: 'مسلم',
                createdDate: '2024-01-05',
                estimatedDelivery: '2024-01-10',
                actualDelivery: '2024-01-10T16:45:00',
                notes: 'تم التسليم بأمان'
            }
        ];
    }

    // حفظ الشحنات
    saveShipments(shipments) {
        try {
            localStorage.setItem('shipments', JSON.stringify(shipments));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الشحنات:', error);
            return false;
        }
    }

    // البحث عن شحنة برقم التتبع
    findShipmentByTracking(trackingNumber) {
        try {
            const shipments = this.getAllShipments();
            return shipments.find(shipment =>
                (shipment.trackingNumber && shipment.trackingNumber.toLowerCase() === trackingNumber.toLowerCase()) ||
                (shipment.id && shipment.id.toLowerCase() === trackingNumber.toLowerCase())
            );
        } catch (error) {
            console.error('خطأ في البحث عن الشحنة:', error);
            return null;
        }
    }

    // === وظائف إدارة المستخدمين والصلاحيات ===

    // الحصول على جميع المستخدمين
    getAllUsers() {
        try {
            const users = localStorage.getItem('users');
            if (users) {
                return JSON.parse(users);
            } else {
                // إنشاء المستخدمين الافتراضيين
                const defaultUsers = this.getDefaultUsers();
                this.saveUsers(defaultUsers);
                return defaultUsers;
            }
        } catch (error) {
            console.error('خطأ في استرجاع المستخدمين:', error);
            return this.getDefaultUsers();
        }
    }

    // الحصول على المستخدمين الافتراضيين
    getDefaultUsers() {
        return [
            {
                id: 'user1',
                name: 'أحمد محمد',
                email: '<EMAIL>',
                phone: '+966501234567',
                role: 'admin',
                status: 'active',
                branch: 'main',
                createdDate: '2024-01-01',
                lastLogin: '2024-01-15T10:30:00',
                avatar: 'أ',
                customPermissions: []
            },
            {
                id: 'user2',
                name: 'فاطمة أحمد',
                email: '<EMAIL>',
                phone: '+966507654321',
                role: 'manager',
                status: 'active',
                branch: 'riyadh',
                createdDate: '2024-01-05',
                lastLogin: '2024-01-15T09:15:00',
                avatar: 'ف',
                customPermissions: []
            },
            {
                id: 'user3',
                name: 'محمد علي',
                email: '<EMAIL>',
                phone: '+966509876543',
                role: 'distributor',
                status: 'active',
                branch: 'jeddah',
                createdDate: '2024-01-10',
                lastLogin: '2024-01-14T16:45:00',
                avatar: 'م',
                customPermissions: []
            },
            {
                id: 'user4',
                name: 'سارة خالد',
                email: '<EMAIL>',
                phone: '+966502468135',
                role: 'viewer',
                status: 'active',
                branch: 'dammam',
                createdDate: '2024-01-12',
                lastLogin: '2024-01-13T14:20:00',
                avatar: 'س',
                customPermissions: []
            }
        ];
    }

    // حفظ المستخدمين
    saveUsers(users) {
        try {
            localStorage.setItem('users', JSON.stringify(users));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ المستخدمين:', error);
            return false;
        }
    }

    // الحصول على مستخدم بالمعرف
    getUserById(userId) {
        const users = this.getAllUsers();
        return users.find(user => user.id === userId);
    }

    // الحصول على مستخدم بالبريد الإلكتروني
    getUserByEmail(email) {
        const users = this.getAllUsers();
        return users.find(user => user.email === email);
    }

    // إضافة مستخدم جديد
    addUser(userData) {
        try {
            const users = this.getAllUsers();
            const newUser = {
                id: 'user' + Date.now(),
                ...userData,
                createdDate: new Date().toISOString().split('T')[0],
                lastLogin: null,
                avatar: userData.name ? userData.name.charAt(0) : '؟'
            };

            users.push(newUser);
            this.saveUsers(users);
            return newUser;
        } catch (error) {
            console.error('خطأ في إضافة المستخدم:', error);
            return false;
        }
    }

    // تحديث مستخدم
    updateUser(userId, updateData) {
        try {
            const users = this.getAllUsers();
            const userIndex = users.findIndex(user => user.id === userId);

            if (userIndex !== -1) {
                users[userIndex] = { ...users[userIndex], ...updateData };
                this.saveUsers(users);
                return users[userIndex];
            }

            return false;
        } catch (error) {
            console.error('خطأ في تحديث المستخدم:', error);
            return false;
        }
    }

    // حذف مستخدم
    deleteUser(userId) {
        try {
            const users = this.getAllUsers();
            const filteredUsers = users.filter(user => user.id !== userId);
            this.saveUsers(filteredUsers);
            return filteredUsers.length < users.length;
        } catch (error) {
            console.error('خطأ في حذف المستخدم:', error);
            return false;
        }
    }

    // البحث في المستخدمين
    searchUsers(searchTerm) {
        const users = this.getAllUsers();
        const term = searchTerm.toLowerCase();

        return users.filter(user =>
            user.name.toLowerCase().includes(term) ||
            user.email.toLowerCase().includes(term) ||
            (user.phone && user.phone.includes(term)) ||
            user.role.toLowerCase().includes(term) ||
            (user.branch && user.branch.toLowerCase().includes(term))
        );
    }

    // الحصول على المستخدمين حسب الدور
    getUsersByRole(role) {
        const users = this.getAllUsers();
        return users.filter(user => user.role === role);
    }

    // الحصول على المستخدمين حسب الحالة
    getUsersByStatus(status) {
        const users = this.getAllUsers();
        return users.filter(user => user.status === status);
    }

    // الحصول على المستخدمين حسب الفرع
    getUsersByBranch(branch) {
        const users = this.getAllUsers();
        return users.filter(user => user.branch === branch);
    }

    // === وظائف إدارة الأدوار ===

    // الحصول على جميع الأدوار
    getAllRoles() {
        try {
            const roles = localStorage.getItem('roles');
            if (roles) {
                return JSON.parse(roles);
            } else {
                const defaultRoles = this.getDefaultRoles();
                this.saveRoles(defaultRoles);
                return defaultRoles;
            }
        } catch (error) {
            console.error('خطأ في استرجاع الأدوار:', error);
            return this.getDefaultRoles();
        }
    }

    // الحصول على الأدوار الافتراضية
    getDefaultRoles() {
        return [
            {
                id: 'admin',
                name: 'مدير النظام',
                description: 'صلاحيات كاملة لجميع أجزاء النظام',
                priority: 1,
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_delete', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit', 'customers_delete', 'customers_export',
                    'financial_view', 'financial_payments', 'financial_invoices', 'financial_commissions', 'financial_reports',
                    'users_view', 'users_create', 'users_edit', 'users_delete', 'users_permissions', 'users_roles',
                    'reports_view', 'reports_create', 'reports_export', 'reports_analytics',
                    'system_settings', 'system_backup', 'system_logs', 'system_maintenance'
                ]
            },
            {
                id: 'manager',
                name: 'مدير',
                description: 'صلاحيات إدارية محدودة',
                priority: 2,
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit', 'customers_export',
                    'financial_view', 'financial_payments', 'financial_invoices', 'financial_reports',
                    'reports_view', 'reports_create', 'reports_export'
                ]
            },
            {
                id: 'distributor',
                name: 'موزع',
                description: 'صلاحيات الموزعين والمناديب',
                priority: 3,
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit',
                    'financial_view', 'financial_payments', 'reports_view'
                ]
            },
            {
                id: 'employee',
                name: 'موظف',
                description: 'صلاحيات أساسية للعمليات اليومية',
                priority: 4,
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit',
                    'financial_view', 'reports_view'
                ]
            },
            {
                id: 'viewer',
                name: 'مشاهد',
                description: 'صلاحيات عرض فقط',
                priority: 5,
                permissions: [
                    'shipments_view', 'shipments_track',
                    'customers_view',
                    'financial_view',
                    'reports_view'
                ]
            }
        ];
    }

    // حفظ الأدوار
    saveRoles(roles) {
        try {
            localStorage.setItem('roles', JSON.stringify(roles));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الأدوار:', error);
            return false;
        }
    }

    // الحصول على دور بالمعرف
    getRoleById(roleId) {
        const roles = this.getAllRoles();
        return roles.find(role => role.id === roleId);
    }

    // إضافة دور جديد
    addRole(roleData) {
        try {
            const roles = this.getAllRoles();
            const newRole = {
                id: 'role' + Date.now(),
                ...roleData
            };

            roles.push(newRole);
            this.saveRoles(roles);
            return newRole;
        } catch (error) {
            console.error('خطأ في إضافة الدور:', error);
            return false;
        }
    }

    // تحديث دور
    updateRole(roleId, updateData) {
        try {
            const roles = this.getAllRoles();
            const roleIndex = roles.findIndex(role => role.id === roleId);

            if (roleIndex !== -1) {
                roles[roleIndex] = { ...roles[roleIndex], ...updateData };
                this.saveRoles(roles);
                return roles[roleIndex];
            }

            return false;
        } catch (error) {
            console.error('خطأ في تحديث الدور:', error);
            return false;
        }
    }

    // حذف دور
    deleteRole(roleId) {
        try {
            // التحقق من عدم استخدام الدور
            const users = this.getAllUsers();
            const usersWithRole = users.filter(user => user.role === roleId);

            if (usersWithRole.length > 0) {
                return { error: 'لا يمكن حذف هذا الدور لأنه مستخدم من قبل ' + usersWithRole.length + ' مستخدم' };
            }

            const roles = this.getAllRoles();
            const filteredRoles = roles.filter(role => role.id !== roleId);
            this.saveRoles(filteredRoles);
            return filteredRoles.length < roles.length;
        } catch (error) {
            console.error('خطأ في حذف الدور:', error);
            return false;
        }
    }
}

// إنشاء مثيل من قاعدة البيانات
const db = new LocalDatabase();

// تأكيد تحميل قاعدة البيانات
console.log('✅ تم تحميل قاعدة البيانات بنجاح - LocalDatabase v2.0');
