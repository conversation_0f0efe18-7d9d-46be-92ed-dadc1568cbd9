@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    نظام إدارة الشحنات
echo    Shipment Management System
echo ========================================
echo.

echo 🔍 فحص حالة النظام...
echo 🔍 Checking system status...

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Node.js غير مثبت!
    echo ❌ Node.js is not installed!
    echo.
    echo يرجى تشغيل: quick-setup.bat أولاً
    echo Please run: quick-setup.bat first
    echo.
    pause
    exit /b 1
)

:: Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير مثبت!
    echo ❌ npm is not installed!
    pause
    exit /b 1
)

echo ✅ Node.js و npm مثبتان
echo ✅ Node.js and npm are installed

:: Check if dependencies are installed
if not exist "node_modules" (
    echo.
    echo 📦 تثبيت التبعيات الرئيسية...
    echo 📦 Installing main dependencies...
    npm install
)

if not exist "backend\node_modules" (
    echo.
    echo 📦 تثبيت تبعيات الخلفية...
    echo 📦 Installing backend dependencies...
    cd backend
    npm install
    cd ..
)

if not exist "frontend\node_modules" (
    echo.
    echo 📦 تثبيت تبعيات الواجهة الأمامية...
    echo 📦 Installing frontend dependencies...
    cd frontend
    npm install
    cd ..
)

:: Check if .env file exists
if not exist "backend\.env" (
    echo.
    echo ⚠️  ملف .env غير موجود في مجلد backend
    echo ⚠️  .env file not found in backend folder
    echo.
    echo يرجى تشغيل: quick-setup.bat أولاً
    echo Please run: quick-setup.bat first
    echo.
    pause
    exit /b 1
)

:: Check if DATABASE_URL is set
findstr /C:"DATABASE_URL=" backend\.env >nul
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  DATABASE_URL غير محدد في ملف .env
    echo ⚠️  DATABASE_URL not set in .env file
    echo.
    echo يرجى تعديل ملف backend\.env وإضافة:
    echo Please edit backend\.env and add:
    echo DATABASE_URL="your-database-url-here"
    echo.
    pause
    exit /b 1
)

:: Check if JWT_SECRET is set
findstr /C:"JWT_SECRET=" backend\.env >nul
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  JWT_SECRET غير محدد في ملف .env
    echo ⚠️  JWT_SECRET not set in .env file
    echo.
    echo يرجى تعديل ملف backend\.env وإضافة:
    echo Please edit backend\.env and add:
    echo JWT_SECRET="your-32-character-secret-key-here"
    echo.
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل النظام...
echo 🚀 Starting the system...
echo.
echo الواجهة الأمامية: http://localhost:3000
echo Frontend: http://localhost:3000
echo.
echo API الخلفي: http://localhost:3001
echo Backend API: http://localhost:3001
echo.
echo توثيق API: http://localhost:3001/api-docs
echo API Documentation: http://localhost:3001/api-docs
echo.
echo اضغط Ctrl+C لإيقاف النظام
echo Press Ctrl+C to stop the system
echo.

:: Start both frontend and backend
npm run dev
