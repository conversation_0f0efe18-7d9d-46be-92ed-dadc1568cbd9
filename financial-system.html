<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المالي - إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display);
            font-weight: 600;
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .financial-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .overview-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .overview-card:hover {
            transform: translateY(-5px);
        }

        .overview-card.revenue {
            border-left-color: #28a745;
        }

        .overview-card.pending {
            border-left-color: #ffc107;
        }

        .overview-card.commissions {
            border-left-color: #17a2b8;
        }

        .overview-card.expenses {
            border-left-color: #dc3545;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .financial-modules {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .module-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .module-card:hover {
            transform: translateY(-8px);
            border-color: var(--color-primary);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .module-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .module-icon {
            font-size: 2.5rem;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .module-icon.collection {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .module-icon.payment {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        }

        .module-icon.commission {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
        }

        .module-icon.invoice {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .module-icon.cod {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }

        .module-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .module-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .module-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--color-primary);
            font-family: var(--font-english-display);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 600;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            text-align: center;
            justify-content: center;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .action-btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .action-btn.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .action-btn.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .action-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .financial-overview {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .financial-modules {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .module-stats {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>💰</span>
                <span>النظام المالي</span>
            </div>
            
            <nav class="nav-links">
                <a href="financial-system.html" class="active">الرئيسية</a>
                <a href="collection-management.html">التحصيل</a>
                <a href="payment-management.html">المدفوعات</a>
                <a href="commission-management.html">العمولات</a>
                <a href="cod-management.html">الدفع عند الاستلام</a>
                <a href="invoice-management.html">الفواتير</a>
                <a href="financial-reports.html">التقارير</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <!-- نظرة عامة مالية -->
        <div class="financial-overview">
            <div class="overview-card revenue">
                <div class="card-icon">💰</div>
                <div class="card-amount" id="totalRevenue">0</div>
                <div class="card-label">إجمالي الإيرادات</div>
            </div>
            
            <div class="overview-card pending">
                <div class="card-icon">⏳</div>
                <div class="card-amount" id="pendingAmount">0</div>
                <div class="card-label">المبالغ المعلقة</div>
            </div>
            
            <div class="overview-card commissions">
                <div class="card-icon">🤝</div>
                <div class="card-amount" id="totalCommissions">0</div>
                <div class="card-label">إجمالي العمولات</div>
            </div>
            
            <div class="overview-card expenses">
                <div class="card-icon">📊</div>
                <div class="card-amount" id="totalExpenses">0</div>
                <div class="card-label">إجمالي المصروفات</div>
            </div>
        </div>

        <!-- وحدات النظام المالي -->
        <div class="financial-modules">
            <div class="module-card" onclick="navigateTo('collection-management.html')">
                <div class="module-header">
                    <div class="module-icon collection">💵</div>
                    <div>
                        <div class="module-title">التحصيل من المناديب</div>
                        <div class="module-description">إدارة تحصيل المبالغ من المناديب والموزعين</div>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="pendingCollections">0</div>
                        <div class="stat-label">معلق</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="collectedToday">0</div>
                        <div class="stat-label">اليوم</div>
                    </div>
                </div>
            </div>

            <div class="module-card" onclick="navigateTo('payment-management.html')">
                <div class="module-header">
                    <div class="module-icon payment">💳</div>
                    <div>
                        <div class="module-title">تحصيل الشحنات المؤجلة</div>
                        <div class="module-description">إدارة الشحنات المسددة لاحقاً والمتأخرة</div>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="deferredShipments">0</div>
                        <div class="stat-label">مؤجلة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="overdueAmount">0</div>
                        <div class="stat-label">متأخرة</div>
                    </div>
                </div>
            </div>

            <div class="module-card" onclick="navigateTo('cod-management.html')">
                <div class="module-header">
                    <div class="module-icon cod">🏪</div>
                    <div>
                        <div class="module-title">الدفع عند الاستلام</div>
                        <div class="module-description">إدارة مدفوعات العملاء عند استلام الشحنات</div>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="codShipments">0</div>
                        <div class="stat-label">شحنات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="codAmount">0</div>
                        <div class="stat-label">المبلغ</div>
                    </div>
                </div>
            </div>

            <div class="module-card" onclick="navigateTo('commission-management.html')">
                <div class="module-header">
                    <div class="module-icon commission">🤝</div>
                    <div>
                        <div class="module-title">عمولات المناديب</div>
                        <div class="module-description">حساب وسداد عمولات المناديب والموزعين</div>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="unpaidCommissions">0</div>
                        <div class="stat-label">غير مسددة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="activeDistributors">0</div>
                        <div class="stat-label">مناديب</div>
                    </div>
                </div>
            </div>

            <div class="module-card" onclick="navigateTo('cod-management.html')">
                <div class="module-header">
                    <div class="module-icon cod">🏪</div>
                    <div>
                        <div class="module-title">الدفع عند الاستلام</div>
                        <div class="module-description">إدارة مدفوعات الدفع عند الاستلام (COD)</div>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="totalCod">0</div>
                        <div class="stat-label">إجمالي COD</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="pendingCod">0</div>
                        <div class="stat-label">معلق</div>
                    </div>
                </div>
            </div>

            <div class="module-card" onclick="navigateTo('invoice-management.html')">
                <div class="module-header">
                    <div class="module-icon invoice">📄</div>
                    <div>
                        <div class="module-title">فواتير الشحنات الآجلة</div>
                        <div class="module-description">إنشاء وإدارة فواتير الشحنات المؤجلة الدفع</div>
                    </div>
                </div>
                <div class="module-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="pendingInvoices">0</div>
                        <div class="stat-label">معلقة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="overdueInvoices">0</div>
                        <div class="stat-label">متأخرة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="quick-actions">
            <h2>🚀 إجراءات سريعة</h2>
            <div class="actions-grid">
                <a href="collection-management.html" class="action-btn success">
                    <span>💵</span>
                    <span>تحصيل جديد</span>
                </a>
                <a href="payment-management.html" class="action-btn warning">
                    <span>💳</span>
                    <span>سداد مؤجل</span>
                </a>
                <a href="commission-management.html" class="action-btn info">
                    <span>🤝</span>
                    <span>حساب عمولة</span>
                </a>
                <a href="cod-management.html" class="action-btn danger">
                    <span>🏪</span>
                    <span>تحصيل COD</span>
                </a>
                <a href="invoice-management.html" class="action-btn">
                    <span>📄</span>
                    <span>إنشاء فاتورة</span>
                </a>
                <a href="financial-reports.html" class="action-btn danger">
                    <span>📊</span>
                    <span>تقرير مالي</span>
                </a>
                <a href="cod-management.html" class="action-btn success">
                    <span>🏪</span>
                    <span>دفع عند الاستلام</span>
                </a>
            </div>
        </div>
    </main>

    <script src="js/database.js"></script>
    <script src="js/financial-database.js"></script>
    <script>
        // تحميل البيانات المالية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💰 تحميل النظام المالي...');
            loadFinancialOverview();
            loadModuleStats();
        });

        // تحميل نظرة عامة مالية
        function loadFinancialOverview() {
            try {
                const shipments = db.getAllShipments();
                const distributors = db.getAllDistributors();
                
                // حساب الإيرادات
                let totalRevenue = 0;
                let pendingAmount = 0;
                let codAmount = 0;
                
                shipments.forEach(shipment => {
                    const cost = parseFloat(shipment.cost) || 0;
                    totalRevenue += cost;
                    
                    if (shipment.status === 'معلق') {
                        pendingAmount += cost;
                    }
                    
                    if (shipment.paymentMethod === 'cod') {
                        codAmount += cost;
                    }
                });
                
                // حساب العمولات
                const totalCommissions = distributors.length * 500; // عمولة افتراضية
                const totalExpenses = totalCommissions + (totalRevenue * 0.1); // 10% مصروفات
                
                // تحديث العرض
                document.getElementById('totalRevenue').textContent = totalRevenue.toFixed(2);
                document.getElementById('pendingAmount').textContent = pendingAmount.toFixed(2);
                document.getElementById('totalCommissions').textContent = totalCommissions.toFixed(2);
                document.getElementById('totalExpenses').textContent = totalExpenses.toFixed(2);
                
            } catch (error) {
                console.error('❌ خطأ في تحميل النظرة المالية:', error);
            }
        }

        // تحميل إحصائيات الوحدات
        function loadModuleStats() {
            try {
                const shipments = db.getAllShipments();
                const distributors = db.getAllDistributors();
                
                // إحصائيات التحصيل
                const pendingCollections = shipments.filter(s => s.status === 'في الطريق').length;
                const collectedToday = shipments.filter(s => 
                    s.status === 'مسلم' && 
                    s.actualDelivery === new Date().toISOString().split('T')[0]
                ).length;
                
                // إحصائيات الشحنات المؤجلة
                const deferredShipments = shipments.filter(s => s.paymentMethod === 'deferred').length;
                const overdueShipments = shipments.filter(s => 
                    s.paymentMethod === 'deferred' && 
                    new Date(s.estimatedDelivery) < new Date()
                ).length;
                
                // إحصائيات الدفع عند الاستلام
                const codShipments = shipments.filter(s => s.paymentMethod === 'cod').length;
                const codTotalAmount = shipments
                    .filter(s => s.paymentMethod === 'cod')
                    .reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);
                
                // إحصائيات العمولات
                const unpaidCommissions = distributors.filter(d => d.isAvailable).length;
                const activeDistributors = distributors.length;
                
                // إحصائيات الفواتير
                const pendingInvoices = deferredShipments;
                const overdueInvoices = overdueShipments;
                
                // تحديث العرض
                document.getElementById('pendingCollections').textContent = pendingCollections;
                document.getElementById('collectedToday').textContent = collectedToday;
                document.getElementById('deferredShipments').textContent = deferredShipments;
                document.getElementById('overdueAmount').textContent = overdueShipments;
                document.getElementById('codShipments').textContent = codShipments;
                document.getElementById('codAmount').textContent = codTotalAmount.toFixed(2);
                document.getElementById('unpaidCommissions').textContent = unpaidCommissions;
                document.getElementById('activeDistributors').textContent = activeDistributors;
                document.getElementById('pendingInvoices').textContent = pendingInvoices;
                document.getElementById('overdueInvoices').textContent = overdueInvoices;
                
            } catch (error) {
                console.error('❌ خطأ في تحميل إحصائيات الوحدات:', error);
            }
        }

        // التنقل للصفحات
        function navigateTo(page) {
            window.location.href = page;
        }
    </script>
</body>
</html>
