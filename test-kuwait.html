<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مناطق الكويت والدينار الكويتي</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: var(--font-arabic-display);
            background: var(--bg-secondary);
            padding: var(--space-6);
        }

        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
        }

        .test-section {
            margin-bottom: var(--space-6);
            padding: var(--space-4);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
        }

        .form-group {
            margin-bottom: var(--space-4);
        }

        .form-label {
            display: block;
            margin-bottom: var(--space-2);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: var(--space-3);
            border: 2px solid var(--border-light);
            border-radius: var(--radius-lg);
            font-family: var(--font-arabic-display);
            transition: border-color var(--transition-fast);
        }

        .form-input:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .autocomplete-container {
            position: relative;
        }

        .autocomplete-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .autocomplete-item {
            padding: var(--space-3);
            cursor: pointer;
            border-bottom: 1px solid var(--border-light);
            transition: background-color var(--transition-fast);
        }

        .autocomplete-item:hover,
        .autocomplete-item.selected {
            background: var(--bg-tertiary);
        }

        .autocomplete-item:last-child {
            border-bottom: none;
        }

        .item-name {
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .item-details {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: var(--space-1);
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: var(--space-3) var(--space-5);
            border: none;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: var(--font-weight-medium);
            margin: var(--space-2);
            transition: all var(--transition-fast);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--color-secondary);
        }

        .btn-warning {
            background: var(--color-warning);
        }

        .results-section {
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-top: var(--space-4);
        }

        .result-item {
            background: white;
            border-radius: var(--radius-md);
            padding: var(--space-3);
            margin-bottom: var(--space-3);
            border: 1px solid var(--border-light);
        }

        .result-item:last-child {
            margin-bottom: 0;
        }

        .kuwait-flag {
            display: inline-block;
            margin-left: var(--space-2);
            font-size: 1.2rem;
        }

        .currency-display {
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            color: white;
            padding: var(--space-3);
            border-radius: var(--radius-lg);
            text-align: center;
            margin: var(--space-3) 0;
            font-weight: var(--font-weight-semibold);
        }

        .back-link {
            position: fixed;
            top: var(--space-4);
            right: var(--space-4);
            background: var(--color-primary);
            color: white;
            padding: var(--space-3) var(--space-5);
            border-radius: var(--radius-lg);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
        }

        .area-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: var(--radius-md);
            padding: var(--space-3);
            margin-top: var(--space-3);
            font-size: 0.9rem;
            color: #1976d2;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            margin-top: var(--space-4);
        }

        .stat-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            text-align: center;
            border: 1px solid var(--border-light);
            box-shadow: var(--shadow-sm);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
            font-family: var(--font-english-display);
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
            margin-top: var(--space-1);
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>
    
    <div class="test-container">
        <h1>🇰🇼 اختبار مناطق الكويت والدينار الكويتي</h1>
        
        <div class="test-section">
            <h2>🏙️ البحث في مناطق الكويت</h2>
            <p>جرب البحث في المناطق الكويتية الجديدة المضافة للنظام:</p>
            
            <div class="form-group">
                <label class="form-label">البحث في المناطق الكويتية:</label>
                <div class="autocomplete-container">
                    <input type="text" class="form-input" id="kuwaitArea" placeholder="ابحث عن منطقة في الكويت... (مثل: السالمية، حولي، الجهراء)">
                    <div class="autocomplete-dropdown" id="kuwaitAreaDropdown"></div>
                </div>
            </div>
            
            <div class="form-group">
                <button class="btn" onclick="searchKuwaitAreas()">🔍 البحث في جميع المناطق الكويتية</button>
                <button class="btn btn-success" onclick="showAllKuwaitGovernates()">🏛️ عرض جميع المحافظات</button>
            </div>
        </div>

        <div class="test-section">
            <h2>💰 اختبار الدينار الكويتي</h2>
            <p>اختبار العملة الجديدة المضافة للنظام:</p>
            
            <div class="form-group">
                <label class="form-label">العملة:</label>
                <select class="form-input" id="currency" onchange="updateCurrencyDisplay()">
                    <option value="SAR">ريال سعودي (SAR)</option>
                    <option value="KWD">دينار كويتي (KWD) 🇰🇼</option>
                    <option value="USD">دولار أمريكي (USD)</option>
                    <option value="EUR">يورو (EUR)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">المبلغ:</label>
                <input type="number" class="form-input" id="amount" placeholder="أدخل المبلغ..." step="0.001" onchange="updateCurrencyDisplay()">
            </div>
            
            <div class="currency-display" id="currencyDisplay">
                اختر العملة والمبلغ لعرض التفاصيل
            </div>
        </div>

        <div class="test-section">
            <h2>📊 إحصائيات المناطق المضافة</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalKuwaitAreas">0</div>
                    <div class="stat-label">إجمالي المناطق الكويتية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalGovernates">0</div>
                    <div class="stat-label">عدد المحافظات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalCities">0</div>
                    <div class="stat-label">عدد المدن</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalCountries">0</div>
                    <div class="stat-label">عدد الدول في النظام</div>
                </div>
            </div>
        </div>

        <div class="results-section" id="resultsSection" style="display: none;">
            <h3>نتائج البحث:</h3>
            <div id="searchResults"></div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        let selectedIndex = -1;

        // تحميل الإحصائيات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🇰🇼 تحميل صفحة اختبار الكويت...');
            
            setupAreaAutocomplete();
            loadStatistics();
            updateCurrencyDisplay();
        });

        // إعداد البحث التلقائي للمناطق
        function setupAreaAutocomplete() {
            const input = document.getElementById('kuwaitArea');
            const dropdown = document.getElementById('kuwaitAreaDropdown');

            input.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length < 2) {
                    hideDropdown(dropdown);
                    return;
                }

                const areas = db.searchAreas(query);
                // فلترة المناطق الكويتية فقط
                const kuwaitAreas = areas.filter(area => area.country === 'الكويت');
                showAreaDropdown(dropdown, kuwaitAreas, input);
                selectedIndex = -1;
            });

            input.addEventListener('keydown', function(e) {
                const items = dropdown.querySelectorAll('.autocomplete-item');
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                    updateSelection(items, selectedIndex);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, -1);
                    updateSelection(items, selectedIndex);
                } else if (e.key === 'Enter' && selectedIndex >= 0) {
                    e.preventDefault();
                    items[selectedIndex].click();
                } else if (e.key === 'Escape') {
                    hideDropdown(dropdown);
                    selectedIndex = -1;
                }
            });

            // إخفاء القائمة عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!input.contains(e.target) && !dropdown.contains(e.target)) {
                    hideDropdown(dropdown);
                }
            });
        }

        // عرض قائمة المناطق
        function showAreaDropdown(dropdown, areas, input) {
            if (areas.length === 0) {
                dropdown.innerHTML = '<div class="autocomplete-item" style="color: #999; cursor: default;">لا توجد مناطق كويتية مطابقة</div>';
                dropdown.style.display = 'block';
                return;
            }

            const maxResults = 8;
            const displayAreas = areas.slice(0, maxResults);
            
            let html = displayAreas.map(area => 
                '<div class="autocomplete-item" onclick="selectArea(\'' + 
                area.country + '\', \'' + area.region + '\', \'' + area.city + '\', \'' + 
                area.area + '\', \'' + area.district + '\', \'' + area.zipCode + '\')">' +
                    '<div class="item-name">🇰🇼 ' + area.area + ' - ' + area.city + '</div>' +
                    '<div class="item-details">' + area.region + ' | الرمز البريدي: ' + area.zipCode + '</div>' +
                '</div>'
            ).join('');
            
            if (areas.length > maxResults) {
                html += '<div class="autocomplete-item" style="color: #666; cursor: default; font-style: italic;">' +
                        'وجدت ' + (areas.length - maxResults) + ' منطقة إضافية...' +
                        '</div>';
            }
            
            dropdown.innerHTML = html;
            dropdown.style.display = 'block';
        }

        // اختيار منطقة
        function selectArea(country, region, city, area, district, zipCode) {
            const input = document.getElementById('kuwaitArea');
            input.value = area + ' - ' + city + ' - ' + country;
            
            // إظهار معلومات المنطقة
            showAreaInfo(region, district, zipCode);
            
            hideDropdown(document.getElementById('kuwaitAreaDropdown'));
        }

        // إظهار معلومات المنطقة
        function showAreaInfo(region, district, zipCode) {
            const container = document.getElementById('kuwaitArea').parentElement;
            
            // إزالة المعلومات السابقة
            const existingInfo = container.querySelector('.area-info');
            if (existingInfo) {
                existingInfo.remove();
            }
            
            // إضافة معلومات جديدة
            const infoDiv = document.createElement('div');
            infoDiv.className = 'area-info';
            infoDiv.innerHTML = 
                '<div><strong>المحافظة:</strong> ' + region + '</div>' +
                '<div><strong>المنطقة:</strong> ' + district + '</div>' +
                '<div><strong>الرمز البريدي:</strong> ' + zipCode + '</div>';
            
            container.appendChild(infoDiv);
        }

        // البحث في جميع المناطق الكويتية
        function searchKuwaitAreas() {
            const allAreas = db.getAllAreas();
            const kuwaitAreas = allAreas.filter(area => area.country === 'الكويت');
            
            displaySearchResults(kuwaitAreas, 'جميع المناطق الكويتية');
        }

        // عرض جميع المحافظات الكويتية
        function showAllKuwaitGovernates() {
            const allAreas = db.getAllAreas();
            const kuwaitAreas = allAreas.filter(area => area.country === 'الكويت');
            
            // تجميع المحافظات
            const governates = {};
            kuwaitAreas.forEach(area => {
                if (!governates[area.region]) {
                    governates[area.region] = [];
                }
                governates[area.region].push(area);
            });
            
            displayGovernatesResults(governates);
        }

        // عرض نتائج البحث
        function displaySearchResults(areas, title) {
            const resultsSection = document.getElementById('resultsSection');
            const searchResults = document.getElementById('searchResults');
            
            let html = '<h4>' + title + ' (' + areas.length + ' منطقة):</h4>';
            
            areas.forEach(area => {
                html += '<div class="result-item">' +
                        '<strong>🇰🇼 ' + area.area + '</strong> - ' + area.city + '<br>' +
                        '<small>' + area.region + ' | ' + area.district + ' | ' + area.zipCode + '</small>' +
                        '</div>';
            });
            
            searchResults.innerHTML = html;
            resultsSection.style.display = 'block';
        }

        // عرض نتائج المحافظات
        function displayGovernatesResults(governates) {
            const resultsSection = document.getElementById('resultsSection');
            const searchResults = document.getElementById('searchResults');
            
            let html = '<h4>المحافظات الكويتية (' + Object.keys(governates).length + ' محافظة):</h4>';
            
            Object.keys(governates).forEach(governate => {
                html += '<div class="result-item">' +
                        '<strong>🏛️ ' + governate + '</strong><br>' +
                        '<small>عدد المناطق: ' + governates[governate].length + '</small><br>';
                
                // عرض أول 5 مناطق من كل محافظة
                const sampleAreas = governates[governate].slice(0, 5);
                html += '<div style="margin-top: 8px;">';
                sampleAreas.forEach(area => {
                    html += '<span style="background: #e3f2fd; padding: 2px 6px; margin: 2px; border-radius: 4px; font-size: 0.8rem;">' + area.area + '</span> ';
                });
                if (governates[governate].length > 5) {
                    html += '<span style="color: #666;">... و' + (governates[governate].length - 5) + ' منطقة أخرى</span>';
                }
                html += '</div></div>';
            });
            
            searchResults.innerHTML = html;
            resultsSection.style.display = 'block';
        }

        // تحديث عرض العملة
        function updateCurrencyDisplay() {
            const currency = document.getElementById('currency').value;
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const display = document.getElementById('currencyDisplay');
            
            let currencyName = '';
            let currencySymbol = '';
            let flag = '';
            
            switch(currency) {
                case 'SAR':
                    currencyName = 'ريال سعودي';
                    currencySymbol = 'ر.س';
                    flag = '🇸🇦';
                    break;
                case 'KWD':
                    currencyName = 'دينار كويتي';
                    currencySymbol = 'د.ك';
                    flag = '🇰🇼';
                    break;
                case 'USD':
                    currencyName = 'دولار أمريكي';
                    currencySymbol = '$';
                    flag = '🇺🇸';
                    break;
                case 'EUR':
                    currencyName = 'يورو';
                    currencySymbol = '€';
                    flag = '🇪🇺';
                    break;
            }
            
            if (amount > 0) {
                display.innerHTML = flag + ' ' + amount.toFixed(3) + ' ' + currencySymbol + '<br>' +
                                  '<small>' + currencyName + '</small>';
            } else {
                display.innerHTML = flag + ' ' + currencyName + '<br>' +
                                  '<small>أدخل المبلغ لعرض التفاصيل</small>';
            }
        }

        // تحميل الإحصائيات
        function loadStatistics() {
            try {
                const allAreas = db.getAllAreas();
                const kuwaitAreas = allAreas.filter(area => area.country === 'الكويت');
                
                // حساب الإحصائيات
                const governates = new Set(kuwaitAreas.map(area => area.region));
                const cities = new Set(kuwaitAreas.map(area => area.city));
                const countries = new Set(allAreas.map(area => area.country));
                
                // تحديث العرض
                document.getElementById('totalKuwaitAreas').textContent = kuwaitAreas.length;
                document.getElementById('totalGovernates').textContent = governates.size;
                document.getElementById('totalCities').textContent = cities.size;
                document.getElementById('totalCountries').textContent = countries.size;
                
                console.log('📊 تم تحميل الإحصائيات:', {
                    kuwaitAreas: kuwaitAreas.length,
                    governates: governates.size,
                    cities: cities.size,
                    countries: countries.size
                });
            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات:', error);
            }
        }

        // دوال مساعدة
        function hideDropdown(dropdown) {
            dropdown.style.display = 'none';
        }

        function updateSelection(items, selectedIndex) {
            items.forEach((item, index) => {
                item.classList.toggle('selected', index === selectedIndex);
            });
        }
    </script>
</body>
</html>
