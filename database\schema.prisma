// Prisma Schema for Shipment Management System
// مخطط Prisma لنظام إدارة الشحنات

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// نموذج المستخدمين
model User {
  id           String   @id @default(cuid())
  email        String   @unique
  passwordHash String   @map("password_hash")
  firstName    String   @map("first_name")
  lastName     String   @map("last_name")
  phone        String?
  role         UserRole @default(USER)
  language     String   @default("ar")
  isActive     Boolean  @default(true) @map("is_active")

  // Two-Factor Authentication fields
  twoFactorEnabled Boolean @default(false) @map("two_factor_enabled")
  twoFactorSecret  String? @map("two_factor_secret")
  backupCodes      String[] @map("backup_codes")

  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  distributor   Distributor?
  createdShipments Shipment[] @relation("CreatedShipments")
  trackingEntries  ShipmentTracking[]
  notifications    Notification[]

  @@map("users")
}

// نموذج العملاء
model Customer {
  id         String   @id @default(cuid())
  name       String
  email      String?
  phone      String?
  address    String?
  city       String?
  country    String?
  postalCode String?  @map("postal_code")
  createdBy  String?  @map("created_by")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  sentShipments     Shipment[] @relation("SenderShipments")
  receivedShipments Shipment[] @relation("ReceiverShipments")
  invoices          Invoice[]

  @@map("customers")
}

// نموذج الموزعين
model Distributor {
  id              String   @id @default(cuid())
  userId          String   @unique @map("user_id")
  name            String
  email           String?
  phone           String
  vehicleType     String?  @map("vehicle_type")
  vehicleNumber   String?  @map("vehicle_number")
  licenseNumber   String?  @map("license_number")
  area            String?
  rating          Float    @default(0.0)
  totalDeliveries Int      @default(0) @map("total_deliveries")
  isAvailable     Boolean  @default(true) @map("is_available")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  user      User       @relation(fields: [userId], references: [id])
  shipments Shipment[]

  @@map("distributors")
}

// نموذج العملات
model Currency {
  id           String   @id @default(cuid())
  code         String   @unique
  name         String
  symbol       String
  exchangeRate Float    @default(1.0) @map("exchange_rate")
  isBase       Boolean  @default(false) @map("is_base")
  isActive     Boolean  @default(true) @map("is_active")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  shipments Shipment[]
  invoices  Invoice[]

  @@map("currencies")
}

// نموذج الشحنات
model Shipment {
  id                   String            @id @default(cuid())
  trackingNumber       String            @unique @map("tracking_number")
  senderId             String?           @map("sender_id")
  receiverId           String?           @map("receiver_id")
  distributorId        String?           @map("distributor_id")
  
  // معلومات الشحنة
  weight               Float?
  length               Float?
  width                Float?
  height               Float?
  contents             String?
  
  // العناوين
  pickupAddress        String            @map("pickup_address")
  pickupCity           String?           @map("pickup_city")
  pickupCountry        String?           @map("pickup_country")
  deliveryAddress      String            @map("delivery_address")
  deliveryCity         String?           @map("delivery_city")
  deliveryCountry      String?           @map("delivery_country")
  
  // التواريخ
  pickupDate           DateTime?         @map("pickup_date")
  estimatedDelivery    DateTime?         @map("estimated_delivery")
  actualDelivery       DateTime?         @map("actual_delivery")
  
  // التكلفة
  cost                 Float
  currencyId           String            @map("currency_id")
  
  // الحالة
  status               ShipmentStatus    @default(PENDING)
  
  // ملاحظات
  notes                String?
  specialInstructions  String?           @map("special_instructions")
  
  // تتبع
  createdBy            String            @map("created_by")
  createdAt            DateTime          @default(now()) @map("created_at")
  updatedAt            DateTime          @updatedAt @map("updated_at")

  // Relations
  sender       Customer?          @relation("SenderShipments", fields: [senderId], references: [id])
  receiver     Customer?          @relation("ReceiverShipments", fields: [receiverId], references: [id])
  distributor  Distributor?       @relation(fields: [distributorId], references: [id])
  currency     Currency           @relation(fields: [currencyId], references: [id])
  creator      User               @relation("CreatedShipments", fields: [createdBy], references: [id])
  tracking     ShipmentTracking[]
  notifications Notification[]
  invoices     Invoice[]

  @@map("shipments")
}

// نموذج تتبع الشحنات
model ShipmentTracking {
  id         String   @id @default(cuid())
  shipmentId String   @map("shipment_id")
  status     String
  location   String?
  latitude   Float?
  longitude  Float?
  notes      String?
  createdBy  String   @map("created_by")
  createdAt  DateTime @default(now()) @map("created_at")

  // Relations
  shipment Shipment @relation(fields: [shipmentId], references: [id], onDelete: Cascade)
  creator  User     @relation(fields: [createdBy], references: [id])

  @@map("shipment_tracking")
}

// نموذج الإشعارات
model Notification {
  id         String           @id @default(cuid())
  userId     String           @map("user_id")
  title      String
  message    String
  type       NotificationType
  isRead     Boolean          @default(false) @map("is_read")
  shipmentId String?          @map("shipment_id")
  createdAt  DateTime         @default(now()) @map("created_at")

  // Relations
  user     User      @relation(fields: [userId], references: [id])
  shipment Shipment? @relation(fields: [shipmentId], references: [id])

  @@map("notifications")
}

// نموذج الفواتير
model Invoice {
  id            String        @id @default(cuid())
  shipmentId    String?       @map("shipment_id")
  customerId    String        @map("customer_id")
  invoiceNumber String        @unique @map("invoice_number")
  subtotal      Float
  taxAmount     Float         @default(0.0) @map("tax_amount")
  totalAmount   Float         @map("total_amount")
  currencyId    String        @map("currency_id")
  status        InvoiceStatus @default(PENDING)
  dueDate       DateTime?     @map("due_date")
  paidDate      DateTime?     @map("paid_date")
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  // Relations
  shipment Shipment? @relation(fields: [shipmentId], references: [id])
  customer Customer  @relation(fields: [customerId], references: [id])
  currency Currency  @relation(fields: [currencyId], references: [id])

  @@map("invoices")
}

// التعدادات (Enums)
enum UserRole {
  ADMIN
  MANAGER
  USER
  DISTRIBUTOR
}

enum ShipmentStatus {
  PENDING
  PICKED_UP
  IN_TRANSIT
  DELIVERED
  CANCELLED
  RETURNED
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
}

enum InvoiceStatus {
  PENDING
  PAID
  OVERDUE
  CANCELLED
}
