<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المدفوعات المؤجلة - النظام المالي</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: var(--font-arabic-display);
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total {
            border-left-color: #007bff;
        }

        .stat-card.pending {
            border-left-color: #ffc107;
        }

        .stat-card.overdue {
            border-left-color: #dc3545;
        }

        .stat-card.paid {
            border-left-color: #28a745;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input,
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            min-width: 200px;
        }

        .search-input:focus,
        .filter-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .payments-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .status-overdue {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .status-paid {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .due-date {
            font-weight: 600;
        }

        .due-date.overdue {
            color: #dc3545;
        }

        .due-date.due-soon {
            color: #ffc107;
        }

        .due-date.normal {
            color: #28a745;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-data h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input,
            .filter-select {
                min-width: auto;
            }

            .actions {
                justify-content: center;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <a href="financial-system.html" class="back-link">← العودة للنظام المالي</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>💳</span>
                <span>إدارة المدفوعات المؤجلة</span>
            </div>
            
            <nav class="nav-links">
                <a href="financial-system.html">الرئيسية</a>
                <a href="collection-management.html">التحصيل</a>
                <a href="payment-management.html" class="active">المدفوعات</a>
                <a href="commission-management.html">العمولات</a>
                <a href="invoice-management.html">الفواتير</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">💳 إدارة المدفوعات المؤجلة</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="openAddModal()">
                    ➕ إضافة دفع مؤجل
                </button>
                <button class="btn" onclick="loadDeferredPayments()">
                    🔄 تحديث القائمة
                </button>
                <button class="btn btn-warning" onclick="sendReminders()">
                    📧 إرسال تذكيرات
                </button>
            </div>
        </div>

        <!-- إحصائيات المدفوعات المؤجلة -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="card-icon">💰</div>
                <div class="card-amount" id="totalDeferredAmount">0</div>
                <div class="card-label">إجمالي المبالغ المؤجلة</div>
            </div>
            
            <div class="stat-card pending">
                <div class="card-icon">⏳</div>
                <div class="card-amount" id="pendingPayments">0</div>
                <div class="card-label">مدفوعات معلقة</div>
            </div>
            
            <div class="stat-card overdue">
                <div class="card-icon">⚠️</div>
                <div class="card-amount" id="overduePayments">0</div>
                <div class="card-label">مدفوعات متأخرة</div>
            </div>
            
            <div class="stat-card paid">
                <div class="card-icon">✅</div>
                <div class="card-amount" id="paidPayments">0</div>
                <div class="card-label">مدفوعات مسددة</div>
            </div>
        </div>

        <!-- أدوات التحكم والبحث -->
        <div class="controls">
            <input type="text" id="searchInput" class="search-input" placeholder="🔍 البحث في المدفوعات...">
            
            <select id="statusFilter" class="filter-select">
                <option value="">جميع الحالات</option>
                <option value="معلق">معلق</option>
                <option value="متأخر">متأخر</option>
                <option value="مسدد">مسدد</option>
            </select>
            
            <select id="customerFilter" class="filter-select">
                <option value="">جميع العملاء</option>
            </select>
            
            <button class="btn" onclick="clearFilters()">مسح الفلاتر</button>
        </div>

        <!-- جدول المدفوعات المؤجلة -->
        <div class="payments-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الدفع</th>
                        <th>العميل</th>
                        <th>رقم الشحنة</th>
                        <th>المبلغ</th>
                        <th>العملة</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="paymentsTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>
            
            <!-- رسالة عدم وجود بيانات -->
            <div id="noDataMessage" class="no-data" style="display: none;">
                <h3>💳 لا توجد مدفوعات مؤجلة</h3>
                <p>لم يتم العثور على أي مدفوعات مؤجلة. ابدأ بإضافة دفع مؤجل جديد.</p>
                <button class="btn btn-success" onclick="openAddModal()" style="margin-top: 15px;">
                    ➕ إضافة دفع مؤجل جديد
                </button>
            </div>
        </div>
    </main>

    <script src="js/database-simple.js"></script>
    <script src="js/financial-database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💳 تحميل صفحة إدارة المدفوعات المؤجلة...');
            
            try {
                loadDeferredPayments();
                loadCustomers();
                setupEventListeners();
                loadStats();
                
                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterPayments);
            document.getElementById('statusFilter').addEventListener('change', filterPayments);
            document.getElementById('customerFilter').addEventListener('change', filterPayments);
        }

        // تحميل قائمة العملاء
        function loadCustomers() {
            try {
                const customers = db.getAllCustomers();
                const customerFilter = document.getElementById('customerFilter');
                
                // مسح الخيارات الحالية
                customerFilter.innerHTML = '<option value="">جميع العملاء</option>';
                
                customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.name;
                    option.textContent = customer.name;
                    customerFilter.appendChild(option);
                });
                
            } catch (error) {
                console.error('❌ خطأ في تحميل العملاء:', error);
            }
        }

        // تحميل وعرض المدفوعات المؤجلة
        function loadDeferredPayments() {
            try {
                const payments = financialDb.getAllDeferredPayments();
                displayPayments(payments);
                console.log('💳 تم تحميل', payments.length, 'دفع مؤجل');
            } catch (error) {
                console.error('❌ خطأ في تحميل المدفوعات المؤجلة:', error);
                alert('خطأ في تحميل المدفوعات المؤجلة: ' + error.message);
            }
        }

        // عرض المدفوعات في الجدول
        function displayPayments(payments) {
            const tbody = document.getElementById('paymentsTableBody');
            const noDataMessage = document.getElementById('noDataMessage');
            
            if (payments.length === 0) {
                tbody.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }

            noDataMessage.style.display = 'none';
            
            tbody.innerHTML = payments.map(payment => {
                const dueDateClass = getDueDateClass(payment.dueDate);
                return `<tr>
                    <td><strong>${payment.id}</strong></td>
                    <td>${payment.customerName}</td>
                    <td>${payment.shipmentId}</td>
                    <td>${(payment.amount || 0).toFixed(2)}</td>
                    <td>${payment.currency || 'SAR'}</td>
                    <td class="due-date ${dueDateClass}">${formatDate(payment.dueDate)}</td>
                    <td><span class="status-badge status-${getStatusClass(payment.status)}">${payment.status}</span></td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-small" onclick="viewPayment('${payment.id}')">عرض</button>
                            <button class="btn btn-small btn-success" onclick="markAsPaid('${payment.id}')">تسديد</button>
                            <button class="btn btn-small btn-warning" onclick="editPayment('${payment.id}')">تعديل</button>
                            <button class="btn btn-small btn-info" onclick="sendReminder('${payment.id}')">تذكير</button>
                        </div>
                    </td>
                </tr>`;
            }).join('');
        }

        // تحميل الإحصائيات
        function loadStats() {
            try {
                const payments = financialDb.getAllDeferredPayments();
                
                const totalAmount = payments.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0);
                const pendingCount = payments.filter(p => p.status === 'معلق').length;
                const overdueCount = payments.filter(p => p.status === 'متأخر').length;
                const paidCount = payments.filter(p => p.status === 'مسدد').length;
                
                document.getElementById('totalDeferredAmount').textContent = totalAmount.toFixed(2);
                document.getElementById('pendingPayments').textContent = pendingCount;
                document.getElementById('overduePayments').textContent = overdueCount;
                document.getElementById('paidPayments').textContent = paidCount;
                
            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات:', error);
            }
        }

        // فلترة المدفوعات
        function filterPayments() {
            try {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;
                const customerFilter = document.getElementById('customerFilter').value;
                
                const allPayments = financialDb.getAllDeferredPayments();
                
                const filteredPayments = allPayments.filter(payment => {
                    const matchesSearch = !searchTerm || 
                        payment.id.toLowerCase().includes(searchTerm) ||
                        payment.customerName.toLowerCase().includes(searchTerm) ||
                        payment.shipmentId.toLowerCase().includes(searchTerm) ||
                        (payment.notes && payment.notes.toLowerCase().includes(searchTerm));
                    
                    const matchesStatus = !statusFilter || payment.status === statusFilter;
                    const matchesCustomer = !customerFilter || payment.customerName === customerFilter;
                    
                    return matchesSearch && matchesStatus && matchesCustomer;
                });
                
                displayPayments(filteredPayments);
            } catch (error) {
                console.error('❌ خطأ في فلترة المدفوعات:', error);
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('customerFilter').value = '';
            loadDeferredPayments();
        }

        // عرض تفاصيل الدفع
        function viewPayment(id) {
            try {
                const payments = financialDb.getAllDeferredPayments();
                const payment = payments.find(p => p.id === id);
                if (!payment) {
                    alert('لم يتم العثور على الدفع');
                    return;
                }
                
                alert(`تفاصيل الدفع المؤجل ${payment.id}:\n\n` +
                      `العميل: ${payment.customerName}\n` +
                      `رقم الشحنة: ${payment.shipmentId}\n` +
                      `المبلغ: ${payment.amount} ${payment.currency}\n` +
                      `تاريخ الاستحقاق: ${formatDate(payment.dueDate)}\n` +
                      `الحالة: ${payment.status}\n` +
                      `تاريخ الإنشاء: ${formatDate(payment.createdDate)}\n` +
                      `ملاحظات: ${payment.notes || 'لا توجد ملاحظات'}`);
            } catch (error) {
                console.error('❌ خطأ في عرض الدفع:', error);
                alert('خطأ في عرض الدفع: ' + error.message);
            }
        }

        // تسديد الدفع
        function markAsPaid(id) {
            try {
                const payments = financialDb.getAllDeferredPayments();
                const payment = payments.find(p => p.id === id);
                if (!payment) {
                    alert('لم يتم العثور على الدفع');
                    return;
                }
                
                if (confirm(`هل أنت متأكد من تسديد الدفع ${payment.id}؟\nالمبلغ: ${payment.amount} ${payment.currency}`)) {
                    const updatedPayment = financialDb.updateDeferredPayment(id, {
                        status: 'مسدد',
                        paidDate: new Date().toISOString().split('T')[0]
                    });
                    
                    if (updatedPayment) {
                        alert('تم تسديد الدفع بنجاح');
                        loadDeferredPayments();
                        loadStats();
                    } else {
                        alert('خطأ في تسديد الدفع');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في تسديد الدفع:', error);
                alert('خطأ في تسديد الدفع: ' + error.message);
            }
        }

        // إرسال تذكير
        function sendReminder(id) {
            try {
                const payments = financialDb.getAllDeferredPayments();
                const payment = payments.find(p => p.id === id);
                if (!payment) {
                    alert('لم يتم العثور على الدفع');
                    return;
                }
                
                alert(`تم إرسال تذكير للعميل ${payment.customerName}\nبخصوص الدفع المؤجل ${payment.id}\nالمبلغ: ${payment.amount} ${payment.currency}\nتاريخ الاستحقاق: ${formatDate(payment.dueDate)}`);
            } catch (error) {
                console.error('❌ خطأ في إرسال التذكير:', error);
                alert('خطأ في إرسال التذكير: ' + error.message);
            }
        }

        // إرسال تذكيرات جماعية
        function sendReminders() {
            try {
                const payments = financialDb.getAllDeferredPayments();
                const overduePayments = payments.filter(p => p.status === 'متأخر');
                
                if (overduePayments.length === 0) {
                    alert('لا توجد مدفوعات متأخرة لإرسال تذكيرات لها');
                    return;
                }
                
                if (confirm(`هل تريد إرسال تذكيرات لجميع المدفوعات المتأخرة؟\nعدد المدفوعات: ${overduePayments.length}`)) {
                    alert(`تم إرسال ${overduePayments.length} تذكير للعملاء المتأخرين في السداد`);
                }
            } catch (error) {
                console.error('❌ خطأ في إرسال التذكيرات:', error);
                alert('خطأ في إرسال التذكيرات: ' + error.message);
            }
        }

        // دوال مساعدة
        function getStatusClass(status) {
            const statusMap = {
                'معلق': 'pending',
                'متأخر': 'overdue',
                'مسدد': 'paid'
            };
            return statusMap[status] || 'pending';
        }

        function getDueDateClass(dueDate) {
            if (!dueDate) return 'normal';
            
            const today = new Date();
            const due = new Date(dueDate);
            const diffDays = Math.ceil((due - today) / (1000 * 60 * 60 * 24));
            
            if (diffDays < 0) return 'overdue';
            if (diffDays <= 3) return 'due-soon';
            return 'normal';
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // وظائف إضافية
        function openAddModal() {
            // إنشاء نموذج إضافة دفع مؤجل
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h2>💳 إضافة دفع مؤجل جديد</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="addPaymentForm" style="text-align: right; direction: rtl;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <div class="form-group">
                                    <label for="customerName">اسم العميل *</label>
                                    <input type="text" id="customerName" required placeholder="أدخل اسم العميل">
                                </div>
                                <div class="form-group">
                                    <label for="amount">المبلغ (ريال) *</label>
                                    <input type="number" id="amount" required min="1" placeholder="100">
                                </div>
                                <div class="form-group">
                                    <label for="dueDate">تاريخ الاستحقاق *</label>
                                    <input type="date" id="dueDate" required>
                                </div>
                                <div class="form-group">
                                    <label for="paymentType">نوع الدفع</label>
                                    <select id="paymentType">
                                        <option value="شحنة">دفع شحنة</option>
                                        <option value="خدمة">دفع خدمة</option>
                                        <option value="عمولة">دفع عمولة</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 15px;">
                                <label for="description">الوصف</label>
                                <textarea id="description" rows="3" placeholder="وصف الدفع المؤجل"></textarea>
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <button type="submit" class="btn btn-primary" style="margin-left: 10px;">✅ إضافة الدفع</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // تعيين تاريخ افتراضي (أسبوع من اليوم)
            const nextWeek = new Date();
            nextWeek.setDate(nextWeek.getDate() + 7);
            document.getElementById('dueDate').value = nextWeek.toISOString().split('T')[0];

            // معالج إرسال النموذج
            document.getElementById('addPaymentForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newPayment = {
                    id: 'PAY' + String(Date.now()).slice(-6),
                    customerName: document.getElementById('customerName').value,
                    amount: parseFloat(document.getElementById('amount').value),
                    dueDate: document.getElementById('dueDate').value,
                    paymentType: document.getElementById('paymentType').value,
                    description: document.getElementById('description').value || '',
                    status: 'معلق',
                    createdAt: new Date().toISOString(),
                    createdBy: 'النظام'
                };

                // إضافة للقائمة
                const payments = JSON.parse(localStorage.getItem('deferred_payments') || '[]');
                payments.push(newPayment);
                localStorage.setItem('deferred_payments', JSON.stringify(payments));

                // تحديث العرض
                loadPayments();

                // إغلاق النموذج
                modal.remove();

                alert('✅ تم إضافة الدفع المؤجل بنجاح!\nرقم الدفع: ' + newPayment.id);
                console.log('➕ تم إضافة دفع مؤجل جديد:', newPayment);
            });
        }

        function editPayment(id) {
            const payments = JSON.parse(localStorage.getItem('deferred_payments') || '[]');
            const payment = payments.find(p => p.id === id);

            if (!payment) {
                alert('❌ لم يتم العثور على الدفع');
                return;
            }

            // إنشاء نموذج تعديل
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h2>✏️ تعديل الدفع المؤجل</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="editPaymentForm" style="text-align: right; direction: rtl;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <div class="form-group">
                                    <label for="editCustomerName">اسم العميل *</label>
                                    <input type="text" id="editCustomerName" required value="${payment.customerName}">
                                </div>
                                <div class="form-group">
                                    <label for="editAmount">المبلغ (ريال) *</label>
                                    <input type="number" id="editAmount" required min="1" value="${payment.amount}">
                                </div>
                                <div class="form-group">
                                    <label for="editDueDate">تاريخ الاستحقاق *</label>
                                    <input type="date" id="editDueDate" required value="${payment.dueDate}">
                                </div>
                                <div class="form-group">
                                    <label for="editStatus">الحالة</label>
                                    <select id="editStatus">
                                        <option value="معلق" ${payment.status === 'معلق' ? 'selected' : ''}>معلق</option>
                                        <option value="مدفوع" ${payment.status === 'مدفوع' ? 'selected' : ''}>مدفوع</option>
                                        <option value="متأخر" ${payment.status === 'متأخر' ? 'selected' : ''}>متأخر</option>
                                        <option value="ملغي" ${payment.status === 'ملغي' ? 'selected' : ''}>ملغي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 15px;">
                                <label for="editDescription">الوصف</label>
                                <textarea id="editDescription" rows="3">${payment.description}</textarea>
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <button type="submit" class="btn btn-primary" style="margin-left: 10px;">✅ حفظ التعديلات</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // معالج إرسال النموذج
            document.getElementById('editPaymentForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // تحديث البيانات
                payment.customerName = document.getElementById('editCustomerName').value;
                payment.amount = parseFloat(document.getElementById('editAmount').value);
                payment.dueDate = document.getElementById('editDueDate').value;
                payment.status = document.getElementById('editStatus').value;
                payment.description = document.getElementById('editDescription').value;
                payment.updatedAt = new Date().toISOString();

                // حفظ التعديلات
                const paymentIndex = payments.findIndex(p => p.id === id);
                payments[paymentIndex] = payment;
                localStorage.setItem('deferred_payments', JSON.stringify(payments));

                // تحديث العرض
                loadPayments();

                // إغلاق النموذج
                modal.remove();

                alert('✅ تم تحديث الدفع المؤجل بنجاح!');
                console.log('✏️ تم تعديل دفع مؤجل:', payment);
            });
        }
    </script>
</body>
</html>
