<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل الوصول للنظام المالي | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #333;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .access-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .access-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .access-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
        }

        .access-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .access-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .access-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            width: 100%;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(30, 126, 52, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(30, 126, 52, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
        }

        .btn-info:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(23, 162, 184, 0.4);
        }

        .path-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .path-title {
            font-size: 1.6rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .path-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 5px solid #28a745;
        }

        .path-item h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .path-item p {
            color: #666;
            margin-bottom: 10px;
        }

        .path-link {
            background: #e9ecef;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
            word-break: break-all;
            color: #495057;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            margin-bottom: 20px;
            padding: 12px 20px;
            border-radius: 25px;
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s;
            font-weight: 500;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(5px);
        }

        @media (max-width: 768px) {
            .access-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">
            <i class="fas fa-arrow-right"></i>
            العودة للصفحة الرئيسية
        </a>

        <div class="header">
            <h1>🏛️ دليل الوصول للنظام المالي المتوافق</h1>
            <p>طرق مختلفة للوصول إلى النظام المالي المتوافق مع هيئة الزكاة والضريبة</p>
        </div>

        <!-- طرق الوصول السريع -->
        <div class="access-grid">
            <div class="access-card">
                <div class="access-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div class="access-title">من الصفحة الرئيسية</div>
                <div class="access-description">
                    اذهب للصفحة الرئيسية واضغط على "النظام المالي المتوافق" في القائمة العلوية أو في قسم الميزات
                </div>
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    الصفحة الرئيسية
                </a>
            </div>

            <div class="access-card">
                <div class="access-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="access-title">الوصول المباشر</div>
                <div class="access-description">
                    اضغط هنا للوصول المباشر إلى النظام المالي المتوافق مع هيئة الزكاة والضريبة
                </div>
                <a href="zatca-financial-system.html" class="btn btn-success">
                    <i class="fas fa-shield-alt"></i>
                    النظام المالي المتوافق
                </a>
            </div>

            <div class="access-card">
                <div class="access-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="access-title">نظام الامتثال</div>
                <div class="access-description">
                    اذهب مباشرة إلى نظام الامتثال لإنشاء الفواتير الإلكترونية وإدارة الضرائب
                </div>
                <a href="zatca-compliance.html" class="btn btn-info">
                    <i class="fas fa-file-invoice"></i>
                    نظام الامتثال
                </a>
            </div>
        </div>

        <!-- مسارات الملفات -->
        <div class="path-section">
            <h2 class="path-title">مسارات الملفات المباشرة</h2>
            
            <div class="path-item">
                <h4>🏛️ النظام المالي الرئيسي</h4>
                <p>الصفحة الرئيسية للنظام المالي المتوافق مع هيئة الزكاة والضريبة</p>
                <div class="path-link">file:///C:/Users/<USER>/برنامج توصيل/zatca-financial-system.html</div>
            </div>

            <div class="path-item">
                <h4>⚙️ نظام الامتثال الشامل</h4>
                <p>إنشاء الفواتير الإلكترونية وإدارة الامتثال</p>
                <div class="path-link">file:///C:/Users/<USER>/برنامج توصيل/zatca-compliance.html</div>
            </div>

            <div class="path-item">
                <h4>💰 إدارة ضريبة القيمة المضافة</h4>
                <p>حساب وإدارة ضريبة القيمة المضافة والتقارير الضريبية</p>
                <div class="path-link">file:///C:/Users/<USER>/برنامج توصيل/vat-management.html</div>
            </div>

            <div class="path-item">
                <h4>📊 التقارير الضريبية</h4>
                <p>إنشاء وعرض التقارير الضريبية المختلفة</p>
                <div class="path-link">file:///C:/Users/<USER>/برنامج توصيل/zatca-reports.html</div>
            </div>
        </div>

        <!-- إرشادات سريعة -->
        <div class="path-section">
            <h2 class="path-title">إرشادات سريعة</h2>
            
            <div class="path-item">
                <h4>🚀 للبدء السريع</h4>
                <p>1. اضغط على "النظام المالي المتوافق" أعلاه</p>
                <p>2. اختر الخدمة المطلوبة (فوترة إلكترونية، ضريبة القيمة المضافة، تقارير)</p>
                <p>3. ابدأ في استخدام النظام</p>
            </div>

            <div class="path-item">
                <h4>📋 لإنشاء فاتورة إلكترونية</h4>
                <p>1. اذهب إلى "نظام الامتثال"</p>
                <p>2. اضغط "إنشاء فاتورة إلكترونية"</p>
                <p>3. املأ البيانات واحصل على فاتورة معتمدة</p>
            </div>

            <div class="path-item">
                <h4>💰 لإدارة ضريبة القيمة المضافة</h4>
                <p>1. اذهب إلى "إدارة ضريبة القيمة المضافة"</p>
                <p>2. استخدم الحاسبة أو راجع التقارير</p>
                <p>3. تتبع المدفوعات والمستحقات</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📖 دليل الوصول للنظام المالي جاهز');
            
            // إضافة تأثيرات تفاعلية
            const cards = document.querySelectorAll('.access-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
