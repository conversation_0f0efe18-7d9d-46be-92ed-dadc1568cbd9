// قاعدة البيانات المبسطة - Simple Database
console.log('🔄 بدء تحميل قاعدة البيانات المبسطة...');

class SimpleDatabase {
    constructor() {
        console.log('📊 إنشاء قاعدة البيانات...');
        this.initializeData();
        this.initializeDistributors();
        this.initializeTransfers();
        this.initializeBranches();
        this.initializeBranchTransfers();
        console.log('✅ تم إنشاء قاعدة البيانات بنجاح');
    }

    // تهيئة البيانات الأساسية
    initializeData() {
        try {
            // تهيئة الشحنات
            if (!localStorage.getItem('shipments')) {
                this.createDefaultShipments();
            }

            // تهيئة المستخدمين
            if (!localStorage.getItem('users')) {
                this.createDefaultUsers();
            }

            // تهيئة الأدوار
            if (!localStorage.getItem('roles')) {
                this.createDefaultRoles();
            }

            // تهيئة العملاء
            if (!localStorage.getItem('customers')) {
                this.createDefaultCustomers();
            }

            console.log('✅ تم تهيئة البيانات الأساسية');
        } catch (error) {
            console.error('❌ خطأ في تهيئة البيانات:', error);
        }
    }

    // إنشاء الشحنات الافتراضية
    createDefaultShipments() {
        const shipments = [
            {
                id: 'SHP001',
                trackingNumber: 'SHP001',
                senderName: 'أحمد محمد السعد',
                senderPhone: '+966501234567',
                senderAddress: 'الرياض، حي النخيل، شارع الملك فهد، مبنى 123',
                senderCity: 'الرياض',
                senderRegion: 'منطقة الرياض',
                receiverName: 'فاطمة أحمد الزهراني',
                receiverPhone: '+966507654321',
                receiverAddress: 'جدة، حي الصفا، شارع التحلية، مبنى 456',
                receiverCity: 'جدة',
                receiverRegion: 'منطقة مكة المكرمة',
                contents: 'ملابس وإكسسوارات نسائية',
                weight: 2.5,
                dimensions: '30x20x15 سم',
                cost: 150,
                codAmount: 0,
                currency: 'ريال سعودي',
                paymentMethod: 'مدفوع مسبقاً',
                status: 'في الطريق',
                createdDate: '2024-01-10',
                estimatedDelivery: '2024-01-15',
                actualDelivery: null,
                notes: 'شحنة عادية - التعامل بحذر',
                priority: 'عادي',
                serviceType: 'توصيل عادي',
                distributor: 'أحمد محمد السعد'
            },
            {
                id: 'SHP002',
                trackingNumber: 'SHP002',
                senderName: 'محمد علي القحطاني',
                senderPhone: '+966509876543',
                senderAddress: 'الدمام، حي الشاطئ، الكورنيش، مبنى 789',
                senderCity: 'الدمام',
                senderRegion: 'المنطقة الشرقية',
                receiverName: 'سارة خالد العتيبي',
                receiverPhone: '+966502468135',
                receiverAddress: 'الرياض، حي العليا، شارع العروبة، مبنى 321',
                receiverCity: 'الرياض',
                receiverRegion: 'منطقة الرياض',
                contents: 'كتب ومجلات تعليمية',
                weight: 1.8,
                dimensions: '25x18x10 سم',
                cost: 120,
                codAmount: 0,
                currency: 'ريال سعودي',
                paymentMethod: 'مدفوع مسبقاً',
                status: 'مسلم',
                createdDate: '2024-01-08',
                estimatedDelivery: '2024-01-12',
                actualDelivery: '2024-01-12T14:30:00',
                notes: 'تم التسليم بنجاح للمستلم',
                priority: 'عادي',
                serviceType: 'توصيل عادي',
                distributor: 'محمد علي الزهراني'
            },
            {
                id: 'SHP003',
                trackingNumber: 'SHP003',
                senderName: 'عبدالله سالم المطيري',
                senderPhone: '+966505555555',
                senderAddress: 'مكة المكرمة، العزيزية، شارع الحرم، مبنى 555',
                senderCity: 'مكة المكرمة',
                senderRegion: 'منطقة مكة المكرمة',
                receiverName: 'نورا أحمد الأنصاري',
                receiverPhone: '+966506666666',
                receiverAddress: 'المدينة المنورة، قباء، شارع المسجد، مبنى 777',
                receiverCity: 'المدينة المنورة',
                receiverRegion: 'منطقة المدينة المنورة',
                contents: 'هدايا ومستحضرات تجميل',
                weight: 3.2,
                dimensions: '35x25x20 سم',
                cost: 200,
                codAmount: 350,
                currency: 'ريال سعودي',
                paymentMethod: 'دفع عند الاستلام',
                status: 'معلق',
                createdDate: '2024-01-12',
                estimatedDelivery: '2024-01-16',
                actualDelivery: null,
                notes: 'في انتظار التأكيد من المستلم',
                priority: 'عاجل',
                serviceType: 'توصيل سريع',
                distributor: 'عبدالله سالم القحطاني'
            }
        ];
        
        localStorage.setItem('shipments', JSON.stringify(shipments));
        console.log('✅ تم إنشاء الشحنات الافتراضية');
    }

    // إنشاء المستخدمين الافتراضيين
    createDefaultUsers() {
        const users = [
            {
                id: 'user1',
                name: 'أحمد محمد',
                email: '<EMAIL>',
                password: 'admin123',
                role: 'admin',
                branch: 'main',
                phone: '+966501234567',
                status: 'active',
                createdDate: '2024-01-01',
                lastLogin: new Date().toISOString(),
                customPermissions: []
            },
            {
                id: 'user2',
                name: 'فاطمة أحمد',
                email: '<EMAIL>',
                password: 'manager123',
                role: 'manager',
                branch: 'riyadh',
                phone: '+966507654321',
                status: 'active',
                createdDate: '2024-01-02',
                lastLogin: new Date().toISOString(),
                customPermissions: []
            },
            {
                id: 'user3',
                name: 'محمد علي',
                email: '<EMAIL>',
                password: 'distributor123',
                role: 'distributor',
                branch: 'jeddah',
                phone: '+966509876543',
                status: 'active',
                createdDate: '2024-01-03',
                lastLogin: new Date().toISOString(),
                customPermissions: []
            }
        ];
        
        localStorage.setItem('users', JSON.stringify(users));
        console.log('✅ تم إنشاء المستخدمين الافتراضيين');
    }

    // إنشاء الأدوار الافتراضية
    createDefaultRoles() {
        const roles = [
            {
                id: 'admin',
                name: 'مدير النظام',
                permissions: ['all'],
                description: 'صلاحيات كاملة للنظام'
            },
            {
                id: 'manager',
                name: 'مدير',
                permissions: ['shipments_manage', 'customers_manage', 'reports_view'],
                description: 'صلاحيات إدارية محدودة'
            },
            {
                id: 'distributor',
                name: 'موزع',
                permissions: ['shipments_view', 'shipments_create', 'customers_view'],
                description: 'صلاحيات الموزعين'
            }
        ];
        
        localStorage.setItem('roles', JSON.stringify(roles));
        console.log('✅ تم إنشاء الأدوار الافتراضية');
    }

    // إنشاء العملاء الافتراضيين
    createDefaultCustomers() {
        const customers = [
            {
                id: 'CUST001',
                name: 'أحمد محمد السعد',
                phone: '+966501234567',
                email: '<EMAIL>',
                address: 'الرياض، حي النخيل',
                type: 'individual',
                status: 'active',
                createdDate: '2024-01-01'
            },
            {
                id: 'CUST002',
                name: 'فاطمة أحمد الزهراني',
                phone: '+966507654321',
                email: '<EMAIL>',
                address: 'جدة، حي الصفا',
                type: 'individual',
                status: 'active',
                createdDate: '2024-01-02'
            }
        ];
        
        localStorage.setItem('customers', JSON.stringify(customers));
        console.log('✅ تم إنشاء العملاء الافتراضيين');
    }

    // تهيئة المناديب
    initializeDistributors() {
        if (!localStorage.getItem('distributors')) {
            this.getAllDistributors(); // سيقوم بإنشاء البيانات الافتراضية
        }
    }

    // تهيئة التحويلات
    initializeTransfers() {
        if (!localStorage.getItem('transfers')) {
            this.getAllTransfers(); // سيقوم بإنشاء البيانات الافتراضية
        }
    }

    // تهيئة الفروع
    initializeBranches() {
        if (!localStorage.getItem('branches')) {
            this.getAllBranches(); // سيقوم بإنشاء البيانات الافتراضية
        }
    }

    // تهيئة التحويلات بين الفروع
    initializeBranchTransfers() {
        if (!localStorage.getItem('branchTransfers')) {
            this.getAllBranchTransfers(); // سيقوم بإنشاء البيانات الافتراضية
        }
    }

    // === وظائف الشحنات ===
    getAllShipments() {
        try {
            const shipments = localStorage.getItem('shipments');
            return shipments ? JSON.parse(shipments) : [];
        } catch (error) {
            console.error('خطأ في استرجاع الشحنات:', error);
            return [];
        }
    }

    findShipmentByTracking(trackingNumber) {
        try {
            const shipments = this.getAllShipments();
            return shipments.find(shipment =>
                (shipment.trackingNumber && shipment.trackingNumber.toLowerCase() === trackingNumber.toLowerCase()) ||
                (shipment.id && shipment.id.toLowerCase() === trackingNumber.toLowerCase())
            );
        } catch (error) {
            console.error('خطأ في البحث عن الشحنة:', error);
            return null;
        }
    }

    getShipmentById(id) {
        try {
            const shipments = this.getAllShipments();
            return shipments.find(shipment => shipment.id === id);
        } catch (error) {
            console.error('خطأ في البحث عن الشحنة بالمعرف:', error);
            return null;
        }
    }

    addShipment(shipmentData) {
        try {
            const shipments = this.getAllShipments();
            const newShipment = {
                id: 'SHP' + String(shipments.length + 1).padStart(3, '0'),
                trackingNumber: 'SHP' + String(shipments.length + 1).padStart(3, '0'),
                ...shipmentData,
                createdDate: new Date().toISOString().split('T')[0],
                status: shipmentData.status || 'جديد'
            };
            shipments.push(newShipment);
            localStorage.setItem('shipments', JSON.stringify(shipments));
            return newShipment;
        } catch (error) {
            console.error('خطأ في إضافة الشحنة:', error);
            return false;
        }
    }

    updateShipment(id, updateData) {
        try {
            const shipments = this.getAllShipments();
            const shipmentIndex = shipments.findIndex(shipment => shipment.id === id);

            if (shipmentIndex !== -1) {
                shipments[shipmentIndex] = { ...shipments[shipmentIndex], ...updateData };
                localStorage.setItem('shipments', JSON.stringify(shipments));
                return shipments[shipmentIndex];
            }

            return false;
        } catch (error) {
            console.error('خطأ في تحديث الشحنة:', error);
            return false;
        }
    }

    deleteShipment(id) {
        try {
            const shipments = this.getAllShipments();
            const filteredShipments = shipments.filter(shipment => shipment.id !== id);
            localStorage.setItem('shipments', JSON.stringify(shipments));
            return true;
        } catch (error) {
            console.error('خطأ في حذف الشحنة:', error);
            return false;
        }
    }

    searchShipments(query) {
        try {
            const shipments = this.getAllShipments();
            const lowerQuery = query.toLowerCase();

            return shipments.filter(shipment =>
                (shipment.trackingNumber && shipment.trackingNumber.toLowerCase().includes(lowerQuery)) ||
                (shipment.senderName && shipment.senderName.toLowerCase().includes(lowerQuery)) ||
                (shipment.receiverName && shipment.receiverName.toLowerCase().includes(lowerQuery)) ||
                (shipment.contents && shipment.contents.toLowerCase().includes(lowerQuery))
            );
        } catch (error) {
            console.error('خطأ في البحث في الشحنات:', error);
            return [];
        }
    }

    getShipmentStats() {
        try {
            const shipments = this.getAllShipments();
            return {
                total: shipments.length,
                pending: shipments.filter(s => s.status === 'في الطريق' || s.status === 'معلق').length,
                delivered: shipments.filter(s => s.status === 'مسلم').length,
                cancelled: shipments.filter(s => s.status === 'ملغي').length
            };
        } catch (error) {
            console.error('خطأ في إحصائيات الشحنات:', error);
            return { total: 0, pending: 0, delivered: 0, cancelled: 0 };
        }
    }

    // === وظائف المستخدمين ===
    getAllUsers() {
        try {
            const users = localStorage.getItem('users');
            return users ? JSON.parse(users) : [];
        } catch (error) {
            console.error('خطأ في استرجاع المستخدمين:', error);
            return [];
        }
    }

    getUserById(userId) {
        try {
            const users = this.getAllUsers();
            return users.find(user => user.id === userId);
        } catch (error) {
            console.error('خطأ في البحث عن المستخدم:', error);
            return null;
        }
    }

    updateUser(userId, updateData) {
        try {
            const users = this.getAllUsers();
            const userIndex = users.findIndex(user => user.id === userId);
            
            if (userIndex !== -1) {
                users[userIndex] = { ...users[userIndex], ...updateData };
                localStorage.setItem('users', JSON.stringify(users));
                return users[userIndex];
            }
            
            return false;
        } catch (error) {
            console.error('خطأ في تحديث المستخدم:', error);
            return false;
        }
    }

    // === وظائف الأدوار ===
    getAllRoles() {
        try {
            const roles = localStorage.getItem('roles');
            return roles ? JSON.parse(roles) : [];
        } catch (error) {
            console.error('خطأ في استرجاع الأدوار:', error);
            return [];
        }
    }

    // === وظائف العملاء ===
    getAllCustomers() {
        try {
            const customers = localStorage.getItem('customers');
            return customers ? JSON.parse(customers) : [];
        } catch (error) {
            console.error('خطأ في استرجاع العملاء:', error);
            return [];
        }
    }

    // === وظائف المناديب ===
    getAllDistributors() {
        try {
            const distributors = localStorage.getItem('distributors');
            if (distributors) {
                return JSON.parse(distributors);
            } else {
                // إنشاء مناديب افتراضيين
                const defaultDistributors = [
                    {
                        id: 'DIST001',
                        name: 'أحمد محمد السعد',
                        phone: '+966501234567',
                        email: '<EMAIL>',
                        area: 'الرياض',
                        vehicleType: 'سيارة صغيرة',
                        rating: 4.8,
                        status: 'متاح',
                        joinDate: '2024-01-01',
                        totalDeliveries: 150,
                        successRate: 98
                    },
                    {
                        id: 'DIST002',
                        name: 'محمد علي الزهراني',
                        phone: '+966507654321',
                        email: '<EMAIL>',
                        area: 'جدة',
                        vehicleType: 'دراجة نارية',
                        rating: 4.6,
                        status: 'مشغول',
                        joinDate: '2024-01-15',
                        totalDeliveries: 89,
                        successRate: 95
                    },
                    {
                        id: 'DIST003',
                        name: 'عبدالله سالم القحطاني',
                        phone: '+966509876543',
                        email: '<EMAIL>',
                        area: 'الدمام',
                        vehicleType: 'شاحنة صغيرة',
                        rating: 4.9,
                        status: 'متاح',
                        joinDate: '2023-12-01',
                        totalDeliveries: 200,
                        successRate: 99
                    }
                ];
                localStorage.setItem('distributors', JSON.stringify(defaultDistributors));
                console.log('✅ تم إنشاء المناديب الافتراضيين');
                return defaultDistributors;
            }
        } catch (error) {
            console.error('خطأ في استرجاع المناديب:', error);
            return [];
        }
    }

    // === وظائف التحويلات ===
    getAllTransfers() {
        try {
            const transfers = localStorage.getItem('transfers');
            if (transfers) {
                return JSON.parse(transfers);
            } else {
                // إنشاء تحويلات افتراضية
                const defaultTransfers = [
                    {
                        id: 'TRANS001',
                        fromBranch: 'الرياض',
                        toBranch: 'جدة',
                        shipmentId: 'SHP001',
                        transferDate: '2024-01-10',
                        status: 'مكتمل',
                        notes: 'تحويل عادي'
                    },
                    {
                        id: 'TRANS002',
                        fromBranch: 'جدة',
                        toBranch: 'الدمام',
                        shipmentId: 'SHP002',
                        transferDate: '2024-01-12',
                        status: 'في الطريق',
                        notes: 'تحويل سريع'
                    }
                ];
                localStorage.setItem('transfers', JSON.stringify(defaultTransfers));
                console.log('✅ تم إنشاء التحويلات الافتراضية');
                return defaultTransfers;
            }
        } catch (error) {
            console.error('خطأ في استرجاع التحويلات:', error);
            return [];
        }
    }

    // === وظائف الفروع ===
    getAllBranches() {
        try {
            const branches = localStorage.getItem('branches');
            if (branches) {
                return JSON.parse(branches);
            } else {
                // إنشاء فروع افتراضية
                const defaultBranches = [
                    {
                        id: 'BRANCH001',
                        name: 'فرع الرياض الرئيسي',
                        city: 'الرياض',
                        address: 'حي النخيل، شارع الملك فهد',
                        phone: '+966112345678',
                        manager: 'أحمد محمد',
                        status: 'نشط',
                        isActive: true,
                        shipmentsCount: 45,
                        revenue: 15000
                    },
                    {
                        id: 'BRANCH002',
                        name: 'فرع جدة',
                        city: 'جدة',
                        address: 'حي الصفا، شارع التحلية',
                        phone: '+966126789012',
                        manager: 'فاطمة أحمد',
                        status: 'نشط',
                        isActive: true,
                        shipmentsCount: 32,
                        revenue: 12000
                    },
                    {
                        id: 'BRANCH003',
                        name: 'فرع الدمام',
                        city: 'الدمام',
                        address: 'حي الشاطئ، الكورنيش',
                        phone: '+966138901234',
                        manager: 'محمد علي',
                        status: 'نشط',
                        isActive: true,
                        shipmentsCount: 28,
                        revenue: 9500
                    }
                ];
                localStorage.setItem('branches', JSON.stringify(defaultBranches));
                console.log('✅ تم إنشاء الفروع الافتراضية');
                return defaultBranches;
            }
        } catch (error) {
            console.error('خطأ في استرجاع الفروع:', error);
            return [];
        }
    }

    // === وظائف التحويلات بين الفروع ===
    getAllBranchTransfers() {
        try {
            const branchTransfers = localStorage.getItem('branchTransfers');
            if (branchTransfers) {
                return JSON.parse(branchTransfers);
            } else {
                // إنشاء تحويلات افتراضية بين الفروع
                const defaultBranchTransfers = [
                    {
                        id: 'BT001',
                        fromBranch: 'فرع الرياض الرئيسي',
                        toBranch: 'فرع جدة',
                        shipmentId: 'SHP001',
                        transferDate: '2024-01-10',
                        status: 'مكتمل',
                        notes: 'تحويل عادي بين الفروع',
                        transferredBy: 'أحمد محمد',
                        receivedBy: 'فاطمة أحمد'
                    },
                    {
                        id: 'BT002',
                        fromBranch: 'فرع جدة',
                        toBranch: 'فرع الدمام',
                        shipmentId: 'SHP002',
                        transferDate: '2024-01-12',
                        status: 'في الطريق',
                        notes: 'تحويل سريع',
                        transferredBy: 'فاطمة أحمد',
                        receivedBy: 'محمد علي'
                    },
                    {
                        id: 'BT003',
                        fromBranch: 'فرع الدمام',
                        toBranch: 'فرع الرياض الرئيسي',
                        shipmentId: 'SHP003',
                        transferDate: '2024-01-14',
                        status: 'معلق',
                        notes: 'في انتظار التأكيد',
                        transferredBy: 'محمد علي',
                        receivedBy: 'أحمد محمد'
                    }
                ];
                localStorage.setItem('branchTransfers', JSON.stringify(defaultBranchTransfers));
                console.log('✅ تم إنشاء التحويلات بين الفروع الافتراضية');
                return defaultBranchTransfers;
            }
        } catch (error) {
            console.error('خطأ في استرجاع التحويلات بين الفروع:', error);
            return [];
        }
    }

    // === وظائف الموظفين ===
    getAllEmployees() {
        try {
            const employees = localStorage.getItem('employees');
            if (employees) {
                return JSON.parse(employees);
            } else {
                return [];
            }
        } catch (error) {
            console.error('خطأ في جلب الموظفين:', error);
            return [];
        }
    }

    // === وظائف الأقسام ===
    getAllDepartments() {
        try {
            const departments = localStorage.getItem('departments');
            if (departments) {
                return JSON.parse(departments);
            } else {
                return [];
            }
        } catch (error) {
            console.error('خطأ في جلب الأقسام:', error);
            return [];
        }
    }

    // === وظائف إضافية للتوافق ===
    getDistributorById(id) {
        try {
            const distributors = this.getAllDistributors();
            return distributors.find(distributor => distributor.id === id);
        } catch (error) {
            console.error('خطأ في البحث عن المندوب:', error);
            return null;
        }
    }

    addDistributor(distributorData) {
        try {
            const distributors = this.getAllDistributors();
            const newDistributor = {
                id: 'DIST' + String(distributors.length + 1).padStart(3, '0'),
                ...distributorData,
                joinDate: new Date().toISOString().split('T')[0],
                totalDeliveries: 0,
                successRate: 100,
                rating: 5.0
            };
            distributors.push(newDistributor);
            localStorage.setItem('distributors', JSON.stringify(distributors));
            return newDistributor;
        } catch (error) {
            console.error('خطأ في إضافة المندوب:', error);
            return false;
        }
    }

    updateDistributor(id, updateData) {
        try {
            const distributors = this.getAllDistributors();
            const distributorIndex = distributors.findIndex(distributor => distributor.id === id);

            if (distributorIndex !== -1) {
                distributors[distributorIndex] = { ...distributors[distributorIndex], ...updateData };
                localStorage.setItem('distributors', JSON.stringify(distributors));
                return distributors[distributorIndex];
            }

            return false;
        } catch (error) {
            console.error('خطأ في تحديث المندوب:', error);
            return false;
        }
    }

    deleteDistributor(id) {
        try {
            const distributors = this.getAllDistributors();
            const filteredDistributors = distributors.filter(distributor => distributor.id !== id);
            localStorage.setItem('distributors', JSON.stringify(filteredDistributors));
            return true;
        } catch (error) {
            console.error('خطأ في حذف المندوب:', error);
            return false;
        }
    }

    // === وظائف إضافية للطباعة والتقارير ===
    getCompanyInfo() {
        return {
            name: 'شركة الشحن السريع',
            nameEn: 'Fast Shipping Company',
            address: 'الرياض، المملكة العربية السعودية',
            phone: '+966112345678',
            email: '<EMAIL>',
            website: 'www.fastshipping.com',
            logo: '🚚',
            cr: '1234567890',
            vatNumber: '123456789012345'
        };
    }

    generateQRCode(shipmentId) {
        // إنشاء رمز QR بسيط (نص فقط للعرض)
        return `QR:${shipmentId}:${new Date().getTime()}`;
    }

    getShipmentForPrint(id) {
        try {
            const shipment = this.getShipmentById(id);
            if (!shipment) return null;

            const company = this.getCompanyInfo();
            const qrCode = this.generateQRCode(id);

            return {
                ...shipment,
                company,
                qrCode,
                printDate: new Date().toLocaleString('ar-SA'),
                printTime: new Date().toLocaleTimeString('ar-SA')
            };
        } catch (error) {
            console.error('خطأ في تحضير الشحنة للطباعة:', error);
            return null;
        }
    }

    // === وظائف العملاء المحسنة ===
    findCustomerByPhone(phone) {
        try {
            const customers = this.getAllCustomers();
            return customers.find(customer => customer.phone === phone);
        } catch (error) {
            console.error('خطأ في البحث عن العميل:', error);
            return null;
        }
    }

    addCustomer(customerData) {
        try {
            const customers = this.getAllCustomers();
            const newCustomer = {
                id: 'CUST' + String(customers.length + 1).padStart(3, '0'),
                ...customerData,
                createdDate: new Date().toISOString().split('T')[0],
                status: customerData.status || 'active'
            };
            customers.push(newCustomer);
            localStorage.setItem('customers', JSON.stringify(customers));
            return newCustomer;
        } catch (error) {
            console.error('خطأ في إضافة العميل:', error);
            return false;
        }
    }

    // === وظائف التقارير ===
    getShipmentsByDateRange(startDate, endDate) {
        try {
            const shipments = this.getAllShipments();
            return shipments.filter(shipment => {
                const shipmentDate = new Date(shipment.createdDate);
                const start = new Date(startDate);
                const end = new Date(endDate);
                return shipmentDate >= start && shipmentDate <= end;
            });
        } catch (error) {
            console.error('خطأ في البحث بالتاريخ:', error);
            return [];
        }
    }

    getShipmentsByStatus(status) {
        try {
            const shipments = this.getAllShipments();
            return shipments.filter(shipment => shipment.status === status);
        } catch (error) {
            console.error('خطأ في البحث بالحالة:', error);
            return [];
        }
    }

    getShipmentsByDistributor(distributorName) {
        try {
            const shipments = this.getAllShipments();
            return shipments.filter(shipment => shipment.distributor === distributorName);
        } catch (error) {
            console.error('خطأ في البحث بالموزع:', error);
            return [];
        }
    }
}

// إنشاء مثيل من قاعدة البيانات
console.log('🚀 إنشاء مثيل قاعدة البيانات...');
const db = new SimpleDatabase();
console.log('✅ تم تحميل قاعدة البيانات المبسطة بنجاح - SimpleDatabase v1.0');

// تأكيد التحميل
window.dbLoaded = true;
console.log('🎉 قاعدة البيانات جاهزة للاستخدام!');
