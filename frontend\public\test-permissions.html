<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الصلاحيات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success { background: #27ae60; }
        .btn-warning { background: #f39c12; }
        .btn-danger { background: #e74c3c; }
        .btn-info { background: #17a2b8; }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام الصلاحيات المتقدم</h1>
        <p>هذه الصفحة تختبر جميع مكونات نظام الصلاحيات</p>
        
        <div id="results"></div>
        
        <!-- اختبار الروابط -->
        <div class="test-section">
            <h3>🔗 اختبار الروابط</h3>
            <a href="user-management.html" class="btn">👥 إدارة المستخدمين</a>
            <a href="user-permissions-advanced.html" class="btn btn-info">🔐 الصلاحيات المتقدمة</a>
            <a href="permissions-matrix.html" class="btn btn-success">📊 مصفوفة الصلاحيات</a>
            <a href="main-dashboard.html" class="btn btn-warning">🏠 الرئيسية</a>
        </div>

        <!-- اختبار الصلاحيات -->
        <div class="test-section">
            <h3>🔐 اختبار الصلاحيات</h3>
            <button class="btn" onclick="testPermissions()">🧪 تشغيل اختبار الصلاحيات</button>
            <button class="btn btn-success" onclick="testUserData()">👤 اختبار بيانات المستخدمين</button>
            <button class="btn btn-warning" onclick="testDatabase()">🗄️ اختبار قاعدة البيانات</button>
        </div>

        <!-- اختبار المستخدمين -->
        <div class="test-section">
            <h3>👥 اختبار المستخدمين</h3>
            <button class="btn" onclick="testUser('user1')">🔴 مدير النظام</button>
            <button class="btn" onclick="testUser('user2')">🟠 مدير</button>
            <button class="btn" onclick="testUser('user3')">🟣 موزع</button>
            <button class="btn" onclick="testUser('user4')">🟡 موظف</button>
            <button class="btn" onclick="testUser('user5')">🟢 مشاهد</button>
        </div>

        <!-- أدوات إضافية -->
        <div class="test-section">
            <h3>🛠️ أدوات إضافية</h3>
            <button class="btn btn-danger" onclick="clearResults()">🗑️ مسح النتائج</button>
            <button class="btn btn-info" onclick="showSystemInfo()">ℹ️ معلومات النظام</button>
            <button class="btn btn-success" onclick="exportResults()">📤 تصدير النتائج</button>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString('ar-SA')}:</strong> ${message}`;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
            addResult('تم مسح جميع النتائج', 'info');
        }

        // اختبار الصلاحيات
        function testPermissions() {
            addResult('🚀 بدء اختبار نظام الصلاحيات...', 'info');

            // اختبار تحميل قاعدة البيانات
            if (typeof db !== 'undefined' && db !== null) {
                addResult('✅ قاعدة البيانات متاحة', 'success');
            } else {
                addResult('❌ قاعدة البيانات غير متاحة', 'error');
                return;
            }

            // اختبار المستخدمين
            try {
                const users = db.getAllUsers();
                if (users && users.length > 0) {
                    addResult(`✅ تم العثور على ${users.length} مستخدم`, 'success');
                } else {
                    addResult('⚠️ لا توجد مستخدمين في النظام', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في تحميل المستخدمين: ' + error.message, 'error');
            }

            // اختبار الأدوار
            try {
                const roles = db.getAllRoles();
                if (roles && roles.length > 0) {
                    addResult(`✅ تم العثور على ${roles.length} دور`, 'success');
                } else {
                    addResult('⚠️ لا توجد أدوار في النظام', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في تحميل الأدوار: ' + error.message, 'error');
            }

            addResult('🏁 انتهى اختبار الصلاحيات', 'info');
        }

        // اختبار بيانات المستخدمين
        function testUserData() {
            addResult('👤 بدء اختبار بيانات المستخدمين...', 'info');

            const testUsers = ['user1', 'user2', 'user3', 'user4', 'user5'];
            
            testUsers.forEach(userId => {
                try {
                    if (typeof db !== 'undefined') {
                        const user = db.getUserById(userId);
                        if (user) {
                            addResult(`✅ ${userId}: ${user.name} (${user.role})`, 'success');
                        } else {
                            addResult(`⚠️ ${userId}: لم يتم العثور على المستخدم`, 'error');
                        }
                    } else {
                        addResult(`⚠️ ${userId}: قاعدة البيانات غير متاحة`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ ${userId}: خطأ - ${error.message}`, 'error');
                }
            });

            addResult('🏁 انتهى اختبار بيانات المستخدمين', 'info');
        }

        // اختبار قاعدة البيانات
        function testDatabase() {
            addResult('🗄️ بدء اختبار قاعدة البيانات...', 'info');

            // اختبار الاتصال
            if (typeof db === 'undefined') {
                addResult('❌ قاعدة البيانات غير محملة', 'error');
                return;
            }

            // اختبار الوظائف الأساسية
            const tests = [
                { name: 'getAllUsers', func: () => db.getAllUsers() },
                { name: 'getAllRoles', func: () => db.getAllRoles() },
                { name: 'getAllShipments', func: () => db.getAllShipments() }
            ];

            tests.forEach(test => {
                try {
                    const result = test.func();
                    if (result) {
                        addResult(`✅ ${test.name}: يعمل بشكل صحيح (${result.length} عنصر)`, 'success');
                    } else {
                        addResult(`⚠️ ${test.name}: لا توجد بيانات`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ ${test.name}: خطأ - ${error.message}`, 'error');
                }
            });

            addResult('🏁 انتهى اختبار قاعدة البيانات', 'info');
        }

        // اختبار مستخدم محدد
        function testUser(userId) {
            addResult(`🔍 اختبار المستخدم ${userId}...`, 'info');

            try {
                if (typeof db !== 'undefined') {
                    const user = db.getUserById(userId);
                    if (user) {
                        addResult(`👤 الاسم: ${user.name}`, 'info');
                        addResult(`📧 البريد: ${user.email}`, 'info');
                        addResult(`🎭 الدور: ${user.role}`, 'info');
                        addResult(`🏢 الفرع: ${user.branch || 'غير محدد'}`, 'info');
                        addResult(`📱 الهاتف: ${user.phone || 'غير محدد'}`, 'info');
                        addResult(`🔐 الصلاحيات المخصصة: ${user.customPermissions ? user.customPermissions.length : 0}`, 'info');
                        
                        // اختبار فتح صفحة الصلاحيات
                        const permissionsUrl = `user-permissions-advanced.html?userId=${userId}`;
                        addResult(`🔗 رابط الصلاحيات: <a href="${permissionsUrl}" target="_blank">فتح صفحة الصلاحيات</a>`, 'success');
                    } else {
                        addResult(`❌ لم يتم العثور على المستخدم ${userId}`, 'error');
                    }
                } else {
                    addResult('❌ قاعدة البيانات غير متاحة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار المستخدم: ${error.message}`, 'error');
            }
        }

        // عرض معلومات النظام
        function showSystemInfo() {
            addResult('ℹ️ معلومات النظام:', 'info');
            addResult(`🌐 المتصفح: ${navigator.userAgent}`, 'info');
            addResult(`📅 التاريخ: ${new Date().toLocaleString('ar-SA')}`, 'info');
            addResult(`🔗 الرابط: ${window.location.href}`, 'info');
            addResult(`💾 التخزين المحلي: ${localStorage.length} عنصر`, 'info');
        }

        // تصدير النتائج
        function exportResults() {
            const results = resultsDiv.innerText;
            const blob = new Blob([results], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'permissions_test_results_' + new Date().toISOString().split('T')[0] + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            addResult('✅ تم تصدير النتائج بنجاح', 'success');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎉 مرحباً بك في صفحة اختبار نظام الصلاحيات', 'success');
            addResult('💡 استخدم الأزرار أعلاه لاختبار مكونات النظام المختلفة', 'info');
            
            // اختبار سريع
            setTimeout(() => {
                if (typeof db !== 'undefined') {
                    addResult('✅ قاعدة البيانات جاهزة للاختبار', 'success');
                } else {
                    addResult('⚠️ قاعدة البيانات قيد التحميل...', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
