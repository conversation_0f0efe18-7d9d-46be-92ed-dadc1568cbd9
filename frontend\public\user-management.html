<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --font-arabic: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic) !important;
            font-weight: 600 !important;
        }

        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic) !important;
            font-weight: 600 !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1400px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .management-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .section-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            border-bottom: 2px solid var(--light-color);
        }

        .tab-btn {
            padding: 12px 24px;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            border-radius: 10px 10px 0 0;
            transition: all 0.3s ease;
            font-family: var(--font-arabic);
            color: #666;
        }

        .tab-btn.active {
            background: var(--secondary-color);
            color: white;
            transform: translateY(-2px);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-container {
            display: flex;
            gap: 10px;
            flex: 1;
            max-width: 400px;
        }

        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            font-family: var(--font-arabic);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-arabic);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, var(--info-color), #138496);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .users-table th,
        .users-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .users-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .users-table tr:hover {
            background: #f8f9fa;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--secondary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 0 auto;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .status-suspended {
            background: #fff3cd;
            color: #856404;
        }

        .role-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
        }

        .role-admin {
            background: #e7f3ff;
            color: #0066cc;
        }

        .role-manager {
            background: #fff2e7;
            color: #cc6600;
        }

        .role-distributor {
            background: #f0e7ff;
            color: #6600cc;
        }

        .role-employee {
            background: #e7ffe7;
            color: #006600;
        }

        .role-viewer {
            background: #f0f0f0;
            color: #666666;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
            border-radius: 15px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--light-color);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f0f0f0;
            color: var(--danger-color);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            font-family: var(--font-arabic);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            font-family: var(--font-arabic);
            background: white;
            cursor: pointer;
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .permission-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .permission-group h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.1rem;
            border-bottom: 2px solid var(--light-color);
            padding-bottom: 8px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .permission-item:hover {
            background: white;
        }

        .permission-checkbox {
            margin-left: 10px;
            transform: scale(1.2);
        }

        .permission-label {
            flex: 1;
            font-weight: 500;
            color: #555;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .management-container {
                padding: 20px;
            }

            .action-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-container {
                max-width: none;
            }

            .section-tabs {
                flex-wrap: wrap;
            }

            .users-table {
                font-size: 0.9rem;
            }

            .users-table th,
            .users-table td {
                padding: 10px 8px;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 20px;
            }

            .permissions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>👥</span>
                <span>نظام إدارة الشحنات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="shipments.html" data-permission="shipments_view">الشحنات</a>
                <a href="customers.html" data-permission="customers_view">العملاء</a>
                <a href="user-management.html" class="active" data-permission="users_view">إدارة المستخدمين</a>
                <a href="reports.html" data-permission="reports_view">التقارير</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1>👥 إدارة المستخدمين والصلاحيات</h1>
            <p>إدارة شاملة للمستخدمين وتحديد صلاحياتهم في النظام</p>
        </div>

        <div class="management-container">
            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeUsers">0</div>
                    <div class="stat-label">المستخدمين النشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="adminUsers">0</div>
                    <div class="stat-label">المديرين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="onlineUsers">0</div>
                    <div class="stat-label">متصل الآن</div>
                </div>
            </div>

            <!-- تبويبات الأقسام -->
            <div class="section-tabs">
                <button class="tab-btn active" onclick="showTab('users')">👥 المستخدمين</button>
                <button class="tab-btn" onclick="showTab('roles')">🔐 الأدوار والصلاحيات</button>
                <button class="tab-btn" onclick="showTab('permissions')">⚙️ إعدادات الصلاحيات</button>
                <button class="tab-btn" onclick="showTab('activity')">📊 سجل النشاطات</button>
            </div>

            <!-- قسم المستخدمين -->
            <div id="usersTab" class="tab-content active">
                <div class="action-bar">
                    <div class="search-container">
                        <input type="text" class="search-input" id="userSearch" placeholder="البحث عن مستخدم...">
                        <button class="btn btn-primary" onclick="searchUsers()">🔍 بحث</button>
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="openAddUserModal()" data-permission="users_create">➕ إضافة مستخدم جديد</button>
                        <button class="btn btn-info" onclick="exportUsers()" data-permission="users_view">📤 تصدير البيانات</button>
                    </div>
                </div>

                <div style="overflow-x: auto;">
                    <table class="users-table" id="usersTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- قسم الأدوار والصلاحيات -->
            <div id="rolesTab" class="tab-content">
                <div class="action-bar">
                    <h3>🔐 إدارة الأدوار والصلاحيات</h3>
                    <button class="btn btn-success" onclick="openAddRoleModal()" data-permission="users_roles">➕ إضافة دور جديد</button>
                </div>

                <div class="permissions-grid" id="rolesGrid">
                    <!-- سيتم ملء الأدوار بواسطة JavaScript -->
                </div>
            </div>

            <!-- قسم إعدادات الصلاحيات -->
            <div id="permissionsTab" class="tab-content">
                <div class="action-bar">
                    <h3>⚙️ إعدادات الصلاحيات العامة</h3>
                    <div>
                        <button class="btn btn-info" onclick="openPermissionsMatrix()">📊 مصفوفة الصلاحيات</button>
                        <button class="btn btn-warning" onclick="resetPermissions()" data-permission="users_permissions">🔄 إعادة تعيين الصلاحيات</button>
                    </div>
                </div>

                <div class="permissions-grid" id="permissionsGrid">
                    <!-- سيتم ملء الصلاحيات بواسطة JavaScript -->
                </div>
            </div>

            <!-- قسم سجل النشاطات -->
            <div id="activityTab" class="tab-content">
                <div class="action-bar">
                    <h3>📊 سجل نشاطات المستخدمين</h3>
                    <div>
                        <button class="btn btn-info" onclick="refreshActivity()" data-permission="system_logs">🔄 تحديث</button>
                        <button class="btn btn-warning" onclick="clearActivity()" data-permission="system_logs">🗑️ مسح السجل</button>
                    </div>
                </div>

                <div style="overflow-x: auto;">
                    <table class="users-table" id="activityTable">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>النشاط</th>
                                <th>التفاصيل</th>
                                <th>التاريخ والوقت</th>
                                <th>عنوان IP</th>
                                <th>المتصفح</th>
                            </tr>
                        </thead>
                        <tbody id="activityTableBody">
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل مستخدم -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="userModalTitle">إضافة مستخدم جديد</h3>
                <button class="close-btn" onclick="closeModal('userModal')">&times;</button>
            </div>

            <form id="userForm">
                <div class="form-group">
                    <label class="form-label">الاسم الكامل *</label>
                    <input type="text" class="form-control" id="userName" required>
                </div>

                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني *</label>
                    <input type="email" class="form-control" id="userEmail" required>
                </div>

                <div class="form-group">
                    <label class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="userPhone">
                </div>

                <div class="form-group">
                    <label class="form-label">كلمة المرور *</label>
                    <input type="password" class="form-control" id="userPassword" required>
                </div>

                <div class="form-group">
                    <label class="form-label">تأكيد كلمة المرور *</label>
                    <input type="password" class="form-control" id="userPasswordConfirm" required>
                </div>

                <div class="form-group">
                    <label class="form-label">الدور *</label>
                    <select class="form-select" id="userRole" required>
                        <option value="">اختر الدور</option>
                        <option value="admin">مدير النظام</option>
                        <option value="manager">مدير</option>
                        <option value="distributor">موزع</option>
                        <option value="employee">موظف</option>
                        <option value="viewer">مشاهد</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الفرع</label>
                    <select class="form-select" id="userBranch">
                        <option value="">اختر الفرع</option>
                        <option value="main">الفرع الرئيسي</option>
                        <option value="riyadh">فرع الرياض</option>
                        <option value="jeddah">فرع جدة</option>
                        <option value="dammam">فرع الدمام</option>
                        <option value="kuwait">فرع الكويت</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" id="userStatus">
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="suspended">معلق</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الصلاحيات المخصصة</label>
                    <div class="permissions-grid" id="userPermissions">
                        <!-- سيتم ملء الصلاحيات بواسطة JavaScript -->
                    </div>
                </div>

                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                    <button type="button" class="btn btn-danger" onclick="closeModal('userModal')">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ المستخدم</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل دور -->
    <div id="roleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="roleModalTitle">إضافة دور جديد</h3>
                <button class="close-btn" onclick="closeModal('roleModal')">&times;</button>
            </div>

            <form id="roleForm">
                <div class="form-group">
                    <label class="form-label">اسم الدور *</label>
                    <input type="text" class="form-control" id="roleName" required>
                </div>

                <div class="form-group">
                    <label class="form-label">وصف الدور</label>
                    <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">مستوى الأولوية</label>
                    <select class="form-select" id="rolePriority">
                        <option value="1">عالي جداً</option>
                        <option value="2">عالي</option>
                        <option value="3">متوسط</option>
                        <option value="4">منخفض</option>
                        <option value="5">منخفض جداً</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">صلاحيات الدور</label>
                    <div class="permissions-grid" id="rolePermissions">
                        <!-- سيتم ملء الصلاحيات بواسطة JavaScript -->
                    </div>
                </div>

                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                    <button type="button" class="btn btn-danger" onclick="closeModal('roleModal')">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ الدور</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/database-simple.js"></script>
    <script>
        // متغيرات عامة
        let currentUsers = [];
        let currentRoles = [];
        let currentPermissions = [];
        let editingUserId = null;
        let editingRoleId = null;

        // تعريف الصلاحيات الأساسية
        const systemPermissions = {
            shipments: {
                name: 'إدارة الشحنات',
                permissions: [
                    { id: 'shipments_view', name: 'عرض الشحنات', description: 'عرض قائمة الشحنات والتفاصيل' },
                    { id: 'shipments_create', name: 'إنشاء شحنة', description: 'إضافة شحنات جديدة' },
                    { id: 'shipments_edit', name: 'تعديل الشحنات', description: 'تعديل بيانات الشحنات الموجودة' },
                    { id: 'shipments_delete', name: 'حذف الشحنات', description: 'حذف الشحنات من النظام' },
                    { id: 'shipments_track', name: 'تتبع الشحنات', description: 'تتبع حالة الشحنات' },
                    { id: 'shipments_print', name: 'طباعة الشحنات', description: 'طباعة تفاصيل وإيصالات الشحنات' }
                ]
            },
            customers: {
                name: 'إدارة العملاء',
                permissions: [
                    { id: 'customers_view', name: 'عرض العملاء', description: 'عرض قائمة العملاء والتفاصيل' },
                    { id: 'customers_create', name: 'إضافة عملاء', description: 'إضافة عملاء جدد' },
                    { id: 'customers_edit', name: 'تعديل العملاء', description: 'تعديل بيانات العملاء' },
                    { id: 'customers_delete', name: 'حذف العملاء', description: 'حذف العملاء من النظام' },
                    { id: 'customers_export', name: 'تصدير العملاء', description: 'تصدير بيانات العملاء' }
                ]
            },
            financial: {
                name: 'الإدارة المالية',
                permissions: [
                    { id: 'financial_view', name: 'عرض التقارير المالية', description: 'عرض التقارير والإحصائيات المالية' },
                    { id: 'financial_payments', name: 'إدارة المدفوعات', description: 'إدارة مدفوعات العملاء والموردين' },
                    { id: 'financial_invoices', name: 'إدارة الفواتير', description: 'إنشاء وإدارة الفواتير' },
                    { id: 'financial_commissions', name: 'إدارة العمولات', description: 'حساب وإدارة عمولات المناديب' },
                    { id: 'financial_reports', name: 'التقارير المالية', description: 'إنشاء وعرض التقارير المالية' }
                ]
            },
            users: {
                name: 'إدارة المستخدمين',
                permissions: [
                    { id: 'users_view', name: 'عرض المستخدمين', description: 'عرض قائمة المستخدمين' },
                    { id: 'users_create', name: 'إضافة مستخدمين', description: 'إضافة مستخدمين جدد' },
                    { id: 'users_edit', name: 'تعديل المستخدمين', description: 'تعديل بيانات المستخدمين' },
                    { id: 'users_delete', name: 'حذف المستخدمين', description: 'حذف المستخدمين من النظام' },
                    { id: 'users_permissions', name: 'إدارة الصلاحيات', description: 'تعديل صلاحيات المستخدمين' },
                    { id: 'users_roles', name: 'إدارة الأدوار', description: 'إنشاء وتعديل أدوار المستخدمين' }
                ]
            },
            reports: {
                name: 'التقارير والإحصائيات',
                permissions: [
                    { id: 'reports_view', name: 'عرض التقارير', description: 'عرض جميع التقارير' },
                    { id: 'reports_create', name: 'إنشاء التقارير', description: 'إنشاء تقارير مخصصة' },
                    { id: 'reports_export', name: 'تصدير التقارير', description: 'تصدير التقارير بصيغ مختلفة' },
                    { id: 'reports_analytics', name: 'التحليلات المتقدمة', description: 'الوصول للتحليلات المتقدمة' }
                ]
            },
            system: {
                name: 'إدارة النظام',
                permissions: [
                    { id: 'system_settings', name: 'إعدادات النظام', description: 'تعديل إعدادات النظام العامة' },
                    { id: 'system_backup', name: 'النسخ الاحتياطي', description: 'إنشاء واستعادة النسخ الاحتياطية' },
                    { id: 'system_logs', name: 'سجلات النظام', description: 'عرض سجلات النظام والأخطاء' },
                    { id: 'system_maintenance', name: 'صيانة النظام', description: 'تنفيذ مهام الصيانة' }
                ]
            }
        };

        // الأدوار الافتراضية
        const defaultRoles = [
            {
                id: 'admin',
                name: 'مدير النظام',
                description: 'صلاحيات كاملة لجميع أجزاء النظام',
                priority: 1,
                permissions: Object.values(systemPermissions).flatMap(group => group.permissions.map(p => p.id))
            },
            {
                id: 'manager',
                name: 'مدير',
                description: 'صلاحيات إدارية محدودة',
                priority: 2,
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit', 'customers_export',
                    'financial_view', 'financial_payments', 'financial_invoices', 'financial_reports',
                    'reports_view', 'reports_create', 'reports_export'
                ]
            },
            {
                id: 'distributor',
                name: 'موزع',
                description: 'صلاحيات الموزعين والمناديب',
                priority: 3,
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit',
                    'financial_view', 'financial_payments', 'reports_view'
                ]
            },
            {
                id: 'employee',
                name: 'موظف',
                description: 'صلاحيات أساسية للعمليات اليومية',
                priority: 4,
                permissions: [
                    'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                    'customers_view', 'customers_create', 'customers_edit',
                    'financial_view', 'reports_view'
                ]
            },
            {
                id: 'viewer',
                name: 'مشاهد',
                description: 'صلاحيات عرض فقط',
                priority: 5,
                permissions: [
                    'shipments_view', 'shipments_track',
                    'customers_view',
                    'financial_view',
                    'reports_view'
                ]
            }
        ];

        // المستخدمين الافتراضيين
        const defaultUsers = [
            {
                id: 'user1',
                name: 'أحمد محمد',
                email: '<EMAIL>',
                phone: '+966501234567',
                role: 'admin',
                status: 'active',
                branch: 'main',
                createdDate: '2024-01-01',
                lastLogin: '2024-01-15T10:30:00',
                avatar: 'أ',
                customPermissions: []
            },
            {
                id: 'user2',
                name: 'فاطمة أحمد',
                email: '<EMAIL>',
                phone: '+966507654321',
                role: 'manager',
                status: 'active',
                branch: 'riyadh',
                createdDate: '2024-01-05',
                lastLogin: '2024-01-15T09:15:00',
                avatar: 'ف',
                customPermissions: []
            },
            {
                id: 'user3',
                name: 'محمد علي',
                email: '<EMAIL>',
                phone: '+966509876543',
                role: 'distributor',
                status: 'active',
                branch: 'jeddah',
                createdDate: '2024-01-10',
                lastLogin: '2024-01-14T16:45:00',
                avatar: 'م',
                customPermissions: []
            },
            {
                id: 'user4',
                name: 'سارة خالد',
                email: '<EMAIL>',
                phone: '+966502468135',
                role: 'viewer',
                status: 'active',
                branch: 'dammam',
                createdDate: '2024-01-12',
                lastLogin: '2024-01-13T14:20:00',
                avatar: 'س',
                customPermissions: []
            }
        ];

        // سجل النشاطات
        const activityLog = [
            {
                id: 'act1',
                userId: 'user1',
                userName: 'أحمد محمد',
                action: 'تسجيل دخول',
                details: 'تسجيل دخول ناجح للنظام',
                timestamp: '2024-01-15T10:30:00',
                ipAddress: '*************',
                browser: 'Chrome 120.0'
            },
            {
                id: 'act2',
                userId: 'user2',
                userName: 'فاطمة أحمد',
                action: 'إنشاء شحنة',
                details: 'إنشاء شحنة جديدة رقم SHP001',
                timestamp: '2024-01-15T09:15:00',
                ipAddress: '*************',
                browser: 'Firefox 121.0'
            },
            {
                id: 'act3',
                userId: 'user3',
                userName: 'محمد علي',
                action: 'تعديل عميل',
                details: 'تعديل بيانات العميل أحمد سالم',
                timestamp: '2024-01-14T16:45:00',
                ipAddress: '*************',
                browser: 'Safari 17.0'
            }
        ];

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📍 تحميل صفحة إدارة المستخدمين...');

            // تحميل البيانات الافتراضية
            loadDefaultData();

            // عرض البيانات
            displayUsers();
            displayRoles();
            displayPermissions();
            displayActivity();
            updateStats();

            // إعداد النماذج
            setupForms();

            console.log('✅ تم تحميل صفحة إدارة المستخدمين بنجاح');
        });

        // تحميل البيانات الافتراضية
        function loadDefaultData() {
            // تحميل المستخدمين
            if (!localStorage.getItem('users')) {
                localStorage.setItem('users', JSON.stringify(defaultUsers));
            }
            currentUsers = JSON.parse(localStorage.getItem('users') || '[]');

            // تحميل الأدوار
            if (!localStorage.getItem('roles')) {
                localStorage.setItem('roles', JSON.stringify(defaultRoles));
            }
            currentRoles = JSON.parse(localStorage.getItem('roles') || '[]');

            // تحميل الصلاحيات
            currentPermissions = systemPermissions;

            // تحميل سجل النشاطات
            if (!localStorage.getItem('activityLog')) {
                localStorage.setItem('activityLog', JSON.stringify(activityLog));
            }
        }

        // عرض المستخدمين
        function displayUsers() {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            currentUsers.forEach(user => {
                const role = currentRoles.find(r => r.id === user.role);
                const tr = document.createElement('tr');
                tr.innerHTML =
                    '<td><div class="user-avatar">' + user.avatar + '</div></td>' +
                    '<td>' + user.name + '</td>' +
                    '<td>' + user.email + '</td>' +
                    '<td><span class="role-badge role-' + user.role + '">' + (role ? role.name : user.role) + '</span></td>' +
                    '<td><span class="status-badge status-' + user.status + '">' + getStatusText(user.status) + '</span></td>' +
                    '<td>' + formatDateTime(user.lastLogin) + '</td>' +
                    '<td>' + formatDate(user.createdDate) + '</td>' +
                    '<td><div class="action-buttons">' +
                    '<button class="btn btn-sm btn-primary" onclick="editUser(\'' + user.id + '\')">✏️ تعديل</button>' +
                    '<button class="btn btn-sm btn-info" onclick="editUserPermissions(\'' + user.id + '\')">🔐 الصلاحيات</button>' +
                    '<button class="btn btn-sm btn-warning" onclick="toggleUserStatus(\'' + user.id + '\')">🔄 تغيير الحالة</button>' +
                    '<button class="btn btn-sm btn-danger" onclick="deleteUser(\'' + user.id + '\')">🗑️ حذف</button>' +
                    '</div></td>';
                tbody.appendChild(tr);
            });
        }

        // عرض الأدوار
        function displayRoles() {
            const grid = document.getElementById('rolesGrid');
            grid.innerHTML = '';

            currentRoles.forEach(role => {
                const div = document.createElement('div');
                div.className = 'permission-group';
                div.innerHTML =
                    '<h4>' + role.name + '</h4>' +
                    '<p style="color: #666; margin-bottom: 15px;">' + role.description + '</p>' +
                    '<p><strong>الأولوية:</strong> ' + getPriorityText(role.priority) + '</p>' +
                    '<p><strong>عدد الصلاحيات:</strong> ' + role.permissions.length + '</p>' +
                    '<div style="margin-top: 15px;">' +
                    '<button class="btn btn-sm btn-primary" onclick="editRole(\'' + role.id + '\')">✏️ تعديل</button>' +
                    '<button class="btn btn-sm btn-danger" onclick="deleteRole(\'' + role.id + '\')">🗑️ حذف</button>' +
                    '</div>';
                grid.appendChild(div);
            });
        }

        // عرض الصلاحيات
        function displayPermissions() {
            const grid = document.getElementById('permissionsGrid');
            grid.innerHTML = '';

            Object.keys(currentPermissions).forEach(groupKey => {
                const group = currentPermissions[groupKey];
                const div = document.createElement('div');
                div.className = 'permission-group';

                let permissionsHtml = '';
                group.permissions.forEach(permission => {
                    permissionsHtml +=
                        '<div class="permission-item">' +
                        '<input type="checkbox" class="permission-checkbox" disabled>' +
                        '<div class="permission-label">' +
                        '<strong>' + permission.name + '</strong><br>' +
                        '<small>' + permission.description + '</small>' +
                        '</div>' +
                        '</div>';
                });

                div.innerHTML =
                    '<h4>' + group.name + '</h4>' +
                    permissionsHtml;
                grid.appendChild(div);
            });
        }

        // عرض سجل النشاطات
        function displayActivity() {
            const tbody = document.getElementById('activityTableBody');
            tbody.innerHTML = '';

            const activities = JSON.parse(localStorage.getItem('activityLog') || '[]');
            activities.slice(0, 50).forEach(activity => {
                const tr = document.createElement('tr');
                tr.innerHTML =
                    '<td>' + activity.userName + '</td>' +
                    '<td>' + activity.action + '</td>' +
                    '<td>' + activity.details + '</td>' +
                    '<td>' + formatDateTime(activity.timestamp) + '</td>' +
                    '<td>' + activity.ipAddress + '</td>' +
                    '<td>' + activity.browser + '</td>';
                tbody.appendChild(tr);
            });
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('totalUsers').textContent = currentUsers.length;
            document.getElementById('activeUsers').textContent = currentUsers.filter(u => u.status === 'active').length;
            document.getElementById('adminUsers').textContent = currentUsers.filter(u => u.role === 'admin').length;
            document.getElementById('onlineUsers').textContent = Math.floor(Math.random() * 5) + 1; // محاكاة
        }

        // التبديل بين التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName + 'Tab').classList.add('active');

            // تفعيل الزر المحدد
            event.target.classList.add('active');
        }

        // فتح نافذة إضافة مستخدم
        function openAddUserModal() {
            editingUserId = null;
            document.getElementById('userModalTitle').textContent = 'إضافة مستخدم جديد';
            document.getElementById('userForm').reset();
            loadUserPermissions();
            document.getElementById('userModal').style.display = 'block';
        }

        // فتح نافذة تعديل مستخدم
        function editUser(userId) {
            editingUserId = userId;
            const user = currentUsers.find(u => u.id === userId);
            if (!user) return;

            document.getElementById('userModalTitle').textContent = 'تعديل المستخدم';
            document.getElementById('userName').value = user.name;
            document.getElementById('userEmail').value = user.email;
            document.getElementById('userPhone').value = user.phone || '';
            document.getElementById('userRole').value = user.role;
            document.getElementById('userBranch').value = user.branch || '';
            document.getElementById('userStatus').value = user.status;

            // إخفاء حقول كلمة المرور عند التعديل
            document.getElementById('userPassword').required = false;
            document.getElementById('userPasswordConfirm').required = false;

            loadUserPermissions(user.customPermissions);
            document.getElementById('userModal').style.display = 'block';
        }

        // تعديل صلاحيات المستخدم
        function editUserPermissions(userId) {
            const user = currentUsers.find(u => u.id === userId);
            if (!user) {
                alert('❌ لم يتم العثور على المستخدم');
                return;
            }

            // حفظ معرف المستخدم في التخزين المؤقت للصفحة التالية
            sessionStorage.setItem('editingUserId', userId);

            // الانتقال لصفحة تعديل الصلاحيات المتقدمة
            window.location.href = 'user-permissions-advanced.html?userId=' + userId;
        }

        // تحميل صلاحيات المستخدم
        function loadUserPermissions(userPermissions = []) {
            const container = document.getElementById('userPermissions');
            container.innerHTML = '';

            Object.keys(currentPermissions).forEach(groupKey => {
                const group = currentPermissions[groupKey];
                const div = document.createElement('div');
                div.className = 'permission-group';

                let permissionsHtml = '<h4>' + group.name + '</h4>';
                group.permissions.forEach(permission => {
                    const isChecked = userPermissions.includes(permission.id) ? 'checked' : '';
                    permissionsHtml +=
                        '<div class="permission-item">' +
                        '<input type="checkbox" class="permission-checkbox" id="perm_' + permission.id + '" ' + isChecked + '>' +
                        '<label for="perm_' + permission.id + '" class="permission-label">' +
                        '<strong>' + permission.name + '</strong><br>' +
                        '<small>' + permission.description + '</small>' +
                        '</label>' +
                        '</div>';
                });

                div.innerHTML = permissionsHtml;
                container.appendChild(div);
            });
        }

        // حفظ المستخدم
        function setupForms() {
            document.getElementById('userForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveUser();
            });

            document.getElementById('roleForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveRole();
            });
        }

        function saveUser() {
            const name = document.getElementById('userName').value;
            const email = document.getElementById('userEmail').value;
            const phone = document.getElementById('userPhone').value;
            const password = document.getElementById('userPassword').value;
            const passwordConfirm = document.getElementById('userPasswordConfirm').value;
            const role = document.getElementById('userRole').value;
            const branch = document.getElementById('userBranch').value;
            const status = document.getElementById('userStatus').value;

            // التحقق من كلمة المرور
            if (!editingUserId && password !== passwordConfirm) {
                alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return;
            }

            // جمع الصلاحيات المخصصة
            const customPermissions = [];
            document.querySelectorAll('#userPermissions input[type="checkbox"]:checked').forEach(checkbox => {
                customPermissions.push(checkbox.id.replace('perm_', ''));
            });

            if (editingUserId) {
                // تعديل مستخدم موجود
                const userIndex = currentUsers.findIndex(u => u.id === editingUserId);
                if (userIndex !== -1) {
                    currentUsers[userIndex] = {
                        ...currentUsers[userIndex],
                        name: name,
                        email: email,
                        phone: phone,
                        role: role,
                        branch: branch,
                        status: status,
                        customPermissions: customPermissions
                    };
                }
            } else {
                // إضافة مستخدم جديد
                const newUser = {
                    id: 'user' + Date.now(),
                    name: name,
                    email: email,
                    phone: phone,
                    role: role,
                    status: status,
                    branch: branch,
                    createdDate: new Date().toISOString().split('T')[0],
                    lastLogin: null,
                    avatar: name.charAt(0),
                    customPermissions: customPermissions
                };
                currentUsers.push(newUser);
            }

            // حفظ في التخزين المحلي
            localStorage.setItem('users', JSON.stringify(currentUsers));

            // تحديث العرض
            displayUsers();
            updateStats();
            closeModal('userModal');

            alert(editingUserId ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح');
        }

        // حذف مستخدم
        function deleteUser(userId) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                currentUsers = currentUsers.filter(u => u.id !== userId);
                localStorage.setItem('users', JSON.stringify(currentUsers));
                displayUsers();
                updateStats();
                alert('تم حذف المستخدم بنجاح');
            }
        }

        // تغيير حالة المستخدم
        function toggleUserStatus(userId) {
            const user = currentUsers.find(u => u.id === userId);
            if (!user) return;

            const newStatus = user.status === 'active' ? 'inactive' : 'active';
            user.status = newStatus;

            localStorage.setItem('users', JSON.stringify(currentUsers));
            displayUsers();
            updateStats();

            alert('تم تغيير حالة المستخدم إلى: ' + getStatusText(newStatus));
        }

        // البحث عن المستخدمين
        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const filteredUsers = currentUsers.filter(user =>
                user.name.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.phone.includes(searchTerm)
            );

            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            filteredUsers.forEach(user => {
                const role = currentRoles.find(r => r.id === user.role);
                const tr = document.createElement('tr');
                tr.innerHTML =
                    '<td><div class="user-avatar">' + user.avatar + '</div></td>' +
                    '<td>' + user.name + '</td>' +
                    '<td>' + user.email + '</td>' +
                    '<td><span class="role-badge role-' + user.role + '">' + (role ? role.name : user.role) + '</span></td>' +
                    '<td><span class="status-badge status-' + user.status + '">' + getStatusText(user.status) + '</span></td>' +
                    '<td>' + formatDateTime(user.lastLogin) + '</td>' +
                    '<td>' + formatDate(user.createdDate) + '</td>' +
                    '<td><div class="action-buttons">' +
                    '<button class="btn btn-sm btn-primary" onclick="editUser(\'' + user.id + '\')">✏️ تعديل</button>' +
                    '<button class="btn btn-sm btn-warning" onclick="toggleUserStatus(\'' + user.id + '\')">🔄 تغيير الحالة</button>' +
                    '<button class="btn btn-sm btn-danger" onclick="deleteUser(\'' + user.id + '\')">🗑️ حذف</button>' +
                    '</div></td>';
                tbody.appendChild(tr);
            });
        }

        // تصدير بيانات المستخدمين
        function exportUsers() {
            const csvContent = 'الاسم,البريد الإلكتروني,الهاتف,الدور,الحالة,الفرع,تاريخ الإنشاء,آخر دخول\n' +
                currentUsers.map(user => {
                    const role = currentRoles.find(r => r.id === user.role);
                    return user.name + ',' + user.email + ',' + (user.phone || '') + ',' +
                           (role ? role.name : user.role) + ',' + getStatusText(user.status) + ',' +
                           (user.branch || '') + ',' + user.createdDate + ',' + (user.lastLogin || '');
                }).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'users_' + new Date().toISOString().split('T')[0] + '.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // إدارة الأدوار
        function openAddRoleModal() {
            editingRoleId = null;
            document.getElementById('roleModalTitle').textContent = 'إضافة دور جديد';
            document.getElementById('roleForm').reset();
            loadRolePermissions();
            document.getElementById('roleModal').style.display = 'block';
        }

        function editRole(roleId) {
            editingRoleId = roleId;
            const role = currentRoles.find(r => r.id === roleId);
            if (!role) return;

            document.getElementById('roleModalTitle').textContent = 'تعديل الدور';
            document.getElementById('roleName').value = role.name;
            document.getElementById('roleDescription').value = role.description || '';
            document.getElementById('rolePriority').value = role.priority;

            loadRolePermissions(role.permissions);
            document.getElementById('roleModal').style.display = 'block';
        }

        function loadRolePermissions(rolePermissions = []) {
            const container = document.getElementById('rolePermissions');
            container.innerHTML = '';

            Object.keys(currentPermissions).forEach(groupKey => {
                const group = currentPermissions[groupKey];
                const div = document.createElement('div');
                div.className = 'permission-group';

                let permissionsHtml = '<h4>' + group.name + '</h4>';
                group.permissions.forEach(permission => {
                    const isChecked = rolePermissions.includes(permission.id) ? 'checked' : '';
                    permissionsHtml +=
                        '<div class="permission-item">' +
                        '<input type="checkbox" class="permission-checkbox" id="role_perm_' + permission.id + '" ' + isChecked + '>' +
                        '<label for="role_perm_' + permission.id + '" class="permission-label">' +
                        '<strong>' + permission.name + '</strong><br>' +
                        '<small>' + permission.description + '</small>' +
                        '</label>' +
                        '</div>';
                });

                div.innerHTML = permissionsHtml;
                container.appendChild(div);
            });
        }

        function saveRole() {
            const name = document.getElementById('roleName').value;
            const description = document.getElementById('roleDescription').value;
            const priority = parseInt(document.getElementById('rolePriority').value);

            // جمع الصلاحيات
            const permissions = [];
            document.querySelectorAll('#rolePermissions input[type="checkbox"]:checked').forEach(checkbox => {
                permissions.push(checkbox.id.replace('role_perm_', ''));
            });

            if (editingRoleId) {
                // تعديل دور موجود
                const roleIndex = currentRoles.findIndex(r => r.id === editingRoleId);
                if (roleIndex !== -1) {
                    currentRoles[roleIndex] = {
                        ...currentRoles[roleIndex],
                        name: name,
                        description: description,
                        priority: priority,
                        permissions: permissions
                    };
                }
            } else {
                // إضافة دور جديد
                const newRole = {
                    id: 'role' + Date.now(),
                    name: name,
                    description: description,
                    priority: priority,
                    permissions: permissions
                };
                currentRoles.push(newRole);
            }

            // حفظ في التخزين المحلي
            localStorage.setItem('roles', JSON.stringify(currentRoles));

            // تحديث العرض
            displayRoles();
            closeModal('roleModal');

            alert(editingRoleId ? 'تم تحديث الدور بنجاح' : 'تم إضافة الدور بنجاح');
        }

        function deleteRole(roleId) {
            // التحقق من عدم استخدام الدور
            const usersWithRole = currentUsers.filter(u => u.role === roleId);
            if (usersWithRole.length > 0) {
                alert('لا يمكن حذف هذا الدور لأنه مستخدم من قبل ' + usersWithRole.length + ' مستخدم');
                return;
            }

            if (confirm('هل أنت متأكد من حذف هذا الدور؟')) {
                currentRoles = currentRoles.filter(r => r.id !== roleId);
                localStorage.setItem('roles', JSON.stringify(currentRoles));
                displayRoles();
                alert('تم حذف الدور بنجاح');
            }
        }

        // وظائف مساعدة
        function getStatusText(status) {
            const statusMap = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'معلق'
            };
            return statusMap[status] || status;
        }

        function getPriorityText(priority) {
            const priorityMap = {
                1: 'عالي جداً',
                2: 'عالي',
                3: 'متوسط',
                4: 'منخفض',
                5: 'منخفض جداً'
            };
            return priorityMap[priority] || 'غير محدد';
        }

        function formatDate(dateString) {
            if (!dateString) return 'غير محدد';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        function formatDateTime(dateString) {
            if (!dateString) return 'لم يسجل دخول';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // فتح مصفوفة الصلاحيات
        function openPermissionsMatrix() {
            window.open('permissions-matrix.html', '_blank');
        }

        // إعادة تعيين الصلاحيات
        function resetPermissions() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الصلاحيات للإعدادات الافتراضية؟')) {
                localStorage.setItem('roles', JSON.stringify(defaultRoles));
                currentRoles = [...defaultRoles];
                displayRoles();
                alert('تم إعادة تعيين الصلاحيات بنجاح');
            }
        }

        // تحديث سجل النشاطات
        function refreshActivity() {
            displayActivity();
            alert('تم تحديث سجل النشاطات');
        }

        function clearActivity() {
            if (confirm('هل أنت متأكد من مسح جميع سجلات النشاطات؟')) {
                localStorage.setItem('activityLog', JSON.stringify([]));
                displayActivity();
                alert('تم مسح سجل النشاطات');
            }
        }

        // إضافة نشاط جديد للسجل
        function addActivity(userId, action, details) {
            const user = currentUsers.find(u => u.id === userId);
            if (!user) return;

            const activity = {
                id: 'act' + Date.now(),
                userId: userId,
                userName: user.name,
                action: action,
                details: details,
                timestamp: new Date().toISOString(),
                ipAddress: '192.168.1.' + Math.floor(Math.random() * 255),
                browser: 'Chrome ' + Math.floor(Math.random() * 10 + 115) + '.0'
            };

            const activities = JSON.parse(localStorage.getItem('activityLog') || '[]');
            activities.unshift(activity);

            // الاحتفاظ بآخر 100 نشاط فقط
            if (activities.length > 100) {
                activities.splice(100);
            }

            localStorage.setItem('activityLog', JSON.stringify(activities));
        }

        // إغلاق النوافذ المنبثقة عند النقر خارجها
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>

    <style>
        /* تطبيق خط SF Pro Arabic Display على جميع العناصر */
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, select, textarea, button, .btn {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }

        .nav a, .nav-links a {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }
    </style>

    <!-- تطبيق خط SF Pro Arabic Display Semibold -->
    <script src="js/sf-pro-arabic-font.js"></script>
</body>
</html>
