<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم العميل | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            font-weight: 600;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .dashboard-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 100vh;
        }

        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            height: 100vh;
            width: 300px;
            overflow-y: auto;
            box-shadow: 4px 0 20px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        .sidebar-header {
            padding: 30px 25px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            background: rgba(255,255,255,0.1);
        }

        .logo {
            width: 70px;
            height: 70px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            backdrop-filter: blur(10px);
        }

        .company-name {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .company-subtitle {
            font-size: 0.95rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .nav-menu {
            padding: 25px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 18px;
            padding: 18px 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            margin: 2px 0;
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.15);
            border-left-color: rgba(255,255,255,0.7);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: rgba(255,255,255,0.25);
            border-left-color: white;
            font-weight: 600;
        }

        .nav-item i {
            font-size: 1.3rem;
            width: 24px;
            text-align: center;
        }

        .main-content {
            margin-right: 300px;
            padding: 40px;
            background: transparent;
            min-height: 100vh;
        }

        .header {
            background: white;
            padding: 25px 35px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 35px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .header h1 {
            color: #333;
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 18px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .user-details h3 {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 2px;
        }

        .user-details p {
            color: #666;
            font-size: 0.9rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 1.8rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #333;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .content-section {
            background: white;
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 35px;
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f2f5;
        }

        .section-title {
            font-size: 1.6rem;
            color: #333;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-title i {
            color: #667eea;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header {
                padding: 20px;
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .section-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    🚚
                </div>
                <div class="company-name">شركة الشحن السريع</div>
                <div class="company-subtitle">لوحة تحكم العميل</div>
            </div>
            
            <nav class="nav-menu">
                <a href="#" class="nav-item active" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('new-shipment')">
                    <i class="fas fa-plus-circle"></i>
                    <span>إنشاء شحنة جديدة</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('track-shipment')">
                    <i class="fas fa-search"></i>
                    <span>تتبع الشحنات</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('my-shipments')">
                    <i class="fas fa-box"></i>
                    <span>شحناتي</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('profile')">
                    <i class="fas fa-user"></i>
                    <span>الملف الشخصي</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('support')">
                    <i class="fas fa-headset"></i>
                    <span>الدعم الفني</span>
                </a>
                <a href="#" class="nav-item" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </nav>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- الرأس -->
            <div class="header">
                <h1 id="page-title">لوحة التحكم الرئيسية</h1>
                <div class="user-info">
                    <div class="user-details">
                        <h3 id="user-name">أحمد محمد</h3>
                        <p id="user-email"><EMAIL></p>
                    </div>
                    <div class="user-avatar" id="user-avatar">أ</div>
                </div>
            </div>

            <!-- قسم لوحة التحكم الرئيسية -->
            <div id="dashboard-section" class="section">
                <!-- الإحصائيات -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-number" id="total-shipments">12</div>
                        <div class="stat-label">إجمالي الشحنات</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number" id="delivered-shipments">8</div>
                        <div class="stat-label">شحنات مسلمة</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number" id="pending-shipments">3</div>
                        <div class="stat-label">شحنات قيد التنفيذ</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-number" id="total-cost">1,250</div>
                        <div class="stat-label">إجمالي التكلفة (ريال)</div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-bolt"></i>
                            الإجراءات السريعة
                        </h2>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <button class="btn btn-primary" onclick="showSection('new-shipment')">
                            <i class="fas fa-plus"></i>
                            إنشاء شحنة جديدة
                        </button>
                        <button class="btn btn-success" onclick="showSection('track-shipment')">
                            <i class="fas fa-search"></i>
                            تتبع شحنة
                        </button>
                    </div>
                </div>
            </div>

            <!-- قسم إنشاء شحنة جديدة -->
            <div id="new-shipment-section" class="section hidden">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-plus-circle"></i>
                            إنشاء شحنة جديدة
                        </h2>
                    </div>

                    <form id="shipment-form">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-bottom: 30px;">
                            <!-- بيانات المرسل -->
                            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px;">
                                <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                                    <i class="fas fa-user" style="color: #667eea;"></i>
                                    بيانات المرسل
                                </h3>
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">الاسم الكامل</label>
                                    <input type="text" id="sender-name" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" placeholder="أدخل اسم المرسل" required>
                                </div>
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">رقم الهاتف</label>
                                    <input type="tel" id="sender-phone" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" placeholder="05xxxxxxxx" required>
                                </div>
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">المدينة</label>
                                    <select id="sender-city" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" required>
                                        <option value="">اختر المدينة</option>
                                        <option value="riyadh">الرياض</option>
                                        <option value="jeddah">جدة</option>
                                        <option value="dammam">الدمام</option>
                                        <option value="mecca">مكة المكرمة</option>
                                        <option value="medina">المدينة المنورة</option>
                                        <option value="kuwait-city">مدينة الكويت</option>
                                        <option value="hawalli">حولي</option>
                                        <option value="farwaniya">الفروانية</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">العنوان التفصيلي</label>
                                    <textarea id="sender-address" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; min-height: 80px;" placeholder="أدخل العنوان التفصيلي" required></textarea>
                                </div>
                            </div>

                            <!-- بيانات المستقبل -->
                            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px;">
                                <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                                    <i class="fas fa-user-check" style="color: #28a745;"></i>
                                    بيانات المستقبل
                                </h3>
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">الاسم الكامل</label>
                                    <input type="text" id="receiver-name" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" placeholder="أدخل اسم المستقبل" required>
                                </div>
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">رقم الهاتف</label>
                                    <input type="tel" id="receiver-phone" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" placeholder="05xxxxxxxx" required>
                                </div>
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">المدينة</label>
                                    <select id="receiver-city" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" required>
                                        <option value="">اختر المدينة</option>
                                        <option value="riyadh">الرياض</option>
                                        <option value="jeddah">جدة</option>
                                        <option value="dammam">الدمام</option>
                                        <option value="mecca">مكة المكرمة</option>
                                        <option value="medina">المدينة المنورة</option>
                                        <option value="kuwait-city">مدينة الكويت</option>
                                        <option value="hawalli">حولي</option>
                                        <option value="farwaniya">الفروانية</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">العنوان التفصيلي</label>
                                    <textarea id="receiver-address" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; min-height: 80px;" placeholder="أدخل العنوان التفصيلي" required></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل الشحنة -->
                        <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                                <i class="fas fa-box" style="color: #ffc107;"></i>
                                تفاصيل الشحنة
                            </h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                                <div>
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">نوع الشحنة</label>
                                    <select id="shipment-type" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" required>
                                        <option value="">اختر نوع الشحنة</option>
                                        <option value="documents">مستندات</option>
                                        <option value="packages">طرود</option>
                                        <option value="electronics">إلكترونيات</option>
                                        <option value="clothes">ملابس</option>
                                        <option value="food">مواد غذائية</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">الوزن (كيلو)</label>
                                    <input type="number" id="shipment-weight" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" placeholder="0.5" step="0.1" min="0.1" required>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">القيمة المعلنة (ريال)</label>
                                    <input type="number" id="shipment-value" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" placeholder="100" min="0">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">طريقة الدفع</label>
                                    <select id="payment-method" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" required>
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="prepaid">مدفوع مسبقاً</option>
                                        <option value="cod">الدفع عند الاستلام</option>
                                    </select>
                                </div>
                            </div>
                            <div style="margin-top: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">وصف المحتويات</label>
                                <textarea id="shipment-description" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; min-height: 80px;" placeholder="وصف تفصيلي لمحتويات الشحنة"></textarea>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div style="display: flex; gap: 15px; justify-content: center;">
                            <button type="submit" class="btn btn-primary" style="min-width: 200px;">
                                <i class="fas fa-paper-plane"></i>
                                إنشاء الشحنة
                            </button>
                            <button type="button" class="btn" style="background: #6c757d; color: white;" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قسم تتبع الشحنات -->
            <div id="track-shipment-section" class="section hidden">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-search"></i>
                            تتبع الشحنات
                        </h2>
                    </div>

                    <div style="max-width: 600px; margin: 0 auto;">
                        <div style="margin-bottom: 30px;">
                            <label style="display: block; margin-bottom: 12px; font-weight: 600; color: #333; font-size: 1.1rem;">رقم الشحنة</label>
                            <div style="display: flex; gap: 15px;">
                                <input type="text" id="tracking-number" style="flex: 1; padding: 15px; border: 2px solid #e9ecef; border-radius: 12px; font-size: 1.1rem;" placeholder="أدخل رقم الشحنة (مثال: SH123456)">
                                <button class="btn btn-primary" onclick="trackShipment()" style="min-width: 120px;">
                                    <i class="fas fa-search"></i>
                                    تتبع
                                </button>
                            </div>
                        </div>

                        <!-- نتائج التتبع -->
                        <div id="tracking-results" style="display: none;">
                            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 25px;">
                                <h3 style="color: #333; margin-bottom: 15px;">معلومات الشحنة</h3>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                    <div>
                                        <strong>رقم الشحنة:</strong>
                                        <span id="track-shipment-id">SH123456</span>
                                    </div>
                                    <div>
                                        <strong>الحالة:</strong>
                                        <span id="track-status" class="status-badge status-transit">قيد النقل</span>
                                    </div>
                                    <div>
                                        <strong>تاريخ الإنشاء:</strong>
                                        <span id="track-date">2024-01-15</span>
                                    </div>
                                    <div>
                                        <strong>المدينة الحالية:</strong>
                                        <span id="track-location">الرياض</span>
                                    </div>
                                </div>
                            </div>

                            <!-- مراحل التتبع -->
                            <div style="background: white; padding: 25px; border-radius: 15px; border: 2px solid #e9ecef;">
                                <h3 style="color: #333; margin-bottom: 20px;">مراحل الشحنة</h3>
                                <div id="tracking-timeline">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تطبيق خط SF Pro Arabic Display على جميع العناصر -->
    <style>
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, textarea, select, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }
    </style>
</body>
</html>
