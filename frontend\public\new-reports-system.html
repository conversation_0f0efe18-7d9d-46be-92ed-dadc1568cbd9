<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات الجديدة</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: #17a2b8;
            color: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-teal { background: #20c997; color: white; }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .report-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            text-align: center;
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #17a2b8;
        }

        .report-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #17a2b8;
        }

        .report-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .report-description {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .report-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .chart-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        .chart-placeholder {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.2rem;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            border-color: #17a2b8;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #17a2b8;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .back-btn {
                justify-content: center;
                width: 100%;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tabs-nav {
                flex-direction: column;
            }

            .tab-button {
                flex-direction: row;
                justify-content: center;
            }

            .reports-grid {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .report-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                    <span>🏠</span>
                    <span>العودة للوحة التحكم</span>
                    <span>←</span>
                </a>
                <div class="header-actions">
                    <button class="btn btn-info btn-sm" onclick="window.location.reload()">
                        <span>🔄</span> تحديث الصفحة
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportAllReports()">
                        <span>📥</span> تصدير جميع التقارير
                    </button>
                </div>
            </div>
            <h1>📊 التقارير والإحصائيات الجديدة</h1>
            <p>نظام متطور لإنشاء وإدارة التقارير والإحصائيات التفصيلية</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navigation Tabs -->
            <div class="tabs-nav">
                <button class="tab-button active" onclick="showTab('overview')">
                    <span>📊</span>
                    <span>نظرة عامة</span>
                </button>
                <button class="tab-button" onclick="showTab('sales-reports')">
                    <span>💰</span>
                    <span>تقارير المبيعات</span>
                </button>
                <button class="tab-button" onclick="showTab('performance')">
                    <span>📈</span>
                    <span>تقارير الأداء</span>
                </button>
                <button class="tab-button" onclick="showTab('analytics')">
                    <span>🔍</span>
                    <span>التحليلات المتقدمة</span>
                </button>
            </div>

            <!-- Tab Contents -->

            <!-- نظرة عامة -->
            <div id="overview" class="tab-content active">
                <div class="section-header">
                    <h2 class="section-title">📊 نظرة عامة على التقارير</h2>
                    <div class="section-actions">
                        <button class="btn btn-teal" onclick="generateDashboard()">
                            <span>📊</span> لوحة معلومات
                        </button>
                        <button class="btn btn-info" onclick="refreshOverview()">
                            <span>🔄</span> تحديث
                        </button>
                    </div>
                </div>

                <div class="stats-overview">
                    <div class="stat-card">
                        <div class="stat-number" id="totalReports">0</div>
                        <div class="stat-label">إجمالي التقارير</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="monthlyReports">0</div>
                        <div class="stat-label">تقارير هذا الشهر</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="automatedReports">0</div>
                        <div class="stat-label">التقارير التلقائية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="scheduledReports">0</div>
                        <div class="stat-label">التقارير المجدولة</div>
                    </div>
                </div>

                <div class="chart-container">
                    <h3>📈 الرسم البياني للتقارير</h3>
                    <div class="chart-placeholder">
                        📊 سيتم عرض الرسم البياني لإحصائيات التقارير هنا
                    </div>
                </div>

                <div class="reports-grid">
                    <div class="report-card">
                        <div class="report-icon">📊</div>
                        <div class="report-title">تقرير المبيعات اليومي</div>
                        <div class="report-description">تقرير شامل للمبيعات والإيرادات اليومية</div>
                        <div class="report-actions">
                            <button class="btn btn-primary btn-sm" onclick="generateDailySales()">إنشاء</button>
                            <button class="btn btn-info btn-sm" onclick="viewReport('daily-sales')">عرض</button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">📈</div>
                        <div class="report-title">تقرير الأداء الشهري</div>
                        <div class="report-description">تحليل شامل لأداء الشركة خلال الشهر</div>
                        <div class="report-actions">
                            <button class="btn btn-primary btn-sm" onclick="generateMonthlyPerformance()">إنشاء</button>
                            <button class="btn btn-info btn-sm" onclick="viewReport('monthly-performance')">عرض</button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">👥</div>
                        <div class="report-title">تقرير العملاء</div>
                        <div class="report-description">إحصائيات وتحليلات قاعدة العملاء</div>
                        <div class="report-actions">
                            <button class="btn btn-primary btn-sm" onclick="generateCustomerReport()">إنشاء</button>
                            <button class="btn btn-info btn-sm" onclick="viewReport('customers')">عرض</button>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">🚚</div>
                        <div class="report-title">تقرير التوصيل</div>
                        <div class="report-description">إحصائيات التوصيل والمناديب</div>
                        <div class="report-actions">
                            <button class="btn btn-primary btn-sm" onclick="generateDeliveryReport()">إنشاء</button>
                            <button class="btn btn-info btn-sm" onclick="viewReport('delivery')">عرض</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقارير المبيعات -->
            <div id="sales-reports" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">💰 تقارير المبيعات</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="createSalesReport()">
                            <span>➕</span> إنشاء تقرير
                        </button>
                        <button class="btn btn-warning" onclick="scheduleSalesReport()">
                            <span>⏰</span> جدولة تقرير
                        </button>
                        <button class="btn btn-success" onclick="exportSalesReports()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>

                <div id="salesReportsGrid" class="reports-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">💰</div>
                        <h3>لا توجد تقارير مبيعات</h3>
                        <p>ابدأ بإنشاء تقرير مبيعات جديد</p>
                        <button class="btn btn-primary" onclick="createSalesReport()">➕ إنشاء تقرير</button>
                    </div>
                </div>
            </div>

            <!-- تقارير الأداء -->
            <div id="performance" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">📈 تقارير الأداء</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="createPerformanceReport()">
                            <span>➕</span> إنشاء تقرير
                        </button>
                        <button class="btn btn-info" onclick="comparePerformance()">
                            <span>⚖️</span> مقارنة الأداء
                        </button>
                        <button class="btn btn-success" onclick="exportPerformanceReports()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>

                <div id="performanceReportsGrid" class="reports-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">📈</div>
                        <h3>لا توجد تقارير أداء</h3>
                        <p>ابدأ بإنشاء تقرير أداء جديد</p>
                        <button class="btn btn-primary" onclick="createPerformanceReport()">➕ إنشاء تقرير</button>
                    </div>
                </div>
            </div>

            <!-- التحليلات المتقدمة -->
            <div id="analytics" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">🔍 التحليلات المتقدمة</h2>
                    <div class="section-actions">
                        <button class="btn btn-teal" onclick="runAdvancedAnalytics()">
                            <span>🔍</span> تشغيل التحليلات
                        </button>
                        <button class="btn btn-warning" onclick="predictivAnalytics()">
                            <span>🔮</span> التحليلات التنبؤية
                        </button>
                        <button class="btn btn-success" onclick="exportAnalytics()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>

                <div id="analyticsGrid" class="reports-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">🔍</div>
                        <h3>لا توجد تحليلات متقدمة</h3>
                        <p>ابدأ بتشغيل التحليلات المتقدمة</p>
                        <button class="btn btn-teal" onclick="runAdvancedAnalytics()">🔍 تشغيل التحليلات</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // متغيرات عامة
        let reportsData = {
            totalReports: 0,
            monthlyReports: 0,
            automatedReports: 0,
            scheduledReports: 0,
            reports: []
        };

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 تحميل نظام التقارير والإحصائيات الجديدة...');
            initializeReportsSystem();
        });

        // تهيئة النظام
        function initializeReportsSystem() {
            loadDefaultReportsData();
            loadReportsData();
            updateReportsStatistics();
            console.log('✅ تم تحميل نظام التقارير بنجاح');
        }

        // تحميل البيانات الافتراضية
        function loadDefaultReportsData() {
            if (!localStorage.getItem('new_reports_data')) {
                const defaultData = {
                    totalReports: 25,
                    monthlyReports: 8,
                    automatedReports: 12,
                    scheduledReports: 5,
                    reports: [
                        {
                            id: 'RPT001',
                            name: 'تقرير المبيعات اليومي',
                            type: 'مبيعات',
                            status: 'مكتمل',
                            createdAt: new Date().toISOString(),
                            data: { revenue: 15000, orders: 45 }
                        },
                        {
                            id: 'RPT002',
                            name: 'تقرير الأداء الشهري',
                            type: 'أداء',
                            status: 'قيد المعالجة',
                            createdAt: new Date().toISOString(),
                            data: { efficiency: 85, satisfaction: 92 }
                        }
                    ]
                };
                localStorage.setItem('new_reports_data', JSON.stringify(defaultData));
                console.log('✅ تم إنشاء بيانات التقارير الافتراضية');
            }
        }

        // تحديث إحصائيات التقارير
        function updateReportsStatistics() {
            const totalReportsEl = document.getElementById('totalReports');
            const monthlyReportsEl = document.getElementById('monthlyReports');
            const automatedReportsEl = document.getElementById('automatedReports');
            const scheduledReportsEl = document.getElementById('scheduledReports');

            if (totalReportsEl) totalReportsEl.textContent = reportsData.totalReports;
            if (monthlyReportsEl) monthlyReportsEl.textContent = reportsData.monthlyReports;
            if (automatedReportsEl) automatedReportsEl.textContent = reportsData.automatedReports;
            if (scheduledReportsEl) scheduledReportsEl.textContent = reportsData.scheduledReports;
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = event.target.closest('.tab-button');
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            console.log(`📋 تم التبديل إلى تبويب: ${tabName}`);
        }

        // وظائف النظرة العامة
        function generateDashboard() {
            alert('📊 سيتم إنشاء لوحة معلومات شاملة...');
            console.log('📊 إنشاء لوحة معلومات');
        }

        function refreshOverview() {
            loadReportsData();
            updateReportsStatistics();
            alert('✅ تم تحديث نظرة عامة التقارير!');
        }

        function generateDailySales() {
            alert('📊 سيتم إنشاء تقرير المبيعات اليومي...');
            console.log('📊 تقرير المبيعات اليومي');
        }

        function generateMonthlyPerformance() {
            alert('📈 سيتم إنشاء تقرير الأداء الشهري...');
            console.log('📈 تقرير الأداء الشهري');
        }

        function generateCustomerReport() {
            alert('👥 سيتم إنشاء تقرير العملاء...');
            console.log('👥 تقرير العملاء');
        }

        function generateDeliveryReport() {
            alert('🚚 سيتم إنشاء تقرير التوصيل...');
            console.log('🚚 تقرير التوصيل');
        }

        function viewReport(reportType) {
            alert(`👁️ سيتم عرض تقرير: ${reportType}`);
            console.log(`👁️ عرض تقرير: ${reportType}`);
        }

        // وظائف تقارير المبيعات
        function createSalesReport() {
            alert('💰 سيتم إنشاء تقرير مبيعات جديد...');
            console.log('➕ إنشاء تقرير مبيعات');
        }

        function scheduleSalesReport() {
            alert('⏰ سيتم جدولة تقرير مبيعات...');
            console.log('⏰ جدولة تقرير مبيعات');
        }

        function exportSalesReports() {
            alert('📤 تصدير تقارير المبيعات...');
            console.log('📤 تصدير تقارير المبيعات');
        }

        // وظائف تقارير الأداء
        function createPerformanceReport() {
            alert('📈 سيتم إنشاء تقرير أداء جديد...');
            console.log('➕ إنشاء تقرير أداء');
        }

        function comparePerformance() {
            alert('⚖️ سيتم مقارنة الأداء...');
            console.log('⚖️ مقارنة الأداء');
        }

        function exportPerformanceReports() {
            alert('📤 تصدير تقارير الأداء...');
            console.log('📤 تصدير تقارير الأداء');
        }

        // وظائف التحليلات المتقدمة
        function runAdvancedAnalytics() {
            alert('🔍 سيتم تشغيل التحليلات المتقدمة...');
            console.log('🔍 تشغيل التحليلات المتقدمة');
        }

        function predictivAnalytics() {
            alert('🔮 سيتم تشغيل التحليلات التنبؤية...');
            console.log('🔮 التحليلات التنبؤية');
        }

        function exportAnalytics() {
            alert('📤 تصدير التحليلات...');
            console.log('📤 تصدير التحليلات');
        }

        function exportAllReports() {
            const dataStr = JSON.stringify(reportsData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `reports_data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('✅ تم تصدير جميع التقارير بنجاح!');
            console.log('✅ تم تصدير جميع التقارير');
        }

        function loadReportsData() {
            try {
                reportsData = JSON.parse(localStorage.getItem('new_reports_data') || '{}');
                console.log('📊 تم تحميل بيانات التقارير');
            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات التقارير:', error);
                reportsData = { totalReports: 0, monthlyReports: 0, automatedReports: 0, scheduledReports: 0, reports: [] };
            }
        }

        // جعل الوظائف متاحة عالمياً
        window.generateDashboard = generateDashboard;
        window.refreshOverview = refreshOverview;
        window.generateDailySales = generateDailySales;
        window.generateMonthlyPerformance = generateMonthlyPerformance;
        window.generateCustomerReport = generateCustomerReport;
        window.generateDeliveryReport = generateDeliveryReport;
        window.viewReport = viewReport;
        window.createSalesReport = createSalesReport;
        window.scheduleSalesReport = scheduleSalesReport;
        window.exportSalesReports = exportSalesReports;
        window.createPerformanceReport = createPerformanceReport;
        window.comparePerformance = comparePerformance;
        window.exportPerformanceReports = exportPerformanceReports;
        window.runAdvancedAnalytics = runAdvancedAnalytics;
        window.predictivAnalytics = predictivAnalytics;
        window.exportAnalytics = exportAnalytics;
        window.exportAllReports = exportAllReports;
        window.showTab = showTab;

        console.log('📊 تم تحميل نظام التقارير والإحصائيات الجديدة بنجاح!');
    </script>
</body>
</html>