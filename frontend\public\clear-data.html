<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف البيانات - نظام إدارة الشحنات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --font-arabic: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            line-height: 1.6;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: var(--warning-color);
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 2rem;
        }

        .description {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .data-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: right;
        }

        .data-list h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .data-name {
            font-weight: 600;
        }

        .data-size {
            color: #666;
            font-size: 0.9rem;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-arabic);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .warning-box {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .warning-box h4 {
            margin-bottom: 10px;
            color: var(--warning-color);
        }

        @media (max-width: 480px) {
            .container {
                margin: 20px;
                padding: 30px 20px;
            }

            .buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🧹</div>
        <h1>تنظيف بيانات النظام</h1>
        
        <div class="description">
            هذه الأداة تساعدك في حل مشاكل البيانات المحفوظة في المتصفح وإعادة تعيين النظام للحالة الافتراضية.
        </div>

        <div class="warning-box">
            <h4>⚠️ تحذير مهم</h4>
            <p>سيؤدي تنظيف البيانات إلى حذف جميع الشحنات والعملاء والمستخدمين المضافين وإعادة النظام للحالة الافتراضية.</p>
        </div>

        <div class="data-list">
            <h3>البيانات المحفوظة حالياً:</h3>
            <div class="data-item">
                <span class="data-name">الشحنات</span>
                <span class="data-size" id="shipmentsSize">-</span>
            </div>
            <div class="data-item">
                <span class="data-name">العملاء</span>
                <span class="data-size" id="customersSize">-</span>
            </div>
            <div class="data-item">
                <span class="data-name">المستخدمين</span>
                <span class="data-size" id="usersSize">-</span>
            </div>
            <div class="data-item">
                <span class="data-name">سجل النشاطات</span>
                <span class="data-size" id="activitySize">-</span>
            </div>
            <div class="data-item">
                <span class="data-name">الجلسة الحالية</span>
                <span class="data-size" id="sessionSize">-</span>
            </div>
        </div>

        <div class="buttons">
            <button class="btn btn-danger" onclick="clearAllData()">
                🗑️ مسح جميع البيانات
            </button>
            <button class="btn btn-warning" onclick="clearUserData()">
                👥 مسح بيانات المستخدمين فقط
            </button>
            <button class="btn btn-secondary" onclick="goBack()">
                ↩️ العودة للنظام
            </button>
        </div>

        <div id="status" class="status"></div>
    </div>

    <script>
        // تحديث أحجام البيانات
        function updateDataSizes() {
            try {
                const shipments = localStorage.getItem('shipments');
                const customers = localStorage.getItem('customers');
                const users = localStorage.getItem('users');
                const activities = localStorage.getItem('activityLog');
                const session = localStorage.getItem('currentUser');

                document.getElementById('shipmentsSize').textContent = 
                    shipments ? JSON.parse(shipments).length + ' شحنة' : 'لا توجد بيانات';
                
                document.getElementById('customersSize').textContent = 
                    customers ? JSON.parse(customers).length + ' عميل' : 'لا توجد بيانات';
                
                document.getElementById('usersSize').textContent = 
                    users ? JSON.parse(users).length + ' مستخدم' : 'لا توجد بيانات';
                
                document.getElementById('activitySize').textContent = 
                    activities ? JSON.parse(activities).length + ' نشاط' : 'لا توجد بيانات';
                
                document.getElementById('sessionSize').textContent = 
                    session ? 'جلسة نشطة' : 'لا توجد جلسة';

            } catch (error) {
                console.error('خطأ في قراءة البيانات:', error);
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء.')) {
                try {
                    // مسح جميع البيانات
                    const keysToRemove = [
                        'shipments', 'customers', 'distributors', 'users', 'roles',
                        'activityLog', 'currentUser', 'sessionStart', 'rememberUser',
                        'pricing', 'branches', 'settings'
                    ];

                    keysToRemove.forEach(key => {
                        localStorage.removeItem(key);
                    });

                    showStatus('✅ تم مسح جميع البيانات بنجاح! سيتم إعادة تحميل الصفحة...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);

                } catch (error) {
                    showStatus('❌ حدث خطأ أثناء مسح البيانات: ' + error.message, 'error');
                }
            }
        }

        // مسح بيانات المستخدمين فقط
        function clearUserData() {
            if (confirm('هل أنت متأكد من مسح بيانات المستخدمين والجلسات؟')) {
                try {
                    localStorage.removeItem('users');
                    localStorage.removeItem('roles');
                    localStorage.removeItem('activityLog');
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('sessionStart');
                    localStorage.removeItem('rememberUser');

                    showStatus('✅ تم مسح بيانات المستخدمين بنجاح!', 'success');
                    updateDataSizes();

                } catch (error) {
                    showStatus('❌ حدث خطأ أثناء مسح بيانات المستخدمين: ' + error.message, 'error');
                }
            }
        }

        // العودة للنظام
        function goBack() {
            window.location.href = 'login.html';
        }

        // إظهار رسالة الحالة
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';

            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDataSizes();
        });
    </script>
</body>
</html>
