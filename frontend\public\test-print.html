<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة بوليصة الشحن</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: var(--font-arabic-display);
            background: var(--bg-secondary);
            padding: var(--space-6);
        }

        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
        }

        .test-section {
            margin-bottom: var(--space-6);
            padding: var(--space-4);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
        }

        .shipment-card {
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-3);
            border: 1px solid var(--border-light);
        }

        .shipment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-3);
        }

        .tracking-number {
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
            font-size: 1.2rem;
        }

        .shipment-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-3);
            margin-bottom: var(--space-3);
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: var(--space-1);
        }

        .detail-label {
            font-weight: var(--font-weight-semibold);
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .detail-value {
            color: var(--text-primary);
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: var(--space-3) var(--space-5);
            border: none;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: var(--font-weight-medium);
            margin: var(--space-2);
            transition: all var(--transition-fast);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-warning {
            background: var(--color-warning);
        }

        .btn-success {
            background: var(--color-secondary);
        }

        .btn-info {
            background: #17a2b8;
        }

        .status-badge {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-md);
            font-size: 0.85rem;
            font-weight: var(--font-weight-medium);
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .status-transit {
            background: rgba(0, 123, 255, 0.2);
            color: #004085;
        }

        .status-delivered {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .status-cancelled {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .back-link {
            position: fixed;
            top: var(--space-4);
            right: var(--space-4);
            background: var(--color-primary);
            color: white;
            padding: var(--space-3) var(--space-5);
            border-radius: var(--radius-lg);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
        }

        .preview-section {
            background: #f8f9fa;
            border: 2px dashed #007bff;
            border-radius: var(--radius-lg);
            padding: var(--space-5);
            text-align: center;
            margin-top: var(--space-4);
        }

        .sample-label {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border: 2px solid #007bff;
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            box-shadow: var(--shadow-md);
            font-size: 0.9rem;
        }

        .sample-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-3);
            padding-bottom: var(--space-2);
            border-bottom: 1px solid #007bff;
        }

        .sample-logo {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .sample-logo-icon {
            width: 40px;
            height: 40px;
            background: #007bff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .sample-qr {
            width: 60px;
            height: 60px;
            background: #e9ecef;
            border: 1px solid #007bff;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <a href="shipments.html" class="back-link">← العودة للشحنات</a>
    
    <div class="test-container">
        <h1>🖨️ اختبار طباعة بوليصة الشحن</h1>
        
        <div class="test-section">
            <h2>📦 الشحنات المتاحة للطباعة</h2>
            <div id="shipmentsList"></div>
        </div>

        <div class="test-section">
            <h2>🎨 معاينة تصميم البوليصة</h2>
            <div class="preview-section">
                <h3>نموذج مصغر لبوليصة الشحن</h3>
                <div class="sample-label">
                    <div class="sample-header">
                        <div class="sample-logo">
                            <div class="sample-logo-icon">🚚</div>
                            <div>
                                <div style="font-weight: bold; color: #007bff;">شركة الشحن السريع</div>
                                <div style="font-size: 0.8rem; color: #6c757d;">خدمات الشحن والتوصيل</div>
                            </div>
                        </div>
                        <div class="sample-qr">QR Code</div>
                    </div>
                    
                    <div style="background: #007bff; color: white; padding: 10px; border-radius: 5px; margin: 10px 0;">
                        <div style="font-size: 0.8rem;">رقم التتبع</div>
                        <div style="font-size: 1.2rem; font-weight: bold;">TRK001234567</div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 10px 0; font-size: 0.8rem;">
                        <div style="background: #f8f9fa; padding: 8px; border-radius: 5px;">
                            <div style="font-weight: bold; color: #007bff;">📤 المرسل</div>
                            <div>أحمد محمد</div>
                            <div>+966501234567</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 8px; border-radius: 5px;">
                            <div style="font-weight: bold; color: #007bff;">📥 المستقبل</div>
                            <div>سارة أحمد</div>
                            <div>+966507654321</div>
                        </div>
                    </div>
                    
                    <div style="font-size: 0.7rem; color: #6c757d; margin-top: 10px; padding-top: 8px; border-top: 1px solid #e9ecef;">
                        تاريخ الطباعة: 2024-01-15 - 14:30
                    </div>
                </div>
                
                <p style="margin-top: var(--space-3); color: var(--text-secondary);">
                    هذا نموذج مصغر. البوليصة الفعلية ستكون أكبر وأكثر تفصيلاً مع QR Code حقيقي.
                </p>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 أدوات الاختبار</h2>
            <div style="display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;">
                <button class="btn btn-success" onclick="createTestShipment()">
                    ➕ إنشاء شحنة تجريبية
                </button>
                <button class="btn btn-warning" onclick="printAllShipments()">
                    🖨️ طباعة جميع الشحنات
                </button>
                <button class="btn btn-info" onclick="showPrintPreview()">
                    👁️ معاينة الطباعة
                </button>
                <a href="shipments.html" class="btn">
                    📦 إدارة الشحنات
                </a>
            </div>
        </div>
    </div>

    <script src="js/database-simple.js"></script>
    <script>
        // تحميل الشحنات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل صفحة اختبار الطباعة...');
            loadShipments();
        });

        function loadShipments() {
            try {
                const shipments = db.getAllShipments();
                const container = document.getElementById('shipmentsList');
                
                if (shipments.length === 0) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <p>لا توجد شحنات للطباعة.</p>
                            <button class="btn" onclick="createTestShipment()">إنشاء شحنة تجريبية</button>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = shipments.map(shipment => `
                    <div class="shipment-card">
                        <div class="shipment-header">
                            <div class="tracking-number">${shipment.trackingNumber}</div>
                            <span class="status-badge status-${getStatusClass(shipment.status)}">${shipment.status}</span>
                        </div>
                        <div class="shipment-details">
                            <div class="detail-item">
                                <span class="detail-label">المرسل:</span>
                                <span class="detail-value">${shipment.senderName}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">المستقبل:</span>
                                <span class="detail-value">${shipment.receiverName}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">المحتويات:</span>
                                <span class="detail-value">${shipment.contents || 'غير محدد'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">التكلفة:</span>
                                <span class="detail-value">${shipment.cost || 0} ${shipment.currency || 'SAR'}</span>
                            </div>
                        </div>
                        <div style="text-align: center;">
                            <button class="btn btn-warning" onclick="printShipment('${shipment.id}')">
                                🖨️ طباعة بوليصة الشحن
                            </button>
                            <button class="btn btn-info" onclick="previewShipment('${shipment.id}')">
                                👁️ معاينة
                            </button>
                        </div>
                    </div>
                `).join('');
                
                console.log('✅ تم تحميل', shipments.length, 'شحنة للطباعة');
            } catch (error) {
                console.error('❌ خطأ في تحميل الشحنات:', error);
                alert('خطأ في تحميل الشحنات: ' + error.message);
            }
        }

        function printShipment(shipmentId) {
            console.log('🖨️ طباعة الشحنة:', shipmentId);
            
            try {
                const shipment = db.getShipmentById(shipmentId);
                if (!shipment) {
                    alert('لم يتم العثور على الشحنة');
                    return;
                }

                // إنشاء محتوى البوليصة
                const printContent = generateShipmentLabel(shipment);
                
                // فتح نافذة طباعة جديدة
                const printWindow = window.open('', '_blank', 'width=800,height=600');
                printWindow.document.write(printContent);
                printWindow.document.close();
                
                // طباعة تلقائية
                printWindow.onload = function() {
                    printWindow.print();
                };

                console.log('✅ تم إنشاء بوليصة الشحن للطباعة');
            } catch (error) {
                console.error('❌ خطأ في طباعة البوليصة:', error);
                alert('خطأ في طباعة البوليصة: ' + error.message);
            }
        }

        function previewShipment(shipmentId) {
            console.log('👁️ معاينة الشحنة:', shipmentId);
            
            try {
                const shipment = db.getShipmentById(shipmentId);
                if (!shipment) {
                    alert('لم يتم العثور على الشحنة');
                    return;
                }

                // إنشاء محتوى البوليصة
                const printContent = generateShipmentLabel(shipment);
                
                // فتح نافذة معاينة جديدة
                const previewWindow = window.open('', '_blank', 'width=800,height=600');
                previewWindow.document.write(printContent);
                previewWindow.document.close();

                console.log('✅ تم فتح معاينة البوليصة');
            } catch (error) {
                console.error('❌ خطأ في معاينة البوليصة:', error);
                alert('خطأ في معاينة البوليصة: ' + error.message);
            }
        }

        function createTestShipment() {
            console.log('➕ إنشاء شحنة تجريبية...');
            
            try {
                const testShipment = {
                    senderName: 'أحمد محمد التجريبي',
                    senderPhone: '+966501234567',
                    senderAddress: 'العليا - الرياض - السعودية',
                    receiverName: 'سارة أحمد التجريبية',
                    receiverPhone: '+966507654321',
                    receiverAddress: 'الحمراء - جدة - السعودية',
                    weight: 2.5,
                    cost: 75,
                    currency: 'SAR',
                    status: 'معلق',
                    contents: 'هدايا ومستلزمات شخصية',
                    distributorName: 'موزع الاختبار',
                    estimatedDelivery: '2024-02-01',
                    notes: 'شحنة تجريبية لاختبار الطباعة'
                };
                
                const newShipment = db.addShipment(testShipment);
                
                if (newShipment) {
                    alert('تم إنشاء شحنة تجريبية بنجاح!\\nرقم التتبع: ' + newShipment.trackingNumber);
                    loadShipments();
                } else {
                    alert('خطأ في إنشاء الشحنة التجريبية');
                }
            } catch (error) {
                console.error('❌ خطأ في إنشاء الشحنة التجريبية:', error);
                alert('خطأ في إنشاء الشحنة التجريبية: ' + error.message);
            }
        }

        function printAllShipments() {
            const shipments = db.getAllShipments();
            if (shipments.length === 0) {
                alert('لا توجد شحنات للطباعة');
                return;
            }
            
            if (!confirm('هل تريد طباعة جميع الشحنات (' + shipments.length + ' شحنة)؟')) {
                return;
            }
            
            shipments.forEach((shipment, index) => {
                setTimeout(() => {
                    printShipment(shipment.id);
                }, index * 1000); // تأخير ثانية واحدة بين كل طباعة
            });
        }

        function showPrintPreview() {
            const shipments = db.getAllShipments();
            if (shipments.length === 0) {
                alert('لا توجد شحنات للمعاينة');
                return;
            }
            
            // معاينة أول شحنة
            previewShipment(shipments[0].id);
        }

        // إنشاء محتوى بوليصة الشحن
        function generateShipmentLabel(shipment) {
            const currentDate = new Date().toLocaleDateString('ar-SA');
            const currentTime = new Date().toLocaleTimeString('ar-SA');

            // الحصول على بيانات الشركة
            const companySettings = JSON.parse(localStorage.getItem('companySettings') || '{}');
            const companyName = companySettings.name || 'شركة الشحن السريع';
            const companyPhone = companySettings.phone || '+966501234567';
            const companyEmail = companySettings.email || '<EMAIL>';
            const companyWebsite = companySettings.website || 'www.fastshipping.com';
            const companyAddress = companySettings.address || 'الرياض، المملكة العربية السعودية';
            const companyLogo = companySettings.logo || null;

            // إنشاء QR Code URL
            const qrData = 'رقم التتبع: ' + shipment.trackingNumber +
                          '\\nالمرسل: ' + shipment.senderName +
                          '\\nالمستقبل: ' + shipment.receiverName +
                          '\\nالحالة: ' + shipment.status +
                          '\\nالشركة: ' + companyName;
            const qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + encodeURIComponent(qrData);

            return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بوليصة شحن - ${shipment.trackingNumber}</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'Cairo', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #000;
            line-height: 1.6;
        }

        .label-container {
            max-width: 800px;
            margin: 0 auto;
            border: 3px solid #007bff;
            border-radius: 15px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }

        .company-logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .company-info h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            color: #007bff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .company-info p {
            margin: 5px 0 0 0;
            color: #6c757d;
            font-weight: 500;
        }

        .tracking-section {
            text-align: center;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .tracking-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 10px 0;
            letter-spacing: 2px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .tracking-label {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .detail-section {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }

        .detail-section h3 {
            margin: 0 0 15px 0;
            font-size: 1.3rem;
            font-weight: 700;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            min-width: 100px;
        }

        .detail-value {
            font-weight: 500;
            color: #212529;
            text-align: left;
        }

        .shipment-info {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
            border: 2px solid #bbdefb;
        }

        .qr-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }

        .qr-code {
            margin: 15px 0;
        }

        .qr-code img {
            border: 3px solid #007bff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
        }

        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-pending {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(255, 193, 7, 0.3);
        }

        .status-transit {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
        }

        .status-delivered {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
        }

        .status-cancelled {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .print-date {
            font-weight: 500;
            color: #495057;
        }

        @media print {
            body {
                padding: 0;
            }

            .label-container {
                border: 2px solid #000;
                box-shadow: none;
                page-break-inside: avoid;
            }

            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="label-container">
        <!-- Header with Company Logo -->
        <div class="header">
            <div class="company-logo">
                ${companyLogo ?
                    `<img src="${companyLogo}" alt="Company Logo" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);">` :
                    '<div class="logo-icon">🚚</div>'
                }
                <div class="company-info">
                    <h1>${companyName}</h1>
                    <p>${companyPhone}</p>
                    ${companyEmail ? `<p style="font-size: 0.9rem;">${companyEmail}</p>` : ''}
                    ${companyWebsite ? `<p style="font-size: 0.9rem;">${companyWebsite}</p>` : ''}
                </div>
            </div>
            <div class="qr-section">
                <div class="qr-code">
                    <img src="${qrCodeUrl}" alt="QR Code" width="120" height="120">
                </div>
                <p style="margin: 5px 0; font-weight: 600; color: #007bff;">امسح للتتبع</p>
            </div>
        </div>

        <!-- Tracking Number -->
        <div class="tracking-section">
            <div class="tracking-label">رقم التتبع</div>
            <div class="tracking-number">${shipment.trackingNumber}</div>
        </div>

        <!-- Details Grid -->
        <div class="details-grid">
            <!-- Sender Information -->
            <div class="detail-section">
                <h3>📤 بيانات المرسل</h3>
                <div class="detail-row">
                    <span class="detail-label">الاسم:</span>
                    <span class="detail-value">${shipment.senderName}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الجوال:</span>
                    <span class="detail-value">${shipment.senderPhone || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">العنوان:</span>
                    <span class="detail-value">${shipment.senderAddress || 'غير محدد'}</span>
                </div>
            </div>

            <!-- Receiver Information -->
            <div class="detail-section">
                <h3>📥 بيانات المستقبل</h3>
                <div class="detail-row">
                    <span class="detail-label">الاسم:</span>
                    <span class="detail-value">${shipment.receiverName}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الجوال:</span>
                    <span class="detail-value">${shipment.receiverPhone || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">العنوان:</span>
                    <span class="detail-value">${shipment.receiverAddress || 'غير محدد'}</span>
                </div>
            </div>

            <!-- Shipment Information -->
            <div class="detail-section shipment-info">
                <h3>📦 تفاصيل الشحنة</h3>
                <div class="detail-row">
                    <span class="detail-label">المحتويات:</span>
                    <span class="detail-value">${shipment.contents || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الوزن:</span>
                    <span class="detail-value">${shipment.weight || 0} كيلو</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">التكلفة:</span>
                    <span class="detail-value">${shipment.cost || 0} ${shipment.currency || 'SAR'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الحالة:</span>
                    <span class="detail-value">
                        <span class="status-badge status-${getStatusClass(shipment.status)}">${shipment.status}</span>
                    </span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">تاريخ الإنشاء:</span>
                    <span class="detail-value">${formatDate(shipment.createdDate)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">التسليم المتوقع:</span>
                    <span class="detail-value">${formatDate(shipment.estimatedDelivery) || 'غير محدد'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">الموزع:</span>
                    <span class="detail-value">${shipment.distributorName || 'غير محدد'}</span>
                </div>
                ${shipment.notes ? `
                <div class="detail-row">
                    <span class="detail-label">ملاحظات:</span>
                    <span class="detail-value">${shipment.notes}</span>
                </div>
                ` : ''}
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
                <div>
                    <h4 style="color: #007bff; margin-bottom: 10px;">معلومات الشركة</h4>
                    <p style="margin: 3px 0;"><strong>الاسم:</strong> ${companyName}</p>
                    <p style="margin: 3px 0;"><strong>الهاتف:</strong> ${companyPhone}</p>
                    ${companyEmail ? `<p style="margin: 3px 0;"><strong>البريد:</strong> ${companyEmail}</p>` : ''}
                    ${companyWebsite ? `<p style="margin: 3px 0;"><strong>الموقع:</strong> ${companyWebsite}</p>` : ''}
                </div>
                <div>
                    <h4 style="color: #007bff; margin-bottom: 10px;">العنوان</h4>
                    <p style="margin: 3px 0; line-height: 1.5;">${companyAddress}</p>
                </div>
            </div>
            <div class="print-date" style="text-align: center; border-top: 1px solid #e9ecef; padding-top: 15px;">
                <strong>تاريخ الطباعة:</strong> ${currentDate} - ${currentTime}
            </div>
            <p style="margin: 10px 0 0 0; font-style: italic; text-align: center;">
                هذه بوليصة شحن رسمية - يرجى الاحتفاظ بها حتى استلام الشحنة
            </p>
        </div>
    </div>
</body>
</html>`;
        }

        // دوال مساعدة
        function getStatusClass(status) {
            const statusMap = {
                'معلق': 'pending',
                'في الطريق': 'transit',
                'مسلم': 'delivered',
                'ملغي': 'cancelled'
            };
            return statusMap[status] || 'pending';
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }
    </script>
</body>
</html>
