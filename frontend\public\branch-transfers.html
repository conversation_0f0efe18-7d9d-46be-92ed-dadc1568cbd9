<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التحويلات بين الفروع - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display);
            font-weight: 600;
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, #17a2b8 0%, #28a745 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total {
            border-left-color: #17a2b8;
        }

        .stat-card.pending {
            border-left-color: #ffc107;
        }

        .stat-card.transit {
            border-left-color: #007bff;
        }

        .stat-card.received {
            border-left-color: #28a745;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input,
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            min-width: 200px;
        }

        .search-input:focus,
        .filter-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .transfers-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .status-transit {
            background: rgba(0, 123, 255, 0.2);
            color: #004085;
        }

        .status-received {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .status-cancelled {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .priority-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .priority-normal {
            background: rgba(108, 117, 125, 0.2);
            color: #495057;
        }

        .priority-urgent {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .priority-emergency {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-data h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #17a2b8 0%, #28a745 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
        }

        .form-input {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input,
            .filter-select {
                min-width: auto;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                justify-content: center;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <a href="branches-management.html" class="back-link">← العودة للفروع</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🔄</span>
                <span>إدارة التحويلات بين الفروع</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="branches-management.html">الفروع</a>
                <a href="branch-transfers.html" class="active">التحويلات</a>
                <a href="shipments.html">الشحنات</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">🔄 إدارة التحويلات بين الفروع</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="openTransferModal()">
                    ➕ تحويل جديد
                </button>
                <button class="btn btn-info" onclick="showReceiveModal()">
                    📥 استلام شحنة
                </button>
                <button class="btn" onclick="loadTransfers()">
                    🔄 تحديث القائمة
                </button>
            </div>
        </div>

        <!-- إحصائيات التحويلات -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="card-icon">🔄</div>
                <div class="card-amount" id="totalTransfers">0</div>
                <div class="card-label">إجمالي التحويلات</div>
            </div>
            
            <div class="stat-card pending">
                <div class="card-icon">⏳</div>
                <div class="card-amount" id="pendingTransfers">0</div>
                <div class="card-label">تحويلات معلقة</div>
            </div>
            
            <div class="stat-card transit">
                <div class="card-icon">🚛</div>
                <div class="card-amount" id="transitTransfers">0</div>
                <div class="card-label">في الطريق</div>
            </div>
            
            <div class="stat-card received">
                <div class="card-icon">✅</div>
                <div class="card-amount" id="receivedTransfers">0</div>
                <div class="card-label">مستلمة</div>
            </div>
        </div>

        <!-- أدوات التحكم والبحث -->
        <div class="controls">
            <input type="text" id="searchInput" class="search-input" placeholder="🔍 البحث في التحويلات...">
            
            <select id="statusFilter" class="filter-select">
                <option value="">جميع الحالات</option>
                <option value="معلق">معلق</option>
                <option value="في الطريق">في الطريق</option>
                <option value="مستلم">مستلم</option>
                <option value="ملغي">ملغي</option>
            </select>
            
            <select id="branchFilter" class="filter-select">
                <option value="">جميع الفروع</option>
            </select>
            
            <select id="priorityFilter" class="filter-select">
                <option value="">جميع الأولويات</option>
                <option value="عادي">عادي</option>
                <option value="عاجل">عاجل</option>
                <option value="طارئ">طارئ</option>
            </select>
            
            <button class="btn" onclick="clearFilters()">مسح الفلاتر</button>
        </div>

        <!-- جدول التحويلات -->
        <div class="transfers-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم التحويل</th>
                        <th>رقم الشحنة</th>
                        <th>من فرع</th>
                        <th>إلى فرع</th>
                        <th>الأولوية</th>
                        <th>الحالة</th>
                        <th>تاريخ الطلب</th>
                        <th>الوصول المتوقع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="transfersTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>
            
            <!-- رسالة عدم وجود بيانات -->
            <div id="noDataMessage" class="no-data" style="display: none;">
                <h3>🔄 لا توجد تحويلات</h3>
                <p>لم يتم العثور على أي تحويلات بين الفروع. ابدأ بإنشاء تحويل جديد.</p>
                <button class="btn btn-success" onclick="openTransferModal()" style="margin-top: 15px;">
                    ➕ تحويل جديد
                </button>
            </div>
        </div>
    </main>

    <!-- نافذة إنشاء تحويل جديد -->
    <div id="transferModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إنشاء تحويل جديد</h2>
                <span class="close" onclick="closeTransferModal()">&times;</span>
            </div>

            <div class="modal-body">
                <form id="transferForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">الشحنة *</label>
                            <select class="form-input" id="shipmentSelect" required>
                                <option value="">اختر الشحنة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">من فرع *</label>
                            <select class="form-input" id="fromBranchSelect" required>
                                <option value="">اختر الفرع المرسل</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">إلى فرع *</label>
                            <select class="form-input" id="toBranchSelect" required>
                                <option value="">اختر الفرع المستقبل</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">الأولوية</label>
                            <select class="form-input" id="prioritySelect">
                                <option value="عادي">عادي</option>
                                <option value="عاجل">عاجل</option>
                                <option value="طارئ">طارئ</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">الوصول المتوقع</label>
                            <input type="datetime-local" class="form-input" id="estimatedArrival">
                        </div>

                        <div class="form-group">
                            <label class="form-label">سبب التحويل</label>
                            <input type="text" class="form-input" id="transferReason" placeholder="أدخل سبب التحويل...">
                        </div>

                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-input" id="transferNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn" onclick="closeTransferModal()">إلغاء</button>
                        <button type="submit" class="btn btn-success">🔄 إنشاء التحويل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة استلام شحنة -->
    <div id="receiveModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>استلام شحنة من فرع آخر</h2>
                <span class="close" onclick="closeReceiveModal()">&times;</span>
            </div>

            <div class="modal-body">
                <form id="receiveForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">التحويل المراد استلامه *</label>
                            <select class="form-input" id="pendingTransferSelect" required>
                                <option value="">اختر التحويل</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">المستلم *</label>
                            <input type="text" class="form-input" id="receivedBy" required placeholder="اسم الشخص المستلم">
                        </div>

                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label class="form-label">ملاحظات الاستلام</label>
                            <textarea class="form-input" id="receiveNotes" rows="3" placeholder="أدخل أي ملاحظات حول الاستلام..."></textarea>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn" onclick="closeReceiveModal()">إلغاء</button>
                        <button type="submit" class="btn btn-success">📥 تأكيد الاستلام</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        let currentBranchFilter = null;

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 تحميل صفحة إدارة التحويلات...');

            try {
                // التحقق من وجود فلتر فرع في URL
                const urlParams = new URLSearchParams(window.location.search);
                currentBranchFilter = urlParams.get('branch');

                loadTransfers();
                loadBranches();
                loadShipments();
                setupEventListeners();
                loadStats();

                // تطبيق فلتر الفرع إذا كان موجوداً
                if (currentBranchFilter) {
                    document.getElementById('branchFilter').value = currentBranchFilter;
                    filterTransfers();
                }

                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('transferForm').addEventListener('submit', createTransfer);
            document.getElementById('receiveForm').addEventListener('submit', receiveTransfer);
            document.getElementById('searchInput').addEventListener('input', filterTransfers);
            document.getElementById('statusFilter').addEventListener('change', filterTransfers);
            document.getElementById('branchFilter').addEventListener('change', filterTransfers);
            document.getElementById('priorityFilter').addEventListener('change', filterTransfers);
        }

        // تحميل قائمة الفروع
        function loadBranches() {
            try {
                const branches = db.getActiveBranches();
                const fromBranchSelect = document.getElementById('fromBranchSelect');
                const toBranchSelect = document.getElementById('toBranchSelect');
                const branchFilter = document.getElementById('branchFilter');

                // مسح الخيارات الحالية
                fromBranchSelect.innerHTML = '<option value="">اختر الفرع المرسل</option>';
                toBranchSelect.innerHTML = '<option value="">اختر الفرع المستقبل</option>';
                branchFilter.innerHTML = '<option value="">جميع الفروع</option>';

                branches.forEach(branch => {
                    const option1 = document.createElement('option');
                    option1.value = branch.id;
                    option1.textContent = branch.name;
                    fromBranchSelect.appendChild(option1);

                    const option2 = document.createElement('option');
                    option2.value = branch.id;
                    option2.textContent = branch.name;
                    toBranchSelect.appendChild(option2);

                    const option3 = document.createElement('option');
                    option3.value = branch.id;
                    option3.textContent = branch.name;
                    branchFilter.appendChild(option3);
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل الفروع:', error);
            }
        }

        // تحميل قائمة الشحنات
        function loadShipments() {
            try {
                // استخدام البيانات من التخزين المحلي
                const shipmentsData = localStorage.getItem('new_shipments');
                let shipments = [];

                if (shipmentsData) {
                    shipments = JSON.parse(shipmentsData);
                } else {
                    // بيانات تجريبية
                    shipments = [
                        {
                            id: 'SHP001',
                            trackingNumber: 'TRK123456789',
                            receiverName: 'أحمد محمد',
                            status: 'معلق',
                            fromBranch: 'الفرع الرئيسي'
                        },
                        {
                            id: 'SHP002',
                            trackingNumber: 'TRK987654321',
                            receiverName: 'فاطمة علي',
                            status: 'في الطريق',
                            fromBranch: 'فرع الرياض'
                        }
                    ];
                }

                const availableShipments = shipments.filter(s =>
                    s.status !== 'ملغي' &&
                    s.status !== 'مسلم' &&
                    s.status !== 'في التحويل'
                );

                const shipmentSelect = document.getElementById('shipmentSelect');
                if (shipmentSelect) {
                    shipmentSelect.innerHTML = '<option value="">اختر الشحنة</option>';

                    availableShipments.forEach(shipment => {
                        const option = document.createElement('option');
                        option.value = shipment.id;
                        option.textContent = `${shipment.trackingNumber} - ${shipment.receiverName}`;
                        shipmentSelect.appendChild(option);
                    });
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل الشحنات:', error);
                // إضافة خيار افتراضي في حالة الخطأ
                const shipmentSelect = document.getElementById('shipmentSelect');
                if (shipmentSelect) {
                    shipmentSelect.innerHTML = '<option value="">لا توجد شحنات متاحة</option>';
                }
            }
        }

        // تحميل التحويلات المعلقة للاستلام
        function loadPendingTransfers() {
            try {
                const transfers = db.getAllBranchTransfers();
                const pendingTransfers = transfers.filter(t =>
                    t.status === 'معلق' || t.status === 'في الطريق'
                );

                const pendingSelect = document.getElementById('pendingTransferSelect');
                pendingSelect.innerHTML = '<option value="">اختر التحويل</option>';

                pendingTransfers.forEach(transfer => {
                    const option = document.createElement('option');
                    option.value = transfer.id;
                    option.textContent = `${transfer.trackingNumber} - من ${transfer.fromBranchName} إلى ${transfer.toBranchName}`;
                    pendingSelect.appendChild(option);
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل التحويلات المعلقة:', error);
            }
        }

        // تحميل وعرض التحويلات
        function loadTransfers() {
            try {
                const transfers = db.getAllBranchTransfers();
                displayTransfers(transfers);
                console.log('🔄 تم تحميل', transfers.length, 'تحويل');
            } catch (error) {
                console.error('❌ خطأ في تحميل التحويلات:', error);
                alert('خطأ في تحميل التحويلات: ' + error.message);
            }
        }

        // عرض التحويلات في الجدول
        function displayTransfers(transfers) {
            const tbody = document.getElementById('transfersTableBody');
            const noDataMessage = document.getElementById('noDataMessage');

            if (transfers.length === 0) {
                tbody.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }

            noDataMessage.style.display = 'none';

            tbody.innerHTML = transfers.map(transfer =>
                `<tr>
                    <td><strong>${transfer.trackingNumber}</strong></td>
                    <td>${transfer.shipmentId}</td>
                    <td>${transfer.fromBranchName}</td>
                    <td>${transfer.toBranchName}</td>
                    <td><span class="priority-badge priority-${getPriorityClass(transfer.priority)}">${transfer.priority}</span></td>
                    <td><span class="status-badge status-${getStatusClass(transfer.status)}">${transfer.status}</span></td>
                    <td>${formatDate(transfer.requestDate)}</td>
                    <td>${transfer.estimatedArrival ? formatDate(transfer.estimatedArrival) : 'غير محدد'}</td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-small" onclick="viewTransfer('${transfer.id}')">عرض</button>
                            ${transfer.status === 'معلق' ?
                                `<button class="btn btn-small btn-info" onclick="markInTransit('${transfer.id}')">في الطريق</button>` :
                                ''}
                            ${(transfer.status === 'معلق' || transfer.status === 'في الطريق') ?
                                `<button class="btn btn-small btn-success" onclick="quickReceive('${transfer.id}')">استلام</button>` :
                                ''}
                            ${transfer.status === 'معلق' ?
                                `<button class="btn btn-small btn-danger" onclick="cancelTransfer('${transfer.id}')">إلغاء</button>` :
                                ''}
                        </div>
                    </td>
                </tr>`
            ).join('');
        }

        // تحميل الإحصائيات
        function loadStats() {
            try {
                const transfers = db.getAllBranchTransfers();

                const totalCount = transfers.length;
                const pendingCount = transfers.filter(t => t.status === 'معلق').length;
                const transitCount = transfers.filter(t => t.status === 'في الطريق').length;
                const receivedCount = transfers.filter(t => t.status === 'مستلم').length;

                document.getElementById('totalTransfers').textContent = totalCount;
                document.getElementById('pendingTransfers').textContent = pendingCount;
                document.getElementById('transitTransfers').textContent = transitCount;
                document.getElementById('receivedTransfers').textContent = receivedCount;

            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات:', error);
            }
        }

        // فلترة التحويلات
        function filterTransfers() {
            try {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;
                const branchFilter = document.getElementById('branchFilter').value;
                const priorityFilter = document.getElementById('priorityFilter').value;

                const allTransfers = db.getAllBranchTransfers();

                const filteredTransfers = allTransfers.filter(transfer => {
                    const matchesSearch = !searchTerm ||
                        transfer.trackingNumber.toLowerCase().includes(searchTerm) ||
                        transfer.shipmentId.toLowerCase().includes(searchTerm) ||
                        transfer.fromBranchName.toLowerCase().includes(searchTerm) ||
                        transfer.toBranchName.toLowerCase().includes(searchTerm);

                    const matchesStatus = !statusFilter || transfer.status === statusFilter;
                    const matchesBranch = !branchFilter ||
                        transfer.fromBranchId === branchFilter ||
                        transfer.toBranchId === branchFilter;
                    const matchesPriority = !priorityFilter || transfer.priority === priorityFilter;

                    return matchesSearch && matchesStatus && matchesBranch && matchesPriority;
                });

                displayTransfers(filteredTransfers);
            } catch (error) {
                console.error('❌ خطأ في فلترة التحويلات:', error);
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('branchFilter').value = '';
            document.getElementById('priorityFilter').value = '';
            loadTransfers();
        }

        // فتح نافذة إنشاء تحويل
        function openTransferModal() {
            loadShipments();
            document.getElementById('transferModal').style.display = 'block';
        }

        // إغلاق نافذة إنشاء تحويل
        function closeTransferModal() {
            document.getElementById('transferModal').style.display = 'none';
            document.getElementById('transferForm').reset();
        }

        // فتح نافذة استلام شحنة
        function showReceiveModal() {
            loadPendingTransfers();
            document.getElementById('receiveModal').style.display = 'block';
        }

        // إغلاق نافذة استلام شحنة
        function closeReceiveModal() {
            document.getElementById('receiveModal').style.display = 'none';
            document.getElementById('receiveForm').reset();
        }

        // إنشاء تحويل جديد
        function createTransfer(e) {
            e.preventDefault();

            try {
                const shipmentId = document.getElementById('shipmentSelect').value;
                const fromBranchId = document.getElementById('fromBranchSelect').value;
                const toBranchId = document.getElementById('toBranchSelect').value;
                const priority = document.getElementById('prioritySelect').value;
                const estimatedArrival = document.getElementById('estimatedArrival').value;
                const transferReason = document.getElementById('transferReason').value.trim();
                const notes = document.getElementById('transferNotes').value.trim();

                if (!shipmentId || !fromBranchId || !toBranchId) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                if (fromBranchId === toBranchId) {
                    alert('لا يمكن تحويل الشحنة إلى نفس الفرع');
                    return;
                }

                const fromBranch = db.getBranchById(fromBranchId);
                const toBranch = db.getBranchById(toBranchId);

                const transferData = {
                    shipmentId: shipmentId,
                    fromBranchId: fromBranchId,
                    toBranchId: toBranchId,
                    fromBranchName: fromBranch.name,
                    toBranchName: toBranch.name,
                    priority: priority,
                    estimatedArrival: estimatedArrival,
                    transferReason: transferReason || 'تحويل عادي',
                    notes: notes,
                    requestedBy: 'المستخدم الحالي'
                };

                const newTransfer = db.createBranchTransfer(transferData);

                if (newTransfer) {
                    alert(`تم إنشاء التحويل بنجاح!\n\nرقم التتبع: ${newTransfer.trackingNumber}\nمن: ${fromBranch.name}\nإلى: ${toBranch.name}`);
                    closeTransferModal();
                    loadTransfers();
                    loadStats();
                } else {
                    alert('خطأ في إنشاء التحويل');
                }

            } catch (error) {
                console.error('❌ خطأ في إنشاء التحويل:', error);
                alert('خطأ في إنشاء التحويل: ' + error.message);
            }
        }

        // استلام تحويل
        function receiveTransfer(e) {
            e.preventDefault();

            try {
                const transferId = document.getElementById('pendingTransferSelect').value;
                const receivedBy = document.getElementById('receivedBy').value.trim();
                const notes = document.getElementById('receiveNotes').value.trim();

                if (!transferId || !receivedBy) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                const transfer = db.getAllBranchTransfers().find(t => t.id === transferId);
                if (!transfer) {
                    alert('لم يتم العثور على التحويل');
                    return;
                }

                if (confirm(`هل تريد تأكيد استلام التحويل؟\n\nرقم التتبع: ${transfer.trackingNumber}\nمن: ${transfer.fromBranchName}\nإلى: ${transfer.toBranchName}`)) {
                    const result = db.receiveTransfer(transferId, receivedBy, notes);

                    if (result) {
                        alert('تم تأكيد استلام التحويل بنجاح');
                        closeReceiveModal();
                        loadTransfers();
                        loadStats();
                    } else {
                        alert('خطأ في تأكيد الاستلام');
                    }
                }

            } catch (error) {
                console.error('❌ خطأ في استلام التحويل:', error);
                alert('خطأ في استلام التحويل: ' + error.message);
            }
        }

        // عرض تفاصيل التحويل
        function viewTransfer(id) {
            try {
                const transfer = db.getAllBranchTransfers().find(t => t.id === id);
                if (!transfer) {
                    alert('لم يتم العثور على التحويل');
                    return;
                }

                let details = `تفاصيل التحويل ${transfer.trackingNumber}:\n\n` +
                             `رقم الشحنة: ${transfer.shipmentId}\n` +
                             `من فرع: ${transfer.fromBranchName}\n` +
                             `إلى فرع: ${transfer.toBranchName}\n` +
                             `الأولوية: ${transfer.priority}\n` +
                             `سبب التحويل: ${transfer.transferReason}\n` +
                             `الحالة: ${transfer.status}\n` +
                             `تاريخ الطلب: ${formatDateTime(transfer.requestDate)}\n` +
                             `الوصول المتوقع: ${transfer.estimatedArrival ? formatDateTime(transfer.estimatedArrival) : 'غير محدد'}\n` +
                             `طلب بواسطة: ${transfer.requestedBy}\n`;

                if (transfer.actualArrival) {
                    details += `الوصول الفعلي: ${formatDateTime(transfer.actualArrival)}\n`;
                }

                if (transfer.receivedBy) {
                    details += `استلم بواسطة: ${transfer.receivedBy}\n`;
                }

                details += `ملاحظات: ${transfer.notes || 'لا توجد ملاحظات'}`;

                alert(details);
            } catch (error) {
                console.error('❌ خطأ في عرض التحويل:', error);
                alert('خطأ في عرض التحويل: ' + error.message);
            }
        }

        // تحديث حالة التحويل إلى "في الطريق"
        function markInTransit(id) {
            try {
                const transfer = db.getAllBranchTransfers().find(t => t.id === id);
                if (!transfer) {
                    alert('لم يتم العثور على التحويل');
                    return;
                }

                if (confirm(`هل تريد تحديث حالة التحويل إلى "في الطريق"؟\n\nرقم التتبع: ${transfer.trackingNumber}`)) {
                    const result = db.updateTransferStatus(id, 'في الطريق');

                    if (result) {
                        alert('تم تحديث حالة التحويل بنجاح');
                        loadTransfers();
                        loadStats();
                    } else {
                        alert('خطأ في تحديث حالة التحويل');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في تحديث حالة التحويل:', error);
                alert('خطأ في تحديث حالة التحويل: ' + error.message);
            }
        }

        // استلام سريع للتحويل
        function quickReceive(id) {
            try {
                const transfer = db.getAllBranchTransfers().find(t => t.id === id);
                if (!transfer) {
                    alert('لم يتم العثور على التحويل');
                    return;
                }

                const receivedBy = prompt(`تأكيد استلام التحويل ${transfer.trackingNumber}\n\nأدخل اسم المستلم:`);

                if (receivedBy && receivedBy.trim()) {
                    const result = db.receiveTransfer(id, receivedBy.trim(), 'استلام سريع');

                    if (result) {
                        alert('تم تأكيد استلام التحويل بنجاح');
                        loadTransfers();
                        loadStats();
                    } else {
                        alert('خطأ في تأكيد الاستلام');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في الاستلام السريع:', error);
                alert('خطأ في الاستلام السريع: ' + error.message);
            }
        }

        // إلغاء التحويل
        function cancelTransfer(id) {
            try {
                const transfer = db.getAllBranchTransfers().find(t => t.id === id);
                if (!transfer) {
                    alert('لم يتم العثور على التحويل');
                    return;
                }

                if (confirm(`هل أنت متأكد من إلغاء التحويل؟\n\nرقم التتبع: ${transfer.trackingNumber}\nمن: ${transfer.fromBranchName}\nإلى: ${transfer.toBranchName}`)) {
                    const result = db.updateTransferStatus(id, 'ملغي');

                    if (result) {
                        alert('تم إلغاء التحويل بنجاح');
                        loadTransfers();
                        loadStats();
                    } else {
                        alert('خطأ في إلغاء التحويل');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في إلغاء التحويل:', error);
                alert('خطأ في إلغاء التحويل: ' + error.message);
            }
        }

        // دوال مساعدة
        function getStatusClass(status) {
            const statusMap = {
                'معلق': 'pending',
                'في الطريق': 'transit',
                'مستلم': 'received',
                'ملغي': 'cancelled'
            };
            return statusMap[status] || 'pending';
        }

        function getPriorityClass(priority) {
            const priorityMap = {
                'عادي': 'normal',
                'عاجل': 'urgent',
                'طارئ': 'emergency'
            };
            return priorityMap[priority] || 'normal';
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        function formatDateTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('ar-SA');
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const transferModal = document.getElementById('transferModal');
            const receiveModal = document.getElementById('receiveModal');

            if (event.target === transferModal) {
                closeTransferModal();
            }
            if (event.target === receiveModal) {
                closeReceiveModal();
            }
        }
    </script>
</body>
</html>
