<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أسباب الإلغاء الجديدة</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .data-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #dc3545;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
            margin-left: 15px;
        }

        .card-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .card-details {
            margin: 15px 0;
        }

        .card-detail {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            border-color: #dc3545;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .back-btn {
                justify-content: center;
                width: 100%;
            }

            .header h1 {
                font-size: 2rem;
            }

            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                    <span>🏠</span>
                    <span>العودة للوحة التحكم</span>
                    <span>←</span>
                </a>
                <div class="header-actions">
                    <button class="btn btn-info btn-sm" onclick="window.location.reload()">
                        <span>🔄</span> تحديث الصفحة
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportAllCancellations()">
                        <span>📥</span> تصدير البيانات
                    </button>
                </div>
            </div>
            <h1>🚫 إدارة أسباب الإلغاء الجديدة</h1>
            <p>نظام متطور لتتبع وتحليل أسباب إلغاء الشحنات وتحسين الخدمة</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="section-header">
                <h2 class="section-title">🚫 إدارة أسباب الإلغاء</h2>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="addCancellationReason()">
                        <span>➕</span> إضافة سبب جديد
                    </button>
                    <button class="btn btn-warning" onclick="generateReport()">
                        <span>📊</span> تقرير تحليلي
                    </button>
                    <button class="btn btn-info" onclick="refreshData()">
                        <span>🔄</span> تحديث
                    </button>
                    <button class="btn btn-success" onclick="exportData()">
                        <span>📤</span> تصدير
                    </button>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalCancellations">0</div>
                    <div class="stat-label">إجمالي الإلغاءات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="todayCancellations">0</div>
                    <div class="stat-label">إلغاءات اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="cancellationRate">0%</div>
                    <div class="stat-label">معدل الإلغاء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="topReason">-</div>
                    <div class="stat-label">السبب الأكثر شيوعاً</div>
                </div>
            </div>
            
            <div id="cancellationGrid" class="data-grid">
                <div class="empty-state">
                    <div class="empty-state-icon">🚫</div>
                    <h3>لا توجد أسباب إلغاء مسجلة</h3>
                    <p>ابدأ بإضافة أسباب الإلغاء لتتبع وتحليل البيانات</p>
                    <button class="btn btn-primary" onclick="addCancellationReason()">➕ إضافة سبب إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // متغيرات عامة
        let cancellationReasons = [];

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚫 تحميل نظام إدارة أسباب الإلغاء الجديدة...');
            initializeCancellations();
        });

        // تهيئة النظام
        function initializeCancellations() {
            loadDefaultCancellations();
            loadCancellations();
            updateStatistics();
            console.log('✅ تم تحميل نظام إدارة أسباب الإلغاء بنجاح');
        }

        // تحميل البيانات الافتراضية
        function loadDefaultCancellations() {
            if (!localStorage.getItem('new_cancellations')) {
                const defaultCancellations = [
                    {
                        id: 'CAN001',
                        reason: 'عدم توفر العنوان',
                        category: 'مشكلة في العنوان',
                        count: 25,
                        percentage: 35,
                        impact: 'عالي',
                        solution: 'تحسين نظام التحقق من العناوين',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'CAN002',
                        reason: 'رفض العميل استلام الطلب',
                        category: 'قرار العميل',
                        count: 18,
                        percentage: 25,
                        impact: 'متوسط',
                        solution: 'تحسين التواصل مع العملاء',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'CAN003',
                        reason: 'تأخير في التوصيل',
                        category: 'مشكلة في الخدمة',
                        count: 15,
                        percentage: 21,
                        impact: 'عالي',
                        solution: 'تحسين جدولة التوصيل',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'CAN004',
                        reason: 'مشكلة في المنتج',
                        category: 'جودة المنتج',
                        count: 12,
                        percentage: 17,
                        impact: 'متوسط',
                        solution: 'تحسين فحص المنتجات',
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('new_cancellations', JSON.stringify(defaultCancellations));
                console.log('✅ تم إنشاء بيانات أسباب الإلغاء الافتراضية');
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalCancellationsEl = document.getElementById('totalCancellations');
            const todayCancellationsEl = document.getElementById('todayCancellations');
            const cancellationRateEl = document.getElementById('cancellationRate');
            const topReasonEl = document.getElementById('topReason');

            const totalCount = cancellationReasons.reduce((sum, reason) => sum + (reason.count || 0), 0);
            const topReason = cancellationReasons.length > 0 ? 
                cancellationReasons.reduce((prev, current) => (prev.count > current.count) ? prev : current) : null;

            if (totalCancellationsEl) totalCancellationsEl.textContent = totalCount;
            if (todayCancellationsEl) todayCancellationsEl.textContent = Math.floor(totalCount * 0.1); // تقدير
            if (cancellationRateEl) cancellationRateEl.textContent = totalCount > 0 ? '12%' : '0%'; // تقدير
            if (topReasonEl) topReasonEl.textContent = topReason ? topReason.reason.substring(0, 15) + '...' : '-';
        }

        // وظائف أسباب الإلغاء
        function addCancellationReason() {
            // إنشاء نموذج إضافة سبب إلغاء
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h2>🚫 إضافة سبب إلغاء جديد</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="addCancellationForm" style="text-align: right; direction: rtl;">
                            <div class="form-group">
                                <label for="reasonTitle">عنوان السبب *</label>
                                <input type="text" id="reasonTitle" required placeholder="مثال: تأخير في التسليم">
                            </div>
                            <div class="form-group">
                                <label for="reasonCategory">فئة السبب *</label>
                                <select id="reasonCategory" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="عميل">متعلق بالعميل</option>
                                    <option value="شركة">متعلق بالشركة</option>
                                    <option value="مندوب">متعلق بالمندوب</option>
                                    <option value="تقني">مشكلة تقنية</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="reasonDescription">وصف السبب *</label>
                                <textarea id="reasonDescription" rows="4" required placeholder="وصف تفصيلي لسبب الإلغاء"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="reasonSeverity">مستوى الخطورة</label>
                                <select id="reasonSeverity">
                                    <option value="منخفض">منخفض</option>
                                    <option value="متوسط" selected>متوسط</option>
                                    <option value="عالي">عالي</option>
                                    <option value="حرج">حرج</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="reasonSolution">الحل المقترح</label>
                                <textarea id="reasonSolution" rows="3" placeholder="الحل أو الإجراء المقترح لتجنب هذا السبب"></textarea>
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <button type="submit" class="btn btn-primary" style="margin-left: 10px;">✅ إضافة السبب</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // معالج إرسال النموذج
            document.getElementById('addCancellationForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newReason = {
                    id: 'CAN' + String(Date.now()).slice(-6),
                    title: document.getElementById('reasonTitle').value,
                    category: document.getElementById('reasonCategory').value,
                    description: document.getElementById('reasonDescription').value,
                    severity: document.getElementById('reasonSeverity').value,
                    solution: document.getElementById('reasonSolution').value || '',
                    status: 'نشط',
                    usageCount: 0,
                    createdAt: new Date().toISOString(),
                    createdBy: 'النظام'
                };

                // إضافة للقائمة
                cancellationReasons.push(newReason);

                // حفظ في التخزين المحلي
                localStorage.setItem('new_cancellation_reasons', JSON.stringify(cancellationReasons));

                // تحديث الإحصائيات
                updateStatistics();

                // إغلاق النموذج
                modal.remove();

                alert('✅ تم إضافة سبب الإلغاء بنجاح!\nرقم السبب: ' + newReason.id);
                console.log('➕ تم إضافة سبب إلغاء جديد:', newReason);
            });
        }

        function generateReport() {
            // إنشاء تقرير تحليلي شامل
            const reportModal = document.createElement('div');
            reportModal.className = 'modal';
            reportModal.style.display = 'block';

            // تحليل البيانات
            const totalReasons = cancellationReasons.length;
            const activeReasons = cancellationReasons.filter(r => r.status === 'نشط').length;
            const categoryStats = {};
            const severityStats = {};

            cancellationReasons.forEach(reason => {
                // إحصائيات الفئات
                categoryStats[reason.category] = (categoryStats[reason.category] || 0) + 1;

                // إحصائيات مستوى الخطورة
                severityStats[reason.severity] = (severityStats[reason.severity] || 0) + 1;
            });

            // أكثر الأسباب استخداماً
            const topReasons = cancellationReasons
                .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
                .slice(0, 5);

            reportModal.innerHTML = `
                <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
                    <div class="modal-header">
                        <h2>📊 التقرير التحليلي الشامل لأسباب الإلغاء</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body" style="text-align: right; direction: rtl;">

                        <!-- الإحصائيات العامة -->
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                            <h3 style="margin-bottom: 15px;">📈 الإحصائيات العامة</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div style="text-align: center;">
                                    <div style="font-size: 2rem; font-weight: bold;">${totalReasons}</div>
                                    <div>إجمالي الأسباب</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 2rem; font-weight: bold;">${activeReasons}</div>
                                    <div>الأسباب النشطة</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 2rem; font-weight: bold;">${Object.keys(categoryStats).length}</div>
                                    <div>عدد الفئات</div>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات الفئات -->
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <h3 style="color: #2c3e50; margin-bottom: 15px;">📋 توزيع الأسباب حسب الفئة</h3>
                            <div style="display: grid; gap: 10px;">
                                ${Object.entries(categoryStats).map(([category, count]) => `
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: white; border-radius: 8px; border-right: 4px solid #667eea;">
                                        <span style="font-weight: 600;">${category}</span>
                                        <span style="background: #667eea; color: white; padding: 5px 15px; border-radius: 20px;">${count}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <!-- التوصيات -->
                        <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 15px;">
                            <h3 style="margin-bottom: 15px;">💡 التوصيات والاقتراحات</h3>
                            <ul style="list-style: none; padding: 0;">
                                <li style="margin-bottom: 10px;">✅ مراجعة الأسباب عالية الاستخدام لتحسين العمليات</li>
                                <li style="margin-bottom: 10px;">✅ وضع خطط وقائية للأسباب ذات الخطورة العالية</li>
                                <li style="margin-bottom: 10px;">✅ تدريب الفريق على التعامل مع الأسباب الشائعة</li>
                                <li style="margin-bottom: 10px;">✅ مراجعة دورية للأسباب غير المستخدمة</li>
                            </ul>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success" onclick="exportAnalyticalReport()">📊 تصدير التقرير</button>
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
                    </div>
                </div>
            `;

            document.body.appendChild(reportModal);
            console.log('📊 تم إنشاء التقرير التحليلي الشامل');
        }

        // تصدير التقرير التحليلي
        function exportAnalyticalReport() {
            const reportData = [
                ['التقرير التحليلي الشامل لأسباب الإلغاء', '', '', ''],
                ['تاريخ التقرير:', new Date().toLocaleDateString('ar-SA'), '', ''],
                ['', '', '', ''],
                ['تفاصيل الأسباب', '', '', ''],
                ['رقم السبب', 'العنوان', 'الفئة', 'مستوى الخطورة', 'الحالة']
            ];

            cancellationReasons.forEach(reason => {
                reportData.push([
                    reason.id,
                    reason.title,
                    reason.category,
                    reason.severity,
                    reason.status
                ]);
            });

            const csvContent = '\uFEFF' + reportData.map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `التقرير_التحليلي_أسباب_الإلغاء_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            alert('✅ تم تصدير التقرير التحليلي بنجاح!');
        }

        function refreshData() {
            loadCancellations();
            updateStatistics();
            alert('✅ تم تحديث بيانات أسباب الإلغاء!');
        }

        function exportData() {
            const dataStr = JSON.stringify(cancellationReasons, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `cancellation_reasons_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            alert('✅ تم تصدير بيانات أسباب الإلغاء بنجاح!');
            console.log('✅ تم تصدير بيانات أسباب الإلغاء');
        }

        function exportAllCancellations() {
            exportData();
        }

        function loadCancellations() {
            try {
                cancellationReasons = JSON.parse(localStorage.getItem('new_cancellations') || '[]');
                displayCancellations();
                console.log(`🚫 تم تحميل ${cancellationReasons.length} سبب إلغاء`);
            } catch (error) {
                console.error('❌ خطأ في تحميل أسباب الإلغاء:', error);
                cancellationReasons = [];
            }
        }

        function displayCancellations() {
            const grid = document.getElementById('cancellationGrid');
            if (!grid) return;

            if (cancellationReasons.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🚫</div>
                        <h3>لا توجد أسباب إلغاء مسجلة</h3>
                        <p>ابدأ بإضافة أسباب الإلغاء لتتبع وتحليل البيانات</p>
                        <button class="btn btn-primary" onclick="addCancellationReason()">➕ إضافة سبب إلغاء</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = cancellationReasons.map(reason => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-icon">🚫</div>
                        <div class="card-info">
                            <h3>${reason.reason}</h3>
                            <p>${reason.category}</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>📊 عدد الحالات:</span>
                            <span>${reason.count}</span>
                        </div>
                        <div class="card-detail">
                            <span>📈 النسبة:</span>
                            <span>${reason.percentage}%</span>
                        </div>
                        <div class="card-detail">
                            <span>⚠️ مستوى التأثير:</span>
                            <span style="color: ${reason.impact === 'عالي' ? '#dc3545' : reason.impact === 'متوسط' ? '#ffc107' : '#28a745'}">${reason.impact}</span>
                        </div>
                        <div class="card-detail">
                            <span>💡 الحل المقترح:</span>
                            <span>${reason.solution}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-info btn-sm" onclick="editReason('${reason.id}')">✏️ تعديل</button>
                        <button class="btn btn-warning btn-sm" onclick="analyzeReason('${reason.id}')">📊 تحليل</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteReason('${reason.id}')">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        function editReason(id) {
            alert(`✏️ سيتم فتح نموذج تعديل السبب: ${id}`);
        }

        function analyzeReason(id) {
            alert(`📊 سيتم عرض تحليل مفصل للسبب: ${id}`);
        }

        function deleteReason(id) {
            if (confirm('هل أنت متأكد من حذف هذا السبب؟')) {
                cancellationReasons = cancellationReasons.filter(reason => reason.id !== id);
                localStorage.setItem('new_cancellations', JSON.stringify(cancellationReasons));
                displayCancellations();
                updateStatistics();
                alert('✅ تم حذف السبب بنجاح!');
            }
        }

        // جعل الوظائف متاحة عالمياً
        window.addCancellationReason = addCancellationReason;
        window.generateReport = generateReport;
        window.refreshData = refreshData;
        window.exportData = exportData;
        window.exportAllCancellations = exportAllCancellations;
        window.editReason = editReason;
        window.analyzeReason = analyzeReason;
        window.deleteReason = deleteReason;

        console.log('🚫 تم تحميل نظام إدارة أسباب الإلغاء الجديدة بنجاح!');
    </script>
</body>
</html>
