<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أسباب الإلغاء الجديدة</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .data-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #dc3545;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
            margin-left: 15px;
        }

        .card-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .card-details {
            margin: 15px 0;
        }

        .card-detail {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            border-color: #dc3545;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .back-btn {
                justify-content: center;
                width: 100%;
            }

            .header h1 {
                font-size: 2rem;
            }

            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                    <span>🏠</span>
                    <span>العودة للوحة التحكم</span>
                    <span>←</span>
                </a>
                <div class="header-actions">
                    <button class="btn btn-info btn-sm" onclick="window.location.reload()">
                        <span>🔄</span> تحديث الصفحة
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportAllCancellations()">
                        <span>📥</span> تصدير البيانات
                    </button>
                </div>
            </div>
            <h1>🚫 إدارة أسباب الإلغاء الجديدة</h1>
            <p>نظام متطور لتتبع وتحليل أسباب إلغاء الشحنات وتحسين الخدمة</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="section-header">
                <h2 class="section-title">🚫 إدارة أسباب الإلغاء</h2>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="addCancellationReason()">
                        <span>➕</span> إضافة سبب جديد
                    </button>
                    <button class="btn btn-warning" onclick="generateReport()">
                        <span>📊</span> تقرير تحليلي
                    </button>
                    <button class="btn btn-info" onclick="refreshData()">
                        <span>🔄</span> تحديث
                    </button>
                    <button class="btn btn-success" onclick="exportData()">
                        <span>📤</span> تصدير
                    </button>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalCancellations">0</div>
                    <div class="stat-label">إجمالي الإلغاءات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="todayCancellations">0</div>
                    <div class="stat-label">إلغاءات اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="cancellationRate">0%</div>
                    <div class="stat-label">معدل الإلغاء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="topReason">-</div>
                    <div class="stat-label">السبب الأكثر شيوعاً</div>
                </div>
            </div>
            
            <div id="cancellationGrid" class="data-grid">
                <div class="empty-state">
                    <div class="empty-state-icon">🚫</div>
                    <h3>لا توجد أسباب إلغاء مسجلة</h3>
                    <p>ابدأ بإضافة أسباب الإلغاء لتتبع وتحليل البيانات</p>
                    <button class="btn btn-primary" onclick="addCancellationReason()">➕ إضافة سبب إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // متغيرات عامة
        let cancellationReasons = [];

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚫 تحميل نظام إدارة أسباب الإلغاء الجديدة...');
            initializeCancellations();
        });

        // تهيئة النظام
        function initializeCancellations() {
            loadDefaultCancellations();
            loadCancellations();
            updateStatistics();
            console.log('✅ تم تحميل نظام إدارة أسباب الإلغاء بنجاح');
        }

        // تحميل البيانات الافتراضية
        function loadDefaultCancellations() {
            if (!localStorage.getItem('new_cancellations')) {
                const defaultCancellations = [
                    {
                        id: 'CAN001',
                        reason: 'عدم توفر العنوان',
                        category: 'مشكلة في العنوان',
                        count: 25,
                        percentage: 35,
                        impact: 'عالي',
                        solution: 'تحسين نظام التحقق من العناوين',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'CAN002',
                        reason: 'رفض العميل استلام الطلب',
                        category: 'قرار العميل',
                        count: 18,
                        percentage: 25,
                        impact: 'متوسط',
                        solution: 'تحسين التواصل مع العملاء',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'CAN003',
                        reason: 'تأخير في التوصيل',
                        category: 'مشكلة في الخدمة',
                        count: 15,
                        percentage: 21,
                        impact: 'عالي',
                        solution: 'تحسين جدولة التوصيل',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'CAN004',
                        reason: 'مشكلة في المنتج',
                        category: 'جودة المنتج',
                        count: 12,
                        percentage: 17,
                        impact: 'متوسط',
                        solution: 'تحسين فحص المنتجات',
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('new_cancellations', JSON.stringify(defaultCancellations));
                console.log('✅ تم إنشاء بيانات أسباب الإلغاء الافتراضية');
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalCancellationsEl = document.getElementById('totalCancellations');
            const todayCancellationsEl = document.getElementById('todayCancellations');
            const cancellationRateEl = document.getElementById('cancellationRate');
            const topReasonEl = document.getElementById('topReason');

            const totalCount = cancellationReasons.reduce((sum, reason) => sum + (reason.count || 0), 0);
            const topReason = cancellationReasons.length > 0 ? 
                cancellationReasons.reduce((prev, current) => (prev.count > current.count) ? prev : current) : null;

            if (totalCancellationsEl) totalCancellationsEl.textContent = totalCount;
            if (todayCancellationsEl) todayCancellationsEl.textContent = Math.floor(totalCount * 0.1); // تقدير
            if (cancellationRateEl) cancellationRateEl.textContent = totalCount > 0 ? '12%' : '0%'; // تقدير
            if (topReasonEl) topReasonEl.textContent = topReason ? topReason.reason.substring(0, 15) + '...' : '-';
        }

        // وظائف أسباب الإلغاء
        function addCancellationReason() {
            alert('🚫 سيتم فتح نموذج إضافة سبب إلغاء جديد...');
            console.log('➕ إضافة سبب إلغاء جديد');
        }

        function generateReport() {
            alert('📊 سيتم إنشاء تقرير تحليلي شامل لأسباب الإلغاء...');
            console.log('📊 إنشاء تقرير تحليلي');
        }

        function refreshData() {
            loadCancellations();
            updateStatistics();
            alert('✅ تم تحديث بيانات أسباب الإلغاء!');
        }

        function exportData() {
            const dataStr = JSON.stringify(cancellationReasons, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `cancellation_reasons_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            alert('✅ تم تصدير بيانات أسباب الإلغاء بنجاح!');
            console.log('✅ تم تصدير بيانات أسباب الإلغاء');
        }

        function exportAllCancellations() {
            exportData();
        }

        function loadCancellations() {
            try {
                cancellationReasons = JSON.parse(localStorage.getItem('new_cancellations') || '[]');
                displayCancellations();
                console.log(`🚫 تم تحميل ${cancellationReasons.length} سبب إلغاء`);
            } catch (error) {
                console.error('❌ خطأ في تحميل أسباب الإلغاء:', error);
                cancellationReasons = [];
            }
        }

        function displayCancellations() {
            const grid = document.getElementById('cancellationGrid');
            if (!grid) return;

            if (cancellationReasons.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🚫</div>
                        <h3>لا توجد أسباب إلغاء مسجلة</h3>
                        <p>ابدأ بإضافة أسباب الإلغاء لتتبع وتحليل البيانات</p>
                        <button class="btn btn-primary" onclick="addCancellationReason()">➕ إضافة سبب إلغاء</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = cancellationReasons.map(reason => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-icon">🚫</div>
                        <div class="card-info">
                            <h3>${reason.reason}</h3>
                            <p>${reason.category}</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>📊 عدد الحالات:</span>
                            <span>${reason.count}</span>
                        </div>
                        <div class="card-detail">
                            <span>📈 النسبة:</span>
                            <span>${reason.percentage}%</span>
                        </div>
                        <div class="card-detail">
                            <span>⚠️ مستوى التأثير:</span>
                            <span style="color: ${reason.impact === 'عالي' ? '#dc3545' : reason.impact === 'متوسط' ? '#ffc107' : '#28a745'}">${reason.impact}</span>
                        </div>
                        <div class="card-detail">
                            <span>💡 الحل المقترح:</span>
                            <span>${reason.solution}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-info btn-sm" onclick="editReason('${reason.id}')">✏️ تعديل</button>
                        <button class="btn btn-warning btn-sm" onclick="analyzeReason('${reason.id}')">📊 تحليل</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteReason('${reason.id}')">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        function editReason(id) {
            alert(`✏️ سيتم فتح نموذج تعديل السبب: ${id}`);
        }

        function analyzeReason(id) {
            alert(`📊 سيتم عرض تحليل مفصل للسبب: ${id}`);
        }

        function deleteReason(id) {
            if (confirm('هل أنت متأكد من حذف هذا السبب؟')) {
                cancellationReasons = cancellationReasons.filter(reason => reason.id !== id);
                localStorage.setItem('new_cancellations', JSON.stringify(cancellationReasons));
                displayCancellations();
                updateStatistics();
                alert('✅ تم حذف السبب بنجاح!');
            }
        }

        // جعل الوظائف متاحة عالمياً
        window.addCancellationReason = addCancellationReason;
        window.generateReport = generateReport;
        window.refreshData = refreshData;
        window.exportData = exportData;
        window.exportAllCancellations = exportAllCancellations;
        window.editReason = editReason;
        window.analyzeReason = analyzeReason;
        window.deleteReason = deleteReason;

        console.log('🚫 تم تحميل نظام إدارة أسباب الإلغاء الجديدة بنجاح!');
    </script>
</body>
</html>
