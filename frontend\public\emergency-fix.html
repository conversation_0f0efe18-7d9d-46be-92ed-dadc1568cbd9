<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح طارئ - حل فوري</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
            min-height: 100vh;
            direction: rtl;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        .header {
            margin-bottom: 30px;
        }
        .header h1 {
            color: #ff4757;
            font-size: 3rem;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .emergency-btn {
            background: linear-gradient(135deg, #ff4757, #ff3838);
            color: white;
            border: none;
            padding: 25px 50px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 20px 0;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 71, 87, 0.4);
        }
        .emergency-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 71, 87, 0.6);
        }
        .status {
            margin: 20px 0;
            padding: 20px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 1.2rem;
        }
        .status-info {
            background: #e3f2fd;
            color: #1565c0;
            border: 3px solid #2196f3;
        }
        .status-success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 3px solid #4caf50;
        }
        .status-error {
            background: #ffebee;
            color: #c62828;
            border: 3px solid #f44336;
        }
        .progress-bar {
            width: 100%;
            height: 30px;
            background: #f0f0f0;
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
            border: 2px solid #ddd;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4caf50, #45a049);
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .quick-links {
            margin-top: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .quick-link {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .quick-link:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #ff4757;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 إصلاح طارئ</h1>
            <p style="font-size: 1.2rem; color: #666;">حل فوري لجميع مشاكل قاعدة البيانات</p>
        </div>

        <div id="status" class="status status-error">
            ❌ تم اكتشاف مشاكل في قاعدة البيانات<br>
            "db is not defined" - "getAllDistributors is not a function"
        </div>

        <button id="emergencyBtn" class="emergency-btn" onclick="startEmergencyFix()">
            🔧 إصلاح فوري - اضغط هنا الآن!
        </button>

        <div class="progress-bar" style="display: none;" id="progressContainer">
            <div id="progressFill" class="progress-fill">0%</div>
        </div>

        <div id="spinner" class="spinner" style="display: none;"></div>

        <div class="quick-links">
            <a href="main-dashboard.html" class="quick-link">🏠 لوحة التحكم</a>
            <a href="distributors-management.html" class="quick-link">👨‍💼 إدارة المناديب</a>
            <a href="test-database-simple.html" class="quick-link">🧪 اختبار النظام</a>
            <a href="fix-all-database-issues.html" class="quick-link">🛠️ أدوات متقدمة</a>
        </div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const spinner = document.getElementById('spinner');
        const emergencyBtn = document.getElementById('emergencyBtn');

        function updateStatus(message, type = 'info') {
            statusDiv.className = `status status-${type}`;
            statusDiv.innerHTML = message;
        }

        function updateProgress(percentage) {
            progressFill.style.width = percentage + '%';
            progressFill.textContent = Math.round(percentage) + '%';
        }

        function startEmergencyFix() {
            emergencyBtn.style.display = 'none';
            progressContainer.style.display = 'block';
            spinner.style.display = 'block';
            
            updateStatus('🚨 بدء الإصلاح الطارئ...', 'error');
            updateProgress(0);

            // الخطوة 1: مسح البيانات التالفة
            setTimeout(() => {
                updateProgress(20);
                updateStatus('🗑️ مسح البيانات التالفة...', 'info');
                clearCorruptedData();
            }, 1000);

            // الخطوة 2: إعادة تحميل قاعدة البيانات
            setTimeout(() => {
                updateProgress(40);
                updateStatus('🔄 إعادة تحميل قاعدة البيانات...', 'info');
                reloadDatabase();
            }, 2000);

            // الخطوة 3: إنشاء البيانات الافتراضية
            setTimeout(() => {
                updateProgress(60);
                updateStatus('📊 إنشاء البيانات الافتراضية...', 'info');
                createDefaultData();
            }, 3000);

            // الخطوة 4: اختبار النظام
            setTimeout(() => {
                updateProgress(80);
                updateStatus('🧪 اختبار النظام...', 'info');
                testSystem();
            }, 4000);

            // الخطوة 5: اكتمال الإصلاح
            setTimeout(() => {
                updateProgress(100);
                spinner.style.display = 'none';
                updateStatus('✅ تم الإصلاح بنجاح! النظام جاهز للاستخدام.', 'success');
                
                setTimeout(() => {
                    if (confirm('هل تريد فتح لوحة التحكم للتأكد من العمل؟')) {
                        window.open('main-dashboard.html', '_blank');
                    }
                }, 2000);
            }, 5000);
        }

        function clearCorruptedData() {
            try {
                // مسح البيانات التالفة
                const keysToCheck = ['shipments', 'users', 'roles', 'customers', 'distributors', 'transfers', 'branches'];
                keysToCheck.forEach(key => {
                    try {
                        const data = localStorage.getItem(key);
                        if (data) {
                            JSON.parse(data); // اختبار صحة JSON
                        }
                    } catch (error) {
                        console.log(`مسح البيانات التالفة: ${key}`);
                        localStorage.removeItem(key);
                    }
                });
                
                // إعداد العلامات
                localStorage.setItem('useSimpleDatabase', 'true');
                localStorage.setItem('emergencyFixApplied', 'true');
                localStorage.setItem('fixTimestamp', new Date().toISOString());
                
            } catch (error) {
                console.error('خطأ في مسح البيانات التالفة:', error);
            }
        }

        function reloadDatabase() {
            try {
                // إعادة تحميل قاعدة البيانات المبسطة
                const script = document.createElement('script');
                script.src = 'js/database-simple.js';
                script.onload = function() {
                    console.log('✅ تم إعادة تحميل قاعدة البيانات');
                };
                document.head.appendChild(script);
                
            } catch (error) {
                console.error('خطأ في إعادة تحميل قاعدة البيانات:', error);
            }
        }

        function createDefaultData() {
            try {
                // إنشاء بيانات افتراضية شاملة
                
                // الشحنات
                if (!localStorage.getItem('shipments')) {
                    const shipments = [
                        {
                            id: 'SHP001',
                            trackingNumber: 'SHP001',
                            senderName: 'أحمد محمد',
                            receiverName: 'فاطمة أحمد',
                            status: 'في الطريق'
                        }
                    ];
                    localStorage.setItem('shipments', JSON.stringify(shipments));
                }

                // المناديب
                if (!localStorage.getItem('distributors')) {
                    const distributors = [
                        {
                            id: 'DIST001',
                            name: 'أحمد محمد السعد',
                            phone: '+966501234567',
                            area: 'الرياض',
                            status: 'متاح'
                        },
                        {
                            id: 'DIST002',
                            name: 'محمد علي الزهراني',
                            phone: '+966507654321',
                            area: 'جدة',
                            status: 'مشغول'
                        }
                    ];
                    localStorage.setItem('distributors', JSON.stringify(distributors));
                }

                // المستخدمين
                if (!localStorage.getItem('users')) {
                    const users = [
                        {
                            id: 'user1',
                            name: 'أحمد محمد',
                            email: '<EMAIL>',
                            role: 'admin'
                        }
                    ];
                    localStorage.setItem('users', JSON.stringify(users));
                }

                // التحويلات
                if (!localStorage.getItem('transfers')) {
                    const transfers = [
                        {
                            id: 'TRANS001',
                            fromBranch: 'الرياض',
                            toBranch: 'جدة',
                            status: 'مكتمل'
                        }
                    ];
                    localStorage.setItem('transfers', JSON.stringify(transfers));
                }

                console.log('✅ تم إنشاء جميع البيانات الافتراضية');
                
            } catch (error) {
                console.error('خطأ في إنشاء البيانات الافتراضية:', error);
            }
        }

        function testSystem() {
            try {
                // اختبار وجود البيانات
                const shipments = localStorage.getItem('shipments');
                const distributors = localStorage.getItem('distributors');
                const users = localStorage.getItem('users');
                const transfers = localStorage.getItem('transfers');

                if (shipments && distributors && users && transfers) {
                    console.log('✅ جميع البيانات موجودة ومتاحة');
                    return true;
                } else {
                    console.warn('⚠️ بعض البيانات مفقودة');
                    return false;
                }
                
            } catch (error) {
                console.error('خطأ في اختبار النظام:', error);
                return false;
            }
        }

        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // فحص سريع للمشكلة
            const hasDistributors = localStorage.getItem('distributors');
            const hasTransfers = localStorage.getItem('transfers');
            
            if (hasDistributors && hasTransfers) {
                updateStatus('ℹ️ تم اكتشاف بيانات موجودة. قد تحتاج لإعادة تحميل فقط.', 'info');
                emergencyBtn.innerHTML = '🔄 إعادة تحميل وإصلاح';
            } else {
                updateStatus('❌ بيانات مفقودة أو تالفة. يحتاج إصلاح شامل.', 'error');
                emergencyBtn.innerHTML = '🔧 إصلاح شامل فوري';
            }
        });
    </script>
</body>
</html>
