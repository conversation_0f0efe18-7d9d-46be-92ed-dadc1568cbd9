<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - نظام الموارد البشرية</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { transform: translateY(-2px); opacity: 0.9; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: monospace; max-height: 200px; overflow-y: auto; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شامل - نظام الموارد البشرية</h1>
        <p>اختبار جميع الوظائف والأقسام في نظام الموارد البشرية</p>

        <div style="margin: 20px 0;">
            <a href="hr-management.html" class="btn btn-primary" target="_blank">🏢 فتح نظام الموارد البشرية</a>
            <button class="btn btn-success" onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
            <button class="btn btn-info" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>

        <div class="test-grid">
            <!-- اختبار الموظفين -->
            <div class="test-section">
                <h3>👨‍💼 اختبار الموظفين</h3>
                <button class="btn btn-success" onclick="testEmployees()">🧪 اختبار الموظفين</button>
                <button class="btn btn-warning" onclick="testEmployeeButtons()">🔘 اختبار الأزرار</button>
                <div id="employeeResults"></div>
            </div>

            <!-- اختبار المناديب -->
            <div class="test-section">
                <h3>🚚 اختبار المناديب</h3>
                <button class="btn btn-success" onclick="testDistributors()">🧪 اختبار المناديب</button>
                <button class="btn btn-warning" onclick="testDistributorButtons()">🔘 اختبار الأزرار</button>
                <div id="distributorResults"></div>
            </div>

            <!-- اختبار الأقسام -->
            <div class="test-section">
                <h3>🏢 اختبار الأقسام</h3>
                <button class="btn btn-success" onclick="testDepartments()">🧪 اختبار الأقسام</button>
                <button class="btn btn-warning" onclick="testDepartmentButtons()">🔘 اختبار الأزرار</button>
                <div id="departmentResults"></div>
            </div>

            <!-- اختبار السيارات -->
            <div class="test-section">
                <h3>🚗 اختبار السيارات</h3>
                <button class="btn btn-success" onclick="testVehicles()">🧪 اختبار السيارات</button>
                <button class="btn btn-warning" onclick="testVehicleButtons()">🔘 اختبار الأزرار</button>
                <div id="vehicleResults"></div>
            </div>

            <!-- اختبار الحضور -->
            <div class="test-section">
                <h3>⏰ اختبار الحضور والانصراف</h3>
                <button class="btn btn-success" onclick="testAttendance()">🧪 اختبار الحضور</button>
                <button class="btn btn-warning" onclick="testAttendanceButtons()">🔘 اختبار الأزرار</button>
                <div id="attendanceResults"></div>
            </div>

            <!-- اختبار الرواتب -->
            <div class="test-section">
                <h3>💰 اختبار الرواتب</h3>
                <button class="btn btn-success" onclick="testPayroll()">🧪 اختبار الرواتب</button>
                <button class="btn btn-warning" onclick="testPayrollButtons()">🔘 اختبار الأزرار</button>
                <div id="payrollResults"></div>
            </div>

            <!-- اختبار التكامل -->
            <div class="test-section">
                <h3>🔗 اختبار التكامل</h3>
                <button class="btn btn-success" onclick="testIntegration()">🧪 اختبار التكامل</button>
                <button class="btn btn-warning" onclick="testIntegrationButtons()">🔘 اختبار الأزرار</button>
                <div id="integrationResults"></div>
            </div>
        </div>

        <div class="test-section" style="margin-top: 20px;">
            <h3>📊 نتائج الاختبار الشامل</h3>
            <div id="overallResults"></div>
        </div>

        <div class="test-section">
            <h3>📝 سجل الاختبارات</h3>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        let testResults = {};
        let logContainer = null;

        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('testLog');
            log('✅ تم تحميل صفحة الاختبار الشامل');
        });

        function log(message) {
            if (logContainer) {
                const timestamp = new Date().toLocaleTimeString('ar-SA');
                logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            console.log(message);
        }

        function showResult(containerId, testName, passed, message) {
            const container = document.getElementById(containerId);
            if (container) {
                const resultClass = passed ? 'pass' : 'fail';
                const icon = passed ? '✅' : '❌';
                container.innerHTML += `<div class="test-result ${resultClass}">${icon} ${testName}: ${message}</div>`;
            }

            testResults[testName] = { passed, message };
            log(`${passed ? '✅' : '❌'} ${testName}: ${message}`);
        }

        function clearResults() {
            const resultContainers = [
                'employeeResults', 'distributorResults', 'departmentResults',
                'vehicleResults', 'attendanceResults', 'payrollResults',
                'integrationResults', 'overallResults'
            ];

            resultContainers.forEach(id => {
                const container = document.getElementById(id);
                if (container) container.innerHTML = '';
            });

            testResults = {};
            if (logContainer) logContainer.innerHTML = '';
            log('🗑️ تم مسح جميع النتائج');
        }

        function testEmployees() {
            log('🧪 بدء اختبار الموظفين...');

            try {
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');
                showResult('employeeResults', 'بيانات الموظفين', employees.length > 0, `تم العثور على ${employees.length} موظف`);

                // اختبار صحة البيانات
                let validEmployees = 0;
                employees.forEach(emp => {
                    if (emp.id && emp.name && emp.email) validEmployees++;
                });

                showResult('employeeResults', 'صحة البيانات', validEmployees === employees.length, `${validEmployees}/${employees.length} موظف صالح`);

            } catch (error) {
                showResult('employeeResults', 'خطأ في الاختبار', false, error.message);
            }
        }

        function testEmployeeButtons() {
            log('🔘 اختبار أزرار الموظفين...');

            // فتح نافذة الموارد البشرية
            const hrWindow = window.open('hr-management.html', 'hrTest');

            setTimeout(() => {
                try {
                    // اختبار وجود الوظائف
                    const functions = ['addEmployee', 'exportEmployees', 'searchEmployees'];
                    let passedTests = 0;

                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('employeeResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('employeeResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });

                    showResult('employeeResults', 'إجمالي الوظائف', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);

                    hrWindow.close();
                } catch (error) {
                    showResult('employeeResults', 'خطأ في اختبار الأزرار', false, error.message);
                }
            }, 2000);
        }

        function testDistributors() {
            log('🧪 بدء اختبار المناديب...');

            try {
                const distributors = JSON.parse(localStorage.getItem('distributors') || '[]');
                showResult('distributorResults', 'بيانات المناديب', distributors.length >= 0, `تم العثور على ${distributors.length} مندوب`);

            } catch (error) {
                showResult('distributorResults', 'خطأ في الاختبار', false, error.message);
            }
        }

        function testDistributorButtons() {
            log('🔘 اختبار أزرار المناديب...');

            const hrWindow = window.open('hr-management.html', 'hrTest');

            setTimeout(() => {
                try {
                    const functions = ['addDistributor', 'exportDistributors', 'searchDistributors'];
                    let passedTests = 0;

                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('distributorResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('distributorResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });

                    showResult('distributorResults', 'إجمالي الوظائف', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);

                    hrWindow.close();
                } catch (error) {
                    showResult('distributorResults', 'خطأ في اختبار الأزرار', false, error.message);
                }
            }, 2000);
        }

        function testDepartments() {
            log('🧪 بدء اختبار الأقسام...');

            try {
                const departments = JSON.parse(localStorage.getItem('departments') || '[]');
                showResult('departmentResults', 'بيانات الأقسام', departments.length >= 0, `تم العثور على ${departments.length} قسم`);

            } catch (error) {
                showResult('departmentResults', 'خطأ في الاختبار', false, error.message);
            }
        }

        function testDepartmentButtons() {
            log('🔘 اختبار أزرار الأقسام...');

            const hrWindow = window.open('hr-management.html', 'hrTest');

            setTimeout(() => {
                try {
                    const functions = ['addDepartment', 'exportDepartments', 'searchDepartments'];
                    let passedTests = 0;

                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('departmentResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('departmentResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });

                    showResult('departmentResults', 'إجمالي الوظائف', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);

                    hrWindow.close();
                } catch (error) {
                    showResult('departmentResults', 'خطأ في اختبار الأزرار', false, error.message);
                }
            }, 2000);
        }

        function testVehicles() {
            log('🧪 بدء اختبار السيارات...');

            try {
                const vehicles = JSON.parse(localStorage.getItem('vehicles') || '[]');
                showResult('vehicleResults', 'بيانات السيارات', vehicles.length >= 0, `تم العثور على ${vehicles.length} سيارة`);

            } catch (error) {
                showResult('vehicleResults', 'خطأ في الاختبار', false, error.message);
            }
        }

        function testVehicleButtons() {
            log('🔘 اختبار أزرار السيارات...');

            const hrWindow = window.open('hr-management.html', 'hrTest');

            setTimeout(() => {
                try {
                    const functions = ['addVehicle', 'exportVehicles', 'searchVehicles'];
                    let passedTests = 0;

                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('vehicleResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('vehicleResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });

                    showResult('vehicleResults', 'إجمالي الوظائف', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);

                    hrWindow.close();
                } catch (error) {
                    showResult('vehicleResults', 'خطأ في اختبار الأزرار', false, error.message);
                }
            }, 2000);
        }

        function testAttendance() {
            log('🧪 بدء اختبار الحضور والانصراف...');

            try {
                const attendance = JSON.parse(localStorage.getItem('attendance') || '[]');
                showResult('attendanceResults', 'بيانات الحضور', attendance.length >= 0, `تم العثور على ${attendance.length} سجل حضور`);

            } catch (error) {
                showResult('attendanceResults', 'خطأ في الاختبار', false, error.message);
            }
        }

        function testAttendanceButtons() {
            log('🔘 اختبار أزرار الحضور...');

            const hrWindow = window.open('hr-management.html', 'hrTest');

            setTimeout(() => {
                try {
                    const functions = ['markAttendance', 'markLeave'];
                    let passedTests = 0;

                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('attendanceResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('attendanceResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });

                    showResult('attendanceResults', 'إجمالي الوظائف', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);

                    hrWindow.close();
                } catch (error) {
                    showResult('attendanceResults', 'خطأ في اختبار الأزرار', false, error.message);
                }
            }, 2000);
        }

        function testPayroll() {
            log('🧪 بدء اختبار الرواتب...');

            try {
                const salaries = JSON.parse(localStorage.getItem('salaries') || '[]');
                showResult('payrollResults', 'بيانات الرواتب', salaries.length >= 0, `تم العثور على ${salaries.length} سجل راتب`);

            } catch (error) {
                showResult('payrollResults', 'خطأ في الاختبار', false, error.message);
            }
        }

        function testPayrollButtons() {
            log('🔘 اختبار أزرار الرواتب...');

            const hrWindow = window.open('hr-management.html', 'hrTest');

            setTimeout(() => {
                try {
                    const functions = ['generatePayroll', 'viewPayrollHistory'];
                    let passedTests = 0;

                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('payrollResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('payrollResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });

                    showResult('payrollResults', 'إجمالي الوظائف', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);

                    hrWindow.close();
                } catch (error) {
                    showResult('payrollResults', 'خطأ في اختبار الأزرار', false, error.message);
                }
            }, 2000);
        }

        function testIntegration() {
            log('🧪 بدء اختبار التكامل...');

            try {
                const lastSync = JSON.parse(localStorage.getItem('lastSync') || '{}');
                showResult('integrationResults', 'بيانات التكامل', Object.keys(lastSync).length > 0, 'تم العثور على بيانات المزامنة');

            } catch (error) {
                showResult('integrationResults', 'خطأ في الاختبار', false, error.message);
            }
        }

        function testIntegrationButtons() {
            log('🔘 اختبار أزرار التكامل...');

            const hrWindow = window.open('hr-management.html', 'hrTest');

            setTimeout(() => {
                try {
                    const functions = ['syncWithMainSystem', 'testConnections', 'loadIntegrationData'];
                    let passedTests = 0;

                    functions.forEach(funcName => {
                        if (typeof hrWindow[funcName] === 'function') {
                            showResult('integrationResults', `وظيفة ${funcName}`, true, 'موجودة ومتاحة');
                            passedTests++;
                        } else {
                            showResult('integrationResults', `وظيفة ${funcName}`, false, 'غير موجودة');
                        }
                    });

                    showResult('integrationResults', 'إجمالي الوظائف', passedTests === functions.length, `${passedTests}/${functions.length} وظيفة تعمل`);

                    hrWindow.close();
                } catch (error) {
                    showResult('integrationResults', 'خطأ في اختبار الأزرار', false, error.message);
                }
            }, 2000);
        }

        function runAllTests() {
            log('🚀 بدء تشغيل جميع الاختبارات...');
            clearResults();

            // تشغيل جميع الاختبارات
            testEmployees();
            testDistributors();
            testDepartments();
            testVehicles();
            testAttendance();
            testPayroll();
            testIntegration();

            // تشغيل اختبارات الأزرار بتأخير
            setTimeout(() => {
                testEmployeeButtons();
                testDistributorButtons();
                testDepartmentButtons();
                testVehicleButtons();
                testAttendanceButtons();
                testPayrollButtons();
                testIntegrationButtons();

                // عرض النتائج الإجمالية
                setTimeout(() => {
                    showOverallResults();
                }, 5000);
            }, 1000);
        }

        function showOverallResults() {
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result.passed).length;
            const percentage = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

            const overallContainer = document.getElementById('overallResults');
            if (overallContainer) {
                overallContainer.innerHTML = `
                    <div class="status status-info">
                        <h4>📊 نتائج الاختبار الشامل</h4>
                        <p><strong>إجمالي الاختبارات:</strong> ${totalTests}</p>
                        <p><strong>الاختبارات الناجحة:</strong> ${passedTests}</p>
                        <p><strong>الاختبارات الفاشلة:</strong> ${totalTests - passedTests}</p>
                        <p><strong>نسبة النجاح:</strong> ${percentage}%</p>
                        <p><strong>الحالة العامة:</strong> ${percentage >= 80 ? '✅ ممتاز' : percentage >= 60 ? '⚠️ جيد' : '❌ يحتاج تحسين'}</p>
                    </div>
                `;
            }

            log(`📊 النتائج الإجمالية: ${passedTests}/${totalTests} (${percentage}%)`);
        }
    </script>
</body>
</html>