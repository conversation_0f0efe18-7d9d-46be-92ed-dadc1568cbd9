<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الموارد البشرية المتكامل</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }

        .tabs-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .tab-icon {
            font-size: 1.5rem;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-secondary { background: #6c757d; color: white; }

        .search-box {
            width: 100%;
            max-width: 400px;
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            margin-bottom: 20px;
            font-size: 1rem;
        }

        .search-box:focus {
            outline: none;
            border-color: #007bff;
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .data-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
            margin-left: 15px;
        }

        .card-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .card-details {
            margin: 15px 0;
        }

        .card-detail {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .modal-header h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin: 0;
        }

        .close {
            font-size: 2rem;
            cursor: pointer;
            color: #6c757d;
            transition: color 0.3s ease;
            background: none;
            border: none;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close:hover {
            color: #dc3545;
            background: #f8f9fa;
            border-radius: 50%;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            border-color: #007bff;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .tabs-nav {
                flex-direction: column;
            }
            
            .tab-button {
                flex-direction: row;
                justify-content: center;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🏢 نظام الموارد البشرية المتكامل</h1>
            <p>إدارة شاملة ومتطورة لجميع موارد الشركة البشرية</p>
        </div>

        <!-- Main Content -->
        <div class="tabs-container">
            <!-- Navigation Tabs -->
            <div class="tabs-nav">
                <button class="tab-button active" onclick="showTab('employees')">
                    <span class="tab-icon">👨‍💼</span>
                    <span>الموظفين</span>
                </button>
                <button class="tab-button" onclick="showTab('distributors')">
                    <span class="tab-icon">🚚</span>
                    <span>المناديب</span>
                </button>
                <button class="tab-button" onclick="showTab('departments')">
                    <span class="tab-icon">🏢</span>
                    <span>الأقسام</span>
                </button>
                <button class="tab-button" onclick="showTab('vehicles')">
                    <span class="tab-icon">🚗</span>
                    <span>السيارات</span>
                </button>
                <button class="tab-button" onclick="showTab('attendance')">
                    <span class="tab-icon">⏰</span>
                    <span>الحضور والانصراف</span>
                </button>
                <button class="tab-button" onclick="showTab('payroll')">
                    <span class="tab-icon">💰</span>
                    <span>الرواتب</span>
                </button>
                <button class="tab-button" onclick="showTab('integration')">
                    <span class="tab-icon">🔗</span>
                    <span>التكامل</span>
                </button>
            </div>

            <!-- Tab Contents -->
            
            <!-- الموظفين -->
            <div id="employees" class="tab-content active">
                <div class="section-header">
                    <h2 class="section-title">👨‍💼 إدارة الموظفين</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="addEmployee()">
                            <span>➕</span> إضافة موظف
                        </button>
                        <button class="btn btn-info" onclick="refreshEmployees()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportEmployees()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>
                
                <input type="text" class="search-box" id="employeeSearch" placeholder="🔍 البحث في الموظفين..." onkeyup="searchEmployees()">
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalEmployees">0</div>
                        <div class="stat-label">إجمالي الموظفين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeEmployees">0</div>
                        <div class="stat-label">الموظفين النشطين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalSalaries">0</div>
                        <div class="stat-label">إجمالي الرواتب</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalDepartments">0</div>
                        <div class="stat-label">عدد الأقسام</div>
                    </div>
                </div>
                
                <div id="employeesGrid" class="data-grid"></div>
            </div>

            <!-- المناديب -->
            <div id="distributors" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">🚚 إدارة المناديب</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="addDistributor()">
                            <span>➕</span> إضافة مندوب
                        </button>
                        <button class="btn btn-info" onclick="refreshDistributors()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportDistributors()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>

                <input type="text" class="search-box" id="distributorSearch" placeholder="🔍 البحث في المناديب..." onkeyup="searchDistributors()">
                <div id="distributorsGrid" class="data-grid"></div>
            </div>

            <!-- الأقسام -->
            <div id="departments" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">🏢 إدارة الأقسام</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="addDepartment()">
                            <span>➕</span> إضافة قسم
                        </button>
                        <button class="btn btn-info" onclick="refreshDepartments()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportDepartments()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>

                <input type="text" class="search-box" id="departmentSearch" placeholder="🔍 البحث في الأقسام..." onkeyup="searchDepartments()">
                <div id="departmentsGrid" class="data-grid"></div>
            </div>

            <!-- السيارات -->
            <div id="vehicles" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">🚗 إدارة السيارات</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="addVehicle()">
                            <span>➕</span> إضافة سيارة
                        </button>
                        <button class="btn btn-info" onclick="refreshVehicles()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportVehicles()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>

                <input type="text" class="search-box" id="vehicleSearch" placeholder="🔍 البحث في السيارات..." onkeyup="searchVehicles()">
                <div id="vehiclesGrid" class="data-grid"></div>
            </div>

            <!-- الحضور والانصراف -->
            <div id="attendance" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">⏰ الحضور والانصراف</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="markAttendance()">
                            <span>✅</span> تسجيل حضور
                        </button>
                        <button class="btn btn-warning" onclick="markLeave()">
                            <span>🚪</span> تسجيل انصراف
                        </button>
                        <button class="btn btn-info" onclick="refreshAttendance()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportAttendance()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>

                <div id="attendanceGrid" class="data-grid"></div>
            </div>

            <!-- الرواتب -->
            <div id="payroll" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">💰 إدارة الرواتب</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="generatePayroll()">
                            <span>💳</span> إنشاء كشف راتب
                        </button>
                        <button class="btn btn-warning" onclick="viewPayrollHistory()">
                            <span>📋</span> تاريخ الرواتب
                        </button>
                        <button class="btn btn-info" onclick="refreshPayroll()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportPayroll()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>

                <div id="payrollGrid" class="data-grid"></div>
            </div>

            <!-- التكامل -->
            <div id="integration" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">🔗 التكامل مع الأنظمة</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="syncWithMainSystem()">
                            <span>🔄</span> مزامنة البيانات
                        </button>
                        <button class="btn btn-info" onclick="testConnections()">
                            <span>🔍</span> اختبار الاتصالات
                        </button>
                        <button class="btn btn-success" onclick="refreshIntegration()">
                            <span>🔄</span> تحديث الحالة
                        </button>
                    </div>
                </div>

                <div id="integrationGrid" class="data-grid"></div>
            </div>
        </div>
    </div>

    <!-- تطبيق خط SF Pro Arabic Display Semibold -->
    <script src="js/sf-pro-arabic-font.js"></script>

    <script>
        // متغيرات عامة
        let employees = [];
        let distributors = [];
        let departments = [];
        let vehicles = [];
        let attendance = [];
        let payroll = [];

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏢 تحميل نظام الموارد البشرية...');
            initializeSystem();

            // تأكد من تحديث المتغيرات العامة
            window.employees = employees;
            window.distributors = distributors;
            window.departments = departments;
            window.vehicles = vehicles;
            window.attendance = attendance;
            window.payroll = payroll;
        });

        // تهيئة النظام
        function initializeSystem() {
            loadDefaultData();
            loadEmployees();
            loadDistributors();
            loadDepartments();
            loadVehicles();
            loadAttendance();
            loadPayroll();
            updateStatistics();
            console.log('✅ تم تحميل نظام الموارد البشرية بنجاح');
        }

        // تحميل البيانات الافتراضية
        function loadDefaultData() {
            // بيانات الموظفين الافتراضية
            if (!localStorage.getItem('hr_employees')) {
                const defaultEmployees = [
                    {
                        id: 'emp001',
                        name: 'أحمد محمد علي',
                        position: 'مطور برمجيات',
                        department: 'تقنية المعلومات',
                        phone: '0501234567',
                        email: '<EMAIL>',
                        salary: 8000,
                        status: 'نشط',
                        joinDate: '2023-01-15',
                        avatar: 'أ',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'emp002',
                        name: 'فاطمة سعد الدين',
                        position: 'محاسبة',
                        department: 'المحاسبة',
                        phone: '0507654321',
                        email: '<EMAIL>',
                        salary: 6500,
                        status: 'نشط',
                        joinDate: '2023-03-10',
                        avatar: 'ف',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'emp003',
                        name: 'محمد عبدالله',
                        position: 'مدير المبيعات',
                        department: 'المبيعات',
                        phone: '0509876543',
                        email: '<EMAIL>',
                        salary: 9500,
                        status: 'نشط',
                        joinDate: '2022-11-20',
                        avatar: 'م',
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('hr_employees', JSON.stringify(defaultEmployees));
                console.log('✅ تم إنشاء بيانات الموظفين الافتراضية');
            }

            // بيانات الأقسام الافتراضية
            if (!localStorage.getItem('hr_departments')) {
                const defaultDepartments = [
                    {
                        id: 'dept001',
                        name: 'تقنية المعلومات',
                        manager: 'أحمد محمد علي',
                        employeeCount: 5,
                        budget: 50000,
                        description: 'قسم تطوير البرمجيات والتقنية',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'dept002',
                        name: 'المحاسبة',
                        manager: 'فاطمة سعد الدين',
                        employeeCount: 3,
                        budget: 30000,
                        description: 'قسم المحاسبة والشؤون المالية',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'dept003',
                        name: 'المبيعات',
                        manager: 'محمد عبدالله',
                        employeeCount: 8,
                        budget: 40000,
                        description: 'قسم المبيعات وخدمة العملاء',
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('hr_departments', JSON.stringify(defaultDepartments));
                console.log('✅ تم إنشاء بيانات الأقسام الافتراضية');
            }

            // بيانات المناديب الافتراضية
            if (!localStorage.getItem('hr_distributors')) {
                const defaultDistributors = [
                    {
                        id: 'dist001',
                        name: 'خالد أحمد الزهراني',
                        phone: '0551234567',
                        area: 'الرياض الشمالية',
                        commission: 5,
                        totalDeliveries: 150,
                        status: 'نشط',
                        avatar: 'خ',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'dist002',
                        name: 'سعد محمد العتيبي',
                        phone: '0557654321',
                        area: 'الرياض الجنوبية',
                        commission: 5,
                        totalDeliveries: 120,
                        status: 'نشط',
                        avatar: 'س',
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('hr_distributors', JSON.stringify(defaultDistributors));
                console.log('✅ تم إنشاء بيانات المناديب الافتراضية');
            }

            // بيانات السيارات الافتراضية
            if (!localStorage.getItem('hr_vehicles')) {
                const defaultVehicles = [
                    {
                        id: 'veh001',
                        plateNumber: 'أ ب ج 1234',
                        model: 'تويوتا هايلكس',
                        type: 'بيك أب',
                        status: 'متاحة',
                        driverId: null,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'veh002',
                        plateNumber: 'د هـ و 5678',
                        model: 'نيسان نافارا',
                        type: 'بيك أب',
                        status: 'متاحة',
                        driverId: null,
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('hr_vehicles', JSON.stringify(defaultVehicles));
                console.log('✅ تم إنشاء بيانات السيارات الافتراضية');
            }
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = event.target.closest('.tab-button');
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            // تحميل البيانات حسب التبويب
            switch(tabName) {
                case 'employees':
                    loadEmployees();
                    break;
                case 'distributors':
                    loadDistributors();
                    break;
                case 'departments':
                    loadDepartments();
                    break;
                case 'vehicles':
                    loadVehicles();
                    break;
                case 'attendance':
                    loadAttendance();
                    break;
                case 'payroll':
                    loadPayroll();
                    break;
                case 'integration':
                    loadIntegration();
                    break;
            }

            console.log(`📋 تم التبديل إلى تبويب: ${tabName}`);
        }

        // وظائف تحميل البيانات
        function loadEmployees() {
            try {
                employees = JSON.parse(localStorage.getItem('hr_employees') || '[]');
                displayEmployees(employees);
                updateStatistics();
                console.log(`👥 تم تحميل ${employees.length} موظف`);
            } catch (error) {
                console.error('❌ خطأ في تحميل الموظفين:', error);
                employees = [];
            }
        }

        function loadDistributors() {
            try {
                distributors = JSON.parse(localStorage.getItem('hr_distributors') || '[]');
                displayDistributors(distributors);
                console.log(`🚚 تم تحميل ${distributors.length} مندوب`);
            } catch (error) {
                console.error('❌ خطأ في تحميل المناديب:', error);
                distributors = [];
            }
        }

        function loadDepartments() {
            try {
                departments = JSON.parse(localStorage.getItem('hr_departments') || '[]');
                displayDepartments(departments);
                console.log(`🏢 تم تحميل ${departments.length} قسم`);
            } catch (error) {
                console.error('❌ خطأ في تحميل الأقسام:', error);
                departments = [];
            }
        }

        function loadVehicles() {
            try {
                vehicles = JSON.parse(localStorage.getItem('hr_vehicles') || '[]');
                displayVehicles(vehicles);
                console.log(`🚗 تم تحميل ${vehicles.length} سيارة`);
            } catch (error) {
                console.error('❌ خطأ في تحميل السيارات:', error);
                vehicles = [];
            }
        }

        function loadAttendance() {
            try {
                attendance = JSON.parse(localStorage.getItem('hr_attendance') || '[]');
                displayAttendance(attendance);
                console.log(`⏰ تم تحميل ${attendance.length} سجل حضور`);
            } catch (error) {
                console.error('❌ خطأ في تحميل الحضور:', error);
                attendance = [];
            }
        }

        function loadPayroll() {
            try {
                payroll = JSON.parse(localStorage.getItem('hr_payroll') || '[]');
                displayPayroll(payroll);
                console.log(`💰 تم تحميل ${payroll.length} سجل راتب`);
            } catch (error) {
                console.error('❌ خطأ في تحميل الرواتب:', error);
                payroll = [];
            }
        }

        function loadIntegration() {
            displayIntegration();
            console.log('🔗 تم تحميل بيانات التكامل');
        }

        // وظائف عرض البيانات
        function displayEmployees(employeesList) {
            const grid = document.getElementById('employeesGrid');
            if (!grid) return;

            if (employeesList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">👥</div>
                        <h3>لا توجد موظفين</h3>
                        <p>ابدأ بإضافة موظف جديد</p>
                        <button class="btn btn-primary" onclick="addEmployee()">➕ إضافة موظف</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = employeesList.map(employee => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">${employee.avatar || employee.name.charAt(0)}</div>
                        <div class="card-info">
                            <h3>${employee.name}</h3>
                            <p>${employee.position} - ${employee.department}</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>📧 البريد:</span>
                            <span>${employee.email}</span>
                        </div>
                        <div class="card-detail">
                            <span>📱 الجوال:</span>
                            <span>${employee.phone}</span>
                        </div>
                        <div class="card-detail">
                            <span>💰 الراتب:</span>
                            <span>${employee.salary} ريال</span>
                        </div>
                        <div class="card-detail">
                            <span>📅 تاريخ التوظيف:</span>
                            <span>${employee.joinDate}</span>
                        </div>
                        <div class="card-detail">
                            <span>🔄 الحالة:</span>
                            <span style="color: ${employee.status === 'نشط' ? '#28a745' : '#dc3545'}">${employee.status}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-info btn-sm" onclick="editEmployee('${employee.id}')">✏️ تعديل</button>
                        <button class="btn btn-secondary btn-sm" onclick="viewEmployee('${employee.id}')">👁️ عرض</button>
                        <button class="btn btn-warning btn-sm" onclick="deleteEmployee('${employee.id}')">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        function displayDistributors(distributorsList) {
            const grid = document.getElementById('distributorsGrid');
            if (!grid) return;

            if (distributorsList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🚚</div>
                        <h3>لا توجد مناديب</h3>
                        <p>ابدأ بإضافة مندوب جديد</p>
                        <button class="btn btn-primary" onclick="addDistributor()">➕ إضافة مندوب</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = distributorsList.map(distributor => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">${distributor.avatar || distributor.name.charAt(0)}</div>
                        <div class="card-info">
                            <h3>${distributor.name}</h3>
                            <p>مندوب توصيل - ${distributor.area}</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>📱 الجوال:</span>
                            <span>${distributor.phone}</span>
                        </div>
                        <div class="card-detail">
                            <span>🗺️ المنطقة:</span>
                            <span>${distributor.area}</span>
                        </div>
                        <div class="card-detail">
                            <span>💰 العمولة:</span>
                            <span>${distributor.commission}%</span>
                        </div>
                        <div class="card-detail">
                            <span>📦 إجمالي التوصيلات:</span>
                            <span>${distributor.totalDeliveries}</span>
                        </div>
                        <div class="card-detail">
                            <span>🔄 الحالة:</span>
                            <span style="color: ${distributor.status === 'نشط' ? '#28a745' : '#dc3545'}">${distributor.status}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-info btn-sm" onclick="editDistributor('${distributor.id}')">✏️ تعديل</button>
                        <button class="btn btn-secondary btn-sm" onclick="viewDistributor('${distributor.id}')">👁️ عرض</button>
                        <button class="btn btn-warning btn-sm" onclick="deleteDistributor('${distributor.id}')">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        function displayDepartments(departmentsList) {
            const grid = document.getElementById('departmentsGrid');
            if (!grid) return;

            if (departmentsList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🏢</div>
                        <h3>لا توجد أقسام</h3>
                        <p>ابدأ بإضافة قسم جديد</p>
                        <button class="btn btn-primary" onclick="addDepartment()">➕ إضافة قسم</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = departmentsList.map(department => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">🏢</div>
                        <div class="card-info">
                            <h3>${department.name}</h3>
                            <p>مدير القسم: ${department.manager}</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>👥 عدد الموظفين:</span>
                            <span>${department.employeeCount}</span>
                        </div>
                        <div class="card-detail">
                            <span>💰 الميزانية:</span>
                            <span>${department.budget} ريال</span>
                        </div>
                        <div class="card-detail">
                            <span>📝 الوصف:</span>
                            <span>${department.description}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-info btn-sm" onclick="editDepartment('${department.id}')">✏️ تعديل</button>
                        <button class="btn btn-secondary btn-sm" onclick="viewDepartment('${department.id}')">👁️ عرض</button>
                        <button class="btn btn-warning btn-sm" onclick="deleteDepartment('${department.id}')">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        function displayVehicles(vehiclesList) {
            const grid = document.getElementById('vehiclesGrid');
            if (!grid) return;

            if (vehiclesList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🚗</div>
                        <h3>لا توجد سيارات</h3>
                        <p>ابدأ بإضافة سيارة جديدة</p>
                        <button class="btn btn-primary" onclick="addVehicle()">➕ إضافة سيارة</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = vehiclesList.map(vehicle => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">🚗</div>
                        <div class="card-info">
                            <h3>${vehicle.plateNumber}</h3>
                            <p>${vehicle.model} - ${vehicle.type}</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>🔄 الحالة:</span>
                            <span style="color: ${vehicle.status === 'متاحة' ? '#28a745' : '#dc3545'}">${vehicle.status}</span>
                        </div>
                        <div class="card-detail">
                            <span>👨‍✈️ السائق:</span>
                            <span>${vehicle.driverId || 'غير محدد'}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-info btn-sm" onclick="editVehicle('${vehicle.id}')">✏️ تعديل</button>
                        <button class="btn btn-secondary btn-sm" onclick="viewVehicle('${vehicle.id}')">👁️ عرض</button>
                        <button class="btn btn-warning btn-sm" onclick="deleteVehicle('${vehicle.id}')">🗑️ حذف</button>
                    </div>
                </div>
            `).join('');
        }

        function displayAttendance(attendanceList) {
            const grid = document.getElementById('attendanceGrid');
            if (!grid) return;

            if (attendanceList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">⏰</div>
                        <h3>لا توجد سجلات حضور</h3>
                        <p>ابدأ بتسجيل الحضور</p>
                        <button class="btn btn-primary" onclick="markAttendance()">✅ تسجيل حضور</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = attendanceList.map(record => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">⏰</div>
                        <div class="card-info">
                            <h3>${record.employeeName}</h3>
                            <p>${record.date}</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>🕐 وقت الحضور:</span>
                            <span>${record.checkIn || 'لم يسجل'}</span>
                        </div>
                        <div class="card-detail">
                            <span>🕕 وقت الانصراف:</span>
                            <span>${record.checkOut || 'لم يسجل'}</span>
                        </div>
                        <div class="card-detail">
                            <span>⏱️ ساعات العمل:</span>
                            <span>${record.workingHours || '0'} ساعة</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function displayPayroll(payrollList) {
            const grid = document.getElementById('payrollGrid');
            if (!grid) return;

            if (payrollList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">💰</div>
                        <h3>لا توجد سجلات رواتب</h3>
                        <p>ابدأ بإنشاء كشف راتب</p>
                        <button class="btn btn-primary" onclick="generatePayroll()">💳 إنشاء كشف راتب</button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = payrollList.map(record => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">💰</div>
                        <div class="card-info">
                            <h3>${record.employeeName}</h3>
                            <p>${record.month} ${record.year}</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>💰 الراتب الأساسي:</span>
                            <span>${record.basicSalary} ريال</span>
                        </div>
                        <div class="card-detail">
                            <span>➕ البدلات:</span>
                            <span>${record.allowances || 0} ريال</span>
                        </div>
                        <div class="card-detail">
                            <span>➖ الخصومات:</span>
                            <span>${record.deductions || 0} ريال</span>
                        </div>
                        <div class="card-detail">
                            <span>💳 الصافي:</span>
                            <span style="color: #28a745; font-weight: bold;">${record.netSalary} ريال</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-info btn-sm" onclick="viewPayslip('${record.id}')">📄 عرض الكشف</button>
                        <button class="btn btn-success btn-sm" onclick="printPayslip('${record.id}')">🖨️ طباعة</button>
                    </div>
                </div>
            `).join('');
        }

        function displayIntegration() {
            const grid = document.getElementById('integrationGrid');
            if (!grid) return;

            grid.innerHTML = `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">🔗</div>
                        <div class="card-info">
                            <h3>النظام الرئيسي</h3>
                            <p>حالة الاتصال: متصل</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>📊 آخر مزامنة:</span>
                            <span>${new Date().toLocaleString('ar-SA')}</span>
                        </div>
                        <div class="card-detail">
                            <span>📈 حالة البيانات:</span>
                            <span style="color: #28a745">محدثة</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary btn-sm" onclick="syncWithMainSystem()">🔄 مزامنة الآن</button>
                        <button class="btn btn-info btn-sm" onclick="testConnections()">🔍 اختبار الاتصال</button>
                    </div>
                </div>
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">🚚</div>
                        <div class="card-info">
                            <h3>نظام إدارة المناديب</h3>
                            <p>حالة الاتصال: متصل</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>📊 آخر مزامنة:</span>
                            <span>${new Date().toLocaleString('ar-SA')}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary btn-sm" onclick="openDistributorManagement()">🔗 فتح النظام</button>
                    </div>
                </div>
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">🚗</div>
                        <div class="card-info">
                            <h3>نظام إدارة السيارات</h3>
                            <p>حالة الاتصال: متصل</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>📊 آخر مزامنة:</span>
                            <span>${new Date().toLocaleString('ar-SA')}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary btn-sm" onclick="openVehicleManagement()">🔗 فتح النظام</button>
                    </div>
                </div>
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">💰</div>
                        <div class="card-info">
                            <h3>النظام المالي</h3>
                            <p>حالة الاتصال: متصل</p>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="card-detail">
                            <span>📊 آخر مزامنة:</span>
                            <span>${new Date().toLocaleString('ar-SA')}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary btn-sm" onclick="openFinancialSystem()">🔗 فتح النظام</button>
                    </div>
                </div>
            `;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalEmployeesEl = document.getElementById('totalEmployees');
            const activeEmployeesEl = document.getElementById('activeEmployees');
            const totalSalariesEl = document.getElementById('totalSalaries');
            const totalDepartmentsEl = document.getElementById('totalDepartments');

            if (totalEmployeesEl) totalEmployeesEl.textContent = employees.length;
            if (activeEmployeesEl) activeEmployeesEl.textContent = employees.filter(emp => emp.status === 'نشط').length;
            if (totalSalariesEl) totalSalariesEl.textContent = employees.reduce((sum, emp) => sum + (emp.salary || 0), 0).toLocaleString();
            if (totalDepartmentsEl) totalDepartmentsEl.textContent = departments.length;
        }

        // وظائف النماذج
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('show');
                modal.style.display = 'flex';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
                modal.style.display = 'none';

                // مسح النموذج
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();
                }
            }
        }

        // إغلاق النموذج عند النقر خارجه
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.classList.remove('show');
                event.target.style.display = 'none';
            }
        }

        // وظائف الموظفين
        function addEmployee() {
            // تعيين تاريخ اليوم كافتراضي
            document.getElementById('employeeJoinDate').value = new Date().toISOString().split('T')[0];
            openModal('addEmployeeModal');
        }

        // معالج إرسال نموذج إضافة الموظف
        document.addEventListener('DOMContentLoaded', function() {
            const addForm = document.getElementById('addEmployeeForm');
            if (addForm) {
                addForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const newEmployee = {
                        id: 'emp' + Date.now(),
                        name: document.getElementById('employeeName').value,
                        position: document.getElementById('employeePosition').value,
                        department: document.getElementById('employeeDepartment').value,
                        phone: document.getElementById('employeePhone').value,
                        email: document.getElementById('employeeEmail').value,
                        salary: parseInt(document.getElementById('employeeSalary').value),
                        joinDate: document.getElementById('employeeJoinDate').value,
                        status: document.getElementById('employeeStatus').value,
                        avatar: document.getElementById('employeeName').value.charAt(0),
                        createdAt: new Date().toISOString()
                    };

                    employees.push(newEmployee);
                    localStorage.setItem('hr_employees', JSON.stringify(employees));
                    displayEmployees(employees);
                    updateStatistics();

                    closeModal('addEmployeeModal');
                    alert('✅ تم إضافة الموظف بنجاح!');
                    console.log('✅ تم إضافة موظف جديد:', newEmployee.name);
                });
            }

            // معالج إرسال نموذج تعديل الموظف
            const editForm = document.getElementById('editEmployeeForm');
            if (editForm) {
                editForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const id = document.getElementById('editEmployeeId').value;
                    const employee = employees.find(emp => emp.id === id);

                    if (employee) {
                        employee.name = document.getElementById('editEmployeeName').value;
                        employee.position = document.getElementById('editEmployeePosition').value;
                        employee.department = document.getElementById('editEmployeeDepartment').value;
                        employee.phone = document.getElementById('editEmployeePhone').value;
                        employee.email = document.getElementById('editEmployeeEmail').value;
                        employee.salary = parseInt(document.getElementById('editEmployeeSalary').value);
                        employee.joinDate = document.getElementById('editEmployeeJoinDate').value;
                        employee.status = document.getElementById('editEmployeeStatus').value;
                        employee.avatar = employee.name.charAt(0);
                        employee.updatedAt = new Date().toISOString();

                        localStorage.setItem('hr_employees', JSON.stringify(employees));
                        displayEmployees(employees);
                        updateStatistics();

                        closeModal('editEmployeeModal');
                        alert('✅ تم تحديث بيانات الموظف بنجاح!');
                        console.log('✅ تم تحديث بيانات الموظف:', employee.name);
                    }
                });
            }
        });

        function editEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (!employee) {
                alert('❌ لم يتم العثور على الموظف');
                return;
            }

            // ملء النموذج ببيانات الموظف الحالية
            document.getElementById('editEmployeeId').value = employee.id;
            document.getElementById('editEmployeeName').value = employee.name;
            document.getElementById('editEmployeePosition').value = employee.position;
            document.getElementById('editEmployeeDepartment').value = employee.department;
            document.getElementById('editEmployeePhone').value = employee.phone;
            document.getElementById('editEmployeeEmail').value = employee.email;
            document.getElementById('editEmployeeSalary').value = employee.salary;
            document.getElementById('editEmployeeJoinDate').value = employee.joinDate;
            document.getElementById('editEmployeeStatus').value = employee.status;

            openModal('editEmployeeModal');
        }

        function deleteEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (!employee) {
                alert('❌ لم يتم العثور على الموظف');
                return;
            }

            if (confirm(`⚠️ هل أنت متأكد من حذف الموظف: ${employee.name}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                employees = employees.filter(emp => emp.id !== id);
                localStorage.setItem('hr_employees', JSON.stringify(employees));
                displayEmployees(employees);
                updateStatistics();

                alert('✅ تم حذف الموظف بنجاح!');
                console.log('✅ تم حذف الموظف:', employee.name);
            }
        }

        function viewEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (!employee) {
                alert('❌ لم يتم العثور على الموظف');
                return;
            }

            const createdDate = new Date(employee.createdAt).toLocaleDateString('ar-SA');
            const updatedDate = employee.updatedAt ? new Date(employee.updatedAt).toLocaleDateString('ar-SA') : 'لم يتم التحديث';

            alert(`
📋 تفاصيل الموظف الكاملة:

👤 الاسم: ${employee.name}
💼 المنصب: ${employee.position}
🏢 القسم: ${employee.department}
📱 الجوال: ${employee.phone}
📧 البريد: ${employee.email}
💰 الراتب: ${employee.salary.toLocaleString()} ريال
📅 تاريخ التوظيف: ${employee.joinDate}
🔄 الحالة: ${employee.status}
🆔 المعرف: ${employee.id}
📅 تاريخ الإنشاء: ${createdDate}
📅 آخر تحديث: ${updatedDate}
            `);
        }

        function refreshEmployees() {
            loadEmployees();
            alert('✅ تم تحديث بيانات الموظفين!');
        }

        function exportEmployees() {
            const dataStr = JSON.stringify(employees, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `employees_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('✅ تم تصدير بيانات الموظفين بنجاح!');
            console.log('✅ تم تصدير بيانات الموظفين');
        }

        function searchEmployees() {
            const searchTerm = document.getElementById('employeeSearch').value.toLowerCase();
            const filteredEmployees = employees.filter(employee =>
                employee.name.toLowerCase().includes(searchTerm) ||
                employee.position.toLowerCase().includes(searchTerm) ||
                employee.department.toLowerCase().includes(searchTerm) ||
                employee.email.toLowerCase().includes(searchTerm)
            );
            displayEmployees(filteredEmployees);
        }

        // وظائف المناديب
        function addDistributor() {
            openModal('addDistributorModal');
        }

        // إضافة معالجات نماذج المناديب
        document.addEventListener('DOMContentLoaded', function() {
            // معالج إضافة مندوب
            const addDistributorForm = document.getElementById('addDistributorForm');
            if (addDistributorForm) {
                addDistributorForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const newDistributor = {
                        id: 'dist' + Date.now(),
                        name: document.getElementById('distributorName').value,
                        phone: document.getElementById('distributorPhone').value,
                        area: document.getElementById('distributorArea').value,
                        commission: parseFloat(document.getElementById('distributorCommission').value),
                        totalDeliveries: 0,
                        status: document.getElementById('distributorStatus').value,
                        avatar: document.getElementById('distributorName').value.charAt(0),
                        createdAt: new Date().toISOString()
                    };

                    distributors.push(newDistributor);
                    localStorage.setItem('hr_distributors', JSON.stringify(distributors));
                    displayDistributors(distributors);

                    closeModal('addDistributorModal');
                    alert('✅ تم إضافة المندوب بنجاح!');
                    console.log('✅ تم إضافة مندوب جديد:', newDistributor.name);
                });
            }

            // معالج تعديل مندوب
            const editDistributorForm = document.getElementById('editDistributorForm');
            if (editDistributorForm) {
                editDistributorForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const id = document.getElementById('editDistributorId').value;
                    const distributor = distributors.find(dist => dist.id === id);

                    if (distributor) {
                        distributor.name = document.getElementById('editDistributorName').value;
                        distributor.phone = document.getElementById('editDistributorPhone').value;
                        distributor.area = document.getElementById('editDistributorArea').value;
                        distributor.commission = parseFloat(document.getElementById('editDistributorCommission').value);
                        distributor.status = document.getElementById('editDistributorStatus').value;
                        distributor.avatar = distributor.name.charAt(0);
                        distributor.updatedAt = new Date().toISOString();

                        localStorage.setItem('hr_distributors', JSON.stringify(distributors));
                        displayDistributors(distributors);

                        closeModal('editDistributorModal');
                        alert('✅ تم تحديث بيانات المندوب بنجاح!');
                        console.log('✅ تم تحديث بيانات المندوب:', distributor.name);
                    }
                });
            }
        });

        function editDistributor(id) {
            const distributor = distributors.find(dist => dist.id === id);
            if (!distributor) {
                alert('❌ لم يتم العثور على المندوب');
                return;
            }

            // ملء النموذج ببيانات المندوب الحالية
            document.getElementById('editDistributorId').value = distributor.id;
            document.getElementById('editDistributorName').value = distributor.name;
            document.getElementById('editDistributorPhone').value = distributor.phone;
            document.getElementById('editDistributorArea').value = distributor.area;
            document.getElementById('editDistributorCommission').value = distributor.commission;
            document.getElementById('editDistributorStatus').value = distributor.status;

            openModal('editDistributorModal');
        }

        function deleteDistributor(id) {
            const distributor = distributors.find(dist => dist.id === id);
            if (!distributor) return;

            if (confirm(`هل أنت متأكد من حذف المندوب: ${distributor.name}؟`)) {
                distributors = distributors.filter(dist => dist.id !== id);
                localStorage.setItem('hr_distributors', JSON.stringify(distributors));
                displayDistributors(distributors);

                alert('✅ تم حذف المندوب بنجاح!');
                console.log('✅ تم حذف المندوب:', distributor.name);
            }
        }

        function viewDistributor(id) {
            const distributor = distributors.find(dist => dist.id === id);
            if (!distributor) return;

            alert(`
📋 بيانات المندوب:
👤 الاسم: ${distributor.name}
📱 الجوال: ${distributor.phone}
🗺️ المنطقة: ${distributor.area}
💰 العمولة: ${distributor.commission}%
📦 إجمالي التوصيلات: ${distributor.totalDeliveries}
🔄 الحالة: ${distributor.status}
            `);
        }

        function refreshDistributors() {
            loadDistributors();
            alert('✅ تم تحديث بيانات المناديب!');
        }

        function exportDistributors() {
            const dataStr = JSON.stringify(distributors, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `distributors_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('✅ تم تصدير بيانات المناديب بنجاح!');
            console.log('✅ تم تصدير بيانات المناديب');
        }

        function searchDistributors() {
            const searchTerm = document.getElementById('distributorSearch').value.toLowerCase();
            const filteredDistributors = distributors.filter(distributor =>
                distributor.name.toLowerCase().includes(searchTerm) ||
                distributor.area.toLowerCase().includes(searchTerm) ||
                distributor.phone.toLowerCase().includes(searchTerm)
            );
            displayDistributors(filteredDistributors);
        }

        // وظائف الأقسام
        function addDepartment() {
            openModal('addDepartmentModal');
        }

        // إضافة معالجات نماذج الأقسام
        document.addEventListener('DOMContentLoaded', function() {
            // معالج إضافة قسم
            const addDepartmentForm = document.getElementById('addDepartmentForm');
            if (addDepartmentForm) {
                addDepartmentForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const newDepartment = {
                        id: 'dept' + Date.now(),
                        name: document.getElementById('departmentName').value,
                        manager: document.getElementById('departmentManager').value,
                        employeeCount: 0,
                        budget: parseInt(document.getElementById('departmentBudget').value),
                        description: document.getElementById('departmentDescription').value,
                        createdAt: new Date().toISOString()
                    };

                    departments.push(newDepartment);
                    localStorage.setItem('hr_departments', JSON.stringify(departments));
                    displayDepartments(departments);
                    updateStatistics();

                    closeModal('addDepartmentModal');
                    alert('✅ تم إضافة القسم بنجاح!');
                    console.log('✅ تم إضافة قسم جديد:', newDepartment.name);
                });
            }
        });

        function editDepartment(id) {
            const department = departments.find(dept => dept.id === id);
            if (!department) return;

            const name = prompt('اسم القسم:', department.name);
            if (name !== null) department.name = name;

            const manager = prompt('مدير القسم:', department.manager);
            if (manager !== null) department.manager = manager;

            const budget = prompt('ميزانية القسم:', department.budget);
            if (budget !== null) department.budget = parseInt(budget);

            const description = prompt('وصف القسم:', department.description);
            if (description !== null) department.description = description;

            department.updatedAt = new Date().toISOString();

            localStorage.setItem('hr_departments', JSON.stringify(departments));
            displayDepartments(departments);
            updateStatistics();

            alert('✅ تم تحديث بيانات القسم بنجاح!');
            console.log('✅ تم تحديث بيانات القسم:', department.name);
        }

        function deleteDepartment(id) {
            const department = departments.find(dept => dept.id === id);
            if (!department) return;

            if (confirm(`هل أنت متأكد من حذف القسم: ${department.name}؟`)) {
                departments = departments.filter(dept => dept.id !== id);
                localStorage.setItem('hr_departments', JSON.stringify(departments));
                displayDepartments(departments);
                updateStatistics();

                alert('✅ تم حذف القسم بنجاح!');
                console.log('✅ تم حذف القسم:', department.name);
            }
        }

        function viewDepartment(id) {
            const department = departments.find(dept => dept.id === id);
            if (!department) return;

            alert(`
📋 بيانات القسم:
🏢 الاسم: ${department.name}
👤 المدير: ${department.manager}
👥 عدد الموظفين: ${department.employeeCount}
💰 الميزانية: ${department.budget} ريال
📝 الوصف: ${department.description}
            `);
        }

        function refreshDepartments() {
            loadDepartments();
            alert('✅ تم تحديث بيانات الأقسام!');
        }

        function exportDepartments() {
            const dataStr = JSON.stringify(departments, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `departments_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('✅ تم تصدير بيانات الأقسام بنجاح!');
            console.log('✅ تم تصدير بيانات الأقسام');
        }

        function searchDepartments() {
            const searchTerm = document.getElementById('departmentSearch').value.toLowerCase();
            const filteredDepartments = departments.filter(department =>
                department.name.toLowerCase().includes(searchTerm) ||
                department.manager.toLowerCase().includes(searchTerm) ||
                department.description.toLowerCase().includes(searchTerm)
            );
            displayDepartments(filteredDepartments);
        }

        // وظائف السيارات
        function addVehicle() {
            openModal('addVehicleModal');
        }

        // إضافة معالجات نماذج السيارات
        document.addEventListener('DOMContentLoaded', function() {
            // معالج إضافة سيارة
            const addVehicleForm = document.getElementById('addVehicleForm');
            if (addVehicleForm) {
                addVehicleForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const newVehicle = {
                        id: 'veh' + Date.now(),
                        plateNumber: document.getElementById('vehiclePlateNumber').value,
                        model: document.getElementById('vehicleModel').value,
                        type: document.getElementById('vehicleType').value,
                        status: document.getElementById('vehicleStatus').value,
                        driverId: null,
                        createdAt: new Date().toISOString()
                    };

                    vehicles.push(newVehicle);
                    localStorage.setItem('hr_vehicles', JSON.stringify(vehicles));
                    displayVehicles(vehicles);

                    closeModal('addVehicleModal');
                    alert('✅ تم إضافة السيارة بنجاح!');
                    console.log('✅ تم إضافة سيارة جديدة:', newVehicle.plateNumber);
                });
            }
        });

        function editVehicle(id) {
            const vehicle = vehicles.find(veh => veh.id === id);
            if (!vehicle) return;

            const plateNumber = prompt('رقم اللوحة:', vehicle.plateNumber);
            if (plateNumber !== null) vehicle.plateNumber = plateNumber;

            const model = prompt('موديل السيارة:', vehicle.model);
            if (model !== null) vehicle.model = model;

            const type = prompt('نوع السيارة:', vehicle.type);
            if (type !== null) vehicle.type = type;

            const status = prompt('حالة السيارة (متاحة/مشغولة/صيانة):', vehicle.status);
            if (status !== null) vehicle.status = status;

            vehicle.updatedAt = new Date().toISOString();

            localStorage.setItem('hr_vehicles', JSON.stringify(vehicles));
            displayVehicles(vehicles);

            alert('✅ تم تحديث بيانات السيارة بنجاح!');
            console.log('✅ تم تحديث بيانات السيارة:', vehicle.plateNumber);
        }

        function deleteVehicle(id) {
            const vehicle = vehicles.find(veh => veh.id === id);
            if (!vehicle) return;

            if (confirm(`هل أنت متأكد من حذف السيارة: ${vehicle.plateNumber}؟`)) {
                vehicles = vehicles.filter(veh => veh.id !== id);
                localStorage.setItem('hr_vehicles', JSON.stringify(vehicles));
                displayVehicles(vehicles);

                alert('✅ تم حذف السيارة بنجاح!');
                console.log('✅ تم حذف السيارة:', vehicle.plateNumber);
            }
        }

        function viewVehicle(id) {
            const vehicle = vehicles.find(veh => veh.id === id);
            if (!vehicle) return;

            alert(`
📋 بيانات السيارة:
🚗 رقم اللوحة: ${vehicle.plateNumber}
🏭 الموديل: ${vehicle.model}
📝 النوع: ${vehicle.type}
🔄 الحالة: ${vehicle.status}
👨‍✈️ السائق: ${vehicle.driverId || 'غير محدد'}
            `);
        }

        function refreshVehicles() {
            loadVehicles();
            alert('✅ تم تحديث بيانات السيارات!');
        }

        function exportVehicles() {
            const dataStr = JSON.stringify(vehicles, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `vehicles_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('✅ تم تصدير بيانات السيارات بنجاح!');
            console.log('✅ تم تصدير بيانات السيارات');
        }

        function searchVehicles() {
            const searchTerm = document.getElementById('vehicleSearch').value.toLowerCase();
            const filteredVehicles = vehicles.filter(vehicle =>
                vehicle.plateNumber.toLowerCase().includes(searchTerm) ||
                vehicle.model.toLowerCase().includes(searchTerm) ||
                vehicle.type.toLowerCase().includes(searchTerm)
            );
            displayVehicles(filteredVehicles);
        }

        // وظائف الحضور والانصراف
        function markAttendance() {
            const employeeName = prompt('اسم الموظف:');
            if (!employeeName) return;

            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentTime = now.toLocaleTimeString('ar-SA');

            // البحث عن سجل حضور اليوم
            let todayRecord = attendance.find(record =>
                record.employeeName === employeeName && record.date === today
            );

            if (!todayRecord) {
                // إنشاء سجل جديد
                todayRecord = {
                    id: 'att' + Date.now(),
                    employeeName: employeeName,
                    date: today,
                    checkIn: currentTime,
                    checkOut: null,
                    workingHours: 0,
                    createdAt: new Date().toISOString()
                };
                attendance.push(todayRecord);
            } else {
                todayRecord.checkIn = currentTime;
            }

            localStorage.setItem('hr_attendance', JSON.stringify(attendance));
            displayAttendance(attendance);

            alert(`✅ تم تسجيل حضور ${employeeName} في ${currentTime}`);
            console.log('✅ تم تسجيل الحضور:', employeeName);
        }

        function markLeave() {
            const employeeName = prompt('اسم الموظف:');
            if (!employeeName) return;

            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentTime = now.toLocaleTimeString('ar-SA');

            // البحث عن سجل حضور اليوم
            const todayRecord = attendance.find(record =>
                record.employeeName === employeeName && record.date === today
            );

            if (!todayRecord) {
                alert('❌ لم يتم العثور على سجل حضور لهذا الموظف اليوم');
                return;
            }

            todayRecord.checkOut = currentTime;

            // حساب ساعات العمل
            if (todayRecord.checkIn) {
                const checkInTime = new Date(`${today} ${todayRecord.checkIn}`);
                const checkOutTime = new Date(`${today} ${currentTime}`);
                const diffMs = checkOutTime - checkInTime;
                const diffHours = Math.round(diffMs / (1000 * 60 * 60) * 100) / 100;
                todayRecord.workingHours = diffHours;
            }

            localStorage.setItem('hr_attendance', JSON.stringify(attendance));
            displayAttendance(attendance);

            alert(`✅ تم تسجيل انصراف ${employeeName} في ${currentTime}\nساعات العمل: ${todayRecord.workingHours} ساعة`);
            console.log('✅ تم تسجيل الانصراف:', employeeName);
        }

        function refreshAttendance() {
            loadAttendance();
            alert('✅ تم تحديث بيانات الحضور!');
        }

        function exportAttendance() {
            const dataStr = JSON.stringify(attendance, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `attendance_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('✅ تم تصدير بيانات الحضور بنجاح!');
            console.log('✅ تم تصدير بيانات الحضور');
        }

        // وظائف الرواتب
        function generatePayroll() {
            const employeeName = prompt('اسم الموظف:');
            if (!employeeName) return;

            const employee = employees.find(emp => emp.name === employeeName);
            if (!employee) {
                alert('❌ لم يتم العثور على الموظف');
                return;
            }

            const month = prompt('الشهر (1-12):');
            if (!month) return;

            const year = prompt('السنة:', new Date().getFullYear());
            if (!year) return;

            const allowances = prompt('البدلات (اختياري):', '0');
            const deductions = prompt('الخصومات (اختياري):', '0');

            const basicSalary = employee.salary;
            const allowancesAmount = parseInt(allowances) || 0;
            const deductionsAmount = parseInt(deductions) || 0;
            const netSalary = basicSalary + allowancesAmount - deductionsAmount;

            const payrollRecord = {
                id: 'pay' + Date.now(),
                employeeId: employee.id,
                employeeName: employee.name,
                month: month,
                year: year,
                basicSalary: basicSalary,
                allowances: allowancesAmount,
                deductions: deductionsAmount,
                netSalary: netSalary,
                createdAt: new Date().toISOString()
            };

            payroll.push(payrollRecord);
            localStorage.setItem('hr_payroll', JSON.stringify(payroll));
            displayPayroll(payroll);

            alert(`✅ تم إنشاء كشف راتب ${employeeName}\nالراتب الصافي: ${netSalary} ريال`);
            console.log('✅ تم إنشاء كشف راتب:', employeeName);
        }

        function viewPayrollHistory() {
            if (payroll.length === 0) {
                alert('❌ لا توجد سجلات رواتب');
                return;
            }

            let history = 'تاريخ الرواتب:\n\n';
            payroll.forEach(record => {
                history += `${record.employeeName} - ${record.month}/${record.year}: ${record.netSalary} ريال\n`;
            });

            alert(history);
        }

        function viewPayslip(id) {
            const record = payroll.find(pay => pay.id === id);
            if (!record) return;

            alert(`
📄 كشف الراتب:
👤 الموظف: ${record.employeeName}
📅 الشهر: ${record.month}/${record.year}
💰 الراتب الأساسي: ${record.basicSalary} ريال
➕ البدلات: ${record.allowances} ريال
➖ الخصومات: ${record.deductions} ريال
💳 الصافي: ${record.netSalary} ريال
            `);
        }

        function printPayslip(id) {
            const record = payroll.find(pay => pay.id === id);
            if (!record) return;

            alert('🖨️ سيتم طباعة كشف الراتب...');
            console.log('🖨️ طباعة كشف راتب:', record.employeeName);
        }

        function refreshPayroll() {
            loadPayroll();
            alert('✅ تم تحديث بيانات الرواتب!');
        }

        function exportPayroll() {
            const dataStr = JSON.stringify(payroll, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `payroll_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            alert('✅ تم تصدير بيانات الرواتب بنجاح!');
            console.log('✅ تم تصدير بيانات الرواتب');
        }

        // وظائف التكامل
        function syncWithMainSystem() {
            alert('🔄 جاري المزامنة مع النظام الرئيسي...');

            setTimeout(() => {
                localStorage.setItem('hr_last_sync', new Date().toISOString());
                displayIntegration();
                alert('✅ تم المزامنة مع النظام الرئيسي بنجاح!');
                console.log('✅ تم المزامنة مع النظام الرئيسي');
            }, 2000);
        }

        function testConnections() {
            alert('🔍 جاري اختبار الاتصالات...');

            setTimeout(() => {
                alert('✅ جميع الاتصالات تعمل بشكل طبيعي!');
                console.log('✅ تم اختبار الاتصالات بنجاح');
            }, 1500);
        }

        function refreshIntegration() {
            displayIntegration();
            alert('✅ تم تحديث حالة التكامل!');
        }

        function openDistributorManagement() {
            window.open('distributors-management.html', '_blank');
            console.log('🔗 فتح نظام إدارة المناديب');
        }

        function openVehicleManagement() {
            window.open('vehicle-management.html', '_blank');
            console.log('🔗 فتح نظام إدارة السيارات');
        }

        function openFinancialSystem() {
            window.open('financial-system.html', '_blank');
            console.log('🔗 فتح النظام المالي');
        }

        // جعل جميع الوظائف متاحة عالمياً للاختبار
        window.addEmployee = addEmployee;
        window.editEmployee = editEmployee;
        window.deleteEmployee = deleteEmployee;
        window.viewEmployee = viewEmployee;
        window.refreshEmployees = refreshEmployees;
        window.exportEmployees = exportEmployees;
        window.searchEmployees = searchEmployees;

        // وظائف النماذج
        window.openModal = openModal;
        window.closeModal = closeModal;

        window.addDistributor = addDistributor;
        window.editDistributor = editDistributor;
        window.deleteDistributor = deleteDistributor;
        window.viewDistributor = viewDistributor;
        window.refreshDistributors = refreshDistributors;
        window.exportDistributors = exportDistributors;
        window.searchDistributors = searchDistributors;

        window.addDepartment = addDepartment;
        window.editDepartment = editDepartment;
        window.deleteDepartment = deleteDepartment;
        window.viewDepartment = viewDepartment;
        window.refreshDepartments = refreshDepartments;
        window.exportDepartments = exportDepartments;
        window.searchDepartments = searchDepartments;

        window.addVehicle = addVehicle;
        window.editVehicle = editVehicle;
        window.deleteVehicle = deleteVehicle;
        window.viewVehicle = viewVehicle;
        window.refreshVehicles = refreshVehicles;
        window.exportVehicles = exportVehicles;
        window.searchVehicles = searchVehicles;

        window.markAttendance = markAttendance;
        window.markLeave = markLeave;
        window.refreshAttendance = refreshAttendance;
        window.exportAttendance = exportAttendance;

        window.generatePayroll = generatePayroll;
        window.viewPayrollHistory = viewPayrollHistory;
        window.viewPayslip = viewPayslip;
        window.printPayslip = printPayslip;
        window.refreshPayroll = refreshPayroll;
        window.exportPayroll = exportPayroll;

        window.syncWithMainSystem = syncWithMainSystem;
        window.testConnections = testConnections;
        window.refreshIntegration = refreshIntegration;
        window.openDistributorManagement = openDistributorManagement;
        window.openVehicleManagement = openVehicleManagement;
        window.openFinancialSystem = openFinancialSystem;

        window.showTab = showTab;

        console.log('🏢 تم تحميل نظام الموارد البشرية المتكامل بنجاح!');
        console.log('✅ جميع الوظائف متاحة ومربوطة بالأزرار');
    </script>

    <!-- نماذج الإضافة والتعديل -->

    <!-- نموذج إضافة موظف -->
    <div id="addEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>➕ إضافة موظف جديد</h3>
                <span class="close" onclick="closeModal('addEmployeeModal')">&times;</span>
            </div>
            <form id="addEmployeeForm">
                <div class="form-group">
                    <label>👤 اسم الموظف:</label>
                    <input type="text" id="employeeName" required>
                </div>
                <div class="form-group">
                    <label>💼 المنصب:</label>
                    <input type="text" id="employeePosition" required>
                </div>
                <div class="form-group">
                    <label>🏢 القسم:</label>
                    <select id="employeeDepartment" required>
                        <option value="">اختر القسم</option>
                        <option value="تقنية المعلومات">تقنية المعلومات</option>
                        <option value="المحاسبة">المحاسبة</option>
                        <option value="المبيعات">المبيعات</option>
                        <option value="الموارد البشرية">الموارد البشرية</option>
                        <option value="التسويق">التسويق</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>📱 رقم الجوال:</label>
                    <input type="tel" id="employeePhone" required>
                </div>
                <div class="form-group">
                    <label>📧 البريد الإلكتروني:</label>
                    <input type="email" id="employeeEmail" required>
                </div>
                <div class="form-group">
                    <label>💰 الراتب:</label>
                    <input type="number" id="employeeSalary" required>
                </div>
                <div class="form-group">
                    <label>📅 تاريخ التوظيف:</label>
                    <input type="date" id="employeeJoinDate" required>
                </div>
                <div class="form-group">
                    <label>🔄 الحالة:</label>
                    <select id="employeeStatus" required>
                        <option value="نشط">نشط</option>
                        <option value="غير نشط">غير نشط</option>
                        <option value="إجازة">إجازة</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addEmployeeModal')">إلغاء</button>
                    <button type="submit" class="btn btn-primary">➕ إضافة الموظف</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نموذج تعديل موظف -->
    <div id="editEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل بيانات الموظف</h3>
                <span class="close" onclick="closeModal('editEmployeeModal')">&times;</span>
            </div>
            <form id="editEmployeeForm">
                <input type="hidden" id="editEmployeeId">
                <div class="form-group">
                    <label>👤 اسم الموظف:</label>
                    <input type="text" id="editEmployeeName" required>
                </div>
                <div class="form-group">
                    <label>💼 المنصب:</label>
                    <input type="text" id="editEmployeePosition" required>
                </div>
                <div class="form-group">
                    <label>🏢 القسم:</label>
                    <select id="editEmployeeDepartment" required>
                        <option value="">اختر القسم</option>
                        <option value="تقنية المعلومات">تقنية المعلومات</option>
                        <option value="المحاسبة">المحاسبة</option>
                        <option value="المبيعات">المبيعات</option>
                        <option value="الموارد البشرية">الموارد البشرية</option>
                        <option value="التسويق">التسويق</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>📱 رقم الجوال:</label>
                    <input type="tel" id="editEmployeePhone" required>
                </div>
                <div class="form-group">
                    <label>📧 البريد الإلكتروني:</label>
                    <input type="email" id="editEmployeeEmail" required>
                </div>
                <div class="form-group">
                    <label>💰 الراتب:</label>
                    <input type="number" id="editEmployeeSalary" required>
                </div>
                <div class="form-group">
                    <label>📅 تاريخ التوظيف:</label>
                    <input type="date" id="editEmployeeJoinDate" required>
                </div>
                <div class="form-group">
                    <label>🔄 الحالة:</label>
                    <select id="editEmployeeStatus" required>
                        <option value="نشط">نشط</option>
                        <option value="غير نشط">غير نشط</option>
                        <option value="إجازة">إجازة</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editEmployeeModal')">إلغاء</button>
                    <button type="submit" class="btn btn-primary">✏️ حفظ التعديلات</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نموذج تعديل مندوب -->
    <div id="editDistributorModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل بيانات المندوب</h3>
                <span class="close" onclick="closeModal('editDistributorModal')">&times;</span>
            </div>
            <form id="editDistributorForm">
                <input type="hidden" id="editDistributorId">
                <div class="form-group">
                    <label>👤 اسم المندوب:</label>
                    <input type="text" id="editDistributorName" required>
                </div>
                <div class="form-group">
                    <label>📱 رقم الجوال:</label>
                    <input type="tel" id="editDistributorPhone" required>
                </div>
                <div class="form-group">
                    <label>🗺️ المنطقة:</label>
                    <select id="editDistributorArea" required>
                        <option value="">اختر المنطقة</option>
                        <option value="الرياض الشمالية">الرياض الشمالية</option>
                        <option value="الرياض الجنوبية">الرياض الجنوبية</option>
                        <option value="الرياض الشرقية">الرياض الشرقية</option>
                        <option value="الرياض الغربية">الرياض الغربية</option>
                        <option value="الرياض الوسط">الرياض الوسط</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>💰 نسبة العمولة (%):</label>
                    <input type="number" id="editDistributorCommission" min="0" max="100" step="0.1" required>
                </div>
                <div class="form-group">
                    <label>🔄 الحالة:</label>
                    <select id="editDistributorStatus" required>
                        <option value="نشط">نشط</option>
                        <option value="غير نشط">غير نشط</option>
                        <option value="إجازة">إجازة</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editDistributorModal')">إلغاء</button>
                    <button type="submit" class="btn btn-primary">✏️ حفظ التعديلات</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نموذج إضافة قسم -->
    <div id="addDepartmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>➕ إضافة قسم جديد</h3>
                <span class="close" onclick="closeModal('addDepartmentModal')">&times;</span>
            </div>
            <form id="addDepartmentForm">
                <div class="form-group">
                    <label>🏢 اسم القسم:</label>
                    <input type="text" id="departmentName" required>
                </div>
                <div class="form-group">
                    <label>👤 مدير القسم:</label>
                    <input type="text" id="departmentManager" required>
                </div>
                <div class="form-group">
                    <label>💰 ميزانية القسم:</label>
                    <input type="number" id="departmentBudget" required>
                </div>
                <div class="form-group">
                    <label>📝 وصف القسم:</label>
                    <textarea id="departmentDescription" rows="3" required></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addDepartmentModal')">إلغاء</button>
                    <button type="submit" class="btn btn-primary">➕ إضافة القسم</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نموذج إضافة سيارة -->
    <div id="addVehicleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>➕ إضافة سيارة جديدة</h3>
                <span class="close" onclick="closeModal('addVehicleModal')">&times;</span>
            </div>
            <form id="addVehicleForm">
                <div class="form-group">
                    <label>🚗 رقم اللوحة:</label>
                    <input type="text" id="vehiclePlateNumber" required>
                </div>
                <div class="form-group">
                    <label>🏭 موديل السيارة:</label>
                    <input type="text" id="vehicleModel" required>
                </div>
                <div class="form-group">
                    <label>📝 نوع السيارة:</label>
                    <select id="vehicleType" required>
                        <option value="">اختر النوع</option>
                        <option value="بيك أب">بيك أب</option>
                        <option value="شاحنة صغيرة">شاحنة صغيرة</option>
                        <option value="شاحنة متوسطة">شاحنة متوسطة</option>
                        <option value="شاحنة كبيرة">شاحنة كبيرة</option>
                        <option value="دراجة نارية">دراجة نارية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>🔄 حالة السيارة:</label>
                    <select id="vehicleStatus" required>
                        <option value="متاحة">متاحة</option>
                        <option value="مشغولة">مشغولة</option>
                        <option value="صيانة">صيانة</option>
                        <option value="خارج الخدمة">خارج الخدمة</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addVehicleModal')">إلغاء</button>
                    <button type="submit" class="btn btn-primary">➕ إضافة السيارة</button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
