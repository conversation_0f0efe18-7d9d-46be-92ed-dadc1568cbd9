<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموارد البشرية المتكامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            color: #667eea;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            padding-top: 80px;
        }

        .header {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 2.8rem;
            font-weight: 700;
        }

        .header p {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .integration-status {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .integration-badge {
            background: #d4edda;
            color: #155724;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .tabs-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tabs-header {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            flex: 1;
            min-width: 200px;
            padding: 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
            position: relative;
            white-space: nowrap;
        }

        .tab-button.active {
            color: #667eea;
            background: white;
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #667eea;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 500px;
        }

        .tab-content.active {
            display: block;
        }

        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-secondary { background: #6c757d; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .search-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            width: 300px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .data-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .card-info h3 {
            margin: 0;
            color: #333;
            font-size: 1.3rem;
        }

        .card-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 0.95rem;
        }

        .detail-label {
            color: #666;
            font-weight: 500;
        }

        .detail-value {
            color: #333;
            font-weight: 600;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.85rem;
            border-radius: 15px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .integration-panel {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
        }

        .integration-panel h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .integration-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .integration-link {
            background: white;
            color: #667eea;
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            border: 2px solid #667eea;
            transition: all 0.3s ease;
        }

        .integration-link:hover {
            background: #667eea;
            color: white;
        }

        /* نماذج التعديل والحضور */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .attendance-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .attendance-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .attendance-card:hover {
            background: #e9ecef;
            transform: translateY(-3px);
            border-color: #667eea;
        }

        .attendance-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .attendance-card h4 {
            margin: 10px 0 5px 0;
            color: #333;
        }

        .attendance-card p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .tabs-header {
                flex-direction: column;
            }
            
            .tab-button {
                min-width: auto;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .search-input {
                width: 200px;
            }
            
            .action-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .integration-status {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">🏠 العودة للوحة الرئيسية</a>
    
    <div class="container">
        <div class="header">
            <h1>🏢 نظام إدارة الموارد البشرية المتكامل</h1>
            <p>إدارة شاملة ومتكاملة للموظفين والمناديب مع ربط جميع الأنظمة</p>
            
            <div class="integration-status">
                <div class="integration-badge">
                    <span>✅</span>
                    <span>مربوط مع إدارة المناديب</span>
                </div>
                <div class="integration-badge">
                    <span>✅</span>
                    <span>مربوط مع إدارة السيارات</span>
                </div>
                <div class="integration-badge">
                    <span>✅</span>
                    <span>مربوط مع الإدارة المالية</span>
                </div>
                <div class="integration-badge">
                    <span>✅</span>
                    <span>مربوط مع نظام الشحنات</span>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card" onclick="showTab('employees')">
                <span class="stat-icon">👨‍💼</span>
                <div class="stat-number" id="totalEmployees">0</div>
                <div class="stat-label">إجمالي الموظفين</div>
            </div>
            <div class="stat-card" onclick="showTab('distributors')">
                <span class="stat-icon">🚚</span>
                <div class="stat-number" id="totalDistributors">0</div>
                <div class="stat-label">إجمالي المناديب</div>
            </div>
            <div class="stat-card" onclick="showTab('departments')">
                <span class="stat-icon">🏢</span>
                <div class="stat-number" id="totalDepartments">0</div>
                <div class="stat-label">إجمالي الأقسام</div>
            </div>
            <div class="stat-card" onclick="showTab('vehicles')">
                <span class="stat-icon">🚗</span>
                <div class="stat-number" id="totalVehicles">0</div>
                <div class="stat-label">إجمالي السيارات</div>
            </div>
            <div class="stat-card" onclick="showTab('payroll')">
                <span class="stat-icon">💰</span>
                <div class="stat-number" id="totalSalary">0</div>
                <div class="stat-label">إجمالي الرواتب (ريال)</div>
            </div>
            <div class="stat-card" onclick="showTab('attendance')">
                <span class="stat-icon">⏰</span>
                <div class="stat-number" id="presentToday">0</div>
                <div class="stat-label">الحاضرين اليوم</div>
            </div>
        </div>

        <!-- التبويبات الرئيسية -->
        <div class="tabs-container">
            <div class="tabs-header">
                <button class="tab-button active" onclick="showTab('employees')">👨‍💼 الموظفين</button>
                <button class="tab-button" onclick="showTab('distributors')">🚚 المناديب</button>
                <button class="tab-button" onclick="showTab('departments')">🏢 الأقسام</button>
                <button class="tab-button" onclick="showTab('vehicles')">🚗 السيارات</button>
                <button class="tab-button" onclick="showTab('attendance')">⏰ الحضور والانصراف</button>
                <button class="tab-button" onclick="showTab('payroll')">💰 الرواتب</button>
                <button class="tab-button" onclick="showTab('integration')">🔗 التكامل</button>
            </div>

            <!-- تبويب الموظفين -->
            <div id="employees" class="tab-content active">
                <div class="action-bar">
                    <div class="search-container">
                        <input type="text" class="search-input" id="employeeSearch" placeholder="البحث عن موظف...">
                        <button class="btn btn-primary" onclick="searchEmployees()">🔍 بحث</button>
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="addEmployee()">➕ إضافة موظف</button>
                        <button class="btn btn-warning" onclick="loadEmployees()">🔄 تحديث</button>
                        <button class="btn btn-info" onclick="exportEmployees()">📤 تصدير</button>
                    </div>
                </div>
                <div id="employeesGrid" class="data-grid">
                    <!-- سيتم ملء البيانات هنا -->
                </div>
            </div>

            <!-- تبويب المناديب -->
            <div id="distributors" class="tab-content">
                <div class="action-bar">
                    <div class="search-container">
                        <input type="text" class="search-input" id="distributorSearch" placeholder="البحث عن مندوب...">
                        <button class="btn btn-primary" onclick="searchDistributors()">🔍 بحث</button>
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="addDistributor()">➕ إضافة مندوب</button>
                        <button class="btn btn-warning" onclick="loadDistributors()">🔄 تحديث</button>
                        <button class="btn btn-info" onclick="exportDistributors()">📤 تصدير</button>
                    </div>
                </div>
                <div id="distributorsGrid" class="data-grid">
                    <!-- سيتم ملء البيانات هنا -->
                </div>
            </div>

            <!-- تبويب الأقسام -->
            <div id="departments" class="tab-content">
                <div class="action-bar">
                    <div class="search-container">
                        <input type="text" class="search-input" id="departmentSearch" placeholder="البحث عن قسم...">
                        <button class="btn btn-primary" onclick="searchDepartments()">🔍 بحث</button>
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="addDepartment()">➕ إضافة قسم</button>
                        <button class="btn btn-warning" onclick="loadDepartments()">🔄 تحديث</button>
                        <button class="btn btn-info" onclick="exportDepartments()">📤 تصدير</button>
                    </div>
                </div>
                <div id="departmentsGrid" class="data-grid">
                    <!-- سيتم ملء البيانات هنا -->
                </div>
            </div>

            <!-- تبويب السيارات -->
            <div id="vehicles" class="tab-content">
                <div class="action-bar">
                    <div class="search-container">
                        <input type="text" class="search-input" id="vehicleSearch" placeholder="البحث عن سيارة...">
                        <button class="btn btn-primary" onclick="searchVehicles()">🔍 بحث</button>
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="addVehicle()">➕ إضافة سيارة</button>
                        <button class="btn btn-warning" onclick="loadVehicles()">🔄 تحديث</button>
                        <button class="btn btn-info" onclick="exportVehicles()">📤 تصدير</button>
                        <button class="btn btn-secondary" onclick="openVehicleManagement()">🚗 إدارة السيارات</button>
                    </div>
                </div>
                <div id="vehiclesGrid" class="data-grid">
                    <!-- سيتم ملء البيانات هنا -->
                </div>
            </div>

            <!-- تبويب الحضور والانصراف -->
            <div id="attendance" class="tab-content">
                <div class="action-bar">
                    <div>
                        <button class="btn btn-success" onclick="markAttendance()">✅ تسجيل حضور</button>
                        <button class="btn btn-danger" onclick="markLeave()">❌ تسجيل انصراف</button>
                        <button class="btn btn-info" onclick="viewAttendanceReport()">📊 تقرير الحضور</button>
                        <button class="btn btn-warning" onclick="exportAttendance()">📤 تصدير الحضور</button>
                    </div>
                </div>
                <div id="attendanceData">
                    <h3>📋 سجل الحضور والانصراف</h3>
                    <p>يمكنك تسجيل الحضور والانصراف ومراجعة التقارير من هنا.</p>
                    <div style="margin-top: 20px;">
                        <p><strong>التاريخ:</strong> <span id="currentDate"></span></p>
                        <p><strong>الوقت الحالي:</strong> <span id="currentTime"></span></p>
                    </div>
                </div>
            </div>

            <!-- تبويب الرواتب -->
            <div id="payroll" class="tab-content">
                <div class="action-bar">
                    <div>
                        <button class="btn btn-success" onclick="generatePayroll()">💰 إنشاء كشف راتب</button>
                        <button class="btn btn-info" onclick="viewPayrollHistory()">📋 تاريخ الرواتب</button>
                        <button class="btn btn-warning" onclick="exportPayroll()">📤 تصدير الرواتب</button>
                        <button class="btn btn-secondary" onclick="openFinancialSystem()">💳 النظام المالي</button>
                    </div>
                </div>
                <div id="payrollData">
                    <h3>💰 إدارة الرواتب</h3>
                    <p>يمكنك إنشاء كشوف الرواتب ومراجعة التاريخ من هنا.</p>
                    <div style="margin-top: 20px;">
                        <p><strong>الشهر الحالي:</strong> <span id="currentMonth"></span></p>
                        <p><strong>إجمالي الرواتب:</strong> <span id="totalPayroll">0</span> ريال</p>
                    </div>
                </div>
            </div>

            <!-- تبويب التكامل -->
            <div id="integration" class="tab-content">
                <div class="integration-panel">
                    <h4>🔗 ربط الأنظمة</h4>
                    <p>نظام الموارد البشرية مربوط مع جميع الأنظمة الأخرى لضمان التكامل الكامل</p>

                    <div class="integration-links">
                        <a href="distributors-management.html" class="integration-link">🚚 إدارة المناديب</a>
                        <a href="vehicle-management.html" class="integration-link">🚗 إدارة السيارات</a>
                        <a href="financial-system.html" class="integration-link">💳 النظام المالي</a>
                        <a href="shipments.html" class="integration-link">📦 نظام الشحنات</a>
                        <a href="user-management.html" class="integration-link">👥 إدارة المستخدمين</a>
                        <a href="commission-management.html" class="integration-link">🤝 إدارة العمولات</a>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <h4>📊 إحصائيات التكامل</h4>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <span class="stat-icon">🔄</span>
                            <div class="stat-number" id="syncStatus">متصل</div>
                            <div class="stat-label">حالة التزامن</div>
                        </div>
                        <div class="stat-card">
                            <span class="stat-icon">📈</span>
                            <div class="stat-number" id="dataIntegrity">100%</div>
                            <div class="stat-label">سلامة البيانات</div>
                        </div>
                        <div class="stat-card">
                            <span class="stat-icon">⚡</span>
                            <div class="stat-number" id="systemPerformance">ممتاز</div>
                            <div class="stat-label">أداء النظام</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تعديل الموظف -->
    <div id="editEmployeeModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل بيانات الموظف</h3>
                <span class="close" onclick="closeEditEmployeeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editEmployeeForm">
                    <input type="hidden" id="editEmployeeId">
                    <div class="form-group">
                        <label>الاسم:</label>
                        <input type="text" id="editEmployeeName" required>
                    </div>
                    <div class="form-group">
                        <label>المسمى الوظيفي:</label>
                        <input type="text" id="editEmployeePosition" required>
                    </div>
                    <div class="form-group">
                        <label>القسم:</label>
                        <input type="text" id="editEmployeeDepartment" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الجوال:</label>
                        <input type="tel" id="editEmployeePhone" required>
                    </div>
                    <div class="form-group">
                        <label>البريد الإلكتروني:</label>
                        <input type="email" id="editEmployeeEmail" required>
                    </div>
                    <div class="form-group">
                        <label>الراتب:</label>
                        <input type="number" id="editEmployeeSalary" min="0">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">💾 حفظ التغييرات</button>
                        <button type="button" class="btn btn-secondary" onclick="closeEditEmployeeModal()">❌ إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج تسجيل الحضور -->
    <div id="attendanceModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⏰ تسجيل الحضور والانصراف</h3>
                <span class="close" onclick="closeAttendanceModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="attendance-options">
                    <div class="attendance-card" onclick="showAttendanceForm('checkin')">
                        <div class="attendance-icon">🟢</div>
                        <h4>تسجيل الحضور</h4>
                        <p>تسجيل وقت الوصول</p>
                    </div>
                    <div class="attendance-card" onclick="showAttendanceForm('checkout')">
                        <div class="attendance-icon">🔴</div>
                        <h4>تسجيل الانصراف</h4>
                        <p>تسجيل وقت المغادرة</p>
                    </div>
                </div>

                <div id="attendanceForm" style="display: none;">
                    <form id="attendanceFormElement">
                        <input type="hidden" id="attendanceType">
                        <div class="form-group">
                            <label>اختر الموظف:</label>
                            <select id="attendanceEmployee" required>
                                <option value="">-- اختر الموظف --</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الوقت الحالي:</label>
                            <input type="text" id="currentTime" readonly>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">✅ تسجيل</button>
                            <button type="button" class="btn btn-secondary" onclick="closeAttendanceModal()">❌ إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="js/database-simple.js"></script>
    <script>
        console.log('🚀 بدء تحميل نظام الموارد البشرية المتكامل...');

        // متغيرات عامة
        let employees = [];
        let distributors = [];
        let departments = [];
        let vehicles = [];
        let db;

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📍 تحميل الصفحة...');
            initializeSystem();
            updateDateTime();
            setInterval(updateDateTime, 1000); // تحديث الوقت كل ثانية
            console.log('✅ تم تحميل النظام بنجاح');
        });

        // تهيئة النظام
        function initializeSystem() {
            console.log('🔧 تهيئة النظام...');

            // تهيئة قاعدة البيانات
            try {
                db = new SimpleDatabase();
                console.log('✅ تم تهيئة قاعدة البيانات');
            } catch (error) {
                console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
            }

            // تحميل البيانات
            loadAllData();
            updateStatistics();
        }

        // تحميل جميع البيانات
        function loadAllData() {
            loadEmployees();
            loadDistributors();
            loadDepartments();
            loadVehicles();
        }

        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const dateElement = document.getElementById('currentDate');
            const timeElement = document.getElementById('currentTime');
            const monthElement = document.getElementById('currentMonth');

            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA');
            }
            if (monthElement) {
                monthElement.textContent = now.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            document.getElementById('totalEmployees').textContent = employees.length;
            document.getElementById('totalDistributors').textContent = distributors.length;
            document.getElementById('totalDepartments').textContent = departments.length;
            document.getElementById('totalVehicles').textContent = vehicles.length;

            const totalSalary = employees.reduce((sum, emp) => sum + (emp.salary || 0), 0);
            document.getElementById('totalSalary').textContent = totalSalary.toLocaleString();

            const presentToday = employees.filter(emp => emp.status === 'active').length;
            document.getElementById('presentToday').textContent = presentToday;

            const totalPayrollElement = document.getElementById('totalPayroll');
            if (totalPayrollElement) {
                totalPayrollElement.textContent = totalSalary.toLocaleString();
            }
        }

        // وظيفة تبديل التبويبات
        function showTab(tabName) {
            console.log('🔄 تبديل التبويب إلى:', tabName);

            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المطلوب
            const targetTab = document.getElementById(tabName);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // إضافة الفئة النشطة للزر المطلوب
            event.target.classList.add('active');

            // تحميل البيانات حسب التبويب
            switch(tabName) {
                case 'employees':
                    loadEmployees();
                    break;
                case 'distributors':
                    loadDistributors();
                    break;
                case 'departments':
                    loadDepartments();
                    break;
                case 'vehicles':
                    loadVehicles();
                    break;
                case 'attendance':
                    loadAttendanceData();
                    break;
                case 'payroll':
                    loadPayrollData();
                    break;
                case 'integration':
                    loadIntegrationData();
                    break;
            }

            console.log('✅ تم التبديل بنجاح إلى:', tabName);
        }

        // تحميل الموظفين
        function loadEmployees() {
            console.log('👥 تحميل الموظفين...');
            try {
                if (db && db.getAllEmployees) {
                    employees = db.getAllEmployees();
                } else {
                    employees = JSON.parse(localStorage.getItem('employees') || '[]');
                }

                // إضافة بيانات افتراضية إذا لم توجد
                if (employees.length === 0) {
                    employees = [
                        {
                            id: '1',
                            name: 'أحمد محمد السعد',
                            position: 'مدير العمليات',
                            department: 'الإدارة',
                            phone: '0501234567',
                            email: '<EMAIL>',
                            status: 'active',
                            salary: 8000,
                            joinDate: '2023-01-15'
                        },
                        {
                            id: '2',
                            name: 'فاطمة علي الأحمد',
                            position: 'محاسبة رئيسية',
                            department: 'المالية',
                            phone: '0507654321',
                            email: '<EMAIL>',
                            status: 'active',
                            salary: 6000,
                            joinDate: '2023-03-10'
                        },
                        {
                            id: '3',
                            name: 'محمد خالد العتيبي',
                            position: 'مشرف الشحنات',
                            department: 'العمليات',
                            phone: '0509876543',
                            email: '<EMAIL>',
                            status: 'active',
                            salary: 5500,
                            joinDate: '2023-05-20'
                        }
                    ];
                    localStorage.setItem('employees', JSON.stringify(employees));
                }

                displayEmployees(employees);
                updateStatistics();
            } catch (error) {
                console.error('❌ خطأ في تحميل الموظفين:', error);
            }
        }

        // عرض الموظفين
        function displayEmployees(employeeList) {
            const grid = document.getElementById('employeesGrid');

            if (employeeList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <h3>👥 لا توجد موظفين</h3>
                        <p>انقر على "إضافة موظف" لإضافة موظف جديد</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = employeeList.map(employee => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">${employee.name.charAt(0)}</div>
                        <div class="card-info">
                            <h3>${employee.name}</h3>
                            <span class="status-badge ${employee.status === 'active' ? 'status-active' : 'status-inactive'}">
                                ${employee.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="detail-row">
                            <span class="detail-label">المسمى الوظيفي:</span>
                            <span class="detail-value">${employee.position}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">القسم:</span>
                            <span class="detail-value">${employee.department}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الجوال:</span>
                            <span class="detail-value">${employee.phone}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الراتب:</span>
                            <span class="detail-value">${employee.salary ? employee.salary.toLocaleString() + ' ريال' : 'غير محدد'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">تاريخ التوظيف:</span>
                            <span class="detail-value">${employee.joinDate || 'غير محدد'}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary" onclick="editEmployee('${employee.id}')">✏️ تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteEmployee('${employee.id}')">🗑️ حذف</button>
                        <button class="btn btn-sm btn-info" onclick="viewEmployee('${employee.id}')">👁️ عرض</button>
                        <button class="btn btn-sm btn-secondary" onclick="assignVehicle('${employee.id}')">🚗 تخصيص سيارة</button>
                    </div>
                </div>
            `).join('');
        }

        // تحميل المناديب
        function loadDistributors() {
            console.log('🚚 تحميل المناديب...');
            try {
                if (db && db.getAllDistributors) {
                    distributors = db.getAllDistributors();
                } else {
                    distributors = JSON.parse(localStorage.getItem('distributors') || '[]');
                }

                // إضافة بيانات افتراضية إذا لم توجد
                if (distributors.length === 0) {
                    distributors = [
                        {
                            id: '1',
                            name: 'خالد السعد',
                            phone: '0501111111',
                            area: 'الرياض - الشمال',
                            isActive: true,
                            vehicleId: 'V001',
                            commission: 5,
                            totalDeliveries: 150
                        },
                        {
                            id: '2',
                            name: 'سعد الأحمد',
                            phone: '0502222222',
                            area: 'جدة - الوسط',
                            isActive: true,
                            vehicleId: 'V002',
                            commission: 4.5,
                            totalDeliveries: 120
                        },
                        {
                            id: '3',
                            name: 'عبدالله المطيري',
                            phone: '0503333333',
                            area: 'الدمام - الشرق',
                            isActive: true,
                            vehicleId: null,
                            commission: 5.5,
                            totalDeliveries: 95
                        }
                    ];
                    localStorage.setItem('distributors', JSON.stringify(distributors));
                }

                displayDistributors(distributors);
                updateStatistics();
            } catch (error) {
                console.error('❌ خطأ في تحميل المناديب:', error);
            }
        }

        // عرض المناديب
        function displayDistributors(distributorList) {
            const grid = document.getElementById('distributorsGrid');

            if (distributorList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <h3>🚚 لا توجد مناديب</h3>
                        <p>انقر على "إضافة مندوب" لإضافة مندوب جديد</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = distributorList.map(distributor => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">🚚</div>
                        <div class="card-info">
                            <h3>${distributor.name}</h3>
                            <span class="status-badge ${distributor.isActive ? 'status-active' : 'status-inactive'}">
                                ${distributor.isActive ? 'نشط' : 'غير نشط'}
                            </span>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="detail-row">
                            <span class="detail-label">الجوال:</span>
                            <span class="detail-value">${distributor.phone}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">المنطقة:</span>
                            <span class="detail-value">${distributor.area}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">السيارة:</span>
                            <span class="detail-value">${distributor.vehicleId || 'غير محددة'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">العمولة:</span>
                            <span class="detail-value">${distributor.commission}%</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">إجمالي التوصيلات:</span>
                            <span class="detail-value">${distributor.totalDeliveries}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary" onclick="editDistributor('${distributor.id}')">✏️ تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteDistributor('${distributor.id}')">🗑️ حذف</button>
                        <button class="btn btn-sm btn-info" onclick="viewDistributor('${distributor.id}')">👁️ عرض</button>
                        <button class="btn btn-sm btn-secondary" onclick="assignVehicleToDistributor('${distributor.id}')">🚗 تخصيص سيارة</button>
                    </div>
                </div>
            `).join('');
        }

        // تحميل الأقسام
        function loadDepartments() {
            console.log('🏢 تحميل الأقسام...');
            try {
                departments = JSON.parse(localStorage.getItem('departments') || '[]');

                // إضافة بيانات افتراضية إذا لم توجد
                if (departments.length === 0) {
                    departments = [
                        {
                            id: '1',
                            name: 'الإدارة العامة',
                            manager: 'أحمد محمد السعد',
                            employeeCount: 5,
                            budget: 50000,
                            description: 'إدارة الشؤون العامة والتخطيط الاستراتيجي'
                        },
                        {
                            id: '2',
                            name: 'المالية والمحاسبة',
                            manager: 'فاطمة علي الأحمد',
                            employeeCount: 3,
                            budget: 30000,
                            description: 'إدارة الحسابات والشؤون المالية'
                        },
                        {
                            id: '3',
                            name: 'العمليات والشحن',
                            manager: 'محمد خالد العتيبي',
                            employeeCount: 8,
                            budget: 40000,
                            description: 'إدارة عمليات الشحن والتوصيل'
                        }
                    ];
                    localStorage.setItem('departments', JSON.stringify(departments));
                }

                displayDepartments(departments);
                updateStatistics();
            } catch (error) {
                console.error('❌ خطأ في تحميل الأقسام:', error);
            }
        }

        // عرض الأقسام
        function displayDepartments(departmentList) {
            const grid = document.getElementById('departmentsGrid');

            if (departmentList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <h3>🏢 لا توجد أقسام</h3>
                        <p>انقر على "إضافة قسم" لإضافة قسم جديد</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = departmentList.map(department => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">🏢</div>
                        <div class="card-info">
                            <h3>${department.name}</h3>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="detail-row">
                            <span class="detail-label">المدير:</span>
                            <span class="detail-value">${department.manager}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">عدد الموظفين:</span>
                            <span class="detail-value">${department.employeeCount}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الميزانية:</span>
                            <span class="detail-value">${department.budget ? department.budget.toLocaleString() + ' ريال' : 'غير محددة'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الوصف:</span>
                            <span class="detail-value">${department.description}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary" onclick="editDepartment('${department.id}')">✏️ تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteDepartment('${department.id}')">🗑️ حذف</button>
                        <button class="btn btn-sm btn-info" onclick="viewDepartment('${department.id}')">👁️ عرض</button>
                        <button class="btn btn-sm btn-secondary" onclick="manageDepartmentEmployees('${department.id}')">👥 إدارة الموظفين</button>
                    </div>
                </div>
            `).join('');
        }

        // تحميل السيارات
        function loadVehicles() {
            console.log('🚗 تحميل السيارات...');
            try {
                vehicles = JSON.parse(localStorage.getItem('vehicles') || '[]');

                // إضافة بيانات افتراضية إذا لم توجد
                if (vehicles.length === 0) {
                    vehicles = [
                        {
                            id: 'V001',
                            plateNumber: 'أ ب ج 1234',
                            model: 'تويوتا هايلكس 2023',
                            type: 'شاحنة صغيرة',
                            status: 'متاحة',
                            driverId: '1',
                            distributorId: '1',
                            purchasePrice: 85000,
                            maintenanceCost: 2500
                        },
                        {
                            id: 'V002',
                            plateNumber: 'د هـ و 5678',
                            model: 'إيسوزو D-Max 2022',
                            type: 'شاحنة متوسطة',
                            status: 'في الخدمة',
                            driverId: '2',
                            distributorId: '2',
                            purchasePrice: 95000,
                            maintenanceCost: 3200
                        },
                        {
                            id: 'V003',
                            plateNumber: 'ز ح ط 9012',
                            model: 'فورد ترانزيت 2023',
                            type: 'فان',
                            status: 'في الصيانة',
                            driverId: null,
                            distributorId: null,
                            purchasePrice: 75000,
                            maintenanceCost: 4500
                        }
                    ];
                    localStorage.setItem('vehicles', JSON.stringify(vehicles));
                }

                displayVehicles(vehicles);
                updateStatistics();
            } catch (error) {
                console.error('❌ خطأ في تحميل السيارات:', error);
            }
        }

        // عرض السيارات
        function displayVehicles(vehicleList) {
            const grid = document.getElementById('vehiclesGrid');

            if (vehicleList.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <h3>🚗 لا توجد سيارات</h3>
                        <p>انقر على "إضافة سيارة" لإضافة سيارة جديدة</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = vehicleList.map(vehicle => `
                <div class="data-card">
                    <div class="card-header">
                        <div class="card-avatar">🚗</div>
                        <div class="card-info">
                            <h3>${vehicle.plateNumber}</h3>
                            <span class="status-badge ${vehicle.status === 'متاحة' ? 'status-active' : vehicle.status === 'في الخدمة' ? 'status-active' : 'status-inactive'}">
                                ${vehicle.status}
                            </span>
                        </div>
                    </div>
                    <div class="card-details">
                        <div class="detail-row">
                            <span class="detail-label">الموديل:</span>
                            <span class="detail-value">${vehicle.model}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">النوع:</span>
                            <span class="detail-value">${vehicle.type}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">السائق:</span>
                            <span class="detail-value">${vehicle.driverId ? 'مخصص' : 'غير مخصص'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">المندوب:</span>
                            <span class="detail-value">${vehicle.distributorId ? 'مخصص' : 'غير مخصص'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">سعر الشراء:</span>
                            <span class="detail-value">${vehicle.purchasePrice ? vehicle.purchasePrice.toLocaleString() + ' ريال' : 'غير محدد'}</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary" onclick="editVehicle('${vehicle.id}')">✏️ تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteVehicle('${vehicle.id}')">🗑️ حذف</button>
                        <button class="btn btn-sm btn-info" onclick="viewVehicle('${vehicle.id}')">👁️ عرض</button>
                        <button class="btn btn-sm btn-secondary" onclick="assignDriver('${vehicle.id}')">👨‍💼 تخصيص سائق</button>
                    </div>
                </div>
            `).join('');
        }

        // وظائف البحث
        function searchEmployees() {
            const searchTerm = document.getElementById('employeeSearch').value.toLowerCase();
            const filtered = employees.filter(emp =>
                emp.name.toLowerCase().includes(searchTerm) ||
                emp.position.toLowerCase().includes(searchTerm) ||
                emp.department.toLowerCase().includes(searchTerm)
            );
            displayEmployees(filtered);
        }

        function searchDistributors() {
            const searchTerm = document.getElementById('distributorSearch').value.toLowerCase();
            const filtered = distributors.filter(dist =>
                dist.name.toLowerCase().includes(searchTerm) ||
                dist.area.toLowerCase().includes(searchTerm)
            );
            displayDistributors(filtered);
        }

        function searchDepartments() {
            const searchTerm = document.getElementById('departmentSearch').value.toLowerCase();
            const filtered = departments.filter(dept =>
                dept.name.toLowerCase().includes(searchTerm) ||
                dept.manager.toLowerCase().includes(searchTerm)
            );
            displayDepartments(filtered);
        }

        function searchVehicles() {
            const searchTerm = document.getElementById('vehicleSearch').value.toLowerCase();
            const filtered = vehicles.filter(vehicle =>
                vehicle.plateNumber.toLowerCase().includes(searchTerm) ||
                vehicle.model.toLowerCase().includes(searchTerm) ||
                vehicle.type.toLowerCase().includes(searchTerm)
            );
            displayVehicles(filtered);
        }

        // وظائف الإضافة
        function addEmployee() {
            const name = prompt('اسم الموظف:');
            if (!name) return;

            const position = prompt('المسمى الوظيفي:');
            if (!position) return;

            const department = prompt('القسم:');
            if (!department) return;

            const phone = prompt('رقم الجوال:');
            if (!phone) return;

            const email = prompt('البريد الإلكتروني:');
            if (!email) return;

            const salary = prompt('الراتب:');

            const newEmployee = {
                id: Date.now().toString(),
                name: name,
                position: position,
                department: department,
                phone: phone,
                email: email,
                status: 'active',
                salary: salary ? parseInt(salary) : 0,
                joinDate: new Date().toISOString().split('T')[0]
            };

            employees.push(newEmployee);
            localStorage.setItem('employees', JSON.stringify(employees));
            displayEmployees(employees);
            updateStatistics();
            alert('✅ تم إضافة الموظف بنجاح');
        }

        function addDistributor() {
            const name = prompt('اسم المندوب:');
            if (!name) return;

            const phone = prompt('رقم الجوال:');
            if (!phone) return;

            const area = prompt('المنطقة:');
            if (!area) return;

            const commission = prompt('نسبة العمولة (%):');

            const newDistributor = {
                id: Date.now().toString(),
                name: name,
                phone: phone,
                area: area,
                isActive: true,
                vehicleId: null,
                commission: commission ? parseFloat(commission) : 5,
                totalDeliveries: 0
            };

            distributors.push(newDistributor);
            localStorage.setItem('distributors', JSON.stringify(distributors));
            displayDistributors(distributors);
            updateStatistics();
            alert('✅ تم إضافة المندوب بنجاح');
        }

        function addDepartment() {
            const name = prompt('اسم القسم:');
            if (!name) return;

            const manager = prompt('مدير القسم:');
            if (!manager) return;

            const budget = prompt('ميزانية القسم:');
            const description = prompt('وصف القسم:');

            const newDepartment = {
                id: Date.now().toString(),
                name: name,
                manager: manager,
                employeeCount: 0,
                budget: budget ? parseInt(budget) : 0,
                description: description || ''
            };

            departments.push(newDepartment);
            localStorage.setItem('departments', JSON.stringify(departments));
            displayDepartments(departments);
            updateStatistics();
            alert('✅ تم إضافة القسم بنجاح');
        }

        function addVehicle() {
            const plateNumber = prompt('رقم اللوحة:');
            if (!plateNumber) return;

            const model = prompt('موديل السيارة:');
            if (!model) return;

            const type = prompt('نوع السيارة:');
            if (!type) return;

            const purchasePrice = prompt('سعر الشراء:');

            const newVehicle = {
                id: 'V' + Date.now().toString().slice(-6),
                plateNumber: plateNumber,
                model: model,
                type: type,
                status: 'متاحة',
                driverId: null,
                distributorId: null,
                purchasePrice: purchasePrice ? parseInt(purchasePrice) : 0,
                maintenanceCost: 0
            };

            vehicles.push(newVehicle);
            localStorage.setItem('vehicles', JSON.stringify(vehicles));
            displayVehicles(vehicles);
            updateStatistics();
            alert('✅ تم إضافة السيارة بنجاح');
        }

        // وظائف التعديل
        function editEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (!employee) {
                alert('❌ لم يتم العثور على الموظف');
                return;
            }

            // ملء النموذج بالبيانات الحالية
            document.getElementById('editEmployeeId').value = employee.id;
            document.getElementById('editEmployeeName').value = employee.name;
            document.getElementById('editEmployeePosition').value = employee.position;
            document.getElementById('editEmployeeDepartment').value = employee.department;
            document.getElementById('editEmployeePhone').value = employee.phone;
            document.getElementById('editEmployeeEmail').value = employee.email;
            document.getElementById('editEmployeeSalary').value = employee.salary || '';

            // إظهار النموذج
            document.getElementById('editEmployeeModal').style.display = 'flex';
        }

        function closeEditEmployeeModal() {
            document.getElementById('editEmployeeModal').style.display = 'none';
        }

        function editDistributor(id) {
            const distributor = distributors.find(dist => dist.id === id);
            if (!distributor) {
                alert('❌ لم يتم العثور على المندوب');
                return;
            }

            const name = prompt('اسم المندوب:', distributor.name);
            if (name === null) return;

            const phone = prompt('رقم الجوال:', distributor.phone);
            if (phone === null) return;

            const area = prompt('المنطقة:', distributor.area);
            if (area === null) return;

            const commission = prompt('نسبة العمولة (%):', distributor.commission);
            if (commission === null) return;

            // تحديث البيانات
            distributor.name = name;
            distributor.phone = phone;
            distributor.area = area;
            distributor.commission = commission ? parseFloat(commission) : 5;

            localStorage.setItem('distributors', JSON.stringify(distributors));
            displayDistributors(distributors);
            updateStatistics();
            alert('✅ تم تحديث بيانات المندوب بنجاح');
        }

        function editDepartment(id) {
            const department = departments.find(dept => dept.id === id);
            if (!department) {
                alert('❌ لم يتم العثور على القسم');
                return;
            }

            const name = prompt('اسم القسم:', department.name);
            if (name === null) return;

            const manager = prompt('مدير القسم:', department.manager);
            if (manager === null) return;

            const budget = prompt('ميزانية القسم:', department.budget);
            if (budget === null) return;

            const description = prompt('وصف القسم:', department.description);
            if (description === null) return;

            // تحديث البيانات
            department.name = name;
            department.manager = manager;
            department.budget = budget ? parseInt(budget) : 0;
            department.description = description;

            localStorage.setItem('departments', JSON.stringify(departments));
            displayDepartments(departments);
            updateStatistics();
            alert('✅ تم تحديث بيانات القسم بنجاح');
        }

        function editVehicle(id) {
            const vehicle = vehicles.find(veh => veh.id === id);
            if (!vehicle) {
                alert('❌ لم يتم العثور على السيارة');
                return;
            }

            const plateNumber = prompt('رقم اللوحة:', vehicle.plateNumber);
            if (plateNumber === null) return;

            const model = prompt('موديل السيارة:', vehicle.model);
            if (model === null) return;

            const type = prompt('نوع السيارة:', vehicle.type);
            if (type === null) return;

            const status = prompt('حالة السيارة (متاحة/في الخدمة/في الصيانة):', vehicle.status);
            if (status === null) return;

            const purchasePrice = prompt('سعر الشراء:', vehicle.purchasePrice);
            if (purchasePrice === null) return;

            // تحديث البيانات
            vehicle.plateNumber = plateNumber;
            vehicle.model = model;
            vehicle.type = type;
            vehicle.status = status;
            vehicle.purchasePrice = purchasePrice ? parseInt(purchasePrice) : 0;

            localStorage.setItem('vehicles', JSON.stringify(vehicles));
            displayVehicles(vehicles);
            updateStatistics();
            alert('✅ تم تحديث بيانات السيارة بنجاح');
        }

        // وظائف الحذف
        function deleteEmployee(id) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                employees = employees.filter(emp => emp.id !== id);
                localStorage.setItem('employees', JSON.stringify(employees));
                displayEmployees(employees);
                updateStatistics();
                alert('✅ تم حذف الموظف بنجاح');
            }
        }

        function deleteDistributor(id) {
            if (confirm('هل أنت متأكد من حذف هذا المندوب؟')) {
                distributors = distributors.filter(dist => dist.id !== id);
                localStorage.setItem('distributors', JSON.stringify(distributors));
                displayDistributors(distributors);
                updateStatistics();
                alert('✅ تم حذف المندوب بنجاح');
            }
        }

        function deleteDepartment(id) {
            if (confirm('هل أنت متأكد من حذف هذا القسم؟')) {
                departments = departments.filter(dept => dept.id !== id);
                localStorage.setItem('departments', JSON.stringify(departments));
                displayDepartments(departments);
                updateStatistics();
                alert('✅ تم حذف القسم بنجاح');
            }
        }

        function deleteVehicle(id) {
            if (confirm('هل أنت متأكد من حذف هذه السيارة؟')) {
                vehicles = vehicles.filter(vehicle => vehicle.id !== id);
                localStorage.setItem('vehicles', JSON.stringify(vehicles));
                displayVehicles(vehicles);
                updateStatistics();
                alert('✅ تم حذف السيارة بنجاح');
            }
        }

        // وظائف التصدير
        function exportEmployees() {
            const dataStr = JSON.stringify(employees, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'employees.json';
            link.click();
            alert('✅ تم تصدير بيانات الموظفين بنجاح');
        }

        function exportDistributors() {
            const dataStr = JSON.stringify(distributors, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'distributors.json';
            link.click();
            alert('✅ تم تصدير بيانات المناديب بنجاح');
        }

        function exportDepartments() {
            const dataStr = JSON.stringify(departments, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'departments.json';
            link.click();
            alert('✅ تم تصدير بيانات الأقسام بنجاح');
        }

        function exportVehicles() {
            const dataStr = JSON.stringify(vehicles, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'vehicles.json';
            link.click();
            alert('✅ تم تصدير بيانات السيارات بنجاح');
        }

        // وظائف الحضور والانصراف
        function markAttendance() {
            // تحديث قائمة الموظفين في النموذج
            const employeeSelect = document.getElementById('attendanceEmployee');
            employeeSelect.innerHTML = '<option value="">-- اختر الموظف --</option>';

            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = employee.name;
                employeeSelect.appendChild(option);
            });

            // إظهار النموذج
            document.getElementById('attendanceModal').style.display = 'flex';
        }

        function showAttendanceForm(type) {
            document.getElementById('attendanceType').value = type;
            document.getElementById('attendanceForm').style.display = 'block';

            // تحديث الوقت الحالي
            const currentTime = new Date();
            document.getElementById('currentTime').value = currentTime.toLocaleString('ar-SA');
        }

        function closeAttendanceModal() {
            document.getElementById('attendanceModal').style.display = 'none';
            document.getElementById('attendanceForm').style.display = 'none';
        }

        function markLeave() {
            const employeeName = prompt('اسم الموظف:');
            if (!employeeName) return;

            let attendance = JSON.parse(localStorage.getItem('attendance') || '[]');
            const todayDate = new Date().toLocaleDateString('ar-SA');

            // البحث عن سجل الحضور لليوم
            const todayRecord = attendance.find(record =>
                record.employeeName === employeeName &&
                record.date === todayDate &&
                !record.checkOut
            );

            if (todayRecord) {
                todayRecord.checkOut = new Date().toLocaleTimeString('ar-SA');
                todayRecord.status = 'منصرف';
                localStorage.setItem('attendance', JSON.stringify(attendance));
                alert(`✅ تم تسجيل انصراف ${employeeName} في ${todayRecord.checkOut}`);
            } else {
                alert('❌ لم يتم العثور على سجل حضور لهذا الموظف اليوم');
            }
        }

        function viewAttendanceReport() {
            const attendance = JSON.parse(localStorage.getItem('attendance') || '[]');
            const todayDate = new Date().toLocaleDateString('ar-SA');
            const todayAttendance = attendance.filter(record => record.date === todayDate);

            if (todayAttendance.length === 0) {
                alert('📊 لا توجد سجلات حضور لليوم');
                return;
            }

            let report = `📊 تقرير الحضور ليوم ${todayDate}\n\n`;
            todayAttendance.forEach(record => {
                report += `👤 ${record.employeeName}\n`;
                report += `⏰ الحضور: ${record.checkIn}\n`;
                report += `⏰ الانصراف: ${record.checkOut || 'لم ينصرف بعد'}\n`;
                report += `📋 الحالة: ${record.status}\n\n`;
            });

            alert(report);
        }

        function exportAttendance() {
            const attendance = JSON.parse(localStorage.getItem('attendance') || '[]');
            if (attendance.length === 0) {
                alert('❌ لا توجد بيانات حضور للتصدير');
                return;
            }

            const dataStr = JSON.stringify(attendance, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'attendance.json';
            link.click();
            alert('✅ تم تصدير بيانات الحضور بنجاح');
        }

        function loadAttendanceData() {
            console.log('⏰ تحميل بيانات الحضور...');
            const attendance = JSON.parse(localStorage.getItem('attendance') || '[]');
            const todayDate = new Date().toLocaleDateString('ar-SA');
            const presentToday = attendance.filter(record =>
                record.date === todayDate && record.status === 'حاضر'
            ).length;

            document.getElementById('presentToday').textContent = presentToday;
        }

        // وظائف الرواتب
        function generatePayroll() {
            if (employees.length === 0) {
                alert('❌ لا توجد موظفين لإنشاء كشف راتب');
                return;
            }

            const currentMonth = new Date().toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
            let payrollData = [];
            let totalPayroll = 0;

            employees.forEach(employee => {
                if (employee.salary && employee.salary > 0) {
                    const payrollRecord = {
                        id: Date.now().toString() + employee.id,
                        employeeId: employee.id,
                        employeeName: employee.name,
                        position: employee.position,
                        department: employee.department,
                        basicSalary: employee.salary,
                        allowances: Math.round(employee.salary * 0.1), // 10% بدلات
                        deductions: Math.round(employee.salary * 0.05), // 5% خصومات
                        netSalary: Math.round(employee.salary + (employee.salary * 0.1) - (employee.salary * 0.05)),
                        month: currentMonth,
                        date: new Date().toISOString().split('T')[0],
                        status: 'مُعد'
                    };

                    payrollData.push(payrollRecord);
                    totalPayroll += payrollRecord.netSalary;
                }
            });

            // حفظ كشف الراتب
            let payrolls = JSON.parse(localStorage.getItem('payrolls') || '[]');
            payrolls.push({
                id: Date.now().toString(),
                month: currentMonth,
                date: new Date().toISOString().split('T')[0],
                employees: payrollData,
                totalAmount: totalPayroll,
                status: 'مُعد'
            });
            localStorage.setItem('payrolls', JSON.stringify(payrolls));

            alert(`✅ تم إنشاء كشف راتب شهر ${currentMonth}\nإجمالي الرواتب: ${totalPayroll.toLocaleString()} ريال\nعدد الموظفين: ${payrollData.length}`);
            updateStatistics();
        }

        function viewPayrollHistory() {
            const payrolls = JSON.parse(localStorage.getItem('payrolls') || '[]');

            if (payrolls.length === 0) {
                alert('📋 لا يوجد تاريخ رواتب');
                return;
            }

            let history = '📋 تاريخ كشوف الرواتب:\n\n';
            payrolls.forEach((payroll, index) => {
                history += `${index + 1}. شهر ${payroll.month}\n`;
                history += `📅 التاريخ: ${payroll.date}\n`;
                history += `👥 عدد الموظفين: ${payroll.employees.length}\n`;
                history += `💰 إجمالي المبلغ: ${payroll.totalAmount.toLocaleString()} ريال\n`;
                history += `📊 الحالة: ${payroll.status}\n\n`;
            });

            alert(history);
        }

        function exportPayroll() {
            const payrolls = JSON.parse(localStorage.getItem('payrolls') || '[]');

            if (payrolls.length === 0) {
                alert('❌ لا توجد بيانات رواتب للتصدير');
                return;
            }

            const dataStr = JSON.stringify(payrolls, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'payrolls.json';
            link.click();
            alert('✅ تم تصدير بيانات الرواتب بنجاح');
        }

        function loadPayrollData() {
            console.log('💰 تحميل بيانات الرواتب...');
            const payrolls = JSON.parse(localStorage.getItem('payrolls') || '[]');
            const currentMonth = new Date().toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });

            // البحث عن كشف راتب الشهر الحالي
            const currentPayroll = payrolls.find(payroll => payroll.month === currentMonth);

            if (currentPayroll) {
                const totalPayrollElement = document.getElementById('totalPayroll');
                if (totalPayrollElement) {
                    totalPayrollElement.textContent = currentPayroll.totalAmount.toLocaleString();
                }
            }
        }

        // وظائف التكامل
        function loadIntegrationData() {
            console.log('🔗 تحميل بيانات التكامل...');
        }

        function openVehicleManagement() {
            window.open('vehicle-management.html', '_blank');
        }

        function openFinancialSystem() {
            window.open('financial-system.html', '_blank');
        }

        // وظائف إضافية
        function viewEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (!employee) {
                alert('❌ لم يتم العثور على الموظف');
                return;
            }

            const details = `👤 تفاصيل الموظف:\n\n` +
                `📝 الاسم: ${employee.name}\n` +
                `💼 المسمى الوظيفي: ${employee.position}\n` +
                `🏢 القسم: ${employee.department}\n` +
                `📱 الجوال: ${employee.phone}\n` +
                `📧 البريد الإلكتروني: ${employee.email}\n` +
                `💰 الراتب: ${employee.salary ? employee.salary.toLocaleString() + ' ريال' : 'غير محدد'}\n` +
                `📅 تاريخ التوظيف: ${employee.joinDate}\n` +
                `📊 الحالة: ${employee.status === 'active' ? 'نشط' : 'غير نشط'}`;

            alert(details);
        }

        function viewDistributor(id) {
            const distributor = distributors.find(dist => dist.id === id);
            if (!distributor) {
                alert('❌ لم يتم العثور على المندوب');
                return;
            }

            const details = `🚚 تفاصيل المندوب:\n\n` +
                `📝 الاسم: ${distributor.name}\n` +
                `📱 الجوال: ${distributor.phone}\n` +
                `📍 المنطقة: ${distributor.area}\n` +
                `🚗 السيارة: ${distributor.vehicleId || 'غير محددة'}\n` +
                `💰 العمولة: ${distributor.commission}%\n` +
                `📦 إجمالي التوصيلات: ${distributor.totalDeliveries}\n` +
                `📊 الحالة: ${distributor.isActive ? 'نشط' : 'غير نشط'}`;

            alert(details);
        }

        function viewDepartment(id) {
            const department = departments.find(dept => dept.id === id);
            if (!department) {
                alert('❌ لم يتم العثور على القسم');
                return;
            }

            const details = `🏢 تفاصيل القسم:\n\n` +
                `📝 اسم القسم: ${department.name}\n` +
                `👨‍💼 المدير: ${department.manager}\n` +
                `👥 عدد الموظفين: ${department.employeeCount}\n` +
                `💰 الميزانية: ${department.budget ? department.budget.toLocaleString() + ' ريال' : 'غير محددة'}\n` +
                `📋 الوصف: ${department.description}`;

            alert(details);
        }

        function viewVehicle(id) {
            const vehicle = vehicles.find(veh => veh.id === id);
            if (!vehicle) {
                alert('❌ لم يتم العثور على السيارة');
                return;
            }

            const details = `🚗 تفاصيل السيارة:\n\n` +
                `🔢 رقم اللوحة: ${vehicle.plateNumber}\n` +
                `🚙 الموديل: ${vehicle.model}\n` +
                `📋 النوع: ${vehicle.type}\n` +
                `📊 الحالة: ${vehicle.status}\n` +
                `👨‍💼 السائق: ${vehicle.driverId ? 'مخصص' : 'غير مخصص'}\n` +
                `🚚 المندوب: ${vehicle.distributorId ? 'مخصص' : 'غير مخصص'}\n` +
                `💰 سعر الشراء: ${vehicle.purchasePrice ? vehicle.purchasePrice.toLocaleString() + ' ريال' : 'غير محدد'}\n` +
                `🔧 تكلفة الصيانة: ${vehicle.maintenanceCost ? vehicle.maintenanceCost.toLocaleString() + ' ريال' : 'غير محددة'}`;

            alert(details);
        }

        function assignVehicle(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) {
                alert('❌ لم يتم العثور على الموظف');
                return;
            }

            const availableVehicles = vehicles.filter(vehicle => vehicle.status === 'متاحة');
            if (availableVehicles.length === 0) {
                alert('❌ لا توجد سيارات متاحة للتخصيص');
                return;
            }

            let vehicleList = 'السيارات المتاحة:\n\n';
            availableVehicles.forEach((vehicle, index) => {
                vehicleList += `${index + 1}. ${vehicle.plateNumber} - ${vehicle.model}\n`;
            });

            const choice = prompt(vehicleList + '\nاختر رقم السيارة:');
            if (choice && choice > 0 && choice <= availableVehicles.length) {
                const selectedVehicle = availableVehicles[choice - 1];
                selectedVehicle.driverId = employeeId;
                selectedVehicle.status = 'في الخدمة';

                localStorage.setItem('vehicles', JSON.stringify(vehicles));
                displayVehicles(vehicles);
                alert(`✅ تم تخصيص السيارة ${selectedVehicle.plateNumber} للموظف ${employee.name}`);
            }
        }

        function assignVehicleToDistributor(distributorId) {
            const distributor = distributors.find(dist => dist.id === distributorId);
            if (!distributor) {
                alert('❌ لم يتم العثور على المندوب');
                return;
            }

            const availableVehicles = vehicles.filter(vehicle => vehicle.status === 'متاحة');
            if (availableVehicles.length === 0) {
                alert('❌ لا توجد سيارات متاحة للتخصيص');
                return;
            }

            let vehicleList = 'السيارات المتاحة:\n\n';
            availableVehicles.forEach((vehicle, index) => {
                vehicleList += `${index + 1}. ${vehicle.plateNumber} - ${vehicle.model}\n`;
            });

            const choice = prompt(vehicleList + '\nاختر رقم السيارة:');
            if (choice && choice > 0 && choice <= availableVehicles.length) {
                const selectedVehicle = availableVehicles[choice - 1];
                selectedVehicle.distributorId = distributorId;
                selectedVehicle.status = 'في الخدمة';
                distributor.vehicleId = selectedVehicle.id;

                localStorage.setItem('vehicles', JSON.stringify(vehicles));
                localStorage.setItem('distributors', JSON.stringify(distributors));
                displayVehicles(vehicles);
                displayDistributors(distributors);
                alert(`✅ تم تخصيص السيارة ${selectedVehicle.plateNumber} للمندوب ${distributor.name}`);
            }
        }

        function assignDriver(vehicleId) {
            const vehicle = vehicles.find(veh => veh.id === vehicleId);
            if (!vehicle) {
                alert('❌ لم يتم العثور على السيارة');
                return;
            }

            const availableEmployees = employees.filter(emp => emp.status === 'active');
            if (availableEmployees.length === 0) {
                alert('❌ لا توجد موظفين متاحين');
                return;
            }

            let employeeList = 'الموظفين المتاحين:\n\n';
            availableEmployees.forEach((employee, index) => {
                employeeList += `${index + 1}. ${employee.name} - ${employee.position}\n`;
            });

            const choice = prompt(employeeList + '\nاختر رقم الموظف:');
            if (choice && choice > 0 && choice <= availableEmployees.length) {
                const selectedEmployee = availableEmployees[choice - 1];
                vehicle.driverId = selectedEmployee.id;
                vehicle.status = 'في الخدمة';

                localStorage.setItem('vehicles', JSON.stringify(vehicles));
                displayVehicles(vehicles);
                alert(`✅ تم تخصيص السائق ${selectedEmployee.name} للسيارة ${vehicle.plateNumber}`);
            }
        }

        function manageDepartmentEmployees(departmentId) {
            const department = departments.find(dept => dept.id === departmentId);
            if (!department) {
                alert('❌ لم يتم العثور على القسم');
                return;
            }

            const departmentEmployees = employees.filter(emp => emp.department === department.name);

            if (departmentEmployees.length === 0) {
                alert(`👥 لا يوجد موظفين في قسم ${department.name}`);
                return;
            }

            let employeeList = `👥 موظفي قسم ${department.name}:\n\n`;
            departmentEmployees.forEach((employee, index) => {
                employeeList += `${index + 1}. ${employee.name} - ${employee.position}\n`;
                employeeList += `   📱 ${employee.phone}\n`;
                employeeList += `   💰 ${employee.salary ? employee.salary.toLocaleString() + ' ريال' : 'غير محدد'}\n\n`;
            });

            alert(employeeList);
        }

        // معالج إرسال نموذج التعديل
        document.getElementById('editEmployeeForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const id = document.getElementById('editEmployeeId').value;
            const employee = employees.find(emp => emp.id === id);

            if (employee) {
                employee.name = document.getElementById('editEmployeeName').value;
                employee.position = document.getElementById('editEmployeePosition').value;
                employee.department = document.getElementById('editEmployeeDepartment').value;
                employee.phone = document.getElementById('editEmployeePhone').value;
                employee.email = document.getElementById('editEmployeeEmail').value;
                employee.salary = parseInt(document.getElementById('editEmployeeSalary').value) || 0;

                localStorage.setItem('employees', JSON.stringify(employees));
                displayEmployees(employees);
                updateStatistics();
                closeEditEmployeeModal();
                alert('✅ تم تحديث بيانات الموظف بنجاح');
            }
        });

        // معالج إرسال نموذج الحضور
        document.getElementById('attendanceFormElement').addEventListener('submit', function(e) {
            e.preventDefault();

            const employeeId = document.getElementById('attendanceEmployee').value;
            const attendanceType = document.getElementById('attendanceType').value;

            if (!employeeId) {
                alert('❌ يرجى اختيار الموظف');
                return;
            }

            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) {
                alert('❌ لم يتم العثور على الموظف');
                return;
            }

            const currentTime = new Date();
            let attendance = JSON.parse(localStorage.getItem('attendance') || '[]');

            if (attendanceType === 'checkin') {
                // تسجيل الحضور
                const attendanceRecord = {
                    id: Date.now().toString(),
                    employeeId: employeeId,
                    employeeName: employee.name,
                    date: currentTime.toLocaleDateString('ar-SA'),
                    checkIn: currentTime.toLocaleTimeString('ar-SA'),
                    checkOut: null,
                    status: 'حاضر'
                };

                attendance.push(attendanceRecord);
                localStorage.setItem('attendance', JSON.stringify(attendance));
                alert(`✅ تم تسجيل حضور ${employee.name} في ${attendanceRecord.checkIn}`);

            } else if (attendanceType === 'checkout') {
                // تسجيل الانصراف
                const todayDate = currentTime.toLocaleDateString('ar-SA');
                const todayRecord = attendance.find(record =>
                    record.employeeId === employeeId &&
                    record.date === todayDate &&
                    !record.checkOut
                );

                if (todayRecord) {
                    todayRecord.checkOut = currentTime.toLocaleTimeString('ar-SA');
                    todayRecord.status = 'منصرف';

                    // حساب ساعات العمل
                    const checkInTime = new Date(`${todayDate} ${todayRecord.checkIn}`);
                    const checkOutTime = new Date(`${todayDate} ${todayRecord.checkOut}`);
                    const workingHours = Math.round((checkOutTime - checkInTime) / (1000 * 60 * 60) * 100) / 100;
                    todayRecord.workingHours = workingHours;

                    localStorage.setItem('attendance', JSON.stringify(attendance));
                    alert(`✅ تم تسجيل انصراف ${employee.name} في ${todayRecord.checkOut}\nساعات العمل: ${workingHours} ساعة`);
                } else {
                    alert('❌ لم يتم العثور على تسجيل حضور لهذا اليوم');
                }
            }

            updateStatistics();
            closeAttendanceModal();
        });

        console.log('✅ تم تحميل جميع وظائف نظام الموارد البشرية المتكامل بنجاح');
    </script>
</body>
</html>
