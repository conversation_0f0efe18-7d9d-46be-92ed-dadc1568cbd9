'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'

type Language = 'ar' | 'en'
type Direction = 'rtl' | 'ltr'

interface LanguageContextType {
  language: Language
  direction: Direction
  setLanguage: (lang: Language) => void
  t: (key: string, params?: Record<string, string>) => string
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

// Import translations
import translations from '@/lib/i18n/translations.json'

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>('ar')
  const router = useRouter()
  const pathname = usePathname()

  const direction: Direction = language === 'ar' ? 'rtl' : 'ltr'
  const isRTL = direction === 'rtl'

  // Load language from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language
    if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
      setLanguageState(savedLanguage)
    }
  }, [])

  // Update document direction and lang when language changes
  useEffect(() => {
    document.documentElement.lang = language
    document.documentElement.dir = direction
    
    // Update body classes for styling
    document.body.classList.remove('rtl', 'ltr')
    document.body.classList.add(direction)
  }, [language, direction])

  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    localStorage.setItem('language', lang)
    
    // Update document attributes immediately
    document.documentElement.lang = lang
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr'
    
    // Update body classes
    document.body.classList.remove('rtl', 'ltr')
    document.body.classList.add(lang === 'ar' ? 'rtl' : 'ltr')
  }

  // Translation function
  const t = (key: string, params?: Record<string, string>): string => {
    try {
      const keys = key.split('.')
      let value: any = translations[language]
      
      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k]
        } else {
          // Fallback to English if key not found in current language
          value = translations['en']
          for (const fallbackKey of keys) {
            if (value && typeof value === 'object' && fallbackKey in value) {
              value = value[fallbackKey]
            } else {
              return key // Return key if not found in any language
            }
          }
          break
        }
      }
      
      if (typeof value !== 'string') {
        return key
      }
      
      // Replace parameters if provided
      if (params) {
        return Object.entries(params).reduce(
          (text, [param, replacement]) => text.replace(`{{${param}}}`, replacement),
          value
        )
      }
      
      return value
    } catch (error) {
      console.error('Translation error:', error)
      return key
    }
  }

  const value: LanguageContextType = {
    language,
    direction,
    setLanguage,
    t,
    isRTL,
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}
