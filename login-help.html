<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مساعدة تسجيل الدخول | نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .help-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            max-width: 900px;
            width: 100%;
        }

        .help-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .help-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }

        .help-section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .help-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .credentials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .credential-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s;
        }

        .credential-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .credential-card h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .credential-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 8px 0;
            font-family: monospace;
            font-size: 0.9rem;
            border: 1px solid #e9ecef;
        }

        .step-list {
            list-style: none;
            padding: 0;
        }

        .step-list li {
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .step-list li:last-child {
            border-bottom: none;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .login-btn {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 20px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .back-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s;
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }

        .troubleshooting {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .troubleshooting h4 {
            margin-bottom: 15px;
            color: #721c24;
        }

        .troubleshooting ul {
            padding-right: 20px;
        }

        .troubleshooting li {
            margin: 8px 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <a href="unified-login.html" class="back-link">← العودة لتسجيل الدخول</a>
    
    <div class="help-container">
        <div class="help-header">
            <h1>🔐 مساعدة تسجيل الدخول</h1>
            <p>دليل شامل لاستخدام نظام تسجيل الدخول الموحد</p>
        </div>

        <div class="help-section">
            <h3>📋 خطوات تسجيل الدخول</h3>
            <ol class="step-list">
                <li>
                    <span class="step-number">1</span>
                    <div>
                        <strong>اختر نوع المستخدم:</strong>
                        <br>اضغط على "👤 عميل" أو "👨‍💼 مدير النظام" حسب نوع حسابك
                    </div>
                </li>
                <li>
                    <span class="step-number">2</span>
                    <div>
                        <strong>أدخل بيانات تسجيل الدخول:</strong>
                        <br>استخدم البريد الإلكتروني/اسم المستخدم وكلمة المرور
                    </div>
                </li>
                <li>
                    <span class="step-number">3</span>
                    <div>
                        <strong>اضغط "تسجيل الدخول":</strong>
                        <br>أو استخدم تسجيل الدخول السريع عبر Google/Apple/Facebook
                    </div>
                </li>
            </ol>
        </div>

        <div class="help-section">
            <h3>🔑 الحسابات التجريبية المتاحة</h3>
            
            <div class="credentials-grid">
                <div class="credential-card">
                    <h4>👨‍💼 مدير النظام</h4>
                    <div class="credential-item">
                        <strong>المستخدم:</strong> admin<br>
                        <strong>كلمة المرور:</strong> 123456
                    </div>
                    <div class="credential-item">
                        <strong>البريد:</strong> <EMAIL><br>
                        <strong>كلمة المرور:</strong> admin123
                    </div>
                </div>

                <div class="credential-card">
                    <h4>👨‍💻 موظف النظام</h4>
                    <div class="credential-item">
                        <strong>المستخدم:</strong> employee<br>
                        <strong>كلمة المرور:</strong> 123456
                    </div>
                    <div class="credential-item">
                        <strong>البريد:</strong> <EMAIL><br>
                        <strong>كلمة المرور:</strong> emp123
                    </div>
                </div>

                <div class="credential-card">
                    <h4>👤 عميل</h4>
                    <div class="credential-item">
                        <strong>المستخدم:</strong> customer<br>
                        <strong>كلمة المرور:</strong> 123456
                    </div>
                    <div class="credential-item">
                        <strong>البريد:</strong> <EMAIL><br>
                        <strong>كلمة المرور:</strong> client123
                    </div>
                </div>
            </div>
        </div>

        <div class="help-section">
            <h3>⚠️ أخطاء شائعة وحلولها</h3>
            
            <div class="troubleshooting">
                <h4>❌ "البريد الإلكتروني أو كلمة المرور غير صحيحة"</h4>
                <ul>
                    <li><strong>تأكد من اختيار نوع المستخدم الصحيح أولاً</strong> (👤 عميل أو 👨‍💼 مدير النظام)</li>
                    <li>تحقق من كتابة البيانات بشكل صحيح (لا توجد مسافات زائدة)</li>
                    <li>تأكد من استخدام الحسابات التجريبية المذكورة أعلاه</li>
                    <li>جرب نسخ ولصق البيانات من هذه الصفحة</li>
                </ul>
            </div>

            <div class="warning-box">
                <strong>💡 نصيحة مهمة:</strong> يجب اختيار نوع المستخدم الصحيح قبل إدخال البيانات. إذا كنت عميل، اختر "👤 عميل" وليس "👨‍💼 مدير النظام".
            </div>
        </div>

        <div class="help-section">
            <h3>🚀 تسجيل الدخول السريع</h3>
            <p>يمكنك أيضاً تسجيل الدخول باستخدام:</p>
            <ul style="padding-right: 20px; margin-top: 15px;">
                <li><strong>🔗 Google:</strong> يعمل مع جميع أنواع المستخدمين</li>
                <li><strong>🍎 Apple:</strong> يعمل مع جميع أنواع المستخدمين</li>
                <li><strong>📘 Facebook:</strong> يعمل مع جميع أنواع المستخدمين</li>
            </ul>
            
            <div class="success-box">
                <strong>✅ ملاحظة:</strong> تسجيل الدخول الاجتماعي يعمل بشكل تجريبي ولا يتطلب حسابات حقيقية.
            </div>
        </div>

        <div class="help-section">
            <h3>🎯 اختبار سريع</h3>
            <p>لاختبار النظام بسرعة:</p>
            <ol style="padding-right: 20px; margin-top: 15px;">
                <li>اختر "👤 عميل"</li>
                <li>أدخل: <code>customer</code> في حقل البريد الإلكتروني</li>
                <li>أدخل: <code>123456</code> في حقل كلمة المرور</li>
                <li>اضغط "تسجيل الدخول"</li>
            </ol>
        </div>

        <a href="unified-login.html" class="login-btn">
            🔐 جرب تسجيل الدخول الآن
        </a>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            const cards = document.querySelectorAll('.credential-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    // نسخ البيانات للحافظة
                    const credentials = this.querySelector('.credential-item').textContent;
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(credentials).then(() => {
                            // إظهار رسالة تأكيد
                            const msg = document.createElement('div');
                            msg.style.cssText = `
                                position: fixed;
                                top: 20px;
                                left: 50%;
                                transform: translateX(-50%);
                                background: #28a745;
                                color: white;
                                padding: 10px 20px;
                                border-radius: 8px;
                                z-index: 1000;
                                font-size: 0.9rem;
                            `;
                            msg.textContent = 'تم نسخ البيانات!';
                            document.body.appendChild(msg);
                            
                            setTimeout(() => {
                                if (msg.parentNode) {
                                    msg.remove();
                                }
                            }, 2000);
                        });
                    }
                });
            });

            console.log('🔐 صفحة مساعدة تسجيل الدخول جاهزة');
        });
    </script>
</body>
</html>
