<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الدفع عند الاستلام - النظام المالي</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 600;
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total {
            border-left-color: #dc3545;
        }

        .stat-card.collected {
            border-left-color: #28a745;
        }

        .stat-card.pending {
            border-left-color: #ffc107;
        }

        .stat-card.shipments {
            border-left-color: #17a2b8;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input,
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            min-width: 200px;
        }

        .search-input:focus,
        .filter-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .cod-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-collected {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .status-failed {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-data h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input,
            .filter-select {
                min-width: auto;
            }

            .actions {
                justify-content: center;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <a href="financial-system.html" class="back-link">← العودة للنظام المالي</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🏪</span>
                <span>إدارة الدفع عند الاستلام</span>
            </div>
            
            <nav class="nav-links">
                <a href="financial-system.html">الرئيسية</a>
                <a href="collection-management.html">التحصيل</a>
                <a href="payment-management.html">المدفوعات</a>
                <a href="commission-management.html">العمولات</a>
                <a href="cod-management.html" class="active">الدفع عند الاستلام</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">🏪 إدارة الدفع عند الاستلام (COD)</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-primary" onclick="addDeferredPayment()">
                    ➕ إضافة دفع مؤجل
                </button>
                <button class="btn btn-success" onclick="markAllAsCollected()">
                    ✅ تحصيل جميع المعلقة
                </button>
                <button class="btn" onclick="loadCodPayments()">
                    🔄 تحديث القائمة
                </button>
            </div>
        </div>

        <!-- إحصائيات الدفع عند الاستلام -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="card-icon">💰</div>
                <div class="card-amount" id="totalCodAmount">0</div>
                <div class="card-label">إجمالي مبالغ COD</div>
            </div>
            
            <div class="stat-card collected">
                <div class="card-icon">✅</div>
                <div class="card-amount" id="collectedAmount">0</div>
                <div class="card-label">المبالغ المحصلة</div>
            </div>
            
            <div class="stat-card pending">
                <div class="card-icon">⏳</div>
                <div class="card-amount" id="pendingAmount">0</div>
                <div class="card-label">المبالغ المعلقة</div>
            </div>
            
            <div class="stat-card shipments">
                <div class="card-icon">📦</div>
                <div class="card-amount" id="totalCodShipments">0</div>
                <div class="card-label">شحنات COD</div>
            </div>
        </div>

        <!-- أدوات التحكم والبحث -->
        <div class="controls">
            <input type="text" id="searchInput" class="search-input" placeholder="🔍 البحث في مدفوعات COD...">
            
            <select id="statusFilter" class="filter-select">
                <option value="">جميع الحالات</option>
                <option value="محصل">محصل</option>
                <option value="معلق">معلق</option>
                <option value="فشل">فشل في التحصيل</option>
            </select>
            
            <select id="distributorFilter" class="filter-select">
                <option value="">جميع المناديب</option>
            </select>
            
            <button class="btn" onclick="clearFilters()">مسح الفلاتر</button>
        </div>

        <!-- جدول مدفوعات COD -->
        <div class="cod-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الدفع</th>
                        <th>رقم الشحنة</th>
                        <th>العميل</th>
                        <th>المندوب</th>
                        <th>المبلغ</th>
                        <th>العملة</th>
                        <th>تاريخ التحصيل</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="codTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>
            
            <!-- رسالة عدم وجود بيانات -->
            <div id="noDataMessage" class="no-data" style="display: none;">
                <h3>🏪 لا توجد مدفوعات COD</h3>
                <p>لم يتم العثور على أي مدفوعات دفع عند الاستلام.</p>
            </div>
        </div>
    </main>

    <script src="js/database-simple.js"></script>
    <script src="js/financial-database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏪 تحميل صفحة إدارة الدفع عند الاستلام...');
            
            try {
                loadCodPayments();
                loadDistributors();
                setupEventListeners();
                loadStats();
                
                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterCodPayments);
            document.getElementById('statusFilter').addEventListener('change', filterCodPayments);
            document.getElementById('distributorFilter').addEventListener('change', filterCodPayments);
        }

        // تحميل قائمة المناديب
        function loadDistributors() {
            try {
                const distributors = db.getAllDistributors();
                const distributorFilter = document.getElementById('distributorFilter');
                
                distributorFilter.innerHTML = '<option value="">جميع المناديب</option>';
                
                distributors.forEach(distributor => {
                    const option = document.createElement('option');
                    option.value = distributor.name;
                    option.textContent = distributor.name;
                    distributorFilter.appendChild(option);
                });
                
            } catch (error) {
                console.error('❌ خطأ في تحميل المناديب:', error);
            }
        }

        // تحميل وعرض مدفوعات COD
        function loadCodPayments() {
            try {
                const codPayments = financialDb.getAllCodPayments();
                displayCodPayments(codPayments);
                console.log('🏪 تم تحميل', codPayments.length, 'دفع COD');
            } catch (error) {
                console.error('❌ خطأ في تحميل مدفوعات COD:', error);
                alert('خطأ في تحميل مدفوعات COD: ' + error.message);
            }
        }

        // عرض مدفوعات COD في الجدول
        function displayCodPayments(payments) {
            const tbody = document.getElementById('codTableBody');
            const noDataMessage = document.getElementById('noDataMessage');
            
            if (payments.length === 0) {
                tbody.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }

            noDataMessage.style.display = 'none';
            
            tbody.innerHTML = payments.map(payment => 
                `<tr>
                    <td><strong>${payment.id}</strong></td>
                    <td>${payment.shipmentId}</td>
                    <td>${payment.customerName}</td>
                    <td>${payment.distributorName}</td>
                    <td>${(payment.amount || 0).toFixed(2)}</td>
                    <td>${payment.currency || 'SAR'}</td>
                    <td>${formatDate(payment.collectionDate)}</td>
                    <td><span class="status-badge status-${getStatusClass(payment.status)}">${payment.status}</span></td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-small" onclick="viewCodPayment('${payment.id}')">عرض</button>
                            ${payment.status === 'معلق' ? 
                                `<button class="btn btn-small btn-success" onclick="markAsCollected('${payment.id}')">تحصيل</button>` : 
                                ''}
                            <button class="btn btn-small btn-warning" onclick="editCodPayment('${payment.id}')">تعديل</button>
                        </div>
                    </td>
                </tr>`
            ).join('');
        }

        // تحميل الإحصائيات
        function loadStats() {
            try {
                const payments = financialDb.getAllCodPayments();

                const totalAmount = payments.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0);
                const collectedPayments = payments.filter(p => p.status === 'محصل');
                const pendingPayments = payments.filter(p => p.status === 'معلق');
                const collectedAmount = collectedPayments.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0);
                const pendingAmount = pendingPayments.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0);

                // تحديث الإحصائيات مع التنسيق المحسن
                document.getElementById('totalCodAmount').textContent = totalAmount.toFixed(2);
                document.getElementById('collectedAmount').textContent = collectedAmount.toFixed(2);
                document.getElementById('pendingAmount').textContent = pendingAmount.toFixed(2);
                document.getElementById('totalCodShipments').textContent = payments.length;

                // تحديث ألوان البطاقات حسب الحالة
                const totalCard = document.querySelector('.stat-card.total');
                const collectedCard = document.querySelector('.stat-card.collected');
                const pendingCard = document.querySelector('.stat-card.pending');
                const shipmentsCard = document.querySelector('.stat-card.shipments');

                if (totalCard) {
                    totalCard.style.background = totalAmount > 0 ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8f9fa';
                    totalCard.style.color = totalAmount > 0 ? 'white' : '#6c757d';
                }

                if (collectedCard) {
                    collectedCard.style.background = collectedPayments.length > 0 ? 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)' : '#f8f9fa';
                    collectedCard.style.color = collectedPayments.length > 0 ? 'white' : '#6c757d';
                }

                if (pendingCard) {
                    pendingCard.style.background = pendingPayments.length > 0 ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' : '#f8f9fa';
                    pendingCard.style.color = pendingPayments.length > 0 ? 'white' : '#6c757d';
                }

                if (shipmentsCard) {
                    shipmentsCard.style.background = payments.length > 0 ? 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' : '#f8f9fa';
                    shipmentsCard.style.color = payments.length > 0 ? 'white' : '#6c757d';
                }

                console.log('📊 تم تحديث الإحصائيات:', {
                    total: totalAmount,
                    collected: collectedAmount,
                    pending: pendingAmount,
                    shipments: payments.length
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات:', error);
            }
        }

        // فلترة مدفوعات COD
        function filterCodPayments() {
            try {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;
                const distributorFilter = document.getElementById('distributorFilter').value;
                
                const allPayments = financialDb.getAllCodPayments();
                
                const filteredPayments = allPayments.filter(payment => {
                    const matchesSearch = !searchTerm || 
                        payment.id.toLowerCase().includes(searchTerm) ||
                        payment.shipmentId.toLowerCase().includes(searchTerm) ||
                        payment.customerName.toLowerCase().includes(searchTerm) ||
                        payment.distributorName.toLowerCase().includes(searchTerm);
                    
                    const matchesStatus = !statusFilter || payment.status === statusFilter;
                    const matchesDistributor = !distributorFilter || payment.distributorName === distributorFilter;
                    
                    return matchesSearch && matchesStatus && matchesDistributor;
                });
                
                displayCodPayments(filteredPayments);
            } catch (error) {
                console.error('❌ خطأ في فلترة مدفوعات COD:', error);
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('distributorFilter').value = '';
            loadCodPayments();
        }

        // عرض تفاصيل دفع COD
        function viewCodPayment(id) {
            try {
                const payment = financialDb.getAllCodPayments().find(p => p.id === id);
                if (!payment) {
                    alert('لم يتم العثور على الدفع');
                    return;
                }
                
                alert(`تفاصيل الدفع عند الاستلام ${payment.id}:\n\n` +
                      `رقم الشحنة: ${payment.shipmentId}\n` +
                      `العميل: ${payment.customerName}\n` +
                      `المندوب: ${payment.distributorName}\n` +
                      `المبلغ: ${payment.amount} ${payment.currency}\n` +
                      `تاريخ التحصيل: ${formatDate(payment.collectionDate)}\n` +
                      `الحالة: ${payment.status}\n` +
                      `تاريخ الإنشاء: ${formatDate(payment.createdDate)}\n` +
                      `ملاحظات: ${payment.notes || 'لا توجد ملاحظات'}`);
            } catch (error) {
                console.error('❌ خطأ في عرض الدفع:', error);
                alert('خطأ في عرض الدفع: ' + error.message);
            }
        }

        // تحصيل دفع COD
        function markAsCollected(id) {
            try {
                const payment = financialDb.getAllCodPayments().find(p => p.id === id);
                if (!payment) {
                    alert('لم يتم العثور على الدفع');
                    return;
                }
                
                if (confirm(`هل تم تحصيل المبلغ؟\n\nالشحنة: ${payment.shipmentId}\nالمبلغ: ${payment.amount} ${payment.currency}`)) {
                    const updatedPayment = financialDb.updateCodPayment(id, {
                        status: 'محصل',
                        collectionDate: new Date().toISOString().split('T')[0]
                    });
                    
                    if (updatedPayment) {
                        alert('تم تحديث حالة الدفع إلى "محصل" بنجاح');
                        loadCodPayments();
                        loadStats();
                    } else {
                        alert('خطأ في تحديث حالة الدفع');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في تحصيل الدفع:', error);
                alert('خطأ في تحصيل الدفع: ' + error.message);
            }
        }

        // تحصيل جميع المدفوعات المعلقة
        function markAllAsCollected() {
            try {
                const payments = financialDb.getAllCodPayments();
                const pendingPayments = payments.filter(p => p.status === 'معلق');
                
                if (pendingPayments.length === 0) {
                    alert('لا توجد مدفوعات معلقة للتحصيل');
                    return;
                }
                
                if (confirm(`هل تريد تحصيل جميع المدفوعات المعلقة؟\nعدد المدفوعات: ${pendingPayments.length}`)) {
                    let collectedCount = 0;
                    
                    pendingPayments.forEach(payment => {
                        const updated = financialDb.updateCodPayment(payment.id, {
                            status: 'محصل',
                            collectionDate: new Date().toISOString().split('T')[0]
                        });
                        if (updated) collectedCount++;
                    });
                    
                    alert(`تم تحصيل ${collectedCount} دفعة بنجاح`);
                    loadCodPayments();
                    loadStats();
                }
            } catch (error) {
                console.error('❌ خطأ في تحصيل جميع المدفوعات:', error);
                alert('خطأ في تحصيل جميع المدفوعات: ' + error.message);
            }
        }

        // تعديل دفع COD
        function editCodPayment(id) {
            try {
                const payment = financialDb.getAllCodPayments().find(p => p.id === id);
                if (!payment) {
                    alert('لم يتم العثور على الدفع');
                    return;
                }

                // إنشاء نافذة التعديل
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                    align-items: center; justify-content: center;
                `;

                modal.innerHTML = `
                    <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 500px; max-height: 90vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px; color: #333;">✏️ تعديل دفع COD</h3>

                        <form id="editCodForm">
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الشحنة:</label>
                                <input type="text" id="editShipmentId" value="${payment.shipmentId}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" readonly>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم العميل:</label>
                                <input type="text" id="editCustomerName" value="${payment.customerName}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">المبلغ:</label>
                                <input type="number" id="editAmount" value="${payment.amount}" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">العملة:</label>
                                <select id="editCurrency" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                                    <option value="SAR" ${payment.currency === 'SAR' ? 'selected' : ''}>ريال سعودي (SAR)</option>
                                    <option value="USD" ${payment.currency === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                                    <option value="AED" ${payment.currency === 'AED' ? 'selected' : ''}>درهم إماراتي (AED)</option>
                                    <option value="KWD" ${payment.currency === 'KWD' ? 'selected' : ''}>دينار كويتي (KWD)</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">المندوب:</label>
                                <input type="text" id="editDistributorName" value="${payment.distributorName}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الحالة:</label>
                                <select id="editStatus" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                                    <option value="معلق" ${payment.status === 'معلق' ? 'selected' : ''}>معلق</option>
                                    <option value="محصل" ${payment.status === 'محصل' ? 'selected' : ''}>محصل</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ التحصيل:</label>
                                <input type="date" id="editCollectionDate" value="${payment.collectionDate}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                                <textarea id="editNotes" rows="3"
                                          style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: vertical;">${payment.notes || ''}</textarea>
                            </div>

                            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                                <button type="button" onclick="closeEditModal()"
                                        style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                                <button type="submit"
                                        style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 8px; cursor: pointer;">حفظ التعديلات</button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);

                // معالجة النموذج
                document.getElementById('editCodForm').addEventListener('submit', function(e) {
                    e.preventDefault();

                    const updatedPayment = {
                        customerName: document.getElementById('editCustomerName').value,
                        amount: parseFloat(document.getElementById('editAmount').value),
                        currency: document.getElementById('editCurrency').value,
                        distributorName: document.getElementById('editDistributorName').value,
                        status: document.getElementById('editStatus').value,
                        collectionDate: document.getElementById('editCollectionDate').value,
                        notes: document.getElementById('editNotes').value,
                        updatedAt: new Date().toISOString()
                    };

                    const result = financialDb.updateCodPayment(id, updatedPayment);
                    if (result) {
                        alert('تم تحديث بيانات الدفع بنجاح!');
                        loadCodPayments();
                        loadStats();
                        closeEditModal();
                    } else {
                        alert('خطأ في تحديث بيانات الدفع');
                    }
                });

                window.closeEditModal = function() {
                    document.body.removeChild(modal);
                };

            } catch (error) {
                console.error('❌ خطأ في تعديل الدفع:', error);
                alert('خطأ في تعديل الدفع: ' + error.message);
            }
        }

        // إضافة دفع مؤجل جديد
        function addDeferredPayment() {
            // إنشاء نافذة إضافة دفع مؤجل
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 500px; max-height: 90vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">➕ إضافة دفع مؤجل جديد</h3>

                    <form id="addDeferredForm">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الشحنة:</label>
                            <input type="text" id="newShipmentId" placeholder="مثال: SHP001"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم العميل:</label>
                            <input type="text" id="newCustomerName" placeholder="اسم العميل"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">المبلغ:</label>
                            <input type="number" id="newAmount" placeholder="0.00" step="0.01" min="0"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">العملة:</label>
                            <select id="newCurrency" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                                <option value="SAR">ريال سعودي (SAR)</option>
                                <option value="USD">دولار أمريكي (USD)</option>
                                <option value="AED">درهم إماراتي (AED)</option>
                                <option value="KWD">دينار كويتي (KWD)</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">المندوب:</label>
                            <input type="text" id="newDistributorName" placeholder="اسم المندوب"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ التحصيل المتوقع:</label>
                            <input type="date" id="newCollectionDate"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                            <textarea id="newNotes" rows="3" placeholder="ملاحظات إضافية..."
                                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: vertical;"></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button type="button" onclick="closeAddModal()"
                                    style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                            <button type="submit"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">إضافة الدفع</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            document.getElementById('addDeferredForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newPayment = {
                    id: 'COD' + Date.now(),
                    shipmentId: document.getElementById('newShipmentId').value,
                    customerId: 'CUST' + Date.now(),
                    customerName: document.getElementById('newCustomerName').value,
                    amount: parseFloat(document.getElementById('newAmount').value),
                    currency: document.getElementById('newCurrency').value,
                    distributorId: 'DIST' + Date.now(),
                    distributorName: document.getElementById('newDistributorName').value,
                    collectionDate: document.getElementById('newCollectionDate').value || new Date().toISOString().split('T')[0],
                    status: 'معلق',
                    createdDate: new Date().toISOString().split('T')[0],
                    notes: document.getElementById('newNotes').value
                };

                const result = financialDb.addCodPayment(newPayment);
                if (result) {
                    alert('تم إضافة الدفع المؤجل بنجاح!');
                    loadCodPayments();
                    loadStats();
                    closeAddModal();
                } else {
                    alert('خطأ في إضافة الدفع المؤجل');
                }
            });

            window.closeAddModal = function() {
                document.body.removeChild(modal);
            };
        }

        // دوال مساعدة
        function getStatusClass(status) {
            const statusMap = {
                'محصل': 'collected',
                'معلق': 'pending',
                'فشل': 'failed'
            };
            return statusMap[status] || 'pending';
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        function formatCurrency(amount, currency) {
            const symbols = {
                'SAR': 'ر.س',
                'USD': '$',
                'AED': 'د.إ',
                'KWD': 'د.ك'
            };
            return `${parseFloat(amount).toFixed(2)} ${symbols[currency] || currency}`;
        }
    </script>

    <!-- تطبيق خط SF Pro Arabic Display على جميع العناصر -->
    <style>
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, textarea, select, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }
    </style>
</body>
</html>
