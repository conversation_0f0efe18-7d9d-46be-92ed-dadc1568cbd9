<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول الموحد | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
            font-weight: 600 !important;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif);
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        .logo-section {
            position: relative;
            z-index: 2;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .welcome-text h1 {
            font-size: 2.2rem;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .welcome-text p {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .login-right {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-header h2 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 1rem;
        }

        .user-type-selector {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 30px;
        }

        .user-type-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            font-family: inherit;
        }

        .user-type-btn.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 1.2rem;
        }

        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }

        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .register-link {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .demo-accounts {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.85rem;
        }

        .demo-accounts h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .demo-accounts p {
            color: #666;
            margin: 5px 0;
        }

        /* فاصل "أو" */
        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
        }

        .divider span {
            background: white;
            padding: 0 15px;
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* أزرار الشبكات الاجتماعية */
        .social-login {
            margin: 20px 0;
        }

        .social-login h4 {
            text-align: center;
            margin-bottom: 15px;
            color: #333;
            font-size: 1rem;
        }

        .social-btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            background: white;
            color: #333;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
            text-decoration: none;
        }

        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .google-btn {
            border-color: #4285F4;
        }

        .google-btn:hover {
            background: #4285F4;
            color: white;
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }

        .google-btn:hover svg path {
            fill: white !important;
        }

        .apple-btn {
            border-color: #000;
            color: #000;
        }

        .apple-btn:hover {
            background: #000;
            color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .facebook-btn {
            border-color: #1877F2;
            color: #1877F2;
        }

        .facebook-btn:hover {
            background: #1877F2;
            color: white;
            box-shadow: 0 5px 15px rgba(24, 119, 242, 0.3);
        }

        .social-btn svg {
            flex-shrink: 0;
        }

        .social-btn span {
            flex: 1;
            text-align: center;
        }

        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 400px;
            }

            .login-left {
                padding: 40px 30px;
                min-height: 200px;
            }

            .login-right {
                padding: 40px 30px;
            }

            .welcome-text h1 {
                font-size: 1.8rem;
            }

            .logo {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            /* تحسينات للأزرار الاجتماعية على الهواتف */
            .social-btn {
                padding: 14px 15px;
                font-size: 0.9rem;
            }

            .social-btn span {
                font-size: 0.85rem;
            }

            .social-login h4 {
                font-size: 0.95rem;
            }

            .divider {
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-left">
            <div class="logo-section">
                <div class="logo">🚚</div>
                <div class="welcome-text">
                    <h1>أهلاً بك</h1>
                    <p>سجل دخولك للوصول إلى لوحة التحكم الخاصة بك وإدارة شحناتك بسهولة</p>
                </div>
            </div>
        </div>

        <div class="login-right">
            <div class="login-header">
                <h2>تسجيل الدخول</h2>
                <p>اختر نوع حسابك وسجل دخولك</p>
            </div>

            <div class="user-type-selector">
                <button class="user-type-btn active" data-type="customer" onclick="selectUserType('customer')">
                    👤 عميل
                </button>
                <button class="user-type-btn" data-type="admin" onclick="selectUserType('admin')">
                    👨‍💼 مدير النظام
                </button>
            </div>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <form class="login-form" id="loginForm" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني أو رقم الهاتف</label>
                    <input type="text" id="email" class="form-control" placeholder="أدخل البريد الإلكتروني أو رقم الهاتف" required>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div style="position: relative;">
                        <input type="password" id="password" class="form-control" placeholder="أدخل كلمة المرور" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">👁️</button>
                    </div>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    تسجيل الدخول
                </button>
            </form>

            <div class="forgot-password">
                <a href="#" onclick="forgotPassword()">نسيت كلمة المرور؟</a>
            </div>

            <!-- فاصل "أو" -->
            <div class="divider">
                <span>أو</span>
            </div>

            <!-- أزرار تسجيل الدخول عبر الشبكات الاجتماعية -->
            <div class="social-login">
                <h4>تسجيل الدخول السريع</h4>

                <button type="button" class="social-btn google-btn" onclick="loginWithGoogle()">
                    <svg width="20" height="20" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span>الدخول بحساب Google</span>
                </button>

                <button type="button" class="social-btn apple-btn" onclick="loginWithApple()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                    <span>الدخول بحساب Apple</span>
                </button>

                <button type="button" class="social-btn facebook-btn" onclick="loginWithFacebook()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    <span>الدخول بحساب Facebook</span>
                </button>
            </div>

            <div class="register-link" id="registerSection">
                <p>ليس لديك حساب؟ <a href="#" onclick="showRegister()">إنشاء حساب جديد</a></p>
            </div>


        </div>
    </div>

    <script>
        let currentUserType = 'customer';

        // اختيار نوع المستخدم
        function selectUserType(type) {
            currentUserType = type;

            // تحديث الأزرار
            document.querySelectorAll('.user-type-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');

            // تحديث النص حسب نوع المستخدم
            const registerSection = document.getElementById('registerSection');
            if (type === 'admin') {
                registerSection.style.display = 'none';
            } else {
                registerSection.style.display = 'block';
            }

            // إظهار رسالة تأكيد نوع المستخدم
            const userTypeNames = {
                'customer': '👤 عميل',
                'admin': '👨‍💼 مدير النظام'
            };

            // إخفاء أي رسائل خطأ سابقة
            hideError();

            // إظهار رسالة تأكيد مؤقتة
            const confirmMsg = document.createElement('div');
            confirmMsg.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #d4edda;
                color: #155724;
                padding: 10px 15px;
                border-radius: 8px;
                border: 1px solid #c3e6cb;
                z-index: 1000;
                font-size: 0.9rem;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            `;
            confirmMsg.textContent = `تم اختيار: ${userTypeNames[type]}`;
            document.body.appendChild(confirmMsg);

            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                if (confirmMsg.parentNode) {
                    confirmMsg.remove();
                }
            }, 3000);

            console.log(`تم اختيار نوع المستخدم: ${type}`);
        }

        // تبديل إظهار كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.password-toggle');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        // التحقق من صحة بيانات تسجيل الدخول
        function checkLoginCredentials(email, password, userType) {
            // تنظيف البيانات المدخلة (إزالة المسافات الزائدة)
            email = email.trim().toLowerCase();
            password = password.trim();

            console.log(`🔍 محاولة تسجيل دخول: نوع المستخدم=${userType}, البريد=${email}, كلمة المرور=${password}`);

            // بيانات تسجيل الدخول الصحيحة
            const validAccounts = {
                admin: [
                    { email: 'admin', password: '123456', name: 'مدير النظام' },
                    { email: '<EMAIL>', password: 'admin123', name: 'مدير الشركة' },
                    { email: '<EMAIL>', password: 'manager123', name: 'مدير العمليات' }
                ],
                employee: [
                    { email: 'employee', password: '123456', name: 'موظف النظام' },
                    { email: '<EMAIL>', password: 'emp123', name: 'موظف الشحن' },
                    { email: '<EMAIL>', password: 'staff123', name: 'موظف خدمة العملاء' }
                ],
                customer: [
                    { email: 'customer', password: '123456', name: 'عميل تجريبي' },
                    { email: '<EMAIL>', password: 'client123', name: 'عميل الشركة' },
                    { email: '**********', password: 'password123', name: 'عميل الهاتف' },
                    { email: '<EMAIL>', password: 'test123', name: 'عميل الاختبار' }
                ]
            };

            const accounts = validAccounts[userType] || [];
            console.log(`📋 الحسابات المتاحة لنوع ${userType}:`, accounts);

            // البحث عن الحساب المطابق (مع تحويل البريد إلى أحرف صغيرة للمقارنة)
            const matchedAccount = accounts.find(account =>
                account.email.toLowerCase() === email && account.password === password
            );

            console.log(`🎯 نتيجة البحث:`, matchedAccount ? 'تم العثور على حساب مطابق' : 'لم يتم العثور على حساب مطابق');

            if (matchedAccount) {
                return {
                    isValid: true,
                    userName: matchedAccount.name,
                    message: 'تم التحقق بنجاح'
                };
            } else {
                return {
                    isValid: false,
                    userName: null,
                    message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
                };
            }
        }

        // معالجة تسجيل الدخول
        function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value.trim();
            const loginBtn = document.getElementById('loginBtn');

            // التحقق من البيانات
            if (!email || !password) {
                showError('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // التحقق من طول كلمة المرور
            if (password.length < 3) {
                showError('كلمة المرور قصيرة جداً');
                return;
            }

            // التحقق من نوع المستخدم المحدد
            if (!currentUserType) {
                showError('يرجى اختيار نوع المستخدم أولاً (👤 عميل أو 👨‍💼 مدير النظام)');
                return;
            }

            // إظهار حالة التحميل
            loginBtn.disabled = true;
            loginBtn.innerHTML = 'جاري تسجيل الدخول... <span class="loading"></span>';

            // التحقق من بيانات تسجيل الدخول
            const validCredentials = checkLoginCredentials(email, password, currentUserType);

            if (!validCredentials.isValid) {
                // فشل تسجيل الدخول - إظهار خطأ مفصل
                console.error(`❌ فشل تسجيل الدخول: ${validCredentials.message}`);

                let errorMessage = validCredentials.message;

                // إضافة تفاصيل إضافية للمساعدة
                if (currentUserType === 'customer') {
                    errorMessage += '\n\n✅ تأكد من:\n• اختيار "👤 عميل" من الأعلى\n• استخدام: customer / 123456\n• أو: <EMAIL> / client123\n\n💡 نصيحة: انسخ والصق البيانات من الأسفل';
                } else if (currentUserType === 'admin') {
                    errorMessage += '\n\n✅ تأكد من:\n• اختيار "👨‍💼 مدير النظام" من الأعلى\n• استخدام: admin / 123456\n• أو: <EMAIL> / admin123';
                } else {
                    errorMessage += '\n\n⚠️ يرجى اختيار نوع المستخدم أولاً من الأزرار في الأعلى';
                }

                // إضافة معلومات تشخيصية
                errorMessage += `\n\n🔍 معلومات تشخيصية:\n• نوع المستخدم المحدد: ${currentUserType || 'غير محدد'}\n• البريد المدخل: "${email}"\n• طول كلمة المرور: ${password.length} أحرف`;

                showError(errorMessage);
                loginBtn.disabled = false;
                loginBtn.innerHTML = 'تسجيل الدخول';
                return;
            }

            // محاكاة عملية تسجيل الدخول (فقط للحسابات الصحيحة)
            setTimeout(() => {
                // نجح تسجيل الدخول
                showSuccess('تم تسجيل الدخول بنجاح! جاري التحويل...');

                // حفظ بيانات المستخدم
                localStorage.setItem('userType', currentUserType);
                localStorage.setItem('userEmail', email);
                localStorage.setItem('userName', validCredentials.userName);
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('loginTime', new Date().toISOString());
                localStorage.setItem('loginProvider', 'traditional');

                // التحويل حسب نوع المستخدم
                setTimeout(() => {
                    if (currentUserType === 'admin' || currentUserType === 'employee') {
                        window.location.href = 'main-dashboard.html';
                    } else {
                        window.location.href = 'customer-dashboard.html';
                    }
                }, 1500);
            }, 2000);
        }

        // إظهار رسالة خطأ
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            successDiv.style.display = 'none';
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // إخفاء رسالة الخطأ
        function hideError() {
            const errorDiv = document.getElementById('errorMessage');
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        }

        // تسجيل دخول سريع
        function quickLogin(userType, email, password) {
            // اختيار نوع المستخدم
            selectUserType(userType);

            // ملء البيانات
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;

            // إخفاء أي رسائل خطأ
            hideError();

            // إظهار رسالة تأكيد
            showSuccess(`تم ملء البيانات تلقائياً! اضغط "تسجيل الدخول" للمتابعة`);

            // تركيز على زر تسجيل الدخول
            setTimeout(() => {
                document.getElementById('loginBtn').focus();
            }, 500);
        }

        // إظهار رسالة نجاح
        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            errorDiv.style.display = 'none';
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // نسيان كلمة المرور
        function forgotPassword() {
            const email = prompt('أدخل البريد الإلكتروني الخاص بك:');
            if (email && email.trim()) {
                showSuccess('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');
            }
        }

        // إظهار نموذج التسجيل
        function showRegister() {
            if (confirm('هل تريد إنشاء حساب عميل جديد؟')) {
                window.location.href = 'customer-register.html';
            }
        }

        // دوال تسجيل الدخول عبر الشبكات الاجتماعية
        function loginWithGoogle() {
            if (!currentUserType) {
                showError('يرجى اختيار نوع المستخدم أولاً');
                return;
            }

            showSuccess('جاري تسجيل الدخول عبر Google...');

            // محاكاة تسجيل الدخول عبر Google
            setTimeout(() => {
                // في التطبيق الحقيقي، ستستخدم Google OAuth API
                const googleUser = {
                    email: '<EMAIL>',
                    name: 'مستخدم Google',
                    picture: 'https://via.placeholder.com/100',
                    provider: 'google'
                };

                completeSocialLogin(googleUser);
            }, 2000);
        }

        function loginWithApple() {
            if (!currentUserType) {
                showError('يرجى اختيار نوع المستخدم أولاً');
                return;
            }

            showSuccess('جاري تسجيل الدخول عبر Apple...');

            // محاكاة تسجيل الدخول عبر Apple
            setTimeout(() => {
                // في التطبيق الحقيقي، ستستخدم Apple Sign In API
                const appleUser = {
                    email: '<EMAIL>',
                    name: 'مستخدم Apple',
                    picture: null,
                    provider: 'apple'
                };

                completeSocialLogin(appleUser);
            }, 2000);
        }

        function loginWithFacebook() {
            if (!currentUserType) {
                showError('يرجى اختيار نوع المستخدم أولاً');
                return;
            }

            showSuccess('جاري تسجيل الدخول عبر Facebook...');

            // محاكاة تسجيل الدخول عبر Facebook
            setTimeout(() => {
                // في التطبيق الحقيقي، ستستخدم Facebook SDK
                const facebookUser = {
                    email: '<EMAIL>',
                    name: 'مستخدم Facebook',
                    picture: 'https://via.placeholder.com/100',
                    provider: 'facebook'
                };

                completeSocialLogin(facebookUser);
            }, 2000);
        }

        function completeSocialLogin(socialUser) {
            try {
                // حفظ بيانات المستخدم
                localStorage.setItem('userType', currentUserType);
                localStorage.setItem('userEmail', socialUser.email);
                localStorage.setItem('userName', socialUser.name);
                localStorage.setItem('userPicture', socialUser.picture || '');
                localStorage.setItem('loginProvider', socialUser.provider);
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('loginTime', new Date().toISOString());

                showSuccess(`تم تسجيل الدخول بنجاح عبر ${getSocialProviderName(socialUser.provider)}! جاري التحويل...`);

                // التحويل حسب نوع المستخدم
                setTimeout(() => {
                    if (currentUserType === 'admin') {
                        window.location.href = 'main-dashboard.html';
                    } else {
                        window.location.href = 'customer-dashboard.html';
                    }
                }, 1500);

            } catch (error) {
                console.error('خطأ في تسجيل الدخول الاجتماعي:', error);
                showError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
            }
        }

        function getSocialProviderName(provider) {
            const providers = {
                'google': 'Google',
                'apple': 'Apple',
                'facebook': 'Facebook'
            };
            return providers[provider] || provider;
        }



        // إضافة زر تسجيل خروج إذا كان المستخدم مسجل دخول
        function addLogoutOption() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userEmail = localStorage.getItem('userEmail');
            const userType = localStorage.getItem('userType');

            if (isLoggedIn === 'true' && userEmail && userType) {
                const loginContainer = document.querySelector('.login-right');
                const logoutDiv = document.createElement('div');
                logoutDiv.style.cssText = `
                    background: #e8f5e8;
                    border: 1px solid #4caf50;
                    border-radius: 10px;
                    padding: 15px;
                    margin-bottom: 20px;
                    text-align: center;
                `;
                logoutDiv.innerHTML = `
                    <h4 style="color: #2e7d32; margin-bottom: 10px;">✅ أنت مسجل دخول بالفعل</h4>
                    <p style="margin: 5px 0; color: #2e7d32;"><strong>المستخدم:</strong> ${userEmail}</p>
                    <p style="margin: 5px 0; color: #2e7d32;"><strong>النوع:</strong> ${userType}</p>
                    <div style="margin-top: 15px;">
                        <button onclick="goToDashboard()" style="background: #4caf50; color: white; border: none; padding: 10px 20px; border-radius: 8px; margin: 5px; cursor: pointer;">
                            🏠 الذهاب للوحة التحكم
                        </button>
                        <button onclick="logout()" style="background: #f44336; color: white; border: none; padding: 10px 20px; border-radius: 8px; margin: 5px; cursor: pointer;">
                            🚪 تسجيل خروج
                        </button>
                    </div>
                `;
                loginContainer.insertBefore(logoutDiv, loginContainer.firstChild);
            }
        }

        // الذهاب للوحة التحكم
        function goToDashboard() {
            const userType = localStorage.getItem('userType');
            if (userType === 'admin' || userType === 'employee') {
                window.location.href = 'main-dashboard.html';
            } else {
                window.location.href = 'customer-dashboard.html';
            }
        }

        // تسجيل خروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userType');
                localStorage.removeItem('userEmail');
                localStorage.removeItem('userName');
                localStorage.removeItem('userPicture');
                localStorage.removeItem('loginProvider');
                localStorage.removeItem('loginTime');

                location.reload(); // إعادة تحميل الصفحة
            }
        }

        // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة خيار تسجيل الخروج إذا كان المستخدم مسجل دخول
            addLogoutOption();

            // لا نقوم بالتحويل التلقائي - نترك الخيار للمستخدم
            console.log('صفحة تسجيل الدخول جاهزة');
        });
    </script>

    <!-- تطبيق خط SF Pro Arabic Display على جميع العناصر -->
    <style>
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 700 !important;
        }

        input, textarea, select, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        p, span, div, label, a {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        .login-title {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 700 !important;
        }

        .user-type-btn {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        .form-group label {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        .login-btn, .register-btn, .social-btn {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }
    </style>
</body>
</html>
