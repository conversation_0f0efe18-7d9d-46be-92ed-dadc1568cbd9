<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصفحات | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .toolbar {
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .search-box {
            padding: 10px 15px;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            width: 250px;
            font-size: 0.95rem;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .main-content {
            padding: 30px;
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .page-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .page-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .page-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            margin-bottom: 15px;
        }

        .page-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .page-description {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .page-body {
            padding: 20px;
        }

        .page-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #666;
        }

        .page-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .action-btn {
            flex: 1;
            min-width: 80px;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s;
        }

        .action-view {
            background: #e3f2fd;
            color: #1976d2;
        }

        .action-view:hover {
            background: #bbdefb;
        }

        .action-edit {
            background: #e8f5e8;
            color: #388e3c;
        }

        .action-edit:hover {
            background: #c8e6c9;
        }

        .action-delete {
            background: #ffebee;
            color: #d32f2f;
        }

        .action-delete:hover {
            background: #ffcdd2;
        }

        .action-media {
            background: #fff3e0;
            color: #f57c00;
        }

        .action-media:hover {
            background: #ffe0b2;
        }

        .action-link {
            background: #e8eaf6;
            color: #3f51b5;
        }

        .action-link:hover {
            background: #c5cae9;
        }

        .external-link-card {
            border-left: 4px solid #3f51b5;
        }

        .external-link-card .page-icon {
            background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%);
        }

        .link-category-badge {
            display: inline-block;
            background: #e8eaf6;
            color: #3f51b5;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 5px;
        }

        .external-url-display {
            background: #f5f5f5;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.85rem;
            color: #666;
            margin-top: 10px;
            word-break: break-all;
        }

        .add-page-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            min-height: 300px;
        }

        .add-page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
        }

        .add-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .add-text {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .add-description {
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            margin-bottom: 25px;
            text-align: center;
        }

        .modal-header h2 {
            color: #333;
            margin-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .file-upload {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .file-upload:hover {
            border-color: #667eea;
            background: #f8f9fa;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #666;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 0.85rem;
            color: #999;
        }

        .media-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .media-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            aspect-ratio: 1;
        }

        .media-item img,
        .media-item video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .media-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #d32f2f;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            transition: background 0.3s;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }

        @media (max-width: 768px) {
            .pages-grid {
                grid-template-columns: 1fr;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar-left,
            .toolbar-right {
                justify-content: center;
            }
            
            .search-box {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="main-dashboard.html" class="back-link">← العودة للوحة التحكم</a>
            <h1>📄 إدارة الصفحات</h1>
            <p>إدارة وتحرير جميع صفحات الموقع مع إمكانية إضافة الوسائط</p>
        </div>

        <div class="toolbar">
            <div class="toolbar-left">
                <input type="text" class="search-box" placeholder="🔍 البحث في الصفحات..." id="searchBox">
                <select class="form-control" style="width: 150px;" id="filterSelect">
                    <option value="all">جميع الصفحات</option>
                    <option value="published">منشورة</option>
                    <option value="draft">مسودة</option>
                    <option value="archived">مؤرشفة</option>
                </select>
            </div>
            <div class="toolbar-right">
                <button class="btn btn-info" onclick="refreshPages()">🔄 تحديث</button>
                <button class="btn btn-secondary" onclick="exportPages()">📤 تصدير</button>
                <button class="btn btn-success" onclick="importPages()">📥 استيراد</button>
            </div>
        </div>

        <div class="main-content">
            <div class="pages-grid" id="pagesGrid">
                <!-- بطاقة إضافة صفحة جديدة -->
                <div class="page-card add-page-card" onclick="showAddPageModal()">
                    <div class="add-icon">➕</div>
                    <div class="add-text">إضافة صفحة جديدة</div>
                    <div class="add-description">انقر لإنشاء صفحة جديدة مع الوسائط</div>
                </div>

                <!-- الصفحة الرئيسية -->
                <div class="page-card" data-page="home">
                    <div class="page-header">
                        <div class="page-icon">🏠</div>
                        <div class="page-title">الصفحة الرئيسية</div>
                        <div class="page-description">الصفحة الرئيسية للموقع مع قسم التتبع والخدمات المتنوعة</div>
                    </div>
                    <div class="page-body">
                        <div class="page-stats">
                            <div class="stat-item">
                                <div class="stat-number">1,250</div>
                                <div class="stat-label">زيارة يومية</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">98%</div>
                                <div class="stat-label">معدل الرضا</div>
                            </div>
                        </div>
                        <div class="page-actions">
                            <button class="action-btn action-view" onclick="viewPage('home.html')">👁️ عرض</button>
                            <button class="action-btn action-edit" onclick="editPage('home.html')">✏️ تعديل</button>
                            <button class="action-btn action-media" onclick="manageMedia('home')">🖼️ وسائط</button>
                            <button class="action-btn" style="background: #6f42c1; color: white;" onclick="openVisualEditor('home')">🎨 محرر بصري</button>
                        </div>
                    </div>
                </div>

                <!-- صفحة من نحن -->
                <div class="page-card" data-page="about">
                    <div class="page-header">
                        <div class="page-icon">📖</div>
                        <div class="page-title">صفحة من نحن</div>
                        <div class="page-description">قصة الشركة وقيمها وفريق العمل والرؤية المستقبلية</div>
                    </div>
                    <div class="page-body">
                        <div class="page-stats">
                            <div class="stat-item">
                                <div class="stat-number">850</div>
                                <div class="stat-label">زيارة يومية</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">95%</div>
                                <div class="stat-label">معدل الرضا</div>
                            </div>
                        </div>
                        <div class="page-actions">
                            <button class="action-btn action-view" onclick="viewPage('about-us.html')">👁️ عرض</button>
                            <button class="action-btn action-edit" onclick="editPage('about-us.html')">✏️ تعديل</button>
                            <button class="action-btn action-media" onclick="manageMedia('about')">🖼️ وسائط</button>
                            <button class="action-btn" style="background: #6f42c1; color: white;" onclick="openAboutEditor()">🎨 محرر مخصص</button>
                        </div>
                    </div>
                </div>

                <!-- شركاء النجاح -->
                <div class="page-card" data-page="partners">
                    <div class="page-header">
                        <div class="page-icon">🤝</div>
                        <div class="page-title">شركاء النجاح</div>
                        <div class="page-description">الشركاء الاستراتيجيون وقصص النجاح والتعاونات المثمرة</div>
                    </div>
                    <div class="page-body">
                        <div class="page-stats">
                            <div class="stat-item">
                                <div class="stat-number">650</div>
                                <div class="stat-label">زيارة يومية</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">92%</div>
                                <div class="stat-label">معدل الرضا</div>
                            </div>
                        </div>
                        <div class="page-actions">
                            <button class="action-btn action-view" onclick="viewPage('success-partners.html')">👁️ عرض</button>
                            <button class="action-btn action-edit" onclick="editPage('success-partners.html')">✏️ تعديل</button>
                            <button class="action-btn action-media" onclick="manageMedia('partners')">🖼️ وسائط</button>
                            <button class="action-btn" style="background: #6f42c1; color: white;" onclick="openPartnersEditor()">🎨 محرر مخصص</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة صفحة جديدة -->
    <div class="modal" id="addPageModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>➕ إضافة صفحة جديدة</h2>
                <p>أنشئ صفحة جديدة مع إمكانية إضافة الصور والفيديوهات</p>
            </div>

            <form id="addPageForm">
                <div class="form-group">
                    <label for="pageTitle">عنوان الصفحة</label>
                    <input type="text" class="form-control" id="pageTitle" placeholder="مثال: خدمات الشحن الدولي" required>
                </div>

                <div class="form-group">
                    <label for="pageIcon">أيقونة الصفحة</label>
                    <input type="text" class="form-control" id="pageIcon" placeholder="🌍" maxlength="2">
                </div>

                <div class="form-group">
                    <label for="pageDescription">وصف الصفحة</label>
                    <textarea class="form-control" id="pageDescription" rows="3" placeholder="وصف مختصر للصفحة ومحتواها"></textarea>
                </div>

                <div class="form-group">
                    <label for="pageType">نوع الصفحة</label>
                    <select class="form-control" id="pageType" onchange="togglePageType()">
                        <option value="content">صفحة محتوى (HTML)</option>
                        <option value="external">رابط خارجي</option>
                    </select>
                </div>

                <div class="form-group" id="contentGroup">
                    <label for="pageContent">محتوى الصفحة (HTML)</label>
                    <textarea class="form-control" id="pageContent" rows="8" placeholder="أدخل محتوى الصفحة بصيغة HTML"></textarea>
                </div>

                <div class="form-group" id="externalGroup" style="display: none;">
                    <label for="externalUrl">الرابط الخارجي</label>
                    <input type="url" class="form-control" id="externalUrl" placeholder="https://example.com">
                    <small style="color: #666; margin-top: 5px; display: block;">أدخل الرابط الكامل مع http:// أو https://</small>

                    <div style="margin-top: 15px;">
                        <label>
                            <input type="checkbox" id="openInNewTab" checked style="margin-left: 8px;">
                            فتح في نافذة جديدة
                        </label>
                    </div>

                    <div style="margin-top: 10px;">
                        <label for="linkCategory">تصنيف الرابط</label>
                        <select class="form-control" id="linkCategory">
                            <option value="partner">شريك</option>
                            <option value="service">خدمة</option>
                            <option value="social">وسائل التواصل</option>
                            <option value="government">جهة حكومية</option>
                            <option value="news">أخبار</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>الصور والفيديوهات</label>
                    <div class="file-upload" id="fileUpload">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">اسحب الملفات هنا أو انقر للاختيار</div>
                        <div class="upload-hint">يدعم: JPG, PNG, GIF, MP4, WebM (حد أقصى 10MB)</div>
                        <input type="file" id="fileInput" multiple accept="image/*,video/*" style="display: none;">
                    </div>
                    <div class="media-preview" id="mediaPreview"></div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeAddPageModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء الصفحة</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إدارة الوسائط -->
    <div class="modal" id="mediaModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🖼️ إدارة الوسائط</h2>
                <p id="mediaModalTitle">إدارة صور وفيديوهات الصفحة</p>
            </div>

            <div class="form-group">
                <label>إضافة وسائط جديدة</label>
                <div class="file-upload" id="mediaFileUpload">
                    <div class="upload-icon">🖼️</div>
                    <div class="upload-text">اسحب الصور والفيديوهات هنا</div>
                    <div class="upload-hint">يدعم: JPG, PNG, GIF, MP4, WebM</div>
                    <input type="file" id="mediaFileInput" multiple accept="image/*,video/*" style="display: none;">
                </div>
                <div class="media-preview" id="currentMediaPreview"></div>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeMediaModal()">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="saveMedia()">حفظ الوسائط</button>
            </div>
        </div>
    </div>

    <script>
        let uploadedFiles = [];
        let currentPageMedia = [];

        // تحويل أسماء التصنيفات إلى العربية
        function getCategoryDisplayName(category) {
            const categories = {
                'partner': 'شريك',
                'service': 'خدمة',
                'social': 'وسائل التواصل',
                'government': 'جهة حكومية',
                'news': 'أخبار',
                'other': 'أخرى'
            };
            return categories[category] || category;
        }

        // إظهار نافذة إضافة صفحة
        function showAddPageModal() {
            document.getElementById('addPageModal').classList.add('show');
        }

        // إغلاق نافذة إضافة صفحة
        function closeAddPageModal() {
            document.getElementById('addPageModal').classList.remove('show');
            document.getElementById('addPageForm').reset();
            uploadedFiles = [];
            document.getElementById('mediaPreview').innerHTML = '';

            // إعادة تعيين النموذج للوضع الافتراضي
            document.getElementById('pageType').value = 'content';
            togglePageType();

            // إعادة تعيين عناوين النافذة
            document.querySelector('#addPageModal .modal-header h2').textContent = '➕ إضافة صفحة جديدة';
            document.querySelector('#addPageModal .modal-header p').textContent = 'أنشئ صفحة جديدة مع إمكانية إضافة الصور والفيديوهات';
            document.querySelector('#addPageModal button[type="submit"]').textContent = 'إنشاء الصفحة';

            // إعادة تعيين وظيفة النموذج
            const form = document.getElementById('addPageForm');
            form.onsubmit = function(e) {
                e.preventDefault();
                createNewPage();
            };
        }

        // إظهار نافذة إدارة الوسائط
        function manageMedia(pageType) {
            const pageNames = {
                'home': 'الصفحة الرئيسية',
                'about': 'صفحة من نحن',
                'partners': 'شركاء النجاح'
            };

            document.getElementById('mediaModalTitle').textContent = `إدارة وسائط ${pageNames[pageType]}`;
            document.getElementById('mediaModal').classList.add('show');
            loadCurrentMedia(pageType);
        }

        // إغلاق نافذة إدارة الوسائط
        function closeMediaModal() {
            document.getElementById('mediaModal').classList.remove('show');
            currentPageMedia = [];
            document.getElementById('currentMediaPreview').innerHTML = '';
        }

        // تحميل الوسائط الحالية للصفحة
        function loadCurrentMedia(pageType) {
            // محاكاة تحميل الوسائط الموجودة
            const sampleMedia = {
                'home': [
                    { type: 'image', name: 'hero-banner.jpg', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzY2N2VlYSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkJhbm5lcjwvdGV4dD48L3N2Zz4=' },
                    { type: 'video', name: 'intro-video.mp4', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzc2NGJhMiIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuKWtjwvdGV4dD48L3N2Zz4=' }
                ],
                'about': [
                    { type: 'image', name: 'team-photo.jpg', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzI4YTc0NSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkZyaXE8L3RleHQ+PC9zdmc+' }
                ],
                'partners': [
                    { type: 'image', name: 'partner-logos.png', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzE3YTJiOCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkxvZ29zPC90ZXh0Pjwvc3ZnPg==' }
                ]
            };

            currentPageMedia = sampleMedia[pageType] || [];
            displayCurrentMedia();
        }

        // عرض الوسائط الحالية
        function displayCurrentMedia() {
            const preview = document.getElementById('currentMediaPreview');
            preview.innerHTML = '';

            currentPageMedia.forEach((media, index) => {
                const mediaItem = document.createElement('div');
                mediaItem.className = 'media-item';

                if (media.type === 'image') {
                    mediaItem.innerHTML = `
                        <img src="${media.url}" alt="${media.name}">
                        <button class="media-remove" onclick="removeCurrentMedia(${index})">×</button>
                    `;
                } else {
                    mediaItem.innerHTML = `
                        <video src="${media.url}" muted></video>
                        <button class="media-remove" onclick="removeCurrentMedia(${index})">×</button>
                    `;
                }

                preview.appendChild(mediaItem);
            });
        }

        // حذف وسائط من الصفحة الحالية
        function removeCurrentMedia(index) {
            currentPageMedia.splice(index, 1);
            displayCurrentMedia();
        }

        // إعداد رفع الملفات للصفحة الجديدة
        function setupFileUpload() {
            const fileUpload = document.getElementById('fileUpload');
            const fileInput = document.getElementById('fileInput');

            fileUpload.addEventListener('click', () => fileInput.click());
            fileUpload.addEventListener('dragover', handleDragOver);
            fileUpload.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);
        }

        // إعداد رفع الملفات للوسائط
        function setupMediaUpload() {
            const mediaUpload = document.getElementById('mediaFileUpload');
            const mediaInput = document.getElementById('mediaFileInput');

            mediaUpload.addEventListener('click', () => mediaInput.click());
            mediaUpload.addEventListener('dragover', handleDragOver);
            mediaUpload.addEventListener('drop', handleMediaDrop);
            mediaInput.addEventListener('change', handleMediaSelect);
        }

        // التعامل مع السحب والإفلات
        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files, 'new');
        }

        function handleMediaDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files, 'current');
        }

        // التعامل مع اختيار الملفات
        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files, 'new');
        }

        function handleMediaSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files, 'current');
        }

        // معالجة الملفات
        function processFiles(files, type) {
            files.forEach(file => {
                if (file.size > 10 * 1024 * 1024) {
                    alert(`الملف ${file.name} كبير جداً. الحد الأقصى 10MB`);
                    return;
                }

                if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
                    alert(`نوع الملف ${file.name} غير مدعوم`);
                    return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                    const mediaData = {
                        name: file.name,
                        type: file.type.startsWith('image/') ? 'image' : 'video',
                        url: e.target.result,
                        size: file.size
                    };

                    if (type === 'new') {
                        uploadedFiles.push(mediaData);
                        displayUploadedFiles();
                    } else {
                        currentPageMedia.push(mediaData);
                        displayCurrentMedia();
                    }
                };
                reader.readAsDataURL(file);
            });
        }

        // عرض الملفات المرفوعة للصفحة الجديدة
        function displayUploadedFiles() {
            const preview = document.getElementById('mediaPreview');
            preview.innerHTML = '';

            uploadedFiles.forEach((file, index) => {
                const mediaItem = document.createElement('div');
                mediaItem.className = 'media-item';

                if (file.type === 'image') {
                    mediaItem.innerHTML = `
                        <img src="${file.url}" alt="${file.name}">
                        <button class="media-remove" onclick="removeUploadedFile(${index})">×</button>
                    `;
                } else {
                    mediaItem.innerHTML = `
                        <video src="${file.url}" muted></video>
                        <button class="media-remove" onclick="removeUploadedFile(${index})">×</button>
                    `;
                }

                preview.appendChild(mediaItem);
            });
        }

        // حذف ملف مرفوع
        function removeUploadedFile(index) {
            uploadedFiles.splice(index, 1);
            displayUploadedFiles();
        }

        // التبديل بين نوع الصفحة
        function togglePageType() {
            const pageType = document.getElementById('pageType').value;
            const contentGroup = document.getElementById('contentGroup');
            const externalGroup = document.getElementById('externalGroup');

            if (pageType === 'external') {
                contentGroup.style.display = 'none';
                externalGroup.style.display = 'block';
            } else {
                contentGroup.style.display = 'block';
                externalGroup.style.display = 'none';
            }
        }

        // إنشاء صفحة جديدة
        function createNewPage() {
            const title = document.getElementById('pageTitle').value;
            const icon = document.getElementById('pageIcon').value || '📄';
            const description = document.getElementById('pageDescription').value;
            const pageType = document.getElementById('pageType').value;

            if (!title.trim()) {
                alert('يرجى إدخال عنوان الصفحة');
                return;
            }

            let pageData = {
                title,
                icon,
                description,
                type: pageType,
                media: uploadedFiles,
                created: new Date().toISOString()
            };

            if (pageType === 'external') {
                const externalUrl = document.getElementById('externalUrl').value;
                const openInNewTab = document.getElementById('openInNewTab').checked;
                const linkCategory = document.getElementById('linkCategory').value;

                if (!externalUrl.trim()) {
                    alert('يرجى إدخال الرابط الخارجي');
                    return;
                }

                if (!externalUrl.startsWith('http://') && !externalUrl.startsWith('https://')) {
                    alert('يرجى إدخال رابط صحيح يبدأ بـ http:// أو https://');
                    return;
                }

                pageData.externalUrl = externalUrl;
                pageData.openInNewTab = openInNewTab;
                pageData.linkCategory = linkCategory;
            } else {
                const content = document.getElementById('pageContent').value;
                pageData.content = content;
            }

            // إنشاء بطاقة الصفحة الجديدة
            const pageCard = document.createElement('div');
            pageCard.className = pageType === 'external' ? 'page-card external-link-card' : 'page-card';
            pageCard.setAttribute('data-page', title.toLowerCase().replace(/\s+/g, '-'));

            // تحديد نوع الإحصائيات حسب نوع الصفحة
            const statsHtml = pageType === 'external' ? `
                <div class="stat-item">
                    <div class="stat-number">🔗</div>
                    <div class="stat-label">رابط خارجي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${pageData.linkCategory}</div>
                    <div class="stat-label">التصنيف</div>
                </div>
            ` : `
                <div class="stat-item">
                    <div class="stat-number">0</div>
                    <div class="stat-label">زيارة يومية</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${uploadedFiles.length}</div>
                    <div class="stat-label">ملف وسائط</div>
                </div>
            `;

            // تحديد أزرار العمل حسب نوع الصفحة
            const actionsHtml = pageType === 'external' ? `
                <button class="action-btn action-link" onclick="openExternalLink('${pageData.externalUrl}', ${pageData.openInNewTab})">🔗 فتح الرابط</button>
                <button class="action-btn action-edit" onclick="editExternalPage('${title}')">✏️ تعديل</button>
                <button class="action-btn action-delete" onclick="deletePage('${title}')">🗑️ حذف</button>
            ` : `
                <button class="action-btn action-view" onclick="viewNewPage('${title}')">👁️ عرض</button>
                <button class="action-btn action-edit" onclick="editNewPage('${title}')">✏️ تعديل</button>
                <button class="action-btn action-media" onclick="manageNewPageMedia('${title}')">🖼️ وسائط</button>
                <button class="action-btn action-delete" onclick="deletePage('${title}')">🗑️ حذف</button>
            `;

            // إضافة معلومات إضافية للروابط الخارجية
            const additionalInfo = pageType === 'external' ? `
                <div class="link-category-badge">${getCategoryDisplayName(pageData.linkCategory)}</div>
                <div class="external-url-display">${pageData.externalUrl}</div>
            ` : '';

            pageCard.innerHTML = `
                <div class="page-header">
                    <div class="page-icon">${icon}</div>
                    <div class="page-title">${title}</div>
                    <div class="page-description">${description}</div>
                    ${additionalInfo}
                </div>
                <div class="page-body">
                    <div class="page-stats">
                        ${statsHtml}
                    </div>
                    <div class="page-actions">
                        ${actionsHtml}
                    </div>
                </div>
            `;

            // إضافة الصفحة للشبكة
            const grid = document.getElementById('pagesGrid');
            grid.insertBefore(pageCard, grid.children[1]); // إدراج بعد بطاقة الإضافة

            // حفظ في التخزين المحلي
            const savedPages = JSON.parse(localStorage.getItem('customPages') || '[]');
            savedPages.push(pageData);
            localStorage.setItem('customPages', JSON.stringify(savedPages));

            closeAddPageModal();

            if (pageType === 'external') {
                alert(`تم إنشاء الرابط الخارجي "${title}" بنجاح!\n\nالرابط: ${pageData.externalUrl}\nالتصنيف: ${pageData.linkCategory}`);
            } else {
                alert(`تم إنشاء الصفحة "${title}" بنجاح!`);
            }
        }

        // عرض الصفحات
        function viewPage(pageUrl) {
            window.open(pageUrl, '_blank');
        }

        function viewNewPage(title) {
            alert(`عرض الصفحة: ${title}\n\nهذه وظيفة تجريبية. سيتم إنشاء ملف HTML للصفحة.`);
        }

        // تعديل الصفحات
        function editPage(pageUrl) {
            window.open(`page-editor.html?page=${pageUrl}`, '_blank');
        }

        function editNewPage(title) {
            alert(`تعديل الصفحة: ${title}\n\nسيتم فتح محرر الصفحة.`);
        }

        // فتح رابط خارجي
        function openExternalLink(url, openInNewTab) {
            if (openInNewTab) {
                window.open(url, '_blank');
            } else {
                window.location.href = url;
            }
        }

        // تعديل صفحة رابط خارجي
        function editExternalPage(title) {
            const savedPages = JSON.parse(localStorage.getItem('customPages') || '[]');
            const page = savedPages.find(p => p.title === title);

            if (page && page.type === 'external') {
                // ملء النموذج ببيانات الصفحة الحالية
                document.getElementById('pageTitle').value = page.title;
                document.getElementById('pageIcon').value = page.icon;
                document.getElementById('pageDescription').value = page.description;
                document.getElementById('pageType').value = 'external';
                document.getElementById('externalUrl').value = page.externalUrl;
                document.getElementById('openInNewTab').checked = page.openInNewTab;
                document.getElementById('linkCategory').value = page.linkCategory;

                togglePageType(); // إظهار حقول الرابط الخارجي
                showAddPageModal();

                // تغيير عنوان النافذة
                document.querySelector('#addPageModal .modal-header h2').textContent = '✏️ تعديل الرابط الخارجي';
                document.querySelector('#addPageModal .modal-header p').textContent = 'تعديل بيانات الرابط الخارجي';

                // تغيير نص الزر
                document.querySelector('#addPageModal button[type="submit"]').textContent = 'حفظ التغييرات';

                // حذف الصفحة القديمة عند الحفظ
                const form = document.getElementById('addPageForm');
                form.onsubmit = function(e) {
                    e.preventDefault();
                    deletePage(title, false); // حذف بدون تأكيد
                    createNewPage();

                    // إعادة تعيين النموذج
                    form.onsubmit = function(e) {
                        e.preventDefault();
                        createNewPage();
                    };
                };
            }
        }

        // إدارة وسائط الصفحات الجديدة
        function manageNewPageMedia(title) {
            document.getElementById('mediaModalTitle').textContent = `إدارة وسائط ${title}`;
            document.getElementById('mediaModal').classList.add('show');

            // تحميل وسائط الصفحة المخصصة
            const savedPages = JSON.parse(localStorage.getItem('customPages') || '[]');
            const page = savedPages.find(p => p.title === title);
            currentPageMedia = page ? page.media || [] : [];
            displayCurrentMedia();
        }

        // حذف صفحة
        function deletePage(title, showConfirm = true) {
            const shouldDelete = showConfirm ? confirm(`هل أنت متأكد من حذف الصفحة "${title}"؟`) : true;

            if (shouldDelete) {
                // حذف من الواجهة
                const pageCard = document.querySelector(`[data-page="${title.toLowerCase().replace(/\s+/g, '-')}"]`);
                if (pageCard) {
                    pageCard.remove();
                }

                // حذف من التخزين
                const savedPages = JSON.parse(localStorage.getItem('customPages') || '[]');
                const updatedPages = savedPages.filter(p => p.title !== title);
                localStorage.setItem('customPages', JSON.stringify(updatedPages));

                if (showConfirm) {
                    alert(`تم حذف الصفحة "${title}" بنجاح!`);
                }
            }
        }

        // حفظ الوسائط
        function saveMedia() {
            alert('تم حفظ الوسائط بنجاح!');
            closeMediaModal();
        }

        // وظائف شريط الأدوات
        function refreshPages() {
            location.reload();
        }

        function exportPages() {
            const pages = {
                builtin: ['home.html', 'about-us.html', 'success-partners.html'],
                custom: JSON.parse(localStorage.getItem('customPages') || '[]')
            };

            const dataStr = JSON.stringify(pages, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = 'pages-export.json';
            link.click();
        }

        function importPages() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = JSON.parse(e.target.result);
                            if (data.custom) {
                                localStorage.setItem('customPages', JSON.stringify(data.custom));
                                alert('تم استيراد الصفحات بنجاح!');
                                location.reload();
                            }
                        } catch (error) {
                            alert('خطأ في قراءة الملف!');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // البحث والفلترة
        function setupSearch() {
            const searchBox = document.getElementById('searchBox');
            const filterSelect = document.getElementById('filterSelect');

            searchBox.addEventListener('input', filterPages);
            filterSelect.addEventListener('change', filterPages);
        }

        function filterPages() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const filterValue = document.getElementById('filterSelect').value;
            const pageCards = document.querySelectorAll('.page-card:not(.add-page-card)');

            pageCards.forEach(card => {
                const title = card.querySelector('.page-title')?.textContent.toLowerCase() || '';
                const description = card.querySelector('.page-description')?.textContent.toLowerCase() || '';

                const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
                const matchesFilter = filterValue === 'all' || true; // يمكن تطوير الفلترة لاحقاً

                card.style.display = matchesSearch && matchesFilter ? 'block' : 'none';
            });
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload();
            setupMediaUpload();
            setupSearch();

            // تحميل الصفحات المخصصة المحفوظة
            const savedPages = JSON.parse(localStorage.getItem('customPages') || '[]');
            savedPages.forEach(page => {
                // يمكن إضافة كود لعرض الصفحات المحفوظة هنا
            });

            // إعداد نموذج إضافة الصفحة
            document.getElementById('addPageForm').addEventListener('submit', function(e) {
                e.preventDefault();
                createNewPage();
            });
        });

        // فتح المحرر البصري
        function openVisualEditor(pageType) {
            const pageNames = {
                'home': 'الصفحة الرئيسية',
                'about': 'صفحة من نحن',
                'partners': 'شركاء النجاح'
            };

            const pageName = pageNames[pageType] || pageType;

            if (confirm(`هل تريد فتح المحرر البصري لـ ${pageName}؟\n\nسيتم فتح محرر متقدم يتيح لك تعديل المحتوى بدون كتابة أكواد برمجية.`)) {
                // فتح المحرر البصري في نافذة جديدة
                const editorUrl = `visual-page-editor.html?page=${pageType}`;
                window.open(editorUrl, '_blank', 'width=1400,height=900');
            }
        }

        // فتح المحرر المخصص لصفحة من نحن
        function openAboutEditor() {
            if (confirm('هل تريد فتح المحرر المخصص لصفحة من نحن؟\n\nسيتم فتح محرر متقدم مخصص لتحرير محتوى صفحة من نحن بسهولة.')) {
                window.open('about-us-editor.html', '_blank', 'width=1400,height=900');
            }
        }

        // فتح المحرر المخصص لصفحة شركاء النجاح
        function openPartnersEditor() {
            if (confirm('هل تريد فتح المحرر المخصص لصفحة شركاء النجاح؟\n\nسيتم فتح محرر متقدم مخصص لتحرير محتوى صفحة شركاء النجاح بسهولة.')) {
                window.open('partners-editor.html', '_blank', 'width=1400,height=900');
            }
        }

        // إغلاق النوافذ المنبثقة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('show');
            }
        });
    </script>

    <style>
        /* تطبيق خط SF Pro Arabic Display على جميع العناصر */
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, select, textarea, button, .btn {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }

        .nav a, .nav-links a {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }
    </style>
</body>
</html>
