<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة الشحن السريع | الصفحة الرئيسية</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            font-weight: 600;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            padding: 0.5rem 1rem;
            border-radius: 8px;
        }

        .nav-menu a:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-2px);
        }

        .login-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 0.7rem 1.5rem;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .login-btn:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .cta-btn.primary {
            background: white;
            color: #667eea;
        }

        .cta-btn.secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        /* Features Section */
        .features {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 1rem;
        }

        .section-title p {
            font-size: 1.2rem;
            color: #666;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #667eea;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }

    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                🚚 شركة الشحن السريع
            </div>
            <nav class="nav-menu">
                <a href="#home">الرئيسية</a>
                <a href="about-us.html">من نحن</a>
                <a href="success-partners.html">شركاء النجاح</a>
                <a href="shipment-tracking.html">تتبع الشحنات</a>
                <a href="zatca-financial-system.html" style="background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%); border-radius: 15px;">🏛️ النظام المالي</a>
            </nav>
            <a href="unified-login.html" class="login-btn">تسجيل الدخول</a>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>شحن سريع وآمن</h1>
            <p>نقدم خدمات الشحن والتوصيل الأكثر موثوقية في المملكة العربية السعودية مع ضمان الوصول في الوقت المحدد</p>
            <div class="cta-buttons">
                <a href="unified-login.html" class="cta-btn primary">ابدأ الشحن الآن</a>
                <a href="customer-register.html" class="cta-btn secondary">إنشاء حساب جديد</a>
                <a href="zatca-financial-system.html" class="cta-btn" style="background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%); color: white; margin-top: 10px;">🏛️ النظام المالي المتوافق</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-title">
                <h2>لماذا تختارنا؟</h2>
                <p>نتميز بالسرعة والأمان والموثوقية في جميع خدماتنا</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>توصيل سريع</h3>
                    <p>نضمن وصول شحناتك في أسرع وقت ممكن مع خدمة التوصيل السريع</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>أمان مضمون</h3>
                    <p>نحافظ على سلامة شحناتك مع أعلى معايير الأمان والحماية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📍</div>
                    <h3>تتبع مباشر</h3>
                    <p>تابع شحنتك لحظة بلحظة من خلال نظام التتبع المتطور</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>أسعار تنافسية</h3>
                    <p>نقدم أفضل الأسعار في السوق مع جودة خدمة عالية</p>
                </div>
                <div class="feature-card" onclick="window.location.href='zatca-financial-system.html'" style="cursor: pointer; transition: transform 0.3s;">
                    <div class="feature-icon">🏛️</div>
                    <h3>نظام مالي متوافق</h3>
                    <p>نظام مالي متكامل متوافق مع هيئة الزكاة والضريبة السعودية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎧</div>
                    <h3>دعم 24/7</h3>
                    <p>فريق دعم العملاء متاح على مدار الساعة لخدمتك</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌍</div>
                    <h3>تغطية شاملة</h3>
                    <p>نغطي جميع مناطق المملكة مع شبكة توزيع واسعة</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // تفعيل التمرير السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة الرئيسية بنجاح');

            // إضافة تأثيرات بصرية للبطاقات
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });

            // إضافة CSS للرسوم المتحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .feature-card {
                    opacity: 0;
                }

                /* تطبيق خط SF Pro Arabic Display على جميع العناصر */
                *, *::before, *::after {
                    font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
                }

                h1, h2, h3, h4, h5, h6 {
                    font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
                    font-weight: 600 !important;
                }

                input, select, textarea, button, .btn {
                    font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
                    font-weight: 500 !important;
                }

                .nav a, .nav-links a {
                    font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
                    font-weight: 600 !important;
                }
            `;
            document.head.appendChild(style);
        });
    </script>

</body>
</html>
