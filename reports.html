<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display);
            font-weight: 600;
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total {
            border-left-color: #007bff;
        }

        .stat-card.delivered {
            border-left-color: #28a745;
        }

        .stat-card.pending {
            border-left-color: #ffc107;
        }

        .stat-card.cancelled {
            border-left-color: #dc3545;
        }

        .stat-card.revenue {
            border-left-color: #6f42c1;
        }

        .stat-card.branches {
            border-left-color: #20c997;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .card-change {
            font-size: 0.9rem;
            margin-top: 8px;
        }

        .card-change.positive {
            color: #28a745;
        }

        .card-change.negative {
            color: #dc3545;
        }

        .card-change.neutral {
            color: #6c757d;
        }

        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .filters-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .filter-input {
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            transition: border-color 0.3s ease;
        }

        .filter-input:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .stats-overview {
                grid-template-columns: 1fr;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>📈</span>
                <span>التقارير والإحصائيات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="reports.html" class="active">التقارير الأساسية</a>
                <a href="advanced-reports.html">التقارير المتقدمة</a>
                <a href="shipments.html">الشحنات</a>
                <a href="branches-management.html">الفروع</a>
                <a href="financial-system.html">النظام المالي</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">📈 التقارير والإحصائيات</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-warning" onclick="window.open('advanced-reports.html', '_blank')">
                    🚀 التقارير المتقدمة
                </button>
                <button class="btn btn-success" onclick="generateReport()">
                    📊 إنشاء تقرير
                </button>
                <button class="btn btn-info" onclick="exportData()">
                    📤 تصدير البيانات
                </button>
                <button class="btn" onclick="refreshReports()">
                    🔄 تحديث التقارير
                </button>
            </div>
        </div>

        <!-- فلاتر التقارير -->
        <div class="filters-section">
            <h3 class="filters-title">🔍 فلاتر التقارير</h3>
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">من تاريخ</label>
                    <input type="date" id="dateFrom" class="filter-input">
                </div>
                <div class="filter-group">
                    <label class="filter-label">إلى تاريخ</label>
                    <input type="date" id="dateTo" class="filter-input">
                </div>
                <div class="filter-group">
                    <label class="filter-label">الفرع</label>
                    <select id="branchFilter" class="filter-input">
                        <option value="">جميع الفروع</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">حالة الشحنة</label>
                    <select id="statusFilter" class="filter-input">
                        <option value="">جميع الحالات</option>
                        <option value="معلق">معلق</option>
                        <option value="في الطريق">في الطريق</option>
                        <option value="مسلم">مسلم</option>
                        <option value="ملغي">ملغي</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button class="btn" onclick="applyFilters()" style="margin-top: 5px;">تطبيق الفلاتر</button>
                </div>
            </div>
        </div>

        <!-- إحصائيات عامة -->
        <div class="stats-overview">
            <div class="stat-card total">
                <div class="card-icon">📦</div>
                <div class="card-amount" id="totalShipments">0</div>
                <div class="card-label">إجمالي الشحنات</div>
                <div class="card-change neutral" id="totalChange">منذ البداية</div>
            </div>

            <div class="stat-card delivered">
                <div class="card-icon">✅</div>
                <div class="card-amount" id="deliveredShipments">0</div>
                <div class="card-label">شحنات مسلمة</div>
                <div class="card-change positive" id="deliveredChange">+0% هذا الشهر</div>
            </div>

            <div class="stat-card pending">
                <div class="card-icon">⏳</div>
                <div class="card-amount" id="pendingShipments">0</div>
                <div class="card-label">شحنات معلقة</div>
                <div class="card-change neutral" id="pendingChange">في الانتظار</div>
            </div>

            <div class="stat-card cancelled">
                <div class="card-icon">❌</div>
                <div class="card-amount" id="cancelledShipments">0</div>
                <div class="card-label">شحنات ملغية</div>
                <div class="card-change negative" id="cancelledChange">0% من الإجمالي</div>
            </div>

            <div class="stat-card revenue">
                <div class="card-icon">💰</div>
                <div class="card-amount" id="totalRevenue">0</div>
                <div class="card-label">إجمالي الإيرادات</div>
                <div class="card-change positive" id="revenueChange">SAR</div>
            </div>

            <div class="stat-card branches">
                <div class="card-icon">🏢</div>
                <div class="card-amount" id="activeBranches">0</div>
                <div class="card-label">فروع نشطة</div>
                <div class="card-change neutral" id="branchesChange">متاحة للخدمة</div>
            </div>
        </div>

        <!-- تقارير مفصلة -->
        <div class="reports-grid">
            <!-- تقرير الشحنات -->
            <div class="report-card">
                <div class="report-header shipments">
                    <h3 class="report-title">
                        <span>📦</span>
                        تقرير الشحنات
                    </h3>
                    <p class="report-description">إحصائيات مفصلة عن جميع الشحنات</p>
                </div>
                <div class="report-content">
                    <div class="report-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="shipmentsToday">0</div>
                            <div class="stat-label">شحنات اليوم</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="shipmentsWeek">0</div>
                            <div class="stat-label">شحنات الأسبوع</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="shipmentsMonth">0</div>
                            <div class="stat-label">شحنات الشهر</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="avgDeliveryTime">0</div>
                            <div class="stat-label">متوسط التسليم (يوم)</div>
                        </div>
                    </div>
                    <div class="report-actions">
                        <button class="btn btn-info" onclick="viewShipmentsReport()">عرض التفاصيل</button>
                        <button class="btn" onclick="exportShipmentsReport()">تصدير</button>
                    </div>
                </div>
            </div>

            <!-- تقرير الفروع -->
            <div class="report-card">
                <div class="report-header branches">
                    <h3 class="report-title">
                        <span>🏢</span>
                        تقرير الفروع
                    </h3>
                    <p class="report-description">أداء الفروع والتحويلات</p>
                </div>
                <div class="report-content">
                    <div class="report-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="totalBranches">0</div>
                            <div class="stat-label">إجمالي الفروع</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="totalTransfers">0</div>
                            <div class="stat-label">إجمالي التحويلات</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="pendingTransfers">0</div>
                            <div class="stat-label">تحويلات معلقة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="avgTransferTime">0</div>
                            <div class="stat-label">متوسط التحويل (يوم)</div>
                        </div>
                    </div>
                    <div class="report-actions">
                        <button class="btn btn-info" onclick="viewBranchesReport()">عرض التفاصيل</button>
                        <button class="btn" onclick="exportBranchesReport()">تصدير</button>
                    </div>
                </div>
            </div>

            <!-- تقرير الإلغاءات -->
            <div class="report-card">
                <div class="report-header cancellation">
                    <h3 class="report-title">
                        <span>❌</span>
                        تقرير الإلغاءات
                    </h3>
                    <p class="report-description">أسباب الإلغاء والاتجاهات</p>
                </div>
                <div class="report-content">
                    <div class="report-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="cancellationRate">0%</div>
                            <div class="stat-label">معدل الإلغاء</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="topCancelReason">-</div>
                            <div class="stat-label">أهم سبب إلغاء</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="deliveryFailures">0</div>
                            <div class="stat-label">فشل التوصيل</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="rejectedShipments">0</div>
                            <div class="stat-label">شحنات مرفوضة</div>
                        </div>
                    </div>
                    <div class="report-actions">
                        <button class="btn btn-info" onclick="viewCancellationReport()">عرض التفاصيل</button>
                        <button class="btn" onclick="exportCancellationReport()">تصدير</button>
                    </div>
                </div>
            </div>

            <!-- تقرير مالي -->
            <div class="report-card">
                <div class="report-header financial">
                    <h3 class="report-title">
                        <span>💰</span>
                        التقرير المالي
                    </h3>
                    <p class="report-description">الإيرادات والمصروفات</p>
                </div>
                <div class="report-content">
                    <div class="report-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="monthlyRevenue">0</div>
                            <div class="stat-label">إيرادات الشهر</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="codCollections">0</div>
                            <div class="stat-label">تحصيلات COD</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="commissionsPaid">0</div>
                            <div class="stat-label">عمولات مدفوعة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="netProfit">0</div>
                            <div class="stat-label">صافي الربح</div>
                        </div>
                    </div>
                    <div class="report-actions">
                        <button class="btn btn-info" onclick="viewFinancialReport()">عرض التفاصيل</button>
                        <button class="btn" onclick="exportFinancialReport()">تصدير</button>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📈 تحميل صفحة التقارير...');

            try {
                loadReportsData();
                loadBranchesFilter();
                setDefaultDates();

                console.log('✅ تم تحميل صفحة التقارير بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل صفحة التقارير:', error);
                alert('خطأ في تحميل صفحة التقارير: ' + error.message);
            }
        });

        // تحميل بيانات التقارير
        function loadReportsData() {
            try {
                const shipments = db.getAllShipments();
                const branches = db.getAllBranches();
                const transfers = db.getAllBranchTransfers();

                // إحصائيات عامة
                updateGeneralStats(shipments, branches, transfers);

                // تقارير مفصلة
                updateDetailedReports(shipments, branches, transfers);

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات التقارير:', error);
            }
        }

        // تحديث الإحصائيات العامة
        function updateGeneralStats(shipments, branches, transfers) {
            try {
                const totalShipments = shipments.length;
                const deliveredShipments = shipments.filter(s => s.status === 'مسلم').length;
                const pendingShipments = shipments.filter(s => s.status === 'معلق').length;
                const cancelledShipments = shipments.filter(s => s.status === 'ملغي').length;
                const activeBranches = branches.filter(b => b.isActive).length;

                // حساب الإيرادات
                const totalRevenue = shipments
                    .filter(s => s.status === 'مسلم')
                    .reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);

                // تحديث العناصر
                document.getElementById('totalShipments').textContent = totalShipments;
                document.getElementById('deliveredShipments').textContent = deliveredShipments;
                document.getElementById('pendingShipments').textContent = pendingShipments;
                document.getElementById('cancelledShipments').textContent = cancelledShipments;
                document.getElementById('totalRevenue').textContent = totalRevenue.toFixed(2);
                document.getElementById('activeBranches').textContent = activeBranches;

                // حساب النسب المئوية
                const cancellationRate = totalShipments > 0 ? ((cancelledShipments / totalShipments) * 100).toFixed(1) : 0;
                document.getElementById('cancelledChange').textContent = `${cancellationRate}% من الإجمالي`;

                const deliveryRate = totalShipments > 0 ? ((deliveredShipments / totalShipments) * 100).toFixed(1) : 0;
                document.getElementById('deliveredChange').textContent = `${deliveryRate}% معدل التسليم`;

            } catch (error) {
                console.error('❌ خطأ في تحديث الإحصائيات العامة:', error);
            }
        }

        // تحديث التقارير المفصلة
        function updateDetailedReports(shipments, branches, transfers) {
            try {
                // تقرير الشحنات
                updateShipmentsReport(shipments);

                // تقرير الفروع
                updateBranchesReport(branches, transfers);

                // تقرير الإلغاءات
                updateCancellationReport(shipments);

                // التقرير المالي
                updateFinancialReport(shipments);

            } catch (error) {
                console.error('❌ خطأ في تحديث التقارير المفصلة:', error);
            }
        }

        // تحديث تقرير الشحنات
        function updateShipmentsReport(shipments) {
            try {
                const today = new Date();
                const todayStr = today.toISOString().split('T')[0];

                const weekAgo = new Date(today);
                weekAgo.setDate(today.getDate() - 7);
                const weekAgoStr = weekAgo.toISOString().split('T')[0];

                const monthAgo = new Date(today);
                monthAgo.setMonth(today.getMonth() - 1);
                const monthAgoStr = monthAgo.toISOString().split('T')[0];

                const shipmentsToday = shipments.filter(s => s.createdDate === todayStr).length;
                const shipmentsWeek = shipments.filter(s => s.createdDate >= weekAgoStr).length;
                const shipmentsMonth = shipments.filter(s => s.createdDate >= monthAgoStr).length;

                // حساب متوسط وقت التسليم
                const deliveredShipments = shipments.filter(s => s.status === 'مسلم');
                let avgDeliveryTime = 0;
                if (deliveredShipments.length > 0) {
                    const totalDays = deliveredShipments.reduce((sum, s) => {
                        const created = new Date(s.createdDate);
                        const delivered = new Date(s.deliveredDate || s.createdDate);
                        const diffTime = Math.abs(delivered - created);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        return sum + diffDays;
                    }, 0);
                    avgDeliveryTime = Math.round(totalDays / deliveredShipments.length);
                }

                document.getElementById('shipmentsToday').textContent = shipmentsToday;
                document.getElementById('shipmentsWeek').textContent = shipmentsWeek;
                document.getElementById('shipmentsMonth').textContent = shipmentsMonth;
                document.getElementById('avgDeliveryTime').textContent = avgDeliveryTime;

            } catch (error) {
                console.error('❌ خطأ في تحديث تقرير الشحنات:', error);
            }
        }

        // تحديث تقرير الفروع
        function updateBranchesReport(branches, transfers) {
            try {
                const totalBranches = branches.length;
                const totalTransfers = transfers.length;
                const pendingTransfers = transfers.filter(t => t.status === 'معلق' || t.status === 'في الطريق').length;

                // حساب متوسط وقت التحويل
                const completedTransfers = transfers.filter(t => t.status === 'مستلم');
                let avgTransferTime = 0;
                if (completedTransfers.length > 0) {
                    const totalDays = completedTransfers.reduce((sum, t) => {
                        const requested = new Date(t.requestDate);
                        const received = new Date(t.actualArrival || t.requestDate);
                        const diffTime = Math.abs(received - requested);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        return sum + diffDays;
                    }, 0);
                    avgTransferTime = Math.round(totalDays / completedTransfers.length);
                }

                document.getElementById('totalBranches').textContent = totalBranches;
                document.getElementById('totalTransfers').textContent = totalTransfers;
                document.getElementById('pendingTransfers').textContent = pendingTransfers;
                document.getElementById('avgTransferTime').textContent = avgTransferTime;

            } catch (error) {
                console.error('❌ خطأ في تحديث تقرير الفروع:', error);
            }
        }

        // تحديث تقرير الإلغاءات
        function updateCancellationReport(shipments) {
            try {
                const totalShipments = shipments.length;
                const cancelledShipments = shipments.filter(s => s.status === 'ملغي');
                const cancellationRate = totalShipments > 0 ? ((cancelledShipments.length / totalShipments) * 100).toFixed(1) : 0;

                // أهم سبب إلغاء
                const cancellationReasons = {};
                cancelledShipments.forEach(s => {
                    if (s.cancellationReason) {
                        cancellationReasons[s.cancellationReason] = (cancellationReasons[s.cancellationReason] || 0) + 1;
                    }
                });

                const topReason = Object.keys(cancellationReasons).reduce((a, b) =>
                    cancellationReasons[a] > cancellationReasons[b] ? a : b, '-'
                );

                // تصنيف الإلغاءات
                const deliveryFailures = cancelledShipments.filter(s =>
                    s.cancellationCategory === 'اسباب عدم التوصيل'
                ).length;

                const rejectedShipments = cancelledShipments.filter(s =>
                    s.cancellationCategory === 'اسباب رفض الشحنات'
                ).length;

                document.getElementById('cancellationRate').textContent = `${cancellationRate}%`;
                document.getElementById('topCancelReason').textContent = topReason.length > 20 ? topReason.substring(0, 20) + '...' : topReason;
                document.getElementById('deliveryFailures').textContent = deliveryFailures;
                document.getElementById('rejectedShipments').textContent = rejectedShipments;

            } catch (error) {
                console.error('❌ خطأ في تحديث تقرير الإلغاءات:', error);
            }
        }

        // تحديث التقرير المالي
        function updateFinancialReport(shipments) {
            try {
                const today = new Date();
                const monthAgo = new Date(today);
                monthAgo.setMonth(today.getMonth() - 1);
                const monthAgoStr = monthAgo.toISOString().split('T')[0];

                const monthlyShipments = shipments.filter(s => s.createdDate >= monthAgoStr);
                const deliveredMonthly = monthlyShipments.filter(s => s.status === 'مسلم');

                const monthlyRevenue = deliveredMonthly.reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);
                const codCollections = deliveredMonthly.filter(s => s.paymentMethod === 'COD').reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);
                const commissionsPaid = monthlyRevenue * 0.1; // افتراض 10% عمولة
                const netProfit = monthlyRevenue - commissionsPaid;

                document.getElementById('monthlyRevenue').textContent = monthlyRevenue.toFixed(2);
                document.getElementById('codCollections').textContent = codCollections.toFixed(2);
                document.getElementById('commissionsPaid').textContent = commissionsPaid.toFixed(2);
                document.getElementById('netProfit').textContent = netProfit.toFixed(2);

            } catch (error) {
                console.error('❌ خطأ في تحديث التقرير المالي:', error);
            }
        }

        // تحميل فلتر الفروع
        function loadBranchesFilter() {
            try {
                const branches = db.getActiveBranches();
                const branchFilter = document.getElementById('branchFilter');

                branchFilter.innerHTML = '<option value="">جميع الفروع</option>';

                branches.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.id;
                    option.textContent = branch.name;
                    branchFilter.appendChild(option);
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل فلتر الفروع:', error);
            }
        }

        // تعيين التواريخ الافتراضية
        function setDefaultDates() {
            const today = new Date();
            const monthAgo = new Date(today);
            monthAgo.setMonth(today.getMonth() - 1);

            document.getElementById('dateFrom').value = monthAgo.toISOString().split('T')[0];
            document.getElementById('dateTo').value = today.toISOString().split('T')[0];
        }

        // تطبيق الفلاتر
        function applyFilters() {
            try {
                const dateFrom = document.getElementById('dateFrom').value;
                const dateTo = document.getElementById('dateTo').value;
                const branchId = document.getElementById('branchFilter').value;
                const status = document.getElementById('statusFilter').value;

                let shipments = db.getAllShipments();

                // تطبيق فلاتر التاريخ
                if (dateFrom) {
                    shipments = shipments.filter(s => s.createdDate >= dateFrom);
                }
                if (dateTo) {
                    shipments = shipments.filter(s => s.createdDate <= dateTo);
                }

                // تطبيق فلتر الفرع
                if (branchId) {
                    shipments = shipments.filter(s => s.currentBranch === branchId);
                }

                // تطبيق فلتر الحالة
                if (status) {
                    shipments = shipments.filter(s => s.status === status);
                }

                // تحديث الإحصائيات بالبيانات المفلترة
                const branches = db.getAllBranches();
                const transfers = db.getAllBranchTransfers();

                updateGeneralStats(shipments, branches, transfers);
                updateDetailedReports(shipments, branches, transfers);

                alert(`تم تطبيق الفلاتر بنجاح!\nعدد الشحنات المفلترة: ${shipments.length}`);

            } catch (error) {
                console.error('❌ خطأ في تطبيق الفلاتر:', error);
                alert('خطأ في تطبيق الفلاتر: ' + error.message);
            }
        }

        // تحديث التقارير
        function refreshReports() {
            try {
                loadReportsData();
                alert('تم تحديث التقارير بنجاح!');
            } catch (error) {
                console.error('❌ خطأ في تحديث التقارير:', error);
                alert('خطأ في تحديث التقارير: ' + error.message);
            }
        }

        // إنشاء تقرير
        function generateReport() {
            try {
                const reportData = {
                    generatedAt: new Date().toISOString(),
                    totalShipments: document.getElementById('totalShipments').textContent,
                    deliveredShipments: document.getElementById('deliveredShipments').textContent,
                    pendingShipments: document.getElementById('pendingShipments').textContent,
                    cancelledShipments: document.getElementById('cancelledShipments').textContent,
                    totalRevenue: document.getElementById('totalRevenue').textContent,
                    activeBranches: document.getElementById('activeBranches').textContent
                };

                const reportText = `تقرير شامل - ${new Date().toLocaleDateString('ar-SA')}\n\n` +
                                 `إجمالي الشحنات: ${reportData.totalShipments}\n` +
                                 `شحنات مسلمة: ${reportData.deliveredShipments}\n` +
                                 `شحنات معلقة: ${reportData.pendingShipments}\n` +
                                 `شحنات ملغية: ${reportData.cancelledShipments}\n` +
                                 `إجمالي الإيرادات: ${reportData.totalRevenue} SAR\n` +
                                 `فروع نشطة: ${reportData.activeBranches}\n\n` +
                                 `تم إنشاء التقرير في: ${new Date().toLocaleString('ar-SA')}`;

                // عرض التقرير
                alert(reportText);

            } catch (error) {
                console.error('❌ خطأ في إنشاء التقرير:', error);
                alert('خطأ في إنشاء التقرير: ' + error.message);
            }
        }

        // تصدير البيانات
        function exportData() {
            try {
                const data = {
                    shipments: db.getAllShipments(),
                    branches: db.getAllBranches(),
                    transfers: db.getAllBranchTransfers(),
                    exportDate: new Date().toISOString()
                };

                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `shipment-data-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                alert('تم تصدير البيانات بنجاح!');

            } catch (error) {
                console.error('❌ خطأ في تصدير البيانات:', error);
                alert('خطأ في تصدير البيانات: ' + error.message);
            }
        }

        // وظائف عرض التقارير المفصلة
        function viewShipmentsReport() {
            window.location.href = 'shipments.html';
        }

        function viewBranchesReport() {
            window.location.href = 'branches-management.html';
        }

        function viewCancellationReport() {
            window.location.href = 'cancellation-reports.html';
        }

        function viewFinancialReport() {
            window.location.href = 'financial-system.html';
        }

        // وظائف تصدير التقارير المفصلة
        function exportShipmentsReport() {
            exportSpecificData('shipments', db.getAllShipments());
        }

        function exportBranchesReport() {
            exportSpecificData('branches', db.getAllBranches());
        }

        function exportCancellationReport() {
            const cancelledShipments = db.getAllShipments().filter(s => s.status === 'ملغي');
            exportSpecificData('cancellations', cancelledShipments);
        }

        function exportFinancialReport() {
            const deliveredShipments = db.getAllShipments().filter(s => s.status === 'مسلم');
            exportSpecificData('financial', deliveredShipments);
        }

        function exportSpecificData(type, data) {
            try {
                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `${type}-report-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                alert(`تم تصدير تقرير ${type} بنجاح!`);

            } catch (error) {
                console.error(`❌ خطأ في تصدير تقرير ${type}:`, error);
                alert(`خطأ في تصدير تقرير ${type}: ` + error.message);
            }
        }
    </script>
</body>
</html>
