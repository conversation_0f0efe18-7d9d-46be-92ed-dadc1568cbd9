# 🚚 نظام إدارة الشحنات المتكامل

نظام شامل ومتطور لإدارة الشحنات والتوصيل مع النظام المالي المتكامل ودعم اللغة العربية.

## ✨ المزايا الرئيسية

### 📦 إدارة الشحنات
- إضافة وتعديل وحذف الشحنات
- تتبع حالة الشحنات في الوقت الفعلي
- طباعة بوليصات شحن احترافية مع QR Code
- دعم العملات المتعددة (SAR, KWD, USD, EUR)

### 💰 النظام المالي المتكامل
- إدارة التحصيل من المناديب
- تحصيل الشحنات المؤجلة
- إدارة الدفع عند الاستلام (COD)
- حساب وسداد عمولات المناديب
- إنشاء فواتير الشحنات الآجلة

### 🌍 دعم جغرافي واسع
- مناطق المملكة العربية السعودية
- مناطق دولة الكويت
- بحث ذكي في المناطق الجغرافية

## 🚀 كيفية التشغيل

### الطريقة السريعة
1. افتح ملف `index.html` في المتصفح
2. استخدم بيانات الدخول الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `123456`

## 📁 الملفات الأساسية

### الصفحات الرئيسية
- `index.html` - صفحة تسجيل الدخول
- `main-dashboard.html` - لوحة التحكم الرئيسية
- `shipments.html` - إدارة الشحنات (الصفحة الأساسية)
- `customers.html` - إدارة العملاء

### النظام المالي
- `financial-system.html` - النظام المالي الرئيسي
- `collection-management.html` - إدارة التحصيل من المناديب
- `payment-management.html` - إدارة المدفوعات المؤجلة

### الأدوات المساعدة
- `currency-converter.html` - محول العملات
- `test-kuwait.html` - اختبار مناطق الكويت
- `test-print.html` - اختبار طباعة البوليصات

## 🔧 التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتخطيط
- **JavaScript (ES6+)** - المنطق والتفاعل
- **Local Storage** - تخزين البيانات
- **خط SF Pro Arabic** - الخط الحديث من Apple

## 📋 متطلبات التثبيت

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من الموقع الرسمي:
```
https://nodejs.org/
```
اختر النسخة LTS (الموصى بها)

### 2. تثبيت Git
قم بتحميل وتثبيت Git من:
```
https://git-scm.com/download/win
```

### 3. محرر الكود
يُنصح باستخدام Visual Studio Code:
```
https://code.visualstudio.com/
```

## 🏗️ هيكل المشروع

```
برنامج توصيل/
├── frontend/          # واجهة الويب (Next.js)
├── backend/           # API الخلفي (Node.js)
├── mobile/            # تطبيق الموبايل (React Native)
├── database/          # ملفات قاعدة البيانات
├── docs/              # التوثيق
└── shared/            # الملفات المشتركة
```

## 🎨 دليل التصميم

### الخطوط
- **SF Pro Display** - للعناوين
- **SF Pro Text** - للنصوص العادية  
- **SF Arabic** - للنصوص العربية

### الألوان
- **Primary**: #007AFF (أزرق آبل)
- **Secondary**: #34C759 (أخضر)
- **Warning**: #FF9500 (برتقالي)
- **Error**: #FF3B30 (أحمر)

## 📱 الميزات المخططة

- [ ] إدارة الشحنات والتتبع
- [ ] لوحة تحكم تفاعلية
- [ ] تحويل العملات
- [ ] تطبيق الموزعين
- [ ] نظام الإشعارات
- [ ] تكامل ERP
- [ ] تقارير مفصلة
- [ ] أمان متقدم

## 🚀 تعليمات التشغيل | Setup Instructions

### المتطلبات الأساسية | Prerequisites

1. **Node.js 18+** - [تحميل من هنا](https://nodejs.org/)
2. **PostgreSQL 14+** - [تحميل من هنا](https://www.postgresql.org/download/)
3. **Git** - [تحميل من هنا](https://git-scm.com/)

### خطوات التشغيل | Setup Steps

#### 1. إعداد قاعدة البيانات | Database Setup

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE shipment_management;

-- إنشاء مستخدم (اختياري)
CREATE USER shipment_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE shipment_management TO shipment_user;
```

#### 2. إعداد الخلفية | Backend Setup

```bash
# الانتقال لمجلد الخلفية
cd backend

# تثبيت التبعيات
npm install

# نسخ ملف البيئة
cp .env.example .env

# تعديل ملف .env بالبيانات الصحيحة
# DATABASE_URL="postgresql://username:password@localhost:5432/shipment_management"
# JWT_SECRET="your-super-secret-jwt-key-here-make-it-at-least-32-characters-long"

# إعداد قاعدة البيانات
npm run db:setup

# تشغيل الخادم
npm run dev
```

#### 3. إعداد الواجهة الأمامية | Frontend Setup

```bash
# في terminal جديد، الانتقال لمجلد الواجهة الأمامية
cd frontend

# تثبيت التبعيات
npm install

# تشغيل التطبيق
npm run dev
```

#### 4. الوصول للتطبيق | Access Application

- **الواجهة الأمامية**: http://localhost:3000
- **API الخلفي**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api-docs

### بيانات تسجيل الدخول التجريبية | Demo Login Credentials

```
المدير | Admin: <EMAIL> / admin123
المدير | Manager: <EMAIL> / manager123
الموزع | Distributor: <EMAIL> / distributor123
```

## 📞 الدعم

للمساعدة والدعم، يرجى التواصل مع فريق التطوير.

---

**تم التطوير بـ ❤️ باستخدام تقنيات حديثة**
