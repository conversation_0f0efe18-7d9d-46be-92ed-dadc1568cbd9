🚀 تعليمات تشغيل نظام إدارة الشحنات
=======================================

📋 الخطوات السريعة:
==================

1️⃣ تثبيت Node.js:
   - اذهب إلى: https://nodejs.org/
   - حمل النسخة LTS وثبتها
   - أعد تشغيل Command Prompt

2️⃣ تشغيل الإعداد السريع:
   - double-click على: quick-setup.bat
   - اتبع التعليمات

3️⃣ إعداد قاعدة البيانات:
   
   الخيار أ - Supabase (سهل ومجاني):
   - اذهب إلى: https://supabase.com/
   - أنشئ حساب وأنشئ مشروع جديد
   - انسخ Database URL من Settings > Database
   
   الخيار ب - PostgreSQL محلي:
   - ثبت PostgreSQL من: https://www.postgresql.org/
   - أنشئ قاعدة بيانات: shipment_management

4️⃣ تعديل ملف البيئة:
   - افتح: backend\.env
   - عدل هذه القيم:
   
   DATABASE_URL="your-database-url-here"
   JWT_SECRET="your-32-character-secret-key-here"

5️⃣ إعداد قاعدة البيانات:
   - افتح Command Prompt في مجلد المشروع
   - شغل: npm run setup:db

6️⃣ تشغيل البرنامج:
   - double-click على: start.bat
   - أو شغل: npm run dev

🌐 الوصول للتطبيق:
==================
- الواجهة الرئيسية: http://localhost:3000
- API الخلفي: http://localhost:3001
- توثيق API: http://localhost:3001/api-docs

🔐 بيانات تسجيل الدخول:
========================
المدير: <EMAIL> / admin123
المدير: <EMAIL> / manager123
الموزع: <EMAIL> / distributor123

🔧 ملفات مساعدة:
================
- quick-setup.bat: إعداد سريع
- check-status.bat: فحص حالة النظام
- start.bat: تشغيل البرنامج
- QUICK_START.md: دليل مفصل

❗ حل المشاكل:
==============
- إذا لم يعمل Node.js: أعد تشغيل Command Prompt
- إذا فشل الاتصال بقاعدة البيانات: تأكد من DATABASE_URL
- إذا ظهر خطأ JWT: تأكد من JWT_SECRET (32 حرف على الأقل)
- إذا كان المنفذ مستخدم: أغلق التطبيقات الأخرى

📞 للمساعدة:
=============
راجع ملف QUICK_START.md للتفاصيل الكاملة
