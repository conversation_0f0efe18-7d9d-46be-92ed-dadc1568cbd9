<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة ضريبة القيمة المضافة | نظام الامتثال الضريبي</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 25px 35px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #666;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.8);
            color: #333;
        }

        .vat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .vat-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .vat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
        }

        .vat-amount {
            font-size: 2.5rem;
            font-weight: 800;
            color: #28a745;
            text-align: center;
            margin: 20px 0;
        }

        .vat-period {
            text-align: center;
            color: #666;
            font-size: 1rem;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 1rem;
            width: 100%;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 126, 52, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 126, 52, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .vat-table-section {
            background: white;
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.6rem;
            color: #333;
            font-weight: 700;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-title i {
            color: #28a745;
        }

        .vat-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .vat-table th,
        .vat-table td {
            padding: 15px 20px;
            text-align: right;
            border-bottom: 1px solid #f0f2f5;
        }

        .vat-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 700;
            color: #333;
            font-size: 1rem;
        }

        .vat-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-align: center;
            display: inline-block;
        }

        .status-paid {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .status-pending {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
        }

        .status-overdue {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .calculator-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .calculator-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #1976d2;
            margin-bottom: 20px;
            text-align: center;
        }

        .calculator-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .result-display {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .result-item {
            text-align: center;
        }

        .result-label {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .result-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #28a745;
        }

        .total-result {
            border-top: 2px solid #e9ecef;
            padding-top: 20px;
            margin-top: 20px;
        }

        .total-label {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 10px;
        }

        .total-value {
            font-size: 2rem;
            font-weight: 800;
            color: #28a745;
        }

        @media (max-width: 768px) {
            .vat-grid {
                grid-template-columns: 1fr;
            }
            
            .calculator-form {
                grid-template-columns: 1fr;
            }
            
            .result-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 15px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>إدارة ضريبة القيمة المضافة</h1>
            <a href="zatca-compliance.html" class="back-link">
                <i class="fas fa-arrow-right"></i>
                العودة لنظام الامتثال
            </a>
        </div>

        <!-- بطاقات ضريبة القيمة المضافة -->
        <div class="vat-grid">
            <!-- ضريبة الشهر الحالي -->
            <div class="vat-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-calendar-month"></i>
                    </div>
                    <div class="card-title">ضريبة الشهر الحالي</div>
                </div>
                <div class="vat-amount" id="current-month-vat">0.00 ريال</div>
                <div class="vat-period" id="current-period">يناير 2024</div>
                <button class="btn btn-primary" onclick="payCurrentVAT()">
                    <i class="fas fa-credit-card"></i>
                    سداد الضريبة
                </button>
            </div>

            <!-- إجمالي الضريبة السنوية -->
            <div class="vat-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="card-title">إجمالي الضريبة السنوية</div>
                </div>
                <div class="vat-amount" id="yearly-vat">0.00 ريال</div>
                <div class="vat-period" id="current-year">2024</div>
                <button class="btn btn-secondary" onclick="generateYearlyReport()">
                    <i class="fas fa-file-alt"></i>
                    تقرير سنوي
                </button>
            </div>

            <!-- الضريبة المستحقة -->
            <div class="vat-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="card-title">الضريبة المستحقة</div>
                </div>
                <div class="vat-amount" id="overdue-vat">0.00 ريال</div>
                <div class="vat-period">متأخرة السداد</div>
                <button class="btn btn-primary" onclick="payOverdueVAT()">
                    <i class="fas fa-exclamation-circle"></i>
                    سداد فوري
                </button>
            </div>
        </div>

        <!-- حاسبة ضريبة القيمة المضافة المتقدمة -->
        <div class="calculator-section">
            <div class="calculator-title">
                <i class="fas fa-calculator"></i>
                حاسبة ضريبة القيمة المضافة المتقدمة
            </div>
            
            <div class="calculator-form">
                <div class="form-group">
                    <label class="form-label">المبلغ الأساسي (ريال)</label>
                    <input type="number" id="base-amount" class="form-control" placeholder="1000.00" step="0.01" oninput="calculateAdvancedVAT()">
                </div>
                
                <div class="form-group">
                    <label class="form-label">نوع الخدمة/السلعة</label>
                    <select id="service-category" class="form-control" onchange="updateVATRate()">
                        <option value="standard">خدمات عامة (15%)</option>
                        <option value="zero">سلع معفاة (0%)</option>
                        <option value="exempt">خدمات معفاة (0%)</option>
                        <option value="reduced">سلع مخفضة (5%)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">نسبة الضريبة (%)</label>
                    <input type="number" id="vat-rate" class="form-control" value="15" step="0.01" readonly>
                </div>
                
                <div class="form-group">
                    <label class="form-label">العملة</label>
                    <select id="currency" class="form-control" onchange="calculateAdvancedVAT()">
                        <option value="SAR">ريال سعودي (SAR)</option>
                        <option value="USD">دولار أمريكي (USD)</option>
                        <option value="EUR">يورو (EUR)</option>
                        <option value="KWD">دينار كويتي (KWD)</option>
                    </select>
                </div>
            </div>
            
            <div class="result-display" id="advanced-vat-result">
                <div class="result-grid">
                    <div class="result-item">
                        <div class="result-label">المبلغ الأساسي</div>
                        <div class="result-value" id="result-base">0.00</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">ضريبة القيمة المضافة</div>
                        <div class="result-value" id="result-vat">0.00</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">نسبة الضريبة</div>
                        <div class="result-value" id="result-rate">15%</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">العملة</div>
                        <div class="result-value" id="result-currency">SAR</div>
                    </div>
                </div>
                
                <div class="total-result">
                    <div class="total-label">المبلغ الإجمالي شامل الضريبة</div>
                    <div class="total-value" id="result-total">0.00</div>
                </div>
            </div>
        </div>

        <!-- جدول سجل ضريبة القيمة المضافة -->
        <div class="vat-table-section">
            <h2 class="section-title">
                <i class="fas fa-history"></i>
                سجل ضريبة القيمة المضافة
            </h2>
            
            <table class="vat-table">
                <thead>
                    <tr>
                        <th>الفترة</th>
                        <th>المبيعات الخاضعة</th>
                        <th>ضريبة القيمة المضافة</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="vat-records-table">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 تحميل نظام إدارة ضريبة القيمة المضافة...');
            initializeVATSystem();
            loadVATData();
            loadVATRecords();
        });

        // تهيئة نظام ضريبة القيمة المضافة
        function initializeVATSystem() {
            // تهيئة البيانات الأساسية
            if (!localStorage.getItem('vatRecords')) {
                const initialVATRecords = [
                    {
                        id: 'VAT-2024-01',
                        period: '2024-01',
                        periodName: 'يناير 2024',
                        taxableSales: 10000.00,
                        vatAmount: 1500.00,
                        dueDate: '2024-02-15',
                        status: 'paid',
                        paidDate: '2024-02-10',
                        createdAt: '2024-01-31'
                    },
                    {
                        id: 'VAT-2024-02',
                        period: '2024-02',
                        periodName: 'فبراير 2024',
                        taxableSales: 12500.00,
                        vatAmount: 1875.00,
                        dueDate: '2024-03-15',
                        status: 'pending',
                        paidDate: null,
                        createdAt: '2024-02-29'
                    },
                    {
                        id: 'VAT-2024-03',
                        period: '2024-03',
                        periodName: 'مارس 2024',
                        taxableSales: 8750.00,
                        vatAmount: 1312.50,
                        dueDate: '2024-04-15',
                        status: 'overdue',
                        paidDate: null,
                        createdAt: '2024-03-31'
                    }
                ];
                localStorage.setItem('vatRecords', JSON.stringify(initialVATRecords));
            }

            // تهيئة إعدادات الضريبة
            if (!localStorage.getItem('vatSettings')) {
                const vatSettings = {
                    standardRate: 15,
                    zeroRate: 0,
                    reducedRate: 5,
                    reportingPeriod: 'monthly',
                    currency: 'SAR',
                    companyTaxNumber: '300000000000003',
                    lastUpdated: new Date().toISOString()
                };
                localStorage.setItem('vatSettings', JSON.stringify(vatSettings));
            }
        }

        // تحميل بيانات ضريبة القيمة المضافة
        function loadVATData() {
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            // حساب ضريبة الشهر الحالي
            const currentPeriod = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;
            const currentMonthRecord = vatRecords.find(record => record.period === currentPeriod);
            const currentMonthVAT = currentMonthRecord ? currentMonthRecord.vatAmount : 0;

            // حساب إجمالي الضريبة السنوية
            const yearlyRecords = vatRecords.filter(record => record.period.startsWith(currentYear.toString()));
            const yearlyVAT = yearlyRecords.reduce((sum, record) => sum + record.vatAmount, 0);

            // حساب الضريبة المستحقة
            const overdueRecords = vatRecords.filter(record => {
                const dueDate = new Date(record.dueDate);
                return record.status === 'overdue' || (record.status === 'pending' && dueDate < currentDate);
            });
            const overdueVAT = overdueRecords.reduce((sum, record) => sum + record.vatAmount, 0);

            // تحديث العرض
            document.getElementById('current-month-vat').textContent = formatCurrency(currentMonthVAT);
            document.getElementById('current-period').textContent = getMonthName(currentMonth) + ' ' + currentYear;
            document.getElementById('yearly-vat').textContent = formatCurrency(yearlyVAT);
            document.getElementById('current-year').textContent = currentYear.toString();
            document.getElementById('overdue-vat').textContent = formatCurrency(overdueVAT);
        }

        // تحميل سجلات ضريبة القيمة المضافة
        function loadVATRecords() {
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const tableBody = document.getElementById('vat-records-table');

            tableBody.innerHTML = '';

            vatRecords.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${record.periodName}</td>
                    <td>${formatCurrency(record.taxableSales)}</td>
                    <td>${formatCurrency(record.vatAmount)}</td>
                    <td>${formatDate(record.dueDate)}</td>
                    <td><span class="status-badge status-${record.status}">${getStatusText(record.status)}</span></td>
                    <td>
                        ${record.status === 'pending' || record.status === 'overdue' ?
                            `<button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;" onclick="payVAT('${record.id}')">
                                <i class="fas fa-credit-card"></i> سداد
                            </button>` :
                            `<button class="btn btn-secondary" style="padding: 6px 12px; font-size: 0.8rem;" onclick="viewVATDetails('${record.id}')">
                                <i class="fas fa-eye"></i> عرض
                            </button>`
                        }
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // تحديث معدل ضريبة القيمة المضافة
        function updateVATRate() {
            const category = document.getElementById('service-category').value;
            const vatRateInput = document.getElementById('vat-rate');

            const rates = {
                'standard': 15,
                'zero': 0,
                'exempt': 0,
                'reduced': 5
            };

            vatRateInput.value = rates[category] || 15;
            calculateAdvancedVAT();
        }

        // حساب ضريبة القيمة المضافة المتقدم
        function calculateAdvancedVAT() {
            const baseAmount = parseFloat(document.getElementById('base-amount').value) || 0;
            const vatRate = parseFloat(document.getElementById('vat-rate').value) || 15;
            const currency = document.getElementById('currency').value;

            const vatAmount = baseAmount * (vatRate / 100);
            const totalAmount = baseAmount + vatAmount;

            // تحديث العرض
            document.getElementById('result-base').textContent = formatCurrency(baseAmount, currency);
            document.getElementById('result-vat').textContent = formatCurrency(vatAmount, currency);
            document.getElementById('result-rate').textContent = vatRate + '%';
            document.getElementById('result-currency').textContent = currency;
            document.getElementById('result-total').textContent = formatCurrency(totalAmount, currency);
        }

        // سداد ضريبة الشهر الحالي
        function payCurrentVAT() {
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();
            const currentPeriod = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;

            payVATByPeriod(currentPeriod);
        }

        // سداد الضريبة المستحقة
        function payOverdueVAT() {
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const currentDate = new Date();

            const overdueRecords = vatRecords.filter(record => {
                const dueDate = new Date(record.dueDate);
                return record.status === 'overdue' || (record.status === 'pending' && dueDate < currentDate);
            });

            if (overdueRecords.length === 0) {
                showAlert('لا توجد ضرائب مستحقة للسداد', 'info');
                return;
            }

            // سداد جميع الضرائب المستحقة
            overdueRecords.forEach(record => {
                payVAT(record.id);
            });
        }

        // سداد ضريبة محددة
        function payVAT(recordId) {
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const recordIndex = vatRecords.findIndex(record => record.id === recordId);

            if (recordIndex === -1) {
                showAlert('لم يتم العثور على السجل المطلوب', 'error');
                return;
            }

            const record = vatRecords[recordIndex];

            if (record.status === 'paid') {
                showAlert('تم سداد هذه الضريبة مسبقاً', 'info');
                return;
            }

            // تأكيد السداد
            if (confirm(`هل تريد سداد ضريبة القيمة المضافة؟\n\nالفترة: ${record.periodName}\nالمبلغ: ${formatCurrency(record.vatAmount)}`)) {
                // تحديث حالة السجل
                vatRecords[recordIndex].status = 'paid';
                vatRecords[recordIndex].paidDate = new Date().toISOString();

                // حفظ التحديث
                localStorage.setItem('vatRecords', JSON.stringify(vatRecords));

                // تحديث العرض
                loadVATData();
                loadVATRecords();

                // عرض رسالة نجاح
                showAlert(`تم سداد ضريبة القيمة المضافة بنجاح!\n\nالفترة: ${record.periodName}\nالمبلغ: ${formatCurrency(record.vatAmount)}`, 'success');

                // إنشاء إيصال سداد
                generatePaymentReceipt(record);
            }
        }

        // سداد ضريبة حسب الفترة
        function payVATByPeriod(period) {
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const record = vatRecords.find(r => r.period === period);

            if (record) {
                payVAT(record.id);
            } else {
                showAlert('لم يتم العثور على سجل ضريبي لهذه الفترة', 'info');
            }
        }

        // عرض تفاصيل ضريبة القيمة المضافة
        function viewVATDetails(recordId) {
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const record = vatRecords.find(r => r.id === recordId);

            if (!record) {
                showAlert('لم يتم العثور على السجل المطلوب', 'error');
                return;
            }

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #333; font-size: 1.4rem;">تفاصيل ضريبة القيمة المضافة</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div>
                                <strong>رقم السجل:</strong><br>
                                <span style="color: #666;">${record.id}</span>
                            </div>
                            <div>
                                <strong>الفترة:</strong><br>
                                <span style="color: #666;">${record.periodName}</span>
                            </div>
                            <div>
                                <strong>المبيعات الخاضعة:</strong><br>
                                <span style="color: #666;">${formatCurrency(record.taxableSales)}</span>
                            </div>
                            <div>
                                <strong>ضريبة القيمة المضافة:</strong><br>
                                <span style="color: #28a745; font-weight: 700;">${formatCurrency(record.vatAmount)}</span>
                            </div>
                            <div>
                                <strong>تاريخ الاستحقاق:</strong><br>
                                <span style="color: #666;">${formatDate(record.dueDate)}</span>
                            </div>
                            <div>
                                <strong>الحالة:</strong><br>
                                <span class="status-badge status-${record.status}">${getStatusText(record.status)}</span>
                            </div>
                        </div>

                        ${record.paidDate ? `
                            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                                <strong>تاريخ السداد:</strong> ${formatDate(record.paidDate)}
                            </div>
                        ` : ''}
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        ${record.status !== 'paid' ? `
                            <button class="btn btn-primary" onclick="payVAT('${record.id}'); this.closest('.modal').remove();">
                                <i class="fas fa-credit-card"></i>
                                سداد الضريبة
                            </button>
                        ` : ''}
                        <button class="btn btn-secondary" onclick="downloadVATCertificate('${record.id}')">
                            <i class="fas fa-download"></i>
                            تحميل الشهادة
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            إغلاق
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // إنشاء إيصال سداد
        function generatePaymentReceipt(record) {
            const receipt = {
                id: 'RECEIPT-' + Date.now(),
                vatRecordId: record.id,
                period: record.periodName,
                amount: record.vatAmount,
                paidDate: new Date().toISOString(),
                paymentMethod: 'electronic',
                status: 'completed'
            };

            // حفظ الإيصال
            const receipts = JSON.parse(localStorage.getItem('vatPaymentReceipts') || '[]');
            receipts.push(receipt);
            localStorage.setItem('vatPaymentReceipts', JSON.stringify(receipts));

            console.log('✅ تم إنشاء إيصال السداد:', receipt.id);
        }

        // إنشاء تقرير سنوي
        function generateYearlyReport() {
            const currentYear = new Date().getFullYear();
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const yearlyRecords = vatRecords.filter(record => record.period.startsWith(currentYear.toString()));

            const report = {
                year: currentYear,
                totalRecords: yearlyRecords.length,
                totalTaxableSales: yearlyRecords.reduce((sum, record) => sum + record.taxableSales, 0),
                totalVAT: yearlyRecords.reduce((sum, record) => sum + record.vatAmount, 0),
                paidRecords: yearlyRecords.filter(record => record.status === 'paid').length,
                pendingRecords: yearlyRecords.filter(record => record.status === 'pending').length,
                overdueRecords: yearlyRecords.filter(record => record.status === 'overdue').length
            };

            showYearlyReport(report);
        }

        // عرض التقرير السنوي
        function showYearlyReport(report) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 800px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #333; font-size: 1.4rem;">التقرير السنوي لضريبة القيمة المضافة ${report.year}</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                        <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 12px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #28a745;">${report.totalRecords}</div>
                            <div style="color: #666;">إجمالي الفترات</div>
                        </div>

                        <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 12px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #007bff;">${formatCurrency(report.totalTaxableSales)}</div>
                            <div style="color: #666;">إجمالي المبيعات</div>
                        </div>

                        <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 12px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #ffc107;">${formatCurrency(report.totalVAT)}</div>
                            <div style="color: #666;">إجمالي الضريبة</div>
                        </div>

                        <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 12px;">
                            <div style="font-size: 2rem; font-weight: 700; color: #28a745;">${report.paidRecords}</div>
                            <div style="color: #666;">فترات مسددة</div>
                        </div>
                    </div>

                    <div style="background: #e3f2fd; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #1976d2; margin-bottom: 15px;">ملخص الحالات</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #28a745;">${report.paidRecords}</div>
                                <div style="color: #666;">مسددة</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #ffc107;">${report.pendingRecords}</div>
                                <div style="color: #666;">معلقة</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #dc3545;">${report.overdueRecords}</div>
                                <div style="color: #666;">متأخرة</div>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button class="btn btn-primary" onclick="downloadYearlyReport(${report.year})">
                            <i class="fas fa-download"></i>
                            تحميل التقرير
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            إغلاق
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // تحميل شهادة ضريبة القيمة المضافة
        function downloadVATCertificate(recordId) {
            alert(`سيتم تحميل شهادة ضريبة القيمة المضافة للسجل ${recordId}\n\n(هذه ميزة تحتاج لتطوير إضافي مع مكتبة PDF)`);
        }

        // تحميل التقرير السنوي
        function downloadYearlyReport(year) {
            alert(`سيتم تحميل التقرير السنوي لضريبة القيمة المضافة ${year}\n\n(هذه ميزة تحتاج لتطوير إضافي مع مكتبة PDF)`);
        }

        // دوال مساعدة
        function formatCurrency(amount, currency = 'SAR') {
            const symbols = {
                'SAR': 'ريال',
                'USD': '$',
                'EUR': '€',
                'KWD': 'د.ك'
            };

            return amount.toFixed(2) + ' ' + (symbols[currency] || currency);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        function getMonthName(month) {
            const months = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            return months[month - 1] || '';
        }

        function getStatusText(status) {
            const statusTexts = {
                'paid': 'مسددة',
                'pending': 'معلقة',
                'overdue': 'متأخرة'
            };
            return statusTexts[status] || status;
        }

        function showAlert(message, type = 'info') {
            const colors = {
                'success': '#d4edda',
                'error': '#f8d7da',
                'info': '#cce5ff',
                'warning': '#fff3cd'
            };

            const alert = document.createElement('div');
            alert.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: #333;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 2000;
                max-width: 400px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                white-space: pre-line;
            `;

            alert.innerHTML = `
                ${message}
                <button onclick="this.parentElement.remove()" style="position: absolute; top: 5px; left: 10px; background: none; border: none; font-size: 1.2rem; cursor: pointer;">×</button>
            `;

            document.body.appendChild(alert);

            setTimeout(() => {
                if (alert.parentElement) {
                    alert.remove();
                }
            }, 5000);
        }

        // تهيئة الحاسبة عند التحميل
        calculateAdvancedVAT();
    </script>
</body>
</html>
