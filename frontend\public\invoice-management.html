<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير | النظام المالي</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: #f5f7fa;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .back-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }

        .invoice-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #ffc107;
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .invoice-preview {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: none;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .company-info h2 {
            color: #ffc107;
            margin-bottom: 10px;
        }

        .invoice-number {
            text-align: left;
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
        }

        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .detail-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }

        .detail-section h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .invoice-table th,
        .invoice-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .invoice-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .invoice-total {
            text-align: left;
            margin-top: 20px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .total-row.final {
            font-size: 1.2rem;
            font-weight: 700;
            color: #ffc107;
            border-bottom: 3px solid #ffc107;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
            
            .invoice-preview {
                box-shadow: none;
                border: 1px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="financial-system.html" class="back-link no-print">← العودة للنظام المالي</a>
        
        <div class="header no-print">
            <h1>📄 إدارة الفواتير</h1>
            <p>إنشاء وطباعة فواتير الشحنات المؤجلة</p>
        </div>

        <!-- نموذج إنشاء الفاتورة -->
        <div class="invoice-form no-print">
            <h2 style="margin-bottom: 20px; color: #333;">إنشاء فاتورة جديدة</h2>
            
            <form id="invoiceForm" onsubmit="generateInvoice(event)">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">العميل</label>
                        <select class="form-select" id="customerId" required>
                            <option value="">اختر العميل</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">تاريخ الاستحقاق</label>
                        <input type="date" class="form-input" id="dueDate" required>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">الشحنات المشمولة</label>
                    <div id="shipmentsContainer" style="max-height: 200px; overflow-y: auto; border: 1px solid #e1e5e9; border-radius: 8px; padding: 15px;">
                        <!-- سيتم تحميل الشحنات هنا -->
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-input" id="notes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">إجمالي المبلغ</label>
                        <input type="number" class="form-input" id="totalAmount" step="0.01" readonly style="background: #f8f9fa;">
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn">📄 إنشاء الفاتورة</button>
                    <button type="button" class="btn btn-info" onclick="loadCustomers()">🔄 تحديث البيانات</button>
                </div>
            </form>
        </div>

        <!-- معاينة الفاتورة -->
        <div class="invoice-preview" id="invoicePreview">
            <!-- سيتم إنشاء محتوى الفاتورة هنا -->
        </div>
    </div>

    <script>
        let selectedShipments = [];
        let customers = [];

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadCustomers();
            loadShipments();
            setDefaultDueDate();
        });

        // تحميل العملاء
        function loadCustomers() {
            try {
                customers = JSON.parse(localStorage.getItem('customers') || '[]');
                const customerSelect = document.getElementById('customerId');
                customerSelect.innerHTML = '<option value="">اختر العميل</option>';

                customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = `${customer.name} - ${customer.phone}`;
                    customerSelect.appendChild(option);
                });

                console.log(`تم تحميل ${customers.length} عميل`);
            } catch (error) {
                console.error('خطأ في تحميل العملاء:', error);
            }
        }

        // تحميل الشحنات المؤجلة
        function loadShipments() {
            try {
                const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                const deferredShipments = shipments.filter(s => s.paymentType === 'deferred' && s.status !== 'cancelled');

                const container = document.getElementById('shipmentsContainer');
                container.innerHTML = '';

                if (deferredShipments.length === 0) {
                    container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد شحنات مؤجلة الدفع</p>';
                    return;
                }

                deferredShipments.forEach(shipment => {
                    const shipmentDiv = document.createElement('div');
                    shipmentDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 10px; border: 1px solid #e9ecef; border-radius: 8px; margin-bottom: 10px;';

                    shipmentDiv.innerHTML = `
                        <div>
                            <input type="checkbox" id="ship_${shipment.id}" onchange="toggleShipment('${shipment.id}', ${shipment.amount || 0})">
                            <label for="ship_${shipment.id}" style="margin-right: 10px;">
                                <strong>${shipment.trackingNumber}</strong> - ${shipment.receiverName}
                                <br><small style="color: #666;">${shipment.receiverCity} - ${new Date(shipment.createdAt).toLocaleDateString('ar-SA')}</small>
                            </label>
                        </div>
                        <div style="text-align: left;">
                            <strong>${shipment.amount || 0} ريال</strong>
                        </div>
                    `;

                    container.appendChild(shipmentDiv);
                });

                console.log(`تم تحميل ${deferredShipments.length} شحنة مؤجلة`);
            } catch (error) {
                console.error('خطأ في تحميل الشحنات:', error);
            }
        }

        // تبديل اختيار الشحنة
        function toggleShipment(shipmentId, amount) {
            const checkbox = document.getElementById(`ship_${shipmentId}`);

            if (checkbox.checked) {
                selectedShipments.push({ id: shipmentId, amount: amount });
            } else {
                selectedShipments = selectedShipments.filter(s => s.id !== shipmentId);
            }

            updateTotalAmount();
        }

        // تحديث إجمالي المبلغ
        function updateTotalAmount() {
            const total = selectedShipments.reduce((sum, shipment) => sum + shipment.amount, 0);
            document.getElementById('totalAmount').value = total.toFixed(2);
        }

        // تعيين تاريخ الاستحقاق الافتراضي (30 يوم من اليوم)
        function setDefaultDueDate() {
            const today = new Date();
            today.setDate(today.getDate() + 30);
            document.getElementById('dueDate').value = today.toISOString().split('T')[0];
        }

        // إنشاء الفاتورة
        function generateInvoice(event) {
            event.preventDefault();

            const customerId = document.getElementById('customerId').value;
            const dueDate = document.getElementById('dueDate').value;
            const notes = document.getElementById('notes').value;
            const totalAmount = parseFloat(document.getElementById('totalAmount').value);

            if (!customerId) {
                alert('يرجى اختيار العميل');
                return;
            }

            if (selectedShipments.length === 0) {
                alert('يرجى اختيار شحنة واحدة على الأقل');
                return;
            }

            // العثور على بيانات العميل
            const customer = customers.find(c => c.id === customerId);
            if (!customer) {
                alert('لم يتم العثور على بيانات العميل');
                return;
            }

            // الحصول على بيانات الشركة
            const companySettings = JSON.parse(localStorage.getItem('companySettings') || '{}');

            // إنشاء رقم الفاتورة
            const invoiceNumber = 'INV-' + Date.now();

            // إنشاء محتوى الفاتورة
            createInvoiceHTML(invoiceNumber, customer, companySettings, selectedShipments, totalAmount, dueDate, notes);

            // إظهار معاينة الفاتورة
            document.getElementById('invoicePreview').style.display = 'block';
            document.querySelector('.invoice-form').style.display = 'none';

            // حفظ الفاتورة
            saveInvoice(invoiceNumber, customerId, selectedShipments, totalAmount, dueDate, notes);
        }

        // إنشاء HTML الفاتورة
        function createInvoiceHTML(invoiceNumber, customer, companySettings, shipments, totalAmount, dueDate, notes) {
            const currentDate = new Date().toLocaleDateString('ar-SA');
            const formattedDueDate = new Date(dueDate).toLocaleDateString('ar-SA');

            const companyName = companySettings.name || 'شركة الشحن السريع';
            const companyPhone = companySettings.phone || '+966501234567';
            const companyEmail = companySettings.email || '<EMAIL>';
            const companyWebsite = companySettings.website || 'www.fastshipping.com';
            const companyAddress = companySettings.address || 'الرياض، المملكة العربية السعودية';
            const companyLogo = companySettings.logo || null;

            // إنشاء جدول الشحنات
            let shipmentsTable = '';
            shipments.forEach((shipment, index) => {
                const shipmentData = JSON.parse(localStorage.getItem('shipments') || '[]').find(s => s.id === shipment.id);
                shipmentsTable += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${shipmentData?.trackingNumber || shipment.id}</td>
                        <td>${shipmentData?.receiverName || 'غير محدد'}</td>
                        <td>${shipmentData?.receiverCity || 'غير محدد'}</td>
                        <td>${shipment.amount.toFixed(2)} ريال</td>
                    </tr>
                `;
            });

            const invoiceHTML = `
                <div class="invoice-header">
                    <div class="company-info">
                        ${companyLogo ?
                            `<img src="${companyLogo}" alt="Company Logo" style="width: 80px; height: 80px; border-radius: 10px; margin-bottom: 15px;">` :
                            '<div style="font-size: 3rem; margin-bottom: 15px;">🚚</div>'
                        }
                        <h2>${companyName}</h2>
                        <p>${companyPhone}</p>
                        ${companyEmail ? `<p>${companyEmail}</p>` : ''}
                        ${companyWebsite ? `<p>${companyWebsite}</p>` : ''}
                        <p style="margin-top: 10px; font-size: 0.9rem;">${companyAddress}</p>
                    </div>
                    <div class="invoice-number">
                        <h3>فاتورة رقم</h3>
                        <p style="color: #ffc107; font-size: 2rem;">${invoiceNumber}</p>
                    </div>
                </div>

                <div class="invoice-details">
                    <div class="detail-section">
                        <h3>بيانات العميل</h3>
                        <p><strong>الاسم:</strong> ${customer.name}</p>
                        <p><strong>الهاتف:</strong> ${customer.phone}</p>
                        <p><strong>البريد:</strong> ${customer.email || 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> ${customer.address || 'غير محدد'}</p>
                    </div>
                    <div class="detail-section">
                        <h3>تفاصيل الفاتورة</h3>
                        <p><strong>تاريخ الإصدار:</strong> ${currentDate}</p>
                        <p><strong>تاريخ الاستحقاق:</strong> ${formattedDueDate}</p>
                        <p><strong>عدد الشحنات:</strong> ${shipments.length}</p>
                        <p><strong>إجمالي المبلغ:</strong> ${totalAmount.toFixed(2)} ريال</p>
                    </div>
                </div>

                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>رقم التتبع</th>
                            <th>المستلم</th>
                            <th>المدينة</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${shipmentsTable}
                    </tbody>
                </table>

                <div class="invoice-total">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>${totalAmount.toFixed(2)} ريال</span>
                    </div>
                    <div class="total-row">
                        <span>ضريبة القيمة المضافة (15%):</span>
                        <span>${(totalAmount * 0.15).toFixed(2)} ريال</span>
                    </div>
                    <div class="total-row final">
                        <span>الإجمالي النهائي:</span>
                        <span>${(totalAmount * 1.15).toFixed(2)} ريال</span>
                    </div>
                </div>

                ${notes ? `
                    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                        <h4>ملاحظات:</h4>
                        <p>${notes}</p>
                    </div>
                ` : ''}

                <div style="margin-top: 40px; text-align: center; border-top: 2px solid #e9ecef; padding-top: 20px;">
                    <p style="color: #666; font-style: italic;">شكراً لتعاملكم معنا</p>
                    <div style="margin-top: 20px;" class="no-print">
                        <button onclick="printInvoice()" class="btn">🖨️ طباعة الفاتورة</button>
                        <button onclick="createNewInvoice()" class="btn btn-success">📄 فاتورة جديدة</button>
                        <button onclick="downloadInvoice('${invoiceNumber}')" class="btn btn-info">💾 تحميل PDF</button>
                    </div>
                </div>
            `;

            document.getElementById('invoicePreview').innerHTML = invoiceHTML;
        }

        // حفظ الفاتورة
        function saveInvoice(invoiceNumber, customerId, shipments, totalAmount, dueDate, notes) {
            try {
                const invoices = JSON.parse(localStorage.getItem('invoices') || '[]');

                const invoice = {
                    id: invoiceNumber,
                    customerId: customerId,
                    shipments: shipments,
                    totalAmount: totalAmount,
                    vatAmount: totalAmount * 0.15,
                    finalAmount: totalAmount * 1.15,
                    dueDate: dueDate,
                    notes: notes,
                    status: 'pending',
                    createdAt: new Date().toISOString(),
                    createdBy: localStorage.getItem('userEmail') || 'النظام'
                };

                invoices.push(invoice);
                localStorage.setItem('invoices', JSON.stringify(invoices));

                console.log('تم حفظ الفاتورة:', invoiceNumber);
            } catch (error) {
                console.error('خطأ في حفظ الفاتورة:', error);
            }
        }

        // طباعة الفاتورة
        function printInvoice() {
            window.print();
        }

        // إنشاء فاتورة جديدة
        function createNewInvoice() {
            document.getElementById('invoicePreview').style.display = 'none';
            document.querySelector('.invoice-form').style.display = 'block';

            // إعادة تعيين النموذج
            document.getElementById('invoiceForm').reset();
            selectedShipments = [];
            updateTotalAmount();
            setDefaultDueDate();

            // إلغاء تحديد جميع الشحنات
            document.querySelectorAll('#shipmentsContainer input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        // تحميل الفاتورة كـ PDF (محاكاة)
        function downloadInvoice(invoiceNumber) {
            alert(`سيتم تحميل الفاتورة ${invoiceNumber} كملف PDF\n\n(هذه ميزة تحتاج لتطوير إضافي مع مكتبة PDF)`);
        }
    </script>
</body>
</html>
