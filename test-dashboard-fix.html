<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح لوحة التحكم</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            max-width: 700px;
            width: 100%;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }

        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-btn {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .test-btn.danger:hover {
            box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3);
        }

        .status-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .back-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s;
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }

        .fix-list {
            list-style: none;
            padding: 0;
        }

        .fix-list li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .fix-list li::before {
            content: "✅";
            font-size: 1.2rem;
            color: #28a745;
        }

        .error-example {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .success-example {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للوحة التحكم</a>
    
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 اختبار إصلاح لوحة التحكم</h1>
            <p>تحقق من إصلاح مشكلة "خطأ في تحميل الفروع: db is not defined"</p>
        </div>

        <div class="test-section">
            <h3>❌ المشكلة الأصلية</h3>
            <div class="error-example">
                خطأ في تحميل الفروع: db is not defined
            </div>
            <p style="color: #666; margin-top: 10px;">
                كانت لوحة التحكم تحاول الوصول لمتغير `db` قبل التأكد من تهيئته، مما يسبب أخطاء في التحميل.
            </p>
        </div>

        <div class="test-section">
            <h3>✅ الإصلاحات المطبقة</h3>
            <ul class="fix-list">
                <li>إنشاء قاعدة بيانات احتياطية فورية</li>
                <li>إضافة فحص وجود قاعدة البيانات قبل الاستخدام</li>
                <li>تحسين دوال تحميل البيانات</li>
                <li>إضافة شحنات وفروع تجريبية</li>
                <li>معالجة أخطاء متعددة المستويات</li>
                <li>استخدام localStorage كبديل آمن</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 اختبار النظام</h3>
            <a href="main-dashboard.html" class="test-btn">
                افتح لوحة التحكم المحدثة
            </a>
            <p style="color: #666; font-size: 0.9rem; margin-top: 10px;">
                يجب أن تعمل لوحة التحكم بدون أخطاء وتعرض الإحصائيات بشكل صحيح
            </p>
            
            <div class="success-example" style="margin-top: 15px;">
                ✅ النتيجة المتوقعة: لا توجد رسائل خطأ، وتظهر الإحصائيات بشكل صحيح
            </div>
        </div>

        <div class="test-section">
            <h3>📊 البيانات التجريبية المضافة</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;">
                <div style="background: white; border: 2px solid #3498db; border-radius: 10px; padding: 15px; text-align: center;">
                    <h4 style="color: #3498db; margin-bottom: 8px;">📦 الشحنات</h4>
                    <p style="font-size: 0.9rem; color: #666;">3 شحنات تجريبية</p>
                    <p style="font-size: 0.8rem; color: #666;">في الطريق، تم التسليم، معلق</p>
                </div>
                
                <div style="background: white; border: 2px solid #27ae60; border-radius: 10px; padding: 15px; text-align: center;">
                    <h4 style="color: #27ae60; margin-bottom: 8px;">🏢 الفروع</h4>
                    <p style="font-size: 0.9rem; color: #666;">3 فروع تجريبية</p>
                    <p style="font-size: 0.8rem; color: #666;">الرياض، جدة، الدمام</p>
                </div>
                
                <div style="background: white; border: 2px solid #f39c12; border-radius: 10px; padding: 15px; text-align: center;">
                    <h4 style="color: #f39c12; margin-bottom: 8px;">👥 المستخدمين</h4>
                    <p style="font-size: 0.9rem; color: #666;">بيانات محفوظة</p>
                    <p style="font-size: 0.8rem; color: #666;">من localStorage</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 خطوات الاختبار</h3>
            <ol style="color: #666; line-height: 1.6; padding-right: 20px;">
                <li>اضغط على "افتح لوحة التحكم المحدثة"</li>
                <li>تأكد من عدم ظهور رسالة "خطأ في تحميل الفروع"</li>
                <li>تأكد من ظهور الإحصائيات في البطاقات العلوية</li>
                <li>تحقق من عمل جميع الأقسام (الشحنات، الفروع، المستخدمين)</li>
                <li>جرب التنقل بين الصفحات المختلفة</li>
                <li>تأكد من عدم ظهور أخطاء في وحدة التحكم (F12)</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات التشخيص</h3>
            <button onclick="checkDatabase()" class="test-btn">
                📊 فحص قاعدة البيانات
            </button>
            <button onclick="testFunctions()" class="test-btn">
                🔍 اختبار الوظائف
            </button>
            <button onclick="clearData()" class="test-btn danger">
                🗑️ مسح البيانات التجريبية
            </button>
            
            <div class="status-box" id="statusBox" style="display: none;">
                <h4 style="color: #1976d2; margin-bottom: 10px;">📊 نتائج التشخيص</h4>
                <div id="statusContent"></div>
            </div>
        </div>
    </div>

    <script>
        // فحص قاعدة البيانات
        function checkDatabase() {
            const statusBox = document.getElementById('statusBox');
            const statusContent = document.getElementById('statusContent');
            
            let results = [];
            
            // فحص متغير db
            if (typeof db !== 'undefined') {
                results.push('✅ متغير db متاح');
                
                // فحص الوظائف
                const functions = ['getAllShipments', 'getAllBranches', 'getAllCustomers'];
                functions.forEach(func => {
                    if (typeof db[func] === 'function') {
                        results.push(`✅ دالة ${func} متاحة`);
                    } else {
                        results.push(`❌ دالة ${func} غير متاحة`);
                    }
                });
            } else {
                results.push('⚠️ متغير db غير متاح (سيتم استخدام البيانات المحفوظة)');
            }
            
            // فحص localStorage
            const dataTypes = ['shipments', 'branches', 'customers'];
            dataTypes.forEach(type => {
                try {
                    const data = JSON.parse(localStorage.getItem(type) || '[]');
                    results.push(`📊 ${type}: ${data.length} عنصر`);
                } catch (error) {
                    results.push(`❌ خطأ في قراءة ${type}`);
                }
            });
            
            statusContent.innerHTML = results.map(r => `<p style="margin: 5px 0;">${r}</p>`).join('');
            statusBox.style.display = 'block';
        }

        // اختبار الوظائف
        function testFunctions() {
            const statusBox = document.getElementById('statusBox');
            const statusContent = document.getElementById('statusContent');
            
            let results = [];
            
            try {
                // اختبار تحميل الشحنات
                const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                results.push(`✅ تم تحميل ${shipments.length} شحنة`);
                
                // اختبار تحميل الفروع
                const branches = JSON.parse(localStorage.getItem('branches') || '[]');
                results.push(`✅ تم تحميل ${branches.length} فرع`);
                
                // اختبار حساب الإحصائيات
                const pending = shipments.filter(s => s.status === 'في الطريق' || s.status === 'معلق').length;
                const delivered = shipments.filter(s => s.status === 'تم التسليم').length;
                
                results.push(`📊 الشحنات المعلقة: ${pending}`);
                results.push(`📊 الشحنات المسلمة: ${delivered}`);
                
            } catch (error) {
                results.push(`❌ خطأ في الاختبار: ${error.message}`);
            }
            
            statusContent.innerHTML = results.map(r => `<p style="margin: 5px 0;">${r}</p>`).join('');
            statusBox.style.display = 'block';
        }

        // مسح البيانات التجريبية
        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟\n\nسيتم إعادة إنشاؤها عند فتح لوحة التحكم مرة أخرى.')) {
                localStorage.removeItem('shipments');
                localStorage.removeItem('branches');
                localStorage.removeItem('customers');
                localStorage.removeItem('users');
                
                alert('✅ تم مسح البيانات التجريبية بنجاح!');
                checkDatabase();
            }
        }

        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 صفحة اختبار إصلاح لوحة التحكم');
            console.log('✅ الإصلاحات المطبقة:');
            console.log('  - إنشاء قاعدة بيانات احتياطية');
            console.log('  - فحص وجود قاعدة البيانات');
            console.log('  - تحسين دوال التحميل');
            console.log('  - إضافة بيانات تجريبية');
            
            // فحص تلقائي للبيانات
            setTimeout(checkDatabase, 1000);
        });
    </script>
</body>
</html>
