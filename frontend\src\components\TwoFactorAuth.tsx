'use client'

import React, { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import { authApi } from '@/lib/api/auth'
import { TwoFactorStatus, TwoFactorSetup } from '@/types/auth'

interface TwoFactorAuthProps {
  onClose?: () => void
}

export default function TwoFactorAuth({ onClose }: TwoFactorAuthProps) {
  const [status, setStatus] = useState<TwoFactorStatus | null>(null)
  const [setup, setSetup] = useState<TwoFactorSetup | null>(null)
  const [loading, setLoading] = useState(false)
  const [step, setStep] = useState<'status' | 'setup' | 'verify'>('status')
  const [token, setToken] = useState('')
  const [backupCodes, setBackupCodes] = useState<string[]>([])
  const [showBackupCodes, setShowBackupCodes] = useState(false)

  useEffect(() => {
    loadStatus()
  }, [])

  const loadStatus = async () => {
    try {
      setLoading(true)
      const statusData = await authApi.get2FAStatus()
      setStatus(statusData)
    } catch (error: any) {
      toast.error(error.message || 'فشل في تحميل حالة المصادقة الثنائية')
    } finally {
      setLoading(false)
    }
  }

  const handleSetup2FA = async () => {
    try {
      setLoading(true)
      const setupData = await authApi.setup2FA()
      setSetup(setupData)
      setStep('setup')
    } catch (error: any) {
      toast.error(error.message || 'فشل في إعداد المصادقة الثنائية')
    } finally {
      setLoading(false)
    }
  }

  const handleEnable2FA = async () => {
    if (!setup || !token) {
      toast.error('يرجى إدخال رمز التحقق')
      return
    }

    try {
      setLoading(true)
      const result = await authApi.enable2FA({
        token,
        secret: setup.secret,
      })
      setBackupCodes(result.backupCodes)
      setShowBackupCodes(true)
      setStep('verify')
      toast.success('تم تفعيل المصادقة الثنائية بنجاح!')
      await loadStatus()
    } catch (error: any) {
      toast.error(error.message || 'فشل في تفعيل المصادقة الثنائية')
    } finally {
      setLoading(false)
    }
  }

  const handleDisable2FA = async () => {
    if (!token) {
      toast.error('يرجى إدخال رمز التحقق')
      return
    }

    try {
      setLoading(true)
      await authApi.disable2FA({ token })
      toast.success('تم إلغاء المصادقة الثنائية بنجاح!')
      await loadStatus()
      setToken('')
    } catch (error: any) {
      toast.error(error.message || 'فشل في إلغاء المصادقة الثنائية')
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateBackupCodes = async () => {
    if (!token) {
      toast.error('يرجى إدخال رمز التحقق')
      return
    }

    try {
      setLoading(true)
      const result = await authApi.generateBackupCodes({ token })
      setBackupCodes(result.backupCodes)
      setShowBackupCodes(true)
      toast.success('تم إنشاء رموز احتياطية جديدة!')
      await loadStatus()
      setToken('')
    } catch (error: any) {
      toast.error(error.message || 'فشل في إنشاء رموز احتياطية جديدة')
    } finally {
      setLoading(false)
    }
  }

  if (loading && !status) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">المصادقة الثنائية</h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        )}
      </div>

      {step === 'status' && status && (
        <div className="space-y-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">الحالة الحالية</h3>
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className={`inline-block w-3 h-3 rounded-full ${status.twoFactorEnabled ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span className="text-gray-700">
                {status.twoFactorEnabled ? 'مُفعلة' : 'غير مُفعلة'}
              </span>
            </div>
            {status.twoFactorEnabled && (
              <p className="text-sm text-gray-600 mt-2">
                عدد الرموز الاحتياطية المتبقية: {status.backupCodesCount}
              </p>
            )}
          </div>

          {!status.twoFactorEnabled ? (
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">ما هي المصادقة الثنائية؟</h4>
                <p className="text-blue-800 text-sm">
                  المصادقة الثنائية تضيف طبقة حماية إضافية لحسابك. بعد إدخال كلمة المرور، ستحتاج إلى إدخال رمز من تطبيق المصادقة على هاتفك.
                </p>
              </div>
              <button
                onClick={handleSetup2FA}
                disabled={loading}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'جاري الإعداد...' : 'تفعيل المصادقة الثنائية'}
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رمز التحقق
                  </label>
                  <input
                    type="text"
                    value={token}
                    onChange={(e) => setToken(e.target.value)}
                    placeholder="أدخل رمز التحقق"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    maxLength={8}
                  />
                </div>
              </div>
              
              <div className="flex space-x-4 space-x-reverse">
                <button
                  onClick={handleDisable2FA}
                  disabled={loading || !token}
                  className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50"
                >
                  {loading ? 'جاري الإلغاء...' : 'إلغاء المصادقة الثنائية'}
                </button>
                <button
                  onClick={handleGenerateBackupCodes}
                  disabled={loading || !token}
                  className="flex-1 bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 disabled:opacity-50"
                >
                  {loading ? 'جاري الإنشاء...' : 'إنشاء رموز احتياطية جديدة'}
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {step === 'setup' && setup && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-4">امسح رمز QR باستخدام تطبيق المصادقة</h3>
            <div className="bg-white p-4 rounded-lg border-2 border-gray-200 inline-block">
              <img src={setup.qrCode} alt="QR Code" className="w-64 h-64" />
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">أو أدخل المفتاح يدوياً:</h4>
            <code className="bg-gray-200 px-2 py-1 rounded text-sm break-all">
              {setup.manualEntryKey}
            </code>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              أدخل رمز التحقق من التطبيق
            </label>
            <input
              type="text"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              placeholder="123456"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              maxLength={6}
            />
          </div>

          <div className="flex space-x-4 space-x-reverse">
            <button
              onClick={() => setStep('status')}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700"
            >
              إلغاء
            </button>
            <button
              onClick={handleEnable2FA}
              disabled={loading || !token}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'جاري التفعيل...' : 'تفعيل'}
            </button>
          </div>
        </div>
      )}

      {showBackupCodes && backupCodes.length > 0 && (
        <div className="mt-6 bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <h4 className="font-semibold text-yellow-900 mb-2">رموز الاحتياط</h4>
          <p className="text-yellow-800 text-sm mb-4">
            احفظ هذه الرموز في مكان آمن. يمكنك استخدامها للدخول إذا فقدت هاتفك.
          </p>
          <div className="grid grid-cols-2 gap-2">
            {backupCodes.map((code, index) => (
              <code key={index} className="bg-white px-2 py-1 rounded text-sm border">
                {code}
              </code>
            ))}
          </div>
          <button
            onClick={() => setShowBackupCodes(false)}
            className="mt-4 bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700"
          >
            تم الحفظ
          </button>
        </div>
      )}
    </div>
  )
}
