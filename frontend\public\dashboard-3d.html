<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم التفاعلية ثلاثية الأبعاد - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow-x: hidden;
        }

        .dashboard-container {
            position: relative;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header-3d {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: var(--space-4) var(--space-6);
            position: relative;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo-3d {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            color: white;
            font-size: 1.5rem;
            font-weight: var(--font-weight-bold);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            animation: rotate3d 4s linear infinite;
        }

        @keyframes rotate3d {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }

        .nav-3d {
            display: flex;
            gap: var(--space-4);
        }

        .nav-3d a {
            color: white;
            text-decoration: none;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all var(--transition-normal);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-3d a:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .main-3d {
            flex: 1;
            padding: var(--space-6);
            position: relative;
            z-index: 10;
        }

        .stats-grid-3d {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        .stat-card-3d {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .stat-card-3d:hover {
            transform: translateY(-10px) rotateX(5deg);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .stat-card-3d::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 300% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .stat-icon-3d {
            font-size: 3rem;
            margin-bottom: var(--space-4);
            display: block;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .stat-number-3d {
            font-size: 2.5rem;
            font-weight: var(--font-weight-bold);
            color: white;
            margin-bottom: var(--space-2);
            font-family: var(--font-english-display);
        }

        .stat-label-3d {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: var(--font-weight-medium);
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-6);
            margin-bottom: var(--space-8);
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .chart-title {
            color: white;
            font-size: 1.25rem;
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--space-4);
            text-align: center;
        }

        .canvas-container {
            position: relative;
            height: 300px;
        }

        .three-scene {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .floating-box {
            position: absolute;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, rgba(255, 107, 107, 0.3), rgba(78, 205, 196, 0.3));
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: floatAround 15s linear infinite;
        }

        @keyframes floatAround {
            0% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(100px, -50px) rotate(90deg); }
            50% { transform: translate(200px, 0) rotate(180deg); }
            75% { transform: translate(100px, 50px) rotate(270deg); }
            100% { transform: translate(0, 0) rotate(360deg); }
        }

        .quick-actions-3d {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-4);
            max-width: 1400px;
            margin: 0 auto;
        }

        .action-card-3d {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--space-5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-decoration: none;
            color: white;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .action-card-3d:hover {
            transform: translateY(-5px) scale(1.02);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .action-card-3d::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            transition: all var(--transition-normal);
            transform: translate(-50%, -50%);
            border-radius: 50%;
        }

        .action-card-3d:hover::after {
            width: 200px;
            height: 200px;
        }

        .action-icon-3d {
            font-size: 2rem;
            margin-bottom: var(--space-3);
            display: block;
        }

        .action-title-3d {
            font-size: 1.1rem;
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--space-2);
        }

        .action-desc-3d {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: var(--line-height-relaxed);
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: var(--space-3);
            }
            
            .nav-3d {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .stat-card-3d:hover {
                transform: translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Three.js Background Scene -->
        <div class="three-scene" id="threeScene"></div>
        
        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-box" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
            <div class="floating-box" style="top: 20%; right: 15%; animation-delay: 2s;"></div>
            <div class="floating-box" style="bottom: 30%; left: 20%; animation-delay: 4s;"></div>
            <div class="floating-box" style="bottom: 15%; right: 10%; animation-delay: 6s;"></div>
        </div>

        <header class="header-3d">
            <div class="header-content">
                <div class="logo-3d">
                    <div class="logo-icon">🚚</div>
                    <span>نظام إدارة الشحنات ثلاثي الأبعاد</span>
                </div>
                
                <nav class="nav-3d">
                    <a href="main-dashboard.html">لوحة التحكم الرئيسية</a>
                    <a href="shipments.html">الشحنات</a>
                    <a href="customers.html">العملاء</a>
                    <a href="index.html">تسجيل الخروج</a>
                </nav>
            </div>
        </header>

        <main class="main-3d">
            <div class="stats-grid-3d">
                <div class="stat-card-3d" onclick="animateCard(this)">
                    <span class="stat-icon-3d">📦</span>
                    <div class="stat-number-3d" id="total-count-3d">0</div>
                    <div class="stat-label-3d">إجمالي الشحنات</div>
                </div>
                
                <div class="stat-card-3d" onclick="animateCard(this)">
                    <span class="stat-icon-3d">⏳</span>
                    <div class="stat-number-3d" id="pending-count-3d">0</div>
                    <div class="stat-label-3d">الشحنات المعلقة</div>
                </div>
                
                <div class="stat-card-3d" onclick="animateCard(this)">
                    <span class="stat-icon-3d">🚛</span>
                    <div class="stat-number-3d" id="transit-count-3d">0</div>
                    <div class="stat-label-3d">في الطريق</div>
                </div>
                
                <div class="stat-card-3d" onclick="animateCard(this)">
                    <span class="stat-icon-3d">✅</span>
                    <div class="stat-number-3d" id="delivered-count-3d">0</div>
                    <div class="stat-label-3d">مسلمة</div>
                </div>
            </div>

            <div class="charts-grid">
                <div class="chart-container">
                    <h3 class="chart-title">توزيع الشحنات حسب الحالة</h3>
                    <div class="canvas-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3 class="chart-title">الشحنات الشهرية</h3>
                    <div class="canvas-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="quick-actions-3d">
                <a href="shipments.html" class="action-card-3d">
                    <span class="action-icon-3d">➕</span>
                    <div class="action-title-3d">إضافة شحنة جديدة</div>
                    <div class="action-desc-3d">إنشاء شحنة جديدة مع البحث الذكي للعملاء والمناطق</div>
                </a>
                
                <a href="customers.html" class="action-card-3d">
                    <span class="action-icon-3d">👥</span>
                    <div class="action-title-3d">إدارة العملاء</div>
                    <div class="action-desc-3d">إضافة وتعديل بيانات العملاء والبحث المتقدم</div>
                </a>
                
                <a href="test-features.html" class="action-card-3d">
                    <span class="action-icon-3d">🧪</span>
                    <div class="action-title-3d">اختبار المزايا</div>
                    <div class="action-desc-3d">تجربة البحث الذكي والمناطق الجغرافية</div>
                </a>
                
                <a href="font-test.html" class="action-card-3d">
                    <span class="action-icon-3d">🎨</span>
                    <div class="action-title-3d">معاينة الخطوط</div>
                    <div class="action-desc-3d">اختبار خطوط Apple الجديدة والتصميم</div>
                </a>
            </div>
        </main>
    </div>

    <script src="js/database.js"></script>
    <script>
        // Three.js Scene Setup
        let scene, camera, renderer, particles;
        
        function initThreeJS() {
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            renderer = new THREE.WebGLRenderer({ alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000000, 0);
            
            document.getElementById('threeScene').appendChild(renderer.domElement);
            
            // Create floating particles
            createParticles();
            
            camera.position.z = 5;
            animate();
        }
        
        function createParticles() {
            const geometry = new THREE.BufferGeometry();
            const particleCount = 100;
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);
            
            for (let i = 0; i < particleCount * 3; i += 3) {
                positions[i] = (Math.random() - 0.5) * 20;
                positions[i + 1] = (Math.random() - 0.5) * 20;
                positions[i + 2] = (Math.random() - 0.5) * 20;
                
                colors[i] = Math.random();
                colors[i + 1] = Math.random();
                colors[i + 2] = Math.random();
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            
            const material = new THREE.PointsMaterial({
                size: 0.1,
                vertexColors: true,
                transparent: true,
                opacity: 0.6
            });
            
            particles = new THREE.Points(geometry, material);
            scene.add(particles);
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            if (particles) {
                particles.rotation.x += 0.001;
                particles.rotation.y += 0.002;
            }
            
            renderer.render(scene, camera);
        }
        
        // Charts Setup
        function initCharts() {
            const stats = db.getShipmentStats();
            
            // Status Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['معلق', 'في الطريق', 'مسلم', 'ملغي'],
                    datasets: [{
                        data: [stats.pending, stats.inTransit, stats.delivered, stats.cancelled],
                        backgroundColor: [
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(0, 123, 255, 0.8)',
                            'rgba(40, 167, 69, 0.8)',
                            'rgba(220, 53, 69, 0.8)'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'white',
                                font: {
                                    family: 'Cairo, sans-serif'
                                }
                            }
                        }
                    }
                }
            });
            
            // Monthly Chart
            const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
            new Chart(monthlyCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'الشحنات',
                        data: [12, 19, 15, 25, 22, 30],
                        borderColor: 'rgba(78, 205, 196, 1)',
                        backgroundColor: 'rgba(78, 205, 196, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'white',
                                font: {
                                    family: 'Cairo, sans-serif'
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }
        
        // Load Dashboard Data
        function loadDashboardData() {
            if (typeof db !== 'undefined') {
                const stats = db.getShipmentStats();
                
                // Animate numbers
                animateNumber('total-count-3d', stats.total);
                animateNumber('pending-count-3d', stats.pending);
                animateNumber('transit-count-3d', stats.inTransit);
                animateNumber('delivered-count-3d', stats.delivered);
            }
        }
        
        // Animate numbers
        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            let currentValue = 0;
            const increment = targetValue / 50;
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentValue);
            }, 30);
        }
        
        // Animate card on click
        function animateCard(card) {
            card.style.transform = 'translateY(-20px) rotateX(10deg) scale(1.05)';
            setTimeout(() => {
                card.style.transform = '';
            }, 300);
        }
        
        // Handle window resize
        function onWindowResize() {
            if (camera && renderer) {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
        }
        
        // Initialize everything
        document.addEventListener('DOMContentLoaded', function() {
            initThreeJS();
            loadDashboardData();
            
            // Delay chart initialization to ensure canvas is ready
            setTimeout(initCharts, 500);
        });
        
        window.addEventListener('resize', onWindowResize);
    </script>
</body>
</html>
