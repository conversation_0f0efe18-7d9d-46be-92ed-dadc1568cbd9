<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚚 نظام إدارة الشحنات المتطور</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .system-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.4rem;
            color: #2c3e50;
            font-weight: 700;
        }

        .system-icon {
            font-size: 2rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 16px;
            border-radius: 25px;
            border: 2px solid rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .user-profile:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .user-info h3 {
            color: #2c3e50;
            font-size: 1rem;
            margin-bottom: 2px;
        }

        .user-info p {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-settings {
            background: #17a2b8;
            color: white;
        }

        .btn-logout {
            background: #dc3545;
            color: white;
        }

        .header-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* Navigation Tabs */
        .nav-tabs {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 0 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 80px;
            left: 0;
            right: 0;
            z-index: 999;
            display: flex;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            padding: 15px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            min-width: 120px;
            text-decoration: none;
            color: #6c757d;
            border-bottom: 3px solid transparent;
        }

        .nav-tab:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .nav-tab.active {
            background: rgba(102, 126, 234, 0.15);
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .nav-tab-icon {
            font-size: 1.5rem;
        }

        .nav-tab-text {
            font-size: 0.85rem;
            font-weight: 600;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            box-shadow: -5px 0 25px rgba(0,0,0,0.2);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 1001;
            overflow-y: auto;
            scrollbar-width: thin;
        }

        .sidebar.open {
            right: 0;
        }

        .sidebar-header {
            padding: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 1.3rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-sidebar {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 1.5rem;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-sidebar:hover {
            background: rgba(255,255,255,0.3);
            transform: rotate(90deg);
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-section {
            margin-bottom: 25px;
        }

        .menu-section-title {
            padding: 10px 25px;
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 10px;
        }

        .menu-item {
            margin-bottom: 5px;
        }

        .menu-link {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px 25px;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
            width: 100%;
            cursor: pointer;
            font-size: 0.95rem;
        }

        .menu-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateX(-5px);
        }

        .menu-link span:first-child {
            font-size: 1.2rem;
            width: 25px;
            text-align: center;
        }

        .has-submenu::after {
            content: '◀';
            margin-right: auto;
            transition: transform 0.3s ease;
        }

        .has-submenu.open::after {
            transform: rotate(90deg);
        }

        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(102, 126, 234, 0.05);
        }

        .submenu.open {
            max-height: 500px;
        }

        .submenu-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 25px 10px 60px;
            color: #495057;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .submenu-link:hover {
            background: rgba(102, 126, 234, 0.15);
            color: #667eea;
            transform: translateX(-3px);
        }

        /* Main Content */
        .main-content {
            margin-top: 140px;
            padding: 30px;
            min-height: calc(100vh - 140px);
        }

        .welcome-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .welcome-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .welcome-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quick-access {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .quick-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .quick-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* Menu Toggle Button */
        .menu-toggle {
            position: fixed;
            top: 160px;
            right: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 15px 20px;
            border-radius: 25px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .menu-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
        }

        /* Overlay */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header {
                padding: 10px 15px;
                flex-direction: column;
                gap: 10px;
            }

            .header-left, .header-right {
                width: 100%;
                justify-content: space-between;
            }

            .system-title {
                font-size: 1.2rem;
            }

            .nav-tabs {
                top: 120px;
                padding: 0 15px;
            }

            .nav-tab {
                min-width: 100px;
                padding: 12px 15px;
            }

            .main-content {
                margin-top: 180px;
                padding: 20px 15px;
            }

            .welcome-section {
                padding: 25px 20px;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .welcome-subtitle {
                font-size: 1rem;
            }

            .quick-access {
                flex-direction: column;
                align-items: center;
            }

            .quick-btn {
                width: 100%;
                justify-content: center;
            }

            .sidebar {
                width: 100%;
                right: -100%;
            }

            .menu-toggle {
                top: 200px;
                right: 15px;
                padding: 12px 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-left">
            <div class="system-title">
                <span class="system-icon">🚚</span>
                <span>نظام إدارة الشحنات المتطور</span>
            </div>
        </div>
        
        <div class="header-right">
            <div class="user-profile">
                <div class="user-avatar">ع</div>
                <div class="user-info">
                    <h3>عصام</h3>
                    <p>مدير النظام</p>
                </div>
            </div>
            
            <div class="header-actions">
                <a href="main-dashboard.html" class="header-btn btn-settings" title="الإعدادات">
                    <span>⚙️</span>
                </a>
                <button class="header-btn btn-logout" onclick="logout()" title="تسجيل الخروج">
                    <span>🚪</span>
                    <span>تسجيل الخروج</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="nav-tabs">
        <a href="main-dashboard.html" class="nav-tab active">
            <span class="nav-tab-icon">📊</span>
            <span class="nav-tab-text">لوحة التحكم</span>
        </a>
        <a href="new-shipments-management.html" class="nav-tab">
            <span class="nav-tab-icon">🚛</span>
            <span class="nav-tab-text">الشحنات الجديدة</span>
        </a>
        <a href="new-customers-management.html" class="nav-tab">
            <span class="nav-tab-icon">👤</span>
            <span class="nav-tab-text">العملاء الجدد</span>
        </a>
        <a href="new-distributors-management.html" class="nav-tab">
            <span class="nav-tab-icon">🚚</span>
            <span class="nav-tab-text">المناديب الجدد</span>
        </a>
        <a href="new-cancellation-management.html" class="nav-tab">
            <span class="nav-tab-icon">🚫</span>
            <span class="nav-tab-text">أسباب الإلغاء الجديدة</span>
        </a>
        <a href="branches-management.html" class="nav-tab">
            <span class="nav-tab-icon">🏢</span>
            <span class="nav-tab-text">الفروع</span>
        </a>
        <a href="new-financial-system.html" class="nav-tab">
            <span class="nav-tab-icon">💳</span>
            <span class="nav-tab-text">النظام المالي الجديد</span>
        </a>
        <a href="new-reports-system.html" class="nav-tab">
            <span class="nav-tab-icon">📊</span>
            <span class="nav-tab-text">التقارير الجديدة</span>
        </a>
        <a href="website-pages.html" class="nav-tab">
            <span class="nav-tab-icon">🌐</span>
            <span class="nav-tab-text">صفحات الموقع</span>
        </a>
        <a href="main-dashboard.html" class="nav-tab">
            <span class="nav-tab-icon">⚙️</span>
            <span class="nav-tab-text">الإعدادات والصلاحيات</span>
        </a>
    </nav>

    <!-- Menu Toggle Button -->
    <button class="menu-toggle" onclick="toggleSidebar()">
        <span>📋</span>
        <span>القائمة الرئيسية</span>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" onclick="closeSidebar()"></div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">
                <span>📋</span>
                <span>قائمة الوظائف</span>
            </div>
            <button class="close-sidebar" onclick="closeSidebar()">✕</button>
        </div>

        <div class="sidebar-menu">
            <!-- إدارة الشحنات -->
            <div class="menu-section">
                <div class="menu-section-title">إدارة الشحنات</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>📦</span>
                        <span>الشحنات</span>
                    </button>
                    <div class="submenu">
                        <a href="shipments.html" class="submenu-link">
                            <span>➕</span>
                            <span>إضافة شحنة جديدة</span>
                        </a>
                        <a href="shipments.html" class="submenu-link">
                            <span>📦</span>
                            <span>إدارة الشحنات</span>
                        </a>
                        <a href="shipment-tracking.html" class="submenu-link">
                            <span>📍</span>
                            <span>تتبع الشحنات</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- إدارة العملاء والمناديب -->
            <div class="menu-section">
                <div class="menu-section-title">إدارة العملاء والمناديب</div>
                <div class="menu-item">
                    <a href="customers.html" class="menu-link">
                        <span>👤</span>
                        <span>إدارة العملاء</span>
                    </a>
                </div>
                <div class="menu-item">
                    <a href="distributors-management.html" class="menu-link">
                        <span>👥</span>
                        <span>إدارة المناديب</span>
                    </a>
                </div>
            </div>

            <!-- إدارة الفروع -->
            <div class="menu-section">
                <div class="menu-section-title">إدارة الفروع</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🏢</span>
                        <span>الفروع والتحويلات</span>
                    </button>
                    <div class="submenu">
                        <a href="branches-management.html" class="submenu-link">
                            <span>🏢</span>
                            <span>إدارة الفروع</span>
                        </a>
                        <a href="branch-transfers.html" class="submenu-link">
                            <span>🔄</span>
                            <span>تحويلات بين الفروع</span>
                        </a>
                        <a href="branch-reports.html" class="submenu-link">
                            <span>📊</span>
                            <span>تقارير الفروع</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- إدارة الإلغاءات -->
            <div class="menu-section">
                <div class="menu-section-title">إدارة الإلغاءات</div>
                <div class="menu-item">
                    <a href="cancellation-management.html" class="menu-link">
                        <span>❌</span>
                        <span>أسباب الإلغاء</span>
                    </a>
                </div>
            </div>

            <!-- الأنظمة المالية -->
            <div class="menu-section">
                <div class="menu-section-title">الأنظمة المالية</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>💰</span>
                        <span>النظام المالي</span>
                    </button>
                    <div class="submenu">
                        <a href="financial-system.html" class="submenu-link">
                            <span>💳</span>
                            <span>النظام المالي</span>
                        </a>
                        <a href="pricing-management.html" class="submenu-link">
                            <span>💰</span>
                            <span>إدارة التسعير</span>
                        </a>
                        <a href="currency-converter.html" class="submenu-link">
                            <span>💱</span>
                            <span>محول العملات</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- إدارة الموارد -->
            <div class="menu-section">
                <div class="menu-section-title">إدارة الموارد</div>
                <div class="menu-item">
                    <a href="user-management.html" class="menu-link">
                        <span>👥</span>
                        <span>إدارة المستخدمين</span>
                    </a>
                </div>
                <div class="menu-item">
                    <a href="hr-management.html" class="menu-link">
                        <span>👨‍💼</span>
                        <span>إدارة الموارد البشرية</span>
                    </a>
                </div>
                <div class="menu-item">
                    <a href="vehicle-management.html" class="menu-link">
                        <span>🚗</span>
                        <span>إدارة السيارات</span>
                    </a>
                </div>
            </div>

            <!-- لوحات التحكم المتقدمة -->
            <div class="menu-section">
                <div class="menu-section-title">لوحات التحكم المتقدمة</div>
                <div class="menu-item">
                    <a href="3d-dashboard.html" class="menu-link">
                        <span>🎮</span>
                        <span>لوحة التحكم ثلاثية الأبعاد</span>
                    </a>
                </div>
            </div>

            <!-- الصلاحيات والأمان -->
            <div class="menu-section">
                <div class="menu-section-title">الصلاحيات والأمان</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🔐</span>
                        <span>إدارة الصلاحيات</span>
                    </button>
                    <div class="submenu">
                        <a href="user-permissions-advanced.html" class="submenu-link">
                            <span>🔐</span>
                            <span>الصلاحيات المتقدمة</span>
                        </a>
                        <a href="permissions-matrix.html" class="submenu-link">
                            <span>📊</span>
                            <span>مصفوفة الصلاحيات</span>
                        </a>
                        <a href="test-permissions.html" class="submenu-link">
                            <span>🧪</span>
                            <span>اختبار الصلاحيات</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- أدوات الاختبار -->
            <div class="menu-section">
                <div class="menu-section-title">أدوات الاختبار</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🧪</span>
                        <span>أدوات الاختبار</span>
                    </button>
                    <div class="submenu">
                        <a href="test-shipments.html" class="submenu-link">
                            <span>📦</span>
                            <span>اختبار الشحنات</span>
                        </a>
                        <a href="test-customers.html" class="submenu-link">
                            <span>👤</span>
                            <span>اختبار العملاء</span>
                        </a>
                        <a href="test-system.html" class="submenu-link">
                            <span>⚙️</span>
                            <span>اختبار النظام</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- المساعدة والدعم -->
            <div class="menu-section">
                <div class="menu-section-title">المساعدة والدعم</div>
                <div class="menu-item">
                    <a href="dashboard-guide.html" class="menu-link">
                        <span>📖</span>
                        <span>دليل لوحة التحكم</span>
                    </a>
                </div>
            </div>

            <!-- أدوات الإصلاح -->
            <div class="menu-section">
                <div class="menu-section-title">أدوات الإصلاح</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🔧</span>
                        <span>أدوات الإصلاح</span>
                    </button>
                    <div class="submenu">
                        <a href="repair-database.html" class="submenu-link">
                            <span>🗄️</span>
                            <span>إصلاح قاعدة البيانات</span>
                        </a>
                        <a href="clear-cache.html" class="submenu-link">
                            <span>🧹</span>
                            <span>مسح الذاكرة المؤقتة</span>
                        </a>
                        <a href="system-backup.html" class="submenu-link">
                            <span>💾</span>
                            <span>نسخ احتياطي</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- صفحات الموقع -->
            <div class="menu-section">
                <div class="menu-section-title">صفحات الموقع</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🌐</span>
                        <span>الصفحات الرئيسية</span>
                    </button>
                    <div class="submenu">
                        <a href="index.html" class="submenu-link">
                            <span>🏠</span>
                            <span>الصفحة الرئيسية</span>
                        </a>
                        <a href="about.html" class="submenu-link">
                            <span>ℹ️</span>
                            <span>من نحن</span>
                        </a>
                        <a href="services.html" class="submenu-link">
                            <span>🛎️</span>
                            <span>خدماتنا</span>
                        </a>
                        <a href="contact.html" class="submenu-link">
                            <span>📞</span>
                            <span>اتصل بنا</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- الإعدادات المتقدمة -->
            <div class="menu-section">
                <div class="menu-section-title">الإعدادات المتقدمة</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>⚙️</span>
                        <span>إدارة الصلاحيات</span>
                    </button>
                    <div class="submenu">
                        <a href="advanced-settings.html" class="submenu-link">
                            <span>🔐</span>
                            <span>الإعدادات المتقدمة</span>
                        </a>
                        <a href="user-permissions-advanced.html" class="submenu-link">
                            <span>👥</span>
                            <span>صلاحيات المستخدمين</span>
                        </a>
                        <a href="permissions-matrix.html" class="submenu-link">
                            <span>📊</span>
                            <span>مصفوفة الصلاحيات</span>
                        </a>
                    </div>
                </div>
                <div class="menu-item">
                    <a href="hr-management.html" class="menu-link">
                        <span>🏢</span>
                        <span>إدارة الموارد البشرية</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="welcome-section">
            <div class="welcome-icon">🚚</div>
            <h1 class="welcome-title">مرحباً بك في نظام إدارة الشحنات المتطور</h1>
            <p class="welcome-subtitle">
                استخدم القائمة الجانبية للوصول إلى جميع وظائف النظام<br>
                <strong>📋 اضغط على أيقونة القائمة</strong><br>
                ⚡ وصول سريع لجميع الوظائف
            </p>

            <div class="quick-access">
                <a href="shipments.html" class="quick-btn">
                    <span>📦</span>
                    <span>إدارة الشحنات</span>
                </a>
                <a href="customers.html" class="quick-btn">
                    <span>👤</span>
                    <span>إدارة العملاء</span>
                </a>
                <a href="branches-management.html" class="quick-btn">
                    <span>🏢</span>
                    <span>إدارة الفروع</span>
                </a>
                <button class="quick-btn" onclick="toggleSidebar()">
                    <span>📋</span>
                    <span>فتح القائمة الكاملة</span>
                </button>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // متغيرات عامة
        let sidebarOpen = false;

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚚 تحميل نظام إدارة الشحنات المتطور...');
            initializeSystem();
        });

        // تهيئة النظام
        function initializeSystem() {
            // تطبيق خط SF Pro Arabic
            applyArabicFont();

            // إعداد الأحداث
            setupEventListeners();

            console.log('✅ تم تحميل النظام بنجاح');
        }

        // تطبيق الخط العربي
        function applyArabicFont() {
            const style = document.createElement('style');
            style.textContent = `
                * {
                    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
                    font-weight: 600 !important;
                }
            `;
            document.head.appendChild(style);
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // إغلاق القائمة عند الضغط على Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && sidebarOpen) {
                    closeSidebar();
                }
            });

            // إغلاق القائمة عند النقر على رابط
            document.addEventListener('click', function(e) {
                if (e.target.matches('.submenu-link')) {
                    setTimeout(() => {
                        closeSidebar();
                    }, 300);
                }
            });
        }

        // فتح/إغلاق القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (sidebarOpen) {
                closeSidebar();
            } else {
                openSidebar();
            }
        }

        function openSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            sidebar.classList.add('open');
            overlay.classList.add('show');
            sidebarOpen = true;

            // منع التمرير في الخلفية
            document.body.style.overflow = 'hidden';
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('show');
            sidebarOpen = false;

            // السماح بالتمرير مرة أخرى
            document.body.style.overflow = '';
        }

        // فتح/إغلاق القوائم الفرعية
        function toggleSubmenu(button) {
            const submenu = button.nextElementSibling;
            const isOpen = submenu.classList.contains('open');

            // إغلاق جميع القوائم الفرعية الأخرى
            document.querySelectorAll('.submenu.open').forEach(menu => {
                if (menu !== submenu) {
                    menu.classList.remove('open');
                    menu.previousElementSibling.classList.remove('open');
                }
            });

            // فتح/إغلاق القائمة الحالية
            if (isOpen) {
                submenu.classList.remove('open');
                button.classList.remove('open');
            } else {
                submenu.classList.add('open');
                button.classList.add('open');
            }
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // إظهار رسالة تحميل
                const loadingDiv = document.createElement('div');
                loadingDiv.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 9999;
                    color: white;
                    font-size: 1.5rem;
                    font-weight: 600;
                `;
                loadingDiv.innerHTML = `
                    <div style="text-align: center;">
                        <div style="font-size: 3rem; margin-bottom: 20px;">🚪</div>
                        <div>جاري تسجيل الخروج...</div>
                    </div>
                `;
                document.body.appendChild(loadingDiv);

                // محاكاة عملية تسجيل الخروج
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        }

        // وظائف مساعدة للتنقل
        function navigateTo(page) {
            window.location.href = page;
        }

        // تحديث التبويب النشط
        function setActiveTab(tabElement) {
            // إزالة الحالة النشطة من جميع التبويبات
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // إضافة الحالة النشطة للتبويب المحدد
            tabElement.classList.add('active');
        }

        // إضافة تأثيرات تفاعلية للتبويبات
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                setActiveTab(this);
            });
        });

        // تأثيرات تحميل الصفحة
        window.addEventListener('load', function() {
            // إضافة تأثير تدرجي للعناصر
            const elements = document.querySelectorAll('.welcome-section, .nav-tabs, .header');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                element.style.transition = 'all 0.6s ease';

                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });

        // جعل الوظائف متاحة عالمياً
        window.toggleSidebar = toggleSidebar;
        window.closeSidebar = closeSidebar;
        window.toggleSubmenu = toggleSubmenu;
        window.logout = logout;
        window.navigateTo = navigateTo;

        console.log('🚚 تم تحميل نظام إدارة الشحنات المتطور بنجاح!');
    </script>
</body>
</html>
