<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الشحنات - الرئيسية</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
            font-weight: 600;
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Header Styles */
        .main-header {
            background: linear-gradient(135deg, var(--color-primary) 0%, #764ba2 100%);
            color: white;
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: 1.5rem;
            font-weight: var(--font-weight-bold);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 15px;
            border-radius: 12px;
            background: rgba(255,255,255,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .user-profile:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            border-color: rgba(255,255,255,0.6);
            transform: scale(1.05);
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .user-name {
            font-weight: 600;
            font-size: 1rem;
            color: white;
        }

        .user-role {
            font-size: 0.8rem;
            color: rgba(255,255,255,0.8);
            font-weight: 500;
        }

        .user-actions {
            display: flex;
            align-items: center;
        }

        .edit-profile-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .edit-profile-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        /* رسوم متحركة للرسائل */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* تحسينات للنماذج */
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: var(--font-weight-bold);
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: var(--space-2) var(--space-4);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-lg);
            text-decoration: none;
            transition: all var(--transition-fast);
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* Navigation Tabs */
        .nav-tabs {
            padding: 0 var(--space-6);
        }

        .tabs-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            gap: var(--space-2);
        }

        .tab-item {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-5);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            transition: all var(--transition-fast);
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            margin-bottom: -1px;
        }

        .tab-item:hover {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            transform: translateY(-2px);
        }

        .tab-item.active {
            background: var(--bg-primary);
            color: var(--text-primary);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .tab-icon {
            font-size: 1.1rem;
        }

        /* Quick Actions Bar */
        .quick-actions-bar {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-light);
            padding: var(--space-4) var(--space-6);
            box-shadow: var(--shadow-sm);
        }

        .actions-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            gap: var(--space-3);
            align-items: center;
            flex-wrap: wrap;
        }

        .quick-action {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-4);
            background: var(--color-primary);
            color: white;
            text-decoration: none;
            border-radius: var(--radius-lg);
            font-weight: var(--font-weight-medium);
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .quick-action.secondary {
            background: var(--color-secondary);
        }

        .quick-action.warning {
            background: var(--color-warning);
        }

        .quick-action.info {
            background: #17a2b8;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: -350px;
            width: 350px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transition: right 0.3s ease;
            z-index: 10000;
            overflow-y: auto;
            box-shadow: -5px 0 15px rgba(0,0,0,0.3);
        }

        .sidebar.open {
            right: 0;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .sidebar-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .sidebar-close:hover {
            background: rgba(255,255,255,0.1);
        }

        .sidebar-menu {
            padding: 10px 0;
        }

        .menu-section {
            margin-bottom: 10px;
        }

        .menu-section-title {
            padding: 15px 20px 10px;
            font-size: 0.9rem;
            font-weight: 600;
            color: rgba(255,255,255,0.8);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .menu-item {
            position: relative;
        }

        .menu-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            cursor: pointer;
        }

        .menu-link:hover {
            background: rgba(255,255,255,0.1);
            padding-right: 25px;
        }

        .menu-link.has-submenu::after {
            content: '◀';
            margin-right: auto;
            transition: transform 0.3s;
        }

        .menu-link.has-submenu.open::after {
            transform: rotate(90deg);
        }

        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0,0,0,0.1);
        }

        .submenu.open {
            max-height: 500px;
        }

        .submenu-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px 10px 40px;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s;
            font-size: 0.9rem;
        }

        .submenu-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            padding-right: 45px;
        }

        .sidebar-toggle {
            position: fixed;
            top: 300px;
            right: 30px;
            background: white;
            color: #333;
            border: 2px solid #667eea;
            padding: 15px 20px;
            border-radius: 15px;
            cursor: pointer;
            z-index: 9999;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            transition: background 0.3s, color 0.3s, border-color 0.3s;
            font-weight: bold;
            max-width: 200px;
            display: flex !important;
            align-items: center;
            justify-content: center;
            visibility: visible !important;
            opacity: 1 !important;
            text-align: center;
            line-height: 1.4;
        }

        .sidebar-toggle:hover {
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);
            background: #667eea;
            border-color: #667eea;
            color: white;
        }

        .sidebar-toggle:active {
            transform: scale(0.98);
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* تأثيرات القائمة المثبتة */
        .fixed-menu-toggle {
            position: fixed !important;
            top: 20px !important;
            right: 30px !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border: 2px solid rgba(255,255,255,0.3) !important;
            padding: 12px 18px !important;
            border-radius: 25px !important;
            cursor: pointer !important;
            z-index: 9999 !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
            transition: all 0.3s ease !important;
            font-weight: bold !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            max-width: 180px !important;
            text-align: center !important;
        }

        .fixed-menu-toggle:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6) !important;
            border-color: rgba(255,255,255,0.5) !important;
        }

        .fixed-menu-toggle:active {
            transform: translateY(0) !important;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
        }

        /* Main Content */
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--space-6);
            transition: margin-right 0.3s ease;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                right: -100%;
            }

            .sidebar-toggle {
                top: 250px;
                right: 10px;
                padding: 10px 15px;
                font-size: 0.85rem;
                max-width: 150px;
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                text-align: center;
                line-height: 1.3;
            }

            .main-content {
                padding: var(--space-4);
            }

            .menu-section-title {
                font-size: 0.8rem;
                padding: 12px 15px 8px;
            }

            .menu-link {
                padding: 10px 15px;
                font-size: 0.9rem;
            }

            .submenu-link {
                padding: 8px 15px 8px 30px;
                font-size: 0.85rem;
            }
        }

        /* تحسينات إضافية للقائمة الجانبية */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        /* تأثيرات إضافية */
        .menu-link:active {
            transform: scale(0.98);
        }

        .submenu-link:active {
            transform: scale(0.98);
        }

        .sidebar-toggle:active {
            transform: scale(0.95);
        }

        .content-frame {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            min-height: 70vh;
            overflow: hidden;
        }

        .frame-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: var(--space-5) var(--space-6);
            border-bottom: 1px solid var(--border-light);
        }

        .frame-title {
            font-size: 1.5rem;
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
        }

        .frame-content {
            padding: var(--space-6);
        }

        /* Dashboard Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-5);
            margin-bottom: var(--space-8);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid var(--border-light);
            border-radius: 16px;
            padding: var(--space-5);
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .stat-header {
            display: flex;
            justify-content: center;
            margin-bottom: var(--space-3);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-bottom: var(--space-3);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .stat-label {
            font-size: 1rem;
            color: var(--text-secondary);
            margin-bottom: var(--space-2);
        }

        .stat-change {
            font-size: 0.85rem;
            font-weight: 500;
        }



        .stat-card {
            background: linear-gradient(135deg, var(--bg-primary) 0%, #f8f9fa 100%);
            border-radius: var(--radius-xl);
            padding: var(--space-5);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-light);
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-3);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.primary {
            background: linear-gradient(135deg, var(--color-primary) 0%, #0056b3 100%);
        }

        .stat-icon.warning {
            background: linear-gradient(135deg, var(--color-warning) 0%, #e6850e 100%);
        }

        .stat-icon.success {
            background: linear-gradient(135deg, var(--color-secondary) 0%, #1e7e34 100%);
        }

        .stat-icon.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .stat-icon.info {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            font-family: var(--font-english-display);
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
            margin-top: var(--space-2);
        }

        .stat-change {
            font-size: 0.8rem;
            font-weight: var(--font-weight-medium);
            margin-top: var(--space-1);
        }

        .stat-change.positive {
            color: var(--color-secondary);
        }

        .stat-change.negative {
            color: var(--color-error);
        }

        .stat-change.neutral {
            color: #6c757d;
        }



        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: var(--space-3);
            }

            .tabs-container {
                flex-wrap: wrap;
                justify-content: center;
            }

            .actions-container {
                justify-content: center;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .welcome-actions {
                flex-direction: column;
                align-items: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Main Header -->
    <header class="main-header">
        <div class="header-top">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">🚚</div>
                    <span>نظام إدارة الشحنات المتطور</span>
                </div>

                <div class="user-info">
                    <div class="user-profile" onclick="openUserProfileModal()">
                        <div class="user-avatar" id="userAvatar">
                            <img id="userProfileImage" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0xMiAxMkM5Ljc5IDEyIDggMTAuMjEgOCA4UzkuNzkgNCA4IDRTMTIgNS43OSAxMiA4UzEwLjIxIDEyIDEyIDEyWk0xMiAxNEM5LjMzIDE0IDQgMTUuMzQgNCAyMFYyMkgyMFYyMEMxNiAxNS4zNCAxNC42NyAxNCAxMiAxNFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K" alt="صورة المدير" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">
                        </div>
                        <div class="user-details">
                            <span class="user-name" id="userName">مدير النظام</span>
                            <span class="user-role" id="userRole">مدير عام</span>
                        </div>
                        <div class="user-actions">
                            <button class="edit-profile-btn" title="تعديل الملف الشخصي">⚙️</button>
                        </div>
                    </div>
                    <a href="index.html" class="logout-btn">تسجيل الخروج</a>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <div class="tabs-container">
                <a href="#dashboard" class="tab-item active" onclick="showTab('dashboard', this)">
                    <span class="tab-icon">📊</span>
                    <span>لوحة التحكم</span>
                </a>

                <a href="#branches" class="tab-item" onclick="showTab('branches', this)">
                    <span class="tab-icon">🏢</span>
                    <span>الفروع</span>
                </a>

                <a href="#website" class="tab-item" onclick="showTab('website', this)">
                    <span class="tab-icon">🌐</span>
                    <span>صفحات الموقع</span>
                </a>
                <a href="#settings" class="tab-item" onclick="showTab('settings', this)">
                    <span class="tab-icon">⚙️</span>
                    <span>الإعدادات والصلاحيات</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Sidebar Toggle Text - Fixed at Top -->
    <div class="fixed-menu-toggle" onclick="toggleSidebar()" title="قائمة الوظائف">
        <span style="font-size: 0.9rem; line-height: 1.3;">📋 القائمة الرئيسية</span>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" onclick="closeSidebar()"></div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">📋 قائمة الوظائف</div>
            <button class="sidebar-close" onclick="closeSidebar()">✕</button>
        </div>

        <div class="sidebar-menu">


            <!-- إدارة الفروع والتحويلات -->
            <div class="menu-section">
                <div class="menu-section-title">إدارة الفروع</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🏢</span>
                        <span>الفروع والتحويلات</span>
                    </button>
                    <div class="submenu">
                        <a href="branches-management.html" class="submenu-link">
                            <span>🏢</span>
                            <span>إدارة الفروع</span>
                        </a>
                        <a href="branch-transfers.html" class="submenu-link">
                            <span>🔄</span>
                            <span>التحويلات</span>
                        </a>
                    </div>
                </div>

            </div>



            <!-- إدارة الموارد -->
            <div class="menu-section">
                <div class="menu-section-title">إدارة الموارد</div>
                <div class="menu-item">
                    <a href="user-management.html" class="menu-link">
                        <span>👥</span>
                        <span>إدارة المستخدمين</span>
                    </a>
                </div>
                <div class="menu-item">
                    <a href="hr-management.html" class="menu-link">
                        <span>👨‍💼</span>
                        <span>إدارة الموارد البشرية</span>
                    </a>
                </div>
                <div class="menu-item">
                    <a href="vehicle-management.html" class="menu-link">
                        <span>🚗</span>
                        <span>إدارة السيارات</span>
                    </a>
                </div>
            </div>

            <!-- لوحات التحكم المتقدمة -->
            <div class="menu-section">
                <div class="menu-section-title">لوحات التحكم المتقدمة</div>
                <div class="menu-item">
                    <a href="dashboard-3d.html" class="menu-link">
                        <span>🎮</span>
                        <span>لوحة التحكم ثلاثية الأبعاد</span>
                    </a>
                </div>
            </div>

            <!-- الصلاحيات والأمان -->
            <div class="menu-section">
                <div class="menu-section-title">الصلاحيات والأمان</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🔐</span>
                        <span>إدارة الصلاحيات</span>
                    </button>
                    <div class="submenu">
                        <a href="user-permissions-advanced.html" class="submenu-link">
                            <span>🔐</span>
                            <span>الصلاحيات المتقدمة</span>
                        </a>
                        <a href="permissions-matrix.html" class="submenu-link">
                            <span>📊</span>
                            <span>مصفوفة الصلاحيات</span>
                        </a>
                        <a href="test-permissions.html" class="submenu-link">
                            <span>🧪</span>
                            <span>اختبار الصلاحيات</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- أدوات الاختبار -->
            <div class="menu-section">
                <div class="menu-section-title">أدوات الاختبار</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🧪</span>
                        <span>أدوات الاختبار</span>
                    </button>
                    <div class="submenu">
                        <a href="test-print.html" class="submenu-link">
                            <span>🖨️</span>
                            <span>اختبار الطباعة</span>
                        </a>
                        <a href="test-kuwait.html" class="submenu-link">
                            <span>🇰🇼</span>
                            <span>اختبار مناطق الكويت</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- المساعدة والدعم -->
            <div class="menu-section">
                <div class="menu-section-title">المساعدة والدعم</div>
                <div class="menu-item">
                    <a href="dashboard-guide.html" class="menu-link">
                        <span>📖</span>
                        <span>دليل لوحة التحكم</span>
                    </a>
                </div>
            </div>

            <!-- أدوات الإصلاح -->
            <div class="menu-section">
                <div class="menu-section-title">أدوات الإصلاح</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🔧</span>
                        <span>أدوات الإصلاح</span>
                    </button>
                    <div class="submenu">
                        <a href="fix-database.html" class="submenu-link">
                            <span>🔧</span>
                            <span>إصلاح قاعدة البيانات</span>
                        </a>
                        <a href="fix-all-database-issues.html" class="submenu-link">
                            <span>🚨</span>
                            <span>إصلاح شامل</span>
                        </a>
                        <a href="fix-print-issue.html" class="submenu-link">
                            <span>🖨️</span>
                            <span>إصلاح الطباعة</span>
                        </a>
                        <a href="fix-broken-links.html" class="submenu-link">
                            <span>🔗</span>
                            <span>إصلاح الروابط</span>
                        </a>
                        <a href="fix-dashboard-issue.html" class="submenu-link">
                            <span>🏠</span>
                            <span>إصلاح لوحة التحكم</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- صفحات الموقع -->
            <div class="menu-section">
                <div class="menu-section-title">صفحات الموقع</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🌐</span>
                        <span>الصفحات الرئيسية</span>
                    </button>
                    <div class="submenu">
                        <a href="home.html" class="submenu-link">
                            <span>🌐</span>
                            <span>الصفحة الرئيسية</span>
                        </a>
                        <a href="about-us.html" class="submenu-link">
                            <span>📖</span>
                            <span>صفحة من نحن</span>
                        </a>
                        <a href="success-partners.html" class="submenu-link">
                            <span>🤝</span>
                            <span>شركاء النجاح</span>
                        </a>
                        <a href="pages-management.html" class="submenu-link">
                            <span>📄</span>
                            <span>إدارة الصفحات</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- الإعدادات المتقدمة -->
            <div class="menu-section">
                <div class="menu-section-title">الإعدادات المتقدمة</div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>⚙️</span>
                        <span>إدارة الصلاحيات</span>
                    </button>
                    <div class="submenu">
                        <a href="advanced-settings.html" class="submenu-link">
                            <span>🔐</span>
                            <span>الإعدادات المتقدمة</span>
                        </a>
                        <a href="user-permissions-advanced.html" class="submenu-link">
                            <span>👥</span>
                            <span>صلاحيات المستخدمين</span>
                        </a>
                        <a href="permissions-matrix.html" class="submenu-link">
                            <span>📊</span>
                            <span>مصفوفة الصلاحيات</span>
                        </a>
                        <a href="test-advanced-permissions.html" class="submenu-link">
                            <span>🧪</span>
                            <span>اختبار نظام الصلاحيات</span>
                        </a>
                    </div>
                </div>
                <div class="menu-item">
                    <button class="menu-link has-submenu" onclick="toggleSubmenu(this)">
                        <span>🏢</span>
                        <span>إدارة الموارد البشرية</span>
                    </button>
                    <div class="submenu">
                        <a href="hr-management.html" class="submenu-link">
                            <span>👨‍💼</span>
                            <span>إدارة الموظفين</span>
                        </a>
                        <a href="distributors-management.html" class="submenu-link">
                            <span>🚚</span>
                            <span>إدارة المناديب</span>
                        </a>
                        <a href="vehicle-management.html" class="submenu-link">
                            <span>🚗</span>
                            <span>إدارة السائقين</span>
                        </a>
                        <a href="branches-management.html" class="submenu-link">
                            <span>🏢</span>
                            <span>إدارة الفروع</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Welcome Section -->
    <div class="welcome-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 20px; text-align: center; margin: 20px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
        <h1 style="font-size: 2.5rem; margin-bottom: 15px; font-weight: 700;">🚚 مرحباً بك في نظام إدارة الشحنات المتطور</h1>
        <p style="font-size: 1.2rem; margin-bottom: 25px; opacity: 0.9;">استخدم القائمة الجانبية للوصول إلى جميع وظائف النظام</p>
        <div style="display: flex; justify-content: center; align-items: center; gap: 15px; flex-wrap: wrap;">
            <div style="background: rgba(255,255,255,0.2); padding: 15px 25px; border-radius: 15px; backdrop-filter: blur(10px);">
                <span style="font-size: 1.5rem;">📋</span>
                <span style="margin-right: 10px; font-weight: 600;">اضغط على أيقونة القائمة</span>
            </div>
            <div style="background: rgba(255,255,255,0.2); padding: 15px 25px; border-radius: 15px; backdrop-filter: blur(10px);">
                <span style="font-size: 1.5rem;">⚡</span>
                <span style="margin-right: 10px; font-weight: 600;">وصول سريع لجميع الوظائف</span>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-frame">
            <!-- Dashboard Content -->
            <div id="dashboard-content" class="tab-content">
                <div class="frame-header">
                    <h1 class="frame-title">📊 لوحة التحكم الرئيسية</h1>
                </div>
                <div class="frame-content">
                    <!-- Stats Grid -->
                    <div class="stats-grid">
                        <div class="stat-card" onclick="navigateTo('branches-management.html')">
                            <div class="stat-header">
                                <div class="stat-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">🏢</div>
                            </div>
                            <div class="stat-number" id="active-branches">0</div>
                            <div class="stat-label">الفروع النشطة</div>
                            <div class="stat-change positive" id="branches-change">متاحة للخدمة</div>
                        </div>

                        <div class="stat-card" onclick="navigateTo('branch-transfers.html')">
                            <div class="stat-header">
                                <div class="stat-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);">🔄</div>
                            </div>
                            <div class="stat-number" id="pending-transfers">0</div>
                            <div class="stat-label">تحويلات معلقة</div>
                            <div class="stat-change neutral" id="transfers-change">بين الفروع</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other Tab Contents (will be loaded dynamically) -->

            <div id="branches-content" class="tab-content" style="display: none;">
                <div class="frame-header">
                    <h1 class="frame-title">🏢 إدارة الفروع والتحويلات</h1>
                </div>
                <div class="frame-content" style="padding: 20px;">
                    <!-- إحصائيات الفروع -->
                    <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <div class="stat-card" style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div class="stat-header" style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                                <div class="stat-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">🏢</div>
                                <div>
                                    <div class="stat-number" style="font-size: 2rem; font-weight: 700; color: #333;" id="total-branches-display">3</div>
                                    <div class="stat-label" style="color: #666; font-size: 0.9rem;">إجمالي الفروع</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card" style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div class="stat-header" style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                                <div class="stat-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">✅</div>
                                <div>
                                    <div class="stat-number" style="font-size: 2rem; font-weight: 700; color: #333;" id="active-branches-display">3</div>
                                    <div class="stat-label" style="color: #666; font-size: 0.9rem;">فروع نشطة</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card" style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div class="stat-header" style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                                <div class="stat-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">🔄</div>
                                <div>
                                    <div class="stat-number" style="font-size: 2rem; font-weight: 700; color: #333;" id="pending-transfers-display">0</div>
                                    <div class="stat-label" style="color: #666; font-size: 0.9rem;">تحويلات معلقة</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات السريعة -->
                    <div class="quick-actions" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
                        <button class="action-btn" onclick="showAddBranchModal()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s;">
                            <i style="margin-left: 8px;">➕</i>
                            إضافة فرع جديد
                        </button>

                        <button class="action-btn" onclick="showTransferModal()" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s;">
                            <i style="margin-left: 8px;">🔄</i>
                            تحويل بين الفروع
                        </button>

                        <button class="action-btn" onclick="generateBranchReport()" style="background: linear-gradient(135deg, #6f42c1 0%, #6610f2 100%); color: white; border: none; padding: 15px 20px; border-radius: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s;">
                            <i style="margin-left: 8px;">📊</i>
                            تقرير الفروع
                        </button>
                    </div>

                    <!-- قائمة الفروع -->
                    <div class="branches-section" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                            <span style="color: #28a745;">🏢</span>
                            قائمة الفروع
                        </h3>

                        <div id="branches-list" style="display: grid; gap: 15px;">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- قائمة التحويلات الأخيرة -->
                    <div class="transfers-section" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-top: 20px;">
                        <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                            <span style="color: #17a2b8;">🔄</span>
                            التحويلات الأخيرة
                        </h3>

                        <div id="transfers-list" style="display: grid; gap: 15px;">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>



            <div id="website-content" class="tab-content" style="display: none;">
                <div class="frame-header">
                    <h1 class="frame-title">🌐 إدارة صفحات الموقع</h1>
                    <p style="color: #666; margin-top: 10px;">إدارة وتعديل الصفحات الرئيسية للموقع</p>
                </div>
                <div class="frame-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <!-- الصفحة الرئيسية -->
                        <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h3 style="color: #667eea; margin-bottom: 15px;">🏠 الصفحة الرئيسية</h3>
                            <p style="color: #666; margin-bottom: 15px;">الصفحة الرئيسية للموقع مع قسم التتبع والخدمات</p>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                <a href="home.html" target="_blank" style="background: #667eea; color: white; padding: 8px 15px; border-radius: 5px; text-decoration: none; font-size: 0.9rem;">👁️ عرض</a>
                                <button onclick="editPage('home.html')" style="background: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9rem;">✏️ تعديل</button>
                                <button onclick="copyPage('home.html')" style="background: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9rem;">📋 نسخ</button>
                            </div>
                        </div>

                        <!-- صفحة من نحن -->
                        <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h3 style="color: #764ba2; margin-bottom: 15px;">📖 صفحة من نحن</h3>
                            <p style="color: #666; margin-bottom: 15px;">قصة الشركة وقيمها وفريق العمل</p>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                <a href="about-us.html" target="_blank" style="background: #764ba2; color: white; padding: 8px 15px; border-radius: 5px; text-decoration: none; font-size: 0.9rem;">👁️ عرض</a>
                                <button onclick="editPage('about-us.html')" style="background: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9rem;">✏️ تعديل</button>
                                <button onclick="copyPage('about-us.html')" style="background: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9rem;">📋 نسخ</button>
                            </div>
                        </div>

                        <!-- صفحة شركاء النجاح -->
                        <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h3 style="color: #28a745; margin-bottom: 15px;">🤝 شركاء النجاح</h3>
                            <p style="color: #666; margin-bottom: 15px;">الشركاء الاستراتيجيون وقصص النجاح</p>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                <a href="success-partners.html" target="_blank" style="background: #28a745; color: white; padding: 8px 15px; border-radius: 5px; text-decoration: none; font-size: 0.9rem;">👁️ عرض</a>
                                <button onclick="editPage('success-partners.html')" style="background: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9rem;">✏️ تعديل</button>
                                <button onclick="copyPage('success-partners.html')" style="background: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9rem;">📋 نسخ</button>
                            </div>
                        </div>
                    </div>

                    <!-- أدوات إضافية -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h3 style="color: #333; margin-bottom: 15px;">🛠️ أدوات إضافية</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <a href="page-editor.html" target="_blank" style="background: #667eea; color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; text-align: center; display: block;">🛠️ محرر الصفحات</a>
                            <button onclick="createNewPage()" style="background: #6f42c1; color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer;">➕ إنشاء صفحة جديدة</button>
                            <button onclick="backupPages()" style="background: #fd7e14; color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer;">💾 نسخ احتياطي</button>
                            <button onclick="restorePages()" style="background: #20c997; color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer;">🔄 استعادة</button>
                            <button onclick="previewSite()" style="background: #e83e8c; color: white; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer;">🌐 معاينة الموقع</button>
                        </div>
                    </div>

                    <!-- إحصائيات الصفحات -->
                    <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3 style="color: #333; margin-bottom: 15px;">📊 إحصائيات الصفحات</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; text-align: center;">
                            <div style="background: #667eea; color: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold;">3</div>
                                <div style="font-size: 0.9rem;">صفحات رئيسية</div>
                            </div>
                            <div style="background: #28a745; color: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold;">100%</div>
                                <div style="font-size: 0.9rem;">متجاوبة</div>
                            </div>
                            <div style="background: #17a2b8; color: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold;">A+</div>
                                <div style="font-size: 0.9rem;">جودة التصميم</div>
                            </div>
                            <div style="background: #fd7e14; color: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold;">2024</div>
                                <div style="font-size: 0.9rem;">آخر تحديث</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="settings-content" class="tab-content" style="display: none;">
                <div class="frame-header">
                    <h1 class="frame-title">⚙️ الإعدادات والصلاحيات المتقدمة</h1>
                    <p style="margin: 10px 0; color: #6c757d;">إدارة شاملة للنظام والصلاحيات والموارد البشرية</p>
                </div>

                <!-- قائمة الإعدادات المتقدمة -->
                <div class="settings-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; padding: 20px;">

                    <!-- الإعدادات المتقدمة -->
                    <div class="settings-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border: 2px solid #e9ecef; transition: all 0.3s;">
                        <div class="settings-card-header" style="display: flex; align-items: center; margin-bottom: 15px;">
                            <span style="font-size: 32px; margin-left: 15px;">🔐</span>
                            <div>
                                <h3 style="margin: 0; color: #2c3e50; font-weight: 600;">الإعدادات المتقدمة</h3>
                                <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">إدارة الصلاحيات والأدوار</p>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <a href="advanced-settings.html" target="_blank" class="btn-primary" style="display: block; text-align: center; padding: 12px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                🔧 الإعدادات المتقدمة
                            </a>
                            <a href="test-advanced-permissions.html" target="_blank" class="btn-secondary" style="display: block; text-align: center; padding: 12px; background: #6c757d; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                🧪 اختبار الصلاحيات
                            </a>
                        </div>
                    </div>

                    <!-- صلاحيات المستخدمين -->
                    <div class="settings-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border: 2px solid #e9ecef; transition: all 0.3s;">
                        <div class="settings-card-header" style="display: flex; align-items: center; margin-bottom: 15px;">
                            <span style="font-size: 32px; margin-left: 15px;">👥</span>
                            <div>
                                <h3 style="margin: 0; color: #2c3e50; font-weight: 600;">إدارة المستخدمين</h3>
                                <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">إدارة وتعديل المستخدمين</p>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <a href="user-management.html" target="_blank" class="btn-primary" style="display: block; text-align: center; padding: 12px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                👥 قائمة المستخدمين
                            </a>
                            <a href="user-permissions-advanced.html" target="_blank" class="btn-secondary" style="display: block; text-align: center; padding: 12px; background: #17a2b8; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                🔐 صلاحيات المستخدمين
                            </a>
                        </div>
                    </div>

                    <!-- الموارد البشرية -->
                    <div class="settings-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border: 2px solid #e9ecef; transition: all 0.3s;">
                        <div class="settings-card-header" style="display: flex; align-items: center; margin-bottom: 15px;">
                            <span style="font-size: 32px; margin-left: 15px;">🏢</span>
                            <div>
                                <h3 style="margin: 0; color: #2c3e50; font-weight: 600;">الموارد البشرية</h3>
                                <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">إدارة الموظفين والمناديب والسائقين</p>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <a href="hr-management.html" target="_blank" class="btn-primary" style="display: block; text-align: center; padding: 12px; background: #fd7e14; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                👨‍💼 إدارة الموظفين
                            </a>

                            <a href="vehicle-management.html" target="_blank" class="btn-secondary" style="display: block; text-align: center; padding: 12px; background: #20c997; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                🚗 إدارة السائقين
                            </a>
                        </div>
                    </div>

                    <!-- إدارة الفروع -->
                    <div class="settings-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border: 2px solid #e9ecef; transition: all 0.3s;">
                        <div class="settings-card-header" style="display: flex; align-items: center; margin-bottom: 15px;">
                            <span style="font-size: 32px; margin-left: 15px;">🏪</span>
                            <div>
                                <h3 style="margin: 0; color: #2c3e50; font-weight: 600;">إدارة الفروع</h3>
                                <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">إدارة الفروع والتحويلات</p>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <a href="branches-management.html" target="_blank" class="btn-primary" style="display: block; text-align: center; padding: 12px; background: #dc3545; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                🏢 إدارة الفروع
                            </a>
                            <a href="branch-transfers.html" target="_blank" class="btn-secondary" style="display: block; text-align: center; padding: 12px; background: #e83e8c; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                🔄 تحويلات الفروع
                            </a>
                        </div>
                    </div>



                    <!-- أدوات النظام -->
                    <div class="settings-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); border: 2px solid #e9ecef; transition: all 0.3s;">
                        <div class="settings-card-header" style="display: flex; align-items: center; margin-bottom: 15px;">
                            <span style="font-size: 32px; margin-left: 15px;">🛠️</span>
                            <div>
                                <h3 style="margin: 0; color: #2c3e50; font-weight: 600;">أدوات النظام</h3>
                                <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">أدوات الصيانة والتحديث</p>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <a href="system-status.html" target="_blank" class="btn-primary" style="display: block; text-align: center; padding: 12px; background: #198754; color: white; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                📊 حالة النظام
                            </a>
                            <a href="update-all-fonts.html" target="_blank" class="btn-secondary" style="display: block; text-align: center; padding: 12px; background: #0dcaf0; color: #212529; text-decoration: none; border-radius: 8px; margin: 8px 0; font-weight: 600;">
                                🔤 إدارة الخطوط
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // إنشاء قاعدة بيانات احتياطية فوراً قبل أي شيء آخر
        console.log('🔧 تهيئة قاعدة البيانات الاحتياطية...');

        window.db = {
            getAllBranches: function() {
                try {
                    let branches = JSON.parse(localStorage.getItem('branches') || '[]');
                    if (branches.length === 0) {
                        const defaultBranches = [
                            { id: 'BR001', name: 'فرع الرياض الرئيسي', city: 'الرياض', isActive: true, createdAt: new Date().toISOString() },
                            { id: 'BR002', name: 'فرع جدة', city: 'جدة', isActive: true, createdAt: new Date().toISOString() },
                            { id: 'BR003', name: 'فرع الدمام', city: 'الدمام', isActive: true, createdAt: new Date().toISOString() }
                        ];
                        localStorage.setItem('branches', JSON.stringify(defaultBranches));
                        branches = defaultBranches;
                        console.log('✅ تم إنشاء فروع تجريبية');
                    }
                    return branches;
                } catch (error) {
                    console.error('خطأ في تحميل الفروع:', error);
                    return [
                        { id: 'BR001', name: 'فرع الرياض الرئيسي', city: 'الرياض', isActive: true },
                        { id: 'BR002', name: 'فرع جدة', city: 'جدة', isActive: true },
                        { id: 'BR003', name: 'فرع الدمام', city: 'الدمام', isActive: true }
                    ];
                }
            },

            getAllShipments: function() {
                try {
                    let shipments = JSON.parse(localStorage.getItem('shipments') || '[]');

                    // إضافة شحنات تجريبية إذا لم تكن موجودة
                    if (shipments.length === 0) {
                        const sampleShipments = [
                            {
                                id: 'SHIP001',
                                trackingNumber: 'TRK123456789',
                                senderName: 'أحمد محمد',
                                receiverName: 'فاطمة علي',
                                receiverCity: 'الرياض',
                                status: 'في الطريق',
                                cost: 45,
                                currency: 'ريال',
                                createdAt: new Date().toISOString()
                            },
                            {
                                id: 'SHIP002',
                                trackingNumber: 'TRK987654321',
                                senderName: 'سارة أحمد',
                                receiverName: 'محمد خالد',
                                receiverCity: 'جدة',
                                status: 'تم التسليم',
                                cost: 25,
                                currency: 'ريال',
                                createdAt: new Date(Date.now() - 24*60*60*1000).toISOString()
                            },
                            {
                                id: 'SHIP003',
                                trackingNumber: 'TRK555666777',
                                senderName: 'عبدالله سعد',
                                receiverName: 'نورا محمد',
                                receiverCity: 'الدمام',
                                status: 'معلق',
                                cost: 35,
                                currency: 'ريال',
                                createdAt: new Date(Date.now() - 2*24*60*60*1000).toISOString()
                            }
                        ];

                        localStorage.setItem('shipments', JSON.stringify(sampleShipments));
                        shipments = sampleShipments;
                        console.log('✅ تم إنشاء شحنات تجريبية');
                    }

                    return shipments;
                } catch (error) {
                    console.error('خطأ في تحميل الشحنات:', error);
                    return [];
                }
            },

            getAllCustomers: function() {
                try {
                    return JSON.parse(localStorage.getItem('customers') || '[]');
                } catch (error) {
                    console.error('خطأ في تحميل العملاء:', error);
                    return [];
                }
            },

            getAllBranchTransfers: function() {
                try {
                    return JSON.parse(localStorage.getItem('branchTransfers') || '[]');
                } catch (error) {
                    console.error('خطأ في تحميل تحويلات الفروع:', error);
                    return [];
                }
            },

            getAllUsers: function() {
                try {
                    return JSON.parse(localStorage.getItem('users') || '[]');
                } catch (error) {
                    console.error('خطأ في تحميل المستخدمين:', error);
                    return [];
                }
            },

            getShipmentStats: function() {
                try {
                    const shipments = this.getAllShipments();
                    return {
                        total: shipments.length,
                        pending: shipments.filter(s => s.status === 'في الطريق' || s.status === 'معلق').length,
                        delivered: shipments.filter(s => s.status === 'تم التسليم' || s.status === 'مسلم').length,
                        cancelled: shipments.filter(s => s.status === 'ملغي').length
                    };
                } catch (error) {
                    console.error('خطأ في حساب إحصائيات الشحنات:', error);
                    return { total: 0, pending: 0, delivered: 0, cancelled: 0 };
                }
            }
        };

        console.log('✅ تم إنشاء قاعدة البيانات الاحتياطية بنجاح');
    </script>

    <script>
        // انتظار تحميل قاعدة البيانات
        function waitForDatabase() {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 10; // ثانية واحدة

                const checkDatabase = () => {
                    attempts++;

                    if (typeof db !== 'undefined' && db !== null) {
                        console.log('✅ قاعدة البيانات متاحة');
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        console.log('⚠️ انتهت محاولات انتظار قاعدة البيانات، استخدام قاعدة البيانات الاحتياطية');
                        resolve(); // نستمر بقاعدة البيانات الاحتياطية
                    } else {
                        setTimeout(checkDatabase, 100);
                    }
                };

                checkDatabase();
            });
        }

        // الحصول على إحصائيات الشحنات
        function getShipmentStats() {
            try {
                // التأكد من وجود قاعدة البيانات
                if (typeof db === 'undefined' || db === null) {
                    console.warn('⚠️ قاعدة البيانات غير متاحة، استخدام البيانات المحفوظة');

                    // البحث المباشر في localStorage
                    const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                    return {
                        total: shipments.length,
                        pending: shipments.filter(s => s.status === 'في الطريق' || s.status === 'معلق').length,
                        delivered: shipments.filter(s => s.status === 'تم التسليم' || s.status === 'مسلم').length,
                        cancelled: shipments.filter(s => s.status === 'ملغي').length
                    };
                }

                // استخدام دالة قاعدة البيانات إذا كانت متاحة
                if (db.getShipmentStats) {
                    return db.getShipmentStats();
                } else {
                    // حساب الإحصائيات يدوياً
                    const shipments = db.getAllShipments() || [];
                    return {
                        total: shipments.length,
                        pending: shipments.filter(s => s.status === 'في الطريق' || s.status === 'معلق').length,
                        delivered: shipments.filter(s => s.status === 'تم التسليم' || s.status === 'مسلم').length,
                        cancelled: shipments.filter(s => s.status === 'ملغي').length
                    };
                }
            } catch (error) {
                console.error('خطأ في الحصول على إحصائيات الشحنات:', error);

                // إرجاع إحصائيات افتراضية
                return {
                    total: 0,
                    pending: 0,
                    delivered: 0,
                    cancelled: 0
                };
            }
        }

        // الحصول على جميع العملاء
        function getAllCustomers() {
            try {
                // التأكد من وجود قاعدة البيانات
                if (typeof db === 'undefined' || db === null) {
                    console.warn('⚠️ قاعدة البيانات غير متاحة، استخدام البيانات المحفوظة');

                    // البحث المباشر في localStorage
                    return JSON.parse(localStorage.getItem('customers') || '[]');
                }

                // استخدام دالة قاعدة البيانات إذا كانت متاحة
                if (db.getAllCustomers) {
                    return db.getAllCustomers();
                } else {
                    // البحث المباشر في localStorage كبديل
                    return JSON.parse(localStorage.getItem('customers') || '[]');
                }
            } catch (error) {
                console.error('خطأ في الحصول على العملاء:', error);

                // محاولة أخيرة للحصول على البيانات
                try {
                    return JSON.parse(localStorage.getItem('customers') || '[]');
                } catch (fallbackError) {
                    console.error('خطأ في البحث البديل للعملاء:', fallbackError);
                    return [];
                }
            }
        }

        // تحميل بيانات لوحة التحكم
        function loadDashboardData() {
            try {
                console.log('📊 تحميل بيانات لوحة التحكم...');

                // التأكد من وجود قاعدة البيانات
                if (typeof db === 'undefined' || db === null) {
                    console.warn('⚠️ قاعدة البيانات غير متاحة، استخدام البيانات المحفوظة');
                }

                // تحميل الإحصائيات
                const stats = getShipmentStats();
                const customers = getAllCustomers();

                // تحديث الإحصائيات في الواجهة
                updateDashboardStats(stats, customers);

                console.log('✅ تم تحميل بيانات لوحة التحكم بنجاح');

            } catch (error) {
                console.error('خطأ في تحميل بيانات لوحة التحكم:', error);

                // محاولة تحميل بيانات افتراضية
                try {
                    loadDefaultDashboardData();
                } catch (fallbackError) {
                    console.error('خطأ في تحميل البيانات الافتراضية:', fallbackError);
                    showDatabaseError();
                }
            }
        }

        // تحديث إحصائيات لوحة التحكم
        function updateDashboardStats(stats, customers) {
            try {
                const totalShipmentsEl = document.getElementById('total-shipments');
                const pendingShipmentsEl = document.getElementById('pending-shipments');
                const deliveredShipmentsEl = document.getElementById('delivered-shipments');
                const totalCustomersEl = document.getElementById('total-customers');

                if (totalShipmentsEl) totalShipmentsEl.textContent = stats.total || 0;
                if (pendingShipmentsEl) pendingShipmentsEl.textContent = stats.pending || 0;
                if (deliveredShipmentsEl) deliveredShipmentsEl.textContent = stats.delivered || 0;
                if (totalCustomersEl) totalCustomersEl.textContent = customers.length || 0;

                console.log('✅ تم تحديث إحصائيات لوحة التحكم');
            } catch (error) {
                console.error('خطأ في تحديث إحصائيات لوحة التحكم:', error);
            }
        }

        // تحميل البيانات الافتراضية
        function loadDefaultDashboardData() {
            try {
                console.log('📊 تحميل البيانات الافتراضية...');

                const defaultStats = { total: 3, pending: 1, delivered: 1, cancelled: 0 };
                const defaultCustomers = [];

                updateDashboardStats(defaultStats, defaultCustomers);

                console.log('✅ تم تحميل البيانات الافتراضية');
            } catch (error) {
                console.error('خطأ في تحميل البيانات الافتراضية:', error);
            }
        }

        // عرض خطأ قاعدة البيانات
        function showDatabaseError() {
            console.error('❌ خطأ في قاعدة البيانات');

            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #f8d7da;
                color: #721c24;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #f5c6cb;
                z-index: 1000;
                max-width: 300px;
            `;
            errorDiv.innerHTML = `
                <strong>⚠️ تحذير:</strong><br>
                حدث خطأ في تحميل قاعدة البيانات.<br>
                يتم استخدام البيانات الاحتياطية.
            `;

            document.body.appendChild(errorDiv);

            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }

        // تهيئة لوحة التحكم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📍 تحميل لوحة التحكم الرئيسية...');

            try {
                // انتظار تحميل قاعدة البيانات
                await waitForDatabase();

                // تحميل بيانات لوحة التحكم
                loadDashboardData();

                // تحميل بيانات الفروع
                loadBranchesData();

                // التأكد من تهيئة القائمة الجانبية
                initializeSidebar();

                console.log('✅ تم تحميل لوحة التحكم بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل لوحة التحكم:', error);

                // تحميل البيانات الافتراضية في حالة الخطأ
                loadDefaultDashboardData();

                // تهيئة القائمة الجانبية حتى في حالة الخطأ
                initializeSidebar();
            }
        });

        // وظائف التنقل والتبويبات
        function showTab(tabName, element) {
            // إخفاء جميع المحتويات
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.style.display = 'none');

            // إزالة الفئة النشطة من جميع التبويبات
            const tabs = document.querySelectorAll('.tab-item');
            tabs.forEach(tab => tab.classList.remove('active'));

            // إظهار المحتوى المطلوب
            const targetContent = document.getElementById(tabName + '-content');
            if (targetContent) {
                targetContent.style.display = 'block';
            }

            // إضافة الفئة النشطة للتبويب الحالي
            if (element) {
                element.classList.add('active');
            }
        }

        // وظائف التنقل
        function navigateTo(url) {
            window.location.href = url;
        }

        function openAddShipmentFromDashboard(event) {
            event.preventDefault();
            window.location.href = 'shipments.html?action=add';
        }

        // وظائف إدارة الفروع
        function showAddBranchModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 500px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; display: flex; align-items: center; gap: 10px;">
                        <span style="color: #28a745;">🏢</span>
                        إضافة فرع جديد
                    </h3>

                    <form id="addBranchForm" style="display: grid; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">اسم الفرع:</label>
                            <input type="text" id="branchName" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" placeholder="مثال: فرع الرياض الشمالي">
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">المدينة:</label>
                            <select id="branchCity" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;">
                                <option value="">اختر المدينة</option>
                                <option value="الرياض">الرياض</option>
                                <option value="جدة">جدة</option>
                                <option value="الدمام">الدمام</option>
                                <option value="مكة المكرمة">مكة المكرمة</option>
                                <option value="المدينة المنورة">المدينة المنورة</option>
                                <option value="الطائف">الطائف</option>
                                <option value="تبوك">تبوك</option>
                                <option value="أبها">أبها</option>
                                <option value="حائل">حائل</option>
                                <option value="الجبيل">الجبيل</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">العنوان:</label>
                            <textarea id="branchAddress" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; height: 80px;" placeholder="العنوان التفصيلي للفرع"></textarea>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">رقم الهاتف:</label>
                            <input type="tel" id="branchPhone" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;" placeholder="مثال: 0112345678">
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" onclick="closeModal()" style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">إلغاء</button>
                            <button type="submit" style="padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">حفظ الفرع</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            document.getElementById('addBranchForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newBranch = {
                    id: 'BR' + Date.now(),
                    name: document.getElementById('branchName').value,
                    city: document.getElementById('branchCity').value,
                    address: document.getElementById('branchAddress').value,
                    phone: document.getElementById('branchPhone').value,
                    isActive: true,
                    createdAt: new Date().toISOString()
                };

                // حفظ الفرع
                const branches = db.getAllBranches();
                branches.push(newBranch);
                localStorage.setItem('branches', JSON.stringify(branches));

                // تحديث العرض
                loadBranchesData();
                closeModal();

                alert('تم إضافة الفرع بنجاح!');
            });

            window.closeModal = function() {
                document.body.removeChild(modal);
            };
        }

        function showTransferModal() {
            const branches = db.getAllBranches();
            const shipments = db.getAllShipments();

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 600px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; display: flex; align-items: center; gap: 10px;">
                        <span style="color: #17a2b8;">🔄</span>
                        تحويل شحنة بين الفروع
                    </h3>

                    <form id="transferForm" style="display: grid; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">رقم الشحنة:</label>
                            <select id="shipmentId" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;">
                                <option value="">اختر الشحنة</option>
                                ${shipments.map(s => `<option value="${s.id}">${s.id} - ${s.customerName}</option>`).join('')}
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">من الفرع:</label>
                            <select id="fromBranch" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;">
                                <option value="">اختر الفرع المرسل</option>
                                ${branches.map(b => `<option value="${b.id}">${b.name}</option>`).join('')}
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">إلى الفرع:</label>
                            <select id="toBranch" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem;">
                                <option value="">اختر الفرع المستقبل</option>
                                ${branches.map(b => `<option value="${b.id}">${b.name}</option>`).join('')}
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">سبب التحويل:</label>
                            <textarea id="transferReason" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 1rem; height: 80px;" placeholder="اذكر سبب التحويل..."></textarea>
                        </div>

                        <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" onclick="closeModal()" style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">إلغاء</button>
                            <button type="submit" style="padding: 12px 24px; background: #17a2b8; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: 600;">تأكيد التحويل</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // معالجة النموذج
            document.getElementById('transferForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const fromBranch = document.getElementById('fromBranch').value;
                const toBranch = document.getElementById('toBranch').value;

                if (fromBranch === toBranch) {
                    alert('لا يمكن تحويل الشحنة إلى نفس الفرع!');
                    return;
                }

                const transfer = {
                    id: 'TR' + Date.now(),
                    shipmentId: document.getElementById('shipmentId').value,
                    fromBranch: fromBranch,
                    toBranch: toBranch,
                    reason: document.getElementById('transferReason').value,
                    status: 'معلق',
                    createdAt: new Date().toISOString()
                };

                // حفظ التحويل
                const transfers = JSON.parse(localStorage.getItem('branchTransfers') || '[]');
                transfers.push(transfer);
                localStorage.setItem('branchTransfers', JSON.stringify(transfers));

                // تحديث العرض
                loadBranchesData();
                closeModal();

                alert('تم إنشاء طلب التحويل بنجاح!');
            });

            window.closeModal = function() {
                document.body.removeChild(modal);
            };
        }

        function generateBranchReport() {
            const branches = db.getAllBranches();
            const transfers = JSON.parse(localStorage.getItem('branchTransfers') || '[]');
            const shipments = db.getAllShipments();

            // إنشاء تقرير شامل
            const report = {
                generatedAt: new Date().toISOString(),
                totalBranches: branches.length,
                activeBranches: branches.filter(b => b.isActive).length,
                totalTransfers: transfers.length,
                pendingTransfers: transfers.filter(t => t.status === 'معلق').length,
                branchDetails: branches.map(branch => {
                    const branchShipments = shipments.filter(s => s.branch === branch.id);
                    const branchTransfersOut = transfers.filter(t => t.fromBranch === branch.id);
                    const branchTransfersIn = transfers.filter(t => t.toBranch === branch.id);

                    return {
                        id: branch.id,
                        name: branch.name,
                        city: branch.city,
                        isActive: branch.isActive,
                        totalShipments: branchShipments.length,
                        transfersOut: branchTransfersOut.length,
                        transfersIn: branchTransfersIn.length
                    };
                })
            };

            // تصدير التقرير
            const dataStr = JSON.stringify(report, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `branch_report_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            // عرض ملخص التقرير
            alert(`
تقرير الفروع:
• إجمالي الفروع: ${report.totalBranches}
• الفروع النشطة: ${report.activeBranches}
• إجمالي التحويلات: ${report.totalTransfers}
• التحويلات المعلقة: ${report.pendingTransfers}

تم تصدير التقرير التفصيلي بنجاح!
            `);
        }

        // تحميل بيانات الفروع
        function loadBranchesData() {
            try {
                const branches = db.getAllBranches();
                const transfers = JSON.parse(localStorage.getItem('branchTransfers') || '[]');

                // تحديث الإحصائيات
                document.getElementById('total-branches-display').textContent = branches.length;
                document.getElementById('active-branches-display').textContent = branches.filter(b => b.isActive).length;
                document.getElementById('pending-transfers-display').textContent = transfers.filter(t => t.status === 'معلق').length;

                // عرض قائمة الفروع
                const branchesContainer = document.getElementById('branches-list');
                if (branchesContainer) {
                    branchesContainer.innerHTML = branches.map(branch => `
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; border-left: 4px solid ${branch.isActive ? '#28a745' : '#dc3545'};">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h4 style="margin: 0 0 5px 0; color: #333; font-size: 1.1rem;">${branch.name}</h4>
                                    <p style="margin: 0; color: #666; font-size: 0.9rem;">📍 ${branch.city}</p>
                                    ${branch.address ? `<p style="margin: 5px 0 0 0; color: #666; font-size: 0.8rem;">${branch.address}</p>` : ''}
                                    ${branch.phone ? `<p style="margin: 5px 0 0 0; color: #666; font-size: 0.8rem;">📞 ${branch.phone}</p>` : ''}
                                </div>
                                <div style="text-align: center;">
                                    <span style="display: inline-block; padding: 6px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; color: white; background: ${branch.isActive ? '#28a745' : '#dc3545'};">
                                        ${branch.isActive ? '✅ نشط' : '❌ غير نشط'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    `).join('');
                }

                // عرض التحويلات الأخيرة
                const transfersContainer = document.getElementById('transfers-list');
                if (transfersContainer) {
                    const recentTransfers = transfers.slice(-5).reverse();
                    transfersContainer.innerHTML = recentTransfers.length > 0 ? recentTransfers.map(transfer => {
                        const fromBranch = branches.find(b => b.id === transfer.fromBranch);
                        const toBranch = branches.find(b => b.id === transfer.toBranch);

                        return `
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 12px; border-left: 4px solid #17a2b8;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <h5 style="margin: 0 0 5px 0; color: #333;">شحنة ${transfer.shipmentId}</h5>
                                        <p style="margin: 0; color: #666; font-size: 0.9rem;">
                                            من: ${fromBranch?.name || 'غير محدد'} → إلى: ${toBranch?.name || 'غير محدد'}
                                        </p>
                                        ${transfer.reason ? `<p style="margin: 5px 0 0 0; color: #666; font-size: 0.8rem;">السبب: ${transfer.reason}</p>` : ''}
                                    </div>
                                    <div style="text-align: center;">
                                        <span style="display: inline-block; padding: 4px 8px; border-radius: 15px; font-size: 0.7rem; font-weight: 600; color: white; background: #ffc107;">
                                            ${transfer.status}
                                        </span>
                                        <p style="margin: 5px 0 0 0; color: #666; font-size: 0.7rem;">
                                            ${new Date(transfer.createdAt).toLocaleDateString('ar-SA')}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('') : '<p style="text-align: center; color: #666; padding: 20px;">لا توجد تحويلات حديثة</p>';
                }

                console.log('✅ تم تحميل بيانات الفروع');
            } catch (error) {
                console.error('خطأ في تحميل بيانات الفروع:', error);
            }
        }

        // تهيئة القائمة الجانبية
        function initializeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            const toggleBtn = document.querySelector('.sidebar-toggle');

            if (sidebar && overlay && toggleBtn) {
                console.log('✅ تم تهيئة القائمة الجانبية بنجاح');

                // التأكد من أن القائمة مغلقة في البداية
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
                document.body.style.overflow = 'auto';

                return true;
            } else {
                console.error('❌ فشل في تهيئة القائمة الجانبية - عناصر مفقودة');
                return false;
            }
        }

        // وظائف القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (sidebar && overlay) {
                sidebar.classList.toggle('open');
                overlay.classList.toggle('show');

                // إضافة تأثير للجسم
                document.body.style.overflow = sidebar.classList.contains('open') ? 'hidden' : 'auto';

                console.log('🔄 تم تبديل حالة القائمة الجانبية:', sidebar.classList.contains('open') ? 'مفتوحة' : 'مغلقة');
            }
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (sidebar && overlay) {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');

                // إعادة تفعيل التمرير
                document.body.style.overflow = 'auto';

                console.log('❌ تم إغلاق القائمة الجانبية');
            }
        }

        function toggleSubmenu(element) {
            if (!element) return;

            const submenu = element.nextElementSibling;
            if (!submenu) return;

            const isOpen = submenu.classList.contains('open');

            // إغلاق جميع القوائم الفرعية الأخرى
            document.querySelectorAll('.submenu.open').forEach(menu => {
                if (menu !== submenu) {
                    menu.classList.remove('open');
                }
            });
            document.querySelectorAll('.menu-link.has-submenu.open').forEach(link => {
                if (link !== element) {
                    link.classList.remove('open');
                }
            });

            // فتح/إغلاق القائمة الحالية
            if (!isOpen) {
                submenu.classList.add('open');
                element.classList.add('open');
                console.log('📂 تم فتح القائمة الفرعية');
            } else {
                submenu.classList.remove('open');
                element.classList.remove('open');
                console.log('📁 تم إغلاق القائمة الفرعية');
            }
        }

        // إغلاق القائمة الجانبية عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSidebar();
            }
        });

        // إغلاق القائمة الجانبية عند النقر على رابط
        document.addEventListener('click', function(e) {
            if (e.target.matches('.submenu-link')) {
                setTimeout(() => {
                    closeSidebar();
                }, 300);
            }
        });

        // وظائف إدارة الملف الشخصي
        function openUserProfileModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                backdrop-filter: blur(5px);
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 40px; border-radius: 20px; width: 90%; max-width: 600px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); max-height: 90vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; border-bottom: 2px solid #f0f0f0; padding-bottom: 20px;">
                        <h2 style="margin: 0; color: #333; display: flex; align-items: center; gap: 10px; font-size: 1.5rem;">
                            <span style="color: #667eea;">👤</span>
                            إدارة الملف الشخصي
                        </h2>
                        <button onclick="closeProfileModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #999; padding: 5px;">✕</button>
                    </div>

                    <div style="display: grid; gap: 25px;">
                        <!-- صورة المدير -->
                        <div style="text-align: center;">
                            <div style="position: relative; display: inline-block;">
                                <img id="modalProfileImage" src="${document.getElementById('userProfileImage').src}"
                                     style="width: 120px; height: 120px; border-radius: 50%; object-fit: cover; border: 4px solid #667eea; box-shadow: 0 8px 25px rgba(0,0,0,0.2);">
                                <button onclick="changeProfileImage()" style="position: absolute; bottom: 5px; right: 5px; background: #667eea; color: white; border: none; border-radius: 50%; width: 35px; height: 35px; cursor: pointer; font-size: 1rem; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">📷</button>
                            </div>
                            <input type="file" id="imageUpload" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                        </div>

                        <!-- معلومات أساسية -->
                        <div style="display: grid; gap: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">الاسم الكامل:</label>
                                <input type="text" id="fullName" value="مدير النظام" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 10px; font-size: 1rem; transition: border-color 0.3s;">
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">المنصب:</label>
                                <select id="userPosition" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 10px; font-size: 1rem;">
                                    <option value="مدير عام">مدير عام</option>
                                    <option value="مدير تنفيذي">مدير تنفيذي</option>
                                    <option value="مدير العمليات">مدير العمليات</option>
                                    <option value="مدير النظام">مدير النظام</option>
                                </select>
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">البريد الإلكتروني:</label>
                                <input type="email" id="userEmail" value="<EMAIL>" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 10px; font-size: 1rem;">
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">رقم الهاتف:</label>
                                <input type="tel" id="userPhone" value="+966501234567" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 10px; font-size: 1rem;">
                            </div>
                        </div>

                        <!-- إدارة كلمة المرور -->
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; border-left: 4px solid #667eea;">
                            <h3 style="margin: 0 0 15px 0; color: #333; display: flex; align-items: center; gap: 8px;">
                                <span>🔐</span>
                                تغيير كلمة المرور
                            </h3>
                            <div style="display: grid; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">كلمة المرور الحالية:</label>
                                    <input type="password" id="currentPassword" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 0.9rem;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">كلمة المرور الجديدة:</label>
                                    <input type="password" id="newPassword" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 0.9rem;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #333;">تأكيد كلمة المرور:</label>
                                    <input type="password" id="confirmPassword" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 0.9rem;">
                                </div>
                            </div>
                        </div>

                        <!-- الصلاحيات -->
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 15px; border-left: 4px solid #17a2b8;">
                            <h3 style="margin: 0 0 15px 0; color: #333; display: flex; align-items: center; gap: 8px;">
                                <span>🛡️</span>
                                الصلاحيات والأذونات
                            </h3>
                            <div style="display: grid; gap: 10px;">

                                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                                    <input type="checkbox" checked style="transform: scale(1.2);">
                                    <span>إدارة المستخدمين</span>
                                </label>

                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 20px; border-top: 2px solid #f0f0f0; padding-top: 20px;">
                            <button onclick="closeProfileModal()" style="padding: 12px 25px; background: #6c757d; color: white; border: none; border-radius: 10px; cursor: pointer; font-weight: 600; transition: all 0.3s;">إلغاء</button>
                            <button onclick="saveUserProfile()" style="padding: 12px 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 10px; cursor: pointer; font-weight: 600; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">حفظ التغييرات</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // تحميل البيانات المحفوظة
            loadUserProfile();

            window.closeProfileModal = function() {
                document.body.removeChild(modal);
            };
        }

        // تغيير صورة الملف الشخصي
        function changeProfileImage() {
            document.getElementById('imageUpload').click();
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageData = e.target.result;
                    document.getElementById('modalProfileImage').src = imageData;

                    // حفظ الصورة في localStorage
                    localStorage.setItem('userProfileImage', imageData);
                };
                reader.readAsDataURL(file);
            }
        }

        // حفظ الملف الشخصي
        function saveUserProfile() {
            const userData = {
                fullName: document.getElementById('fullName').value,
                position: document.getElementById('userPosition').value,
                email: document.getElementById('userEmail').value,
                phone: document.getElementById('userPhone').value,
                profileImage: document.getElementById('modalProfileImage').src
            };

            // التحقق من كلمة المرور
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword && newPassword !== confirmPassword) {
                alert('كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين');
                return;
            }

            if (newPassword && newPassword.length < 6) {
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            // حفظ البيانات
            localStorage.setItem('userData', JSON.stringify(userData));

            if (newPassword) {
                localStorage.setItem('userPassword', newPassword);
            }

            // تحديث الواجهة
            updateUserInterface(userData);

            // إغلاق النافذة
            closeProfileModal();

            // رسالة نجاح
            showSuccessMessage('تم حفظ التغييرات بنجاح!');
        }

        // تحديث واجهة المستخدم
        function updateUserInterface(userData) {
            document.getElementById('userName').textContent = userData.fullName;
            document.getElementById('userRole').textContent = userData.position;
            document.getElementById('userProfileImage').src = userData.profileImage;
        }

        // تحميل الملف الشخصي
        function loadUserProfile() {
            const savedData = localStorage.getItem('userData');
            if (savedData) {
                const userData = JSON.parse(savedData);
                document.getElementById('fullName').value = userData.fullName || 'مدير النظام';
                document.getElementById('userPosition').value = userData.position || 'مدير عام';
                document.getElementById('userEmail').value = userData.email || '<EMAIL>';
                document.getElementById('userPhone').value = userData.phone || '+966501234567';

                if (userData.profileImage) {
                    document.getElementById('modalProfileImage').src = userData.profileImage;
                }
            }
        }

        // عرض رسالة نجاح
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                z-index: 10001;
                font-weight: 600;
                animation: slideIn 0.3s ease;
            `;

            successDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 1.2rem;">✅</span>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(successDiv);
                }, 300);
            }, 3000);
        }

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            const savedData = localStorage.getItem('userData');
            if (savedData) {
                const userData = JSON.parse(savedData);
                updateUserInterface(userData);
            }

            const savedImage = localStorage.getItem('userProfileImage');
            if (savedImage) {
                document.getElementById('userProfileImage').src = savedImage;
            }
        });
    </script>

    <style>
        /* تطبيق خط SF Pro Arabic Display على جميع العناصر */
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', sans-serif !important;
            font-weight: 600 !important;
        }

        input, select, textarea, button, .btn {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', sans-serif !important;
            font-weight: 600 !important;
        }

        .nav a, .nav-links a, .tab {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', sans-serif !important;
            font-weight: 600 !important;
        }

        /* تحسين مظهر بطاقات الإعدادات */
        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15) !important;
            border-color: #007bff !important;
        }

        .settings-card .btn-primary:hover {
            background: #0056b3 !important;
            transform: translateY(-2px);
        }

        .settings-card .btn-secondary:hover {
            background: #5a6268 !important;
            transform: translateY(-2px);
        }

        .settings-grid {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .settings-card-header span {
            transition: transform 0.3s ease;
        }

        .settings-card:hover .settings-card-header span {
            transform: scale(1.1);
        }
    </style>

    <!-- تطبيق خط SF Pro Arabic Display Semibold -->
    <script src="js/sf-pro-arabic-font.js"></script>
</body>
</html>
