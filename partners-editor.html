<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر صفحة شركاء النجاح | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: #f5f7fa;
            min-height: 100vh;
        }

        .editor-container {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            height: 100vh;
        }

        .sidebar {
            background: white;
            border-right: 1px solid #e1e5e9;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.05);
        }

        .sidebar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .sections-list {
            padding: 20px;
        }

        .section-item {
            background: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 10px;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s;
            cursor: pointer;
        }

        .section-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .section-item.active {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .section-header {
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
        }

        .main-editor {
            background: white;
            overflow-y: auto;
            position: relative;
        }

        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #e1e5e9;
            padding: 15px 20px;
            display: flex;
            gap: 10px;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .toolbar-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s;
        }

        .toolbar-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .toolbar-btn.success {
            background: #28a745;
        }

        .toolbar-btn.danger {
            background: #dc3545;
        }

        .editor-content {
            padding: 20px;
        }

        .drag-drop-area {
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .drag-drop-area:hover,
        .drag-drop-area.dragover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .elements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .element-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: grab;
            transition: all 0.3s;
        }

        .element-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .element-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #667eea;
        }

        .element-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .element-description {
            font-size: 0.85rem;
            color: #666;
        }

        .properties-panel {
            background: white;
            border-left: 1px solid #e1e5e9;
            overflow-y: auto;
            box-shadow: -2px 0 10px rgba(0,0,0,0.05);
        }

        .panel-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .panel-content {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .color-picker {
            width: 100%;
            height: 45px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
        }

        .btn-group {
            display: flex;
            gap: 8px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 8px 15px;
            font-size: 0.85rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.95);
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
            transition: all 0.3s;
        }

        .back-link:hover {
            background: white;
            transform: translateY(-2px);
        }

        .preview-area {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            min-height: 200px;
        }

        .preview-element {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s;
        }

        .preview-element:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .category-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .category-tab {
            background: #f8f9fa;
            border: 2px solid #e1e5e9;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .category-tab.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        @media (max-width: 1024px) {
            .editor-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr auto;
            }
            
            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e1e5e9;
                max-height: 300px;
            }
            
            .properties-panel {
                border-left: none;
                border-top: 1px solid #e1e5e9;
                max-height: 400px;
            }
        }
    </style>
</head>
<body>
    <a href="pages-management.html" class="back-link">← العودة لإدارة الصفحات</a>

    <div class="editor-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🤝 محرر شركاء النجاح</h2>
                <p>تحرير بصري مع السحب والإفلات</p>
            </div>
            
            <div class="sections-list" id="sectionsList">
                <div class="section-item active" data-section="hero" onclick="selectSection('hero')">
                    <div class="section-header">
                        <div class="section-icon">🤝</div>
                        <div class="section-info">
                            <h3>القسم الرئيسي</h3>
                            <p>عنوان الصفحة والمقدمة</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="strategic" onclick="selectSection('strategic')">
                    <div class="section-header">
                        <div class="section-icon">🏢</div>
                        <div class="section-info">
                            <h3>الشركاء الاستراتيجيون</h3>
                            <p>الشركاء الرئيسيون</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="categories" onclick="selectSection('categories')">
                    <div class="section-header">
                        <div class="section-icon">📂</div>
                        <div class="section-info">
                            <h3>تصنيف الشركاء</h3>
                            <p>تصنيف حسب القطاع</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="success-stories" onclick="selectSection('success-stories')">
                    <div class="section-header">
                        <div class="section-icon">📈</div>
                        <div class="section-info">
                            <h3>قصص النجاح</h3>
                            <p>قصص نجاح مع الشركاء</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="testimonials" onclick="selectSection('testimonials')">
                    <div class="section-header">
                        <div class="section-icon">💬</div>
                        <div class="section-info">
                            <h3>آراء الشركاء</h3>
                            <p>شهادات وآراء الشركاء</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="join-us" onclick="selectSection('join-us')">
                    <div class="section-header">
                        <div class="section-icon">➕</div>
                        <div class="section-info">
                            <h3>انضم إلينا</h3>
                            <p>دعوة للشراكة الجديدة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحرر الرئيسي -->
        <div class="main-editor">
            <div class="editor-toolbar">
                <button class="toolbar-btn" onclick="saveChanges()">💾 حفظ</button>
                <button class="toolbar-btn" onclick="previewPage()">👁️ معاينة</button>
                <button class="toolbar-btn success" onclick="publishPage()">🚀 نشر</button>
                <button class="toolbar-btn danger" onclick="resetChanges()">🔄 إعادة تعيين</button>
            </div>

            <div class="editor-content">
                <!-- منطقة السحب والإفلات -->
                <div class="drag-drop-area" id="dragDropArea">
                    <div style="font-size: 3rem; margin-bottom: 15px;">📁</div>
                    <div style="font-size: 1.2rem; font-weight: 600; margin-bottom: 10px;">اسحب العناصر هنا لإضافتها</div>
                    <div style="color: #666;">أو انقر لاختيار عنصر من المكتبة</div>
                </div>

                <!-- مكتبة العناصر -->
                <h3 style="margin-bottom: 20px; color: #333;">🧩 مكتبة العناصر المخصصة لشركاء النجاح</h3>
                <div class="elements-grid" id="elementsGrid">
                    <!-- سيتم تحميل العناصر هنا -->
                </div>

                <!-- منطقة المعاينة -->
                <h3 style="margin-bottom: 20px; color: #333;">👁️ معاينة القسم المحدد</h3>
                <div class="preview-area" id="previewArea">
                    <div style="text-align: center; color: #666; padding: 40px 20px;">
                        <div style="font-size: 3rem; margin-bottom: 15px;">🤝</div>
                        <p>اختر قسماً من الشريط الجانبي لرؤية المعاينة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة الخصائص -->
        <div class="properties-panel">
            <div class="panel-header">
                <h3 id="panelTitle">خصائص القسم الرئيسي</h3>
                <p id="panelDescription">عنوان الصفحة والمقدمة</p>
            </div>
            <div class="panel-content" id="panelContent">
                <!-- سيتم تحميل خصائص القسم هنا -->
            </div>
        </div>
    </div>

    <script>
        // بيانات العناصر المخصصة لصفحة شركاء النجاح
        let partnersElements = [
            { id: 'partner-card', name: 'بطاقة شريك', icon: '🏢', description: 'بطاقة تعرض معلومات شريك', type: 'special' },
            { id: 'partner-logo', name: 'شعار شريك', icon: '🎨', description: 'شعار أو لوجو الشريك', type: 'media' },
            { id: 'success-story', name: 'قصة نجاح', icon: '📈', description: 'قصة نجاح مع شريك', type: 'special' },
            { id: 'testimonial', name: 'شهادة شريك', icon: '💬', description: 'شهادة أو رأي من شريك', type: 'special' },
            { id: 'partnership-stats', name: 'إحصائيات الشراكة', icon: '📊', description: 'أرقام وإحصائيات الشراكة', type: 'special' },
            { id: 'category-filter', name: 'مرشح الفئات', icon: '🔍', description: 'تصفية الشركاء حسب الفئة', type: 'interactive' },
            { id: 'contact-form', name: 'نموذج تواصل', icon: '📝', description: 'نموذج للتواصل مع الشركاء', type: 'interactive' },
            { id: 'partnership-benefits', name: 'مزايا الشراكة', icon: '⭐', description: 'قائمة مزايا الشراكة', type: 'content' },
            { id: 'join-cta', name: 'دعوة للانضمام', icon: '➕', description: 'دعوة للشراكة الجديدة', type: 'interactive' },
            { id: 'partners-gallery', name: 'معرض الشركاء', icon: '🖼️', description: 'معرض صور الشركاء', type: 'media' }
        ];

        let currentSection = 'hero';
        let selectedElement = null;

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadElementsLibrary();
            selectSection('hero');
        });

        // اختيار قسم
        function selectSection(sectionId) {
            currentSection = sectionId;

            // تحديث الواجهة
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

            // تحديث لوحة الخصائص
            updatePropertiesPanel(sectionId);

            // تحديث المعاينة
            updatePreview(sectionId);

            console.log(`تم اختيار القسم: ${sectionId}`);
        }

        // تحميل مكتبة العناصر
        function loadElementsLibrary() {
            const elementsGrid = document.getElementById('elementsGrid');
            elementsGrid.innerHTML = '';

            partnersElements.forEach(element => {
                const elementCard = document.createElement('div');
                elementCard.className = 'element-card';
                elementCard.draggable = true;
                elementCard.dataset.elementId = element.id;

                elementCard.innerHTML = `
                    <div class="element-icon">${element.icon}</div>
                    <div class="element-title">${element.name}</div>
                    <div class="element-description">${element.description}</div>
                `;

                elementCard.addEventListener('click', () => selectElement(element));

                elementsGrid.appendChild(elementCard);
            });
        }

        // اختيار عنصر
        function selectElement(element) {
            selectedElement = element;

            // تحديث لوحة الخصائص
            updateElementProperties(element);

            // تحديث الواجهة
            document.querySelectorAll('.element-card').forEach(card => {
                card.style.borderColor = card.dataset.elementId === element.id ? '#667eea' : '#e1e5e9';
            });

            console.log(`تم اختيار العنصر: ${element.name}`);
        }

        // تحديث لوحة الخصائص للقسم
        function updatePropertiesPanel(sectionId) {
            const panelTitle = document.getElementById('panelTitle');
            const panelDescription = document.getElementById('panelDescription');
            const panelContent = document.getElementById('panelContent');

            const sectionData = {
                hero: { name: 'القسم الرئيسي', description: 'عنوان الصفحة والمقدمة' },
                strategic: { name: 'الشركاء الاستراتيجيون', description: 'الشركاء الرئيسيون' },
                categories: { name: 'تصنيف الشركاء', description: 'تصنيف حسب القطاع' },
                'success-stories': { name: 'قصص النجاح', description: 'قصص نجاح مع الشركاء' },
                testimonials: { name: 'آراء الشركاء', description: 'شهادات وآراء الشركاء' },
                'join-us': { name: 'انضم إلينا', description: 'دعوة للشراكة الجديدة' }
            };

            const section = sectionData[sectionId];
            panelTitle.textContent = `خصائص ${section.name}`;
            panelDescription.textContent = section.description;

            panelContent.innerHTML = generateSectionProperties(sectionId);
        }

        // تحديث لوحة الخصائص للعنصر
        function updateElementProperties(element) {
            const panelTitle = document.getElementById('panelTitle');
            const panelDescription = document.getElementById('panelDescription');
            const panelContent = document.getElementById('panelContent');

            panelTitle.textContent = `خصائص ${element.name}`;
            panelDescription.textContent = element.description;

            panelContent.innerHTML = generateElementProperties(element);
        }

        // إنشاء خصائص القسم
        function generateSectionProperties(sectionId) {
            const commonProperties = `
                <div class="form-group">
                    <label>عنوان القسم</label>
                    <input type="text" class="form-control" placeholder="أدخل عنوان القسم" value="${getSectionTitle(sectionId)}">
                </div>
                <div class="form-group">
                    <label>وصف القسم</label>
                    <textarea class="form-control" rows="3" placeholder="أدخل وصف القسم">${getSectionDescription(sectionId)}</textarea>
                </div>
                <div class="form-group">
                    <label>لون الخلفية</label>
                    <input type="color" class="color-picker" value="#ffffff">
                </div>
                <div class="form-group">
                    <label>ترتيب العرض</label>
                    <select class="form-control">
                        <option>شبكي</option>
                        <option>قائمة</option>
                        <option>بطاقات</option>
                    </select>
                </div>
                <div class="btn-group">
                    <button class="btn-small btn-primary" onclick="applyChanges()">تطبيق</button>
                    <button class="btn-small btn-secondary" onclick="resetSection()">إعادة تعيين</button>
                </div>
            `;

            return commonProperties;
        }

        // إنشاء خصائص العنصر
        function generateElementProperties(element) {
            const baseProperties = `
                <div class="form-group">
                    <label>نوع العنصر</label>
                    <input type="text" class="form-control" value="${element.name}" readonly>
                </div>
            `;

            const specificProperties = {
                'partner-card': `
                    <div class="form-group">
                        <label>اسم الشريك</label>
                        <input type="text" class="form-control" placeholder="اسم الشركة الشريكة" value="شركة التقنية المتقدمة">
                    </div>
                    <div class="form-group">
                        <label>نوع الشراكة</label>
                        <select class="form-control">
                            <option>شريك استراتيجي</option>
                            <option>شريك تجاري</option>
                            <option>شريك تقني</option>
                            <option>شريك خدمات</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>وصف الشراكة</label>
                        <textarea class="form-control" rows="3" placeholder="وصف الشراكة">شراكة استراتيجية في مجال التكنولوجيا</textarea>
                    </div>
                    <div class="form-group">
                        <label>شعار الشريك</label>
                        <input type="file" class="form-control" accept="image/*">
                    </div>
                `,
                'success-story': `
                    <div class="form-group">
                        <label>عنوان قصة النجاح</label>
                        <input type="text" class="form-control" placeholder="عنوان القصة" value="نجاح باهر في التوصيل">
                    </div>
                    <div class="form-group">
                        <label>اسم الشريك</label>
                        <input type="text" class="form-control" placeholder="اسم الشريك" value="متجر الإلكترونيات">
                    </div>
                    <div class="form-group">
                        <label>تفاصيل القصة</label>
                        <textarea class="form-control" rows="4" placeholder="تفاصيل قصة النجاح">تمكنا من زيادة معدل التوصيل بنسبة 95% خلال 6 أشهر</textarea>
                    </div>
                    <div class="form-group">
                        <label>النتائج المحققة</label>
                        <input type="text" class="form-control" placeholder="مثل: زيادة 50%" value="زيادة 95% في معدل التوصيل">
                    </div>
                `,
                'testimonial': `
                    <div class="form-group">
                        <label>نص الشهادة</label>
                        <textarea class="form-control" rows="4" placeholder="نص الشهادة">"خدمة ممتازة وتوصيل سريع، ننصح بالتعامل معهم"</textarea>
                    </div>
                    <div class="form-group">
                        <label>اسم صاحب الشهادة</label>
                        <input type="text" class="form-control" placeholder="الاسم" value="أحمد محمد">
                    </div>
                    <div class="form-group">
                        <label>المنصب</label>
                        <input type="text" class="form-control" placeholder="المنصب" value="مدير العمليات">
                    </div>
                    <div class="form-group">
                        <label>اسم الشركة</label>
                        <input type="text" class="form-control" placeholder="اسم الشركة" value="شركة التجارة الذكية">
                    </div>
                `,
                'partnership-stats': `
                    <div class="form-group">
                        <label>الرقم/الإحصائية</label>
                        <input type="text" class="form-control" placeholder="مثل: 500+" value="500+">
                    </div>
                    <div class="form-group">
                        <label>وصف الإحصائية</label>
                        <input type="text" class="form-control" placeholder="مثل: شريك نشط" value="شريك نشط">
                    </div>
                    <div class="form-group">
                        <label>أيقونة الإحصائية</label>
                        <input type="text" class="form-control" placeholder="🤝" value="🤝">
                    </div>
                `,
                'join-cta': `
                    <div class="form-group">
                        <label>عنوان الدعوة</label>
                        <input type="text" class="form-control" placeholder="عنوان الدعوة" value="انضم إلى شركائنا">
                    </div>
                    <div class="form-group">
                        <label>نص الدعوة</label>
                        <textarea class="form-control" rows="3" placeholder="نص الدعوة">كن جزءاً من شبكة شركائنا المتميزة واستفد من خدماتنا المتطورة</textarea>
                    </div>
                    <div class="form-group">
                        <label>نص الزر</label>
                        <input type="text" class="form-control" placeholder="نص الزر" value="ابدأ الشراكة">
                    </div>
                    <div class="form-group">
                        <label>رابط الزر</label>
                        <input type="url" class="form-control" placeholder="https://example.com/contact">
                    </div>
                `
            };

            const elementSpecific = specificProperties[element.id] || '';

            return baseProperties + elementSpecific + `
                <div class="btn-group">
                    <button class="btn-small btn-primary" onclick="updateElement()">تحديث</button>
                    <button class="btn-small btn-success" onclick="addToPreview()">إضافة للمعاينة</button>
                    <button class="btn-small btn-danger" onclick="deleteElement()">حذف</button>
                </div>
            `;
        }

        // تحديث المعاينة
        function updatePreview(sectionId) {
            const previewArea = document.getElementById('previewArea');

            const previews = {
                hero: `
                    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px;">
                        <h1 style="font-size: 2.5rem; margin-bottom: 20px;">🤝 شركاء النجاح</h1>
                        <p style="font-size: 1.2rem; line-height: 1.6;">نفخر بشراكتنا مع أفضل الشركات والمؤسسات في المملكة</p>
                    </div>
                `,
                strategic: `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div style="background: white; padding: 25px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <div style="font-size: 3rem; margin-bottom: 15px;">🏢</div>
                            <h3 style="color: #667eea; margin-bottom: 10px;">شركة التقنية المتقدمة</h3>
                            <p style="color: #666; margin-bottom: 15px;">شريك استراتيجي في التكنولوجيا</p>
                            <div style="background: #f8f9fa; padding: 10px; border-radius: 8px;">
                                <span style="color: #667eea; font-weight: 600;">1000+</span>
                                <span style="color: #666; font-size: 0.9rem;">مشروع مشترك</span>
                            </div>
                        </div>
                    </div>
                `,
                categories: `
                    <div class="category-tabs">
                        <div class="category-tab active">الكل</div>
                        <div class="category-tab">التجارة الإلكترونية</div>
                        <div class="category-tab">التصنيع</div>
                        <div class="category-tab">الخدمات</div>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div style="background: white; padding: 20px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <div style="font-size: 2.5rem; margin-bottom: 10px;">🛒</div>
                            <h4 style="color: #333; margin-bottom: 5px;">متجر الإلكترونيات</h4>
                            <p style="color: #666; font-size: 0.9rem;">تجارة إلكترونية</p>
                        </div>
                    </div>
                `,
                'success-stories': `
                    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
                            <div style="font-size: 3rem;">📈</div>
                            <div>
                                <h3 style="color: #667eea; margin-bottom: 5px;">نجاح باهر في التوصيل</h3>
                                <p style="color: #666; font-size: 0.9rem;">مع متجر الإلكترونيات</p>
                            </div>
                        </div>
                        <p style="color: #333; line-height: 1.6; margin-bottom: 15px;">تمكنا من زيادة معدل التوصيل بنسبة 95% خلال 6 أشهر من بداية الشراكة</p>
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                            <span style="color: #667eea; font-weight: 600; font-size: 1.2rem;">95%</span>
                            <span style="color: #666; margin-right: 10px;">زيادة في معدل التوصيل</span>
                        </div>
                    </div>
                `,
                testimonials: `
                    <div style="background: white; padding: 25px; border-radius: 12px; border-left: 4px solid #667eea; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        <p style="color: #333; font-style: italic; font-size: 1.1rem; line-height: 1.6; margin-bottom: 20px;">"خدمة ممتازة وتوصيل سريع، ننصح بالتعامل معهم بقوة"</p>
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="width: 50px; height: 50px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">👤</div>
                            <div>
                                <div style="font-weight: 600; color: #333; margin-bottom: 3px;">أحمد محمد</div>
                                <div style="color: #667eea; font-size: 0.9rem; margin-bottom: 2px;">مدير العمليات</div>
                                <div style="color: #666; font-size: 0.85rem;">شركة التجارة الذكية</div>
                            </div>
                        </div>
                    </div>
                `,
                'join-us': `
                    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 12px;">
                        <div style="font-size: 4rem; margin-bottom: 20px;">➕</div>
                        <h2 style="margin-bottom: 15px;">انضم إلى شركائنا</h2>
                        <p style="font-size: 1.1rem; line-height: 1.6; margin-bottom: 25px;">كن جزءاً من شبكة شركائنا المتميزة واستفد من خدماتنا المتطورة</p>
                        <button style="background: white; color: #28a745; border: none; padding: 15px 30px; border-radius: 25px; font-weight: 600; font-size: 1rem; cursor: pointer;">ابدأ الشراكة</button>
                    </div>
                `
            };

            previewArea.innerHTML = previews[sectionId] || `
                <div style="text-align: center; color: #666; padding: 40px 20px;">
                    <div style="font-size: 3rem; margin-bottom: 15px;">🤝</div>
                    <p>معاينة ${sectionId}</p>
                </div>
            `;
        }

        // الحصول على عنوان القسم
        function getSectionTitle(sectionId) {
            const titles = {
                hero: 'شركاء النجاح',
                strategic: 'شركاؤنا الاستراتيجيون',
                categories: 'شركاء حسب القطاع',
                'success-stories': 'قصص نجاح ملهمة',
                testimonials: 'آراء شركائنا',
                'join-us': 'انضم إلى عائلة الشركاء'
            };
            return titles[sectionId] || 'عنوان القسم';
        }

        // الحصول على وصف القسم
        function getSectionDescription(sectionId) {
            const descriptions = {
                hero: 'نفخر بشراكتنا مع أفضل الشركات والمؤسسات',
                strategic: 'شركاؤنا الرئيسيون في رحلة النجاح',
                categories: 'شركاء متنوعون من مختلف القطاعات',
                'success-stories': 'قصص نجاح حقيقية مع شركائنا',
                testimonials: 'شهادات وآراء شركائنا الكرام',
                'join-us': 'ادعوة للانضمام إلى شبكة شركائنا المتميزة'
            };
            return descriptions[sectionId] || 'وصف القسم';
        }

        // وظائف شريط الأدوات
        function saveChanges() {
            alert('تم حفظ التغييرات بنجاح!');
        }

        function previewPage() {
            window.open('success-partners.html', '_blank');
        }

        function publishPage() {
            if (confirm('هل أنت متأكد من نشر صفحة شركاء النجاح؟')) {
                alert('تم نشر الصفحة بنجاح!');
            }
        }

        function resetChanges() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
                selectSection('hero');
                alert('تم إعادة تعيين التغييرات');
            }
        }

        // وظائف العناصر
        function applyChanges() {
            alert('تم تطبيق التغييرات على القسم');
        }

        function resetSection() {
            if (confirm('هل تريد إعادة تعيين خصائص هذا القسم؟')) {
                updatePropertiesPanel(currentSection);
                alert('تم إعادة تعيين خصائص القسم');
            }
        }

        function updateElement() {
            alert('تم تحديث العنصر');
        }

        function addToPreview() {
            alert('تم إضافة العنصر للمعاينة');
        }

        function deleteElement() {
            if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                alert('تم حذف العنصر');
            }
        }
    </script>
</body>
</html>
