<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح تتبع الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            max-width: 700px;
            width: 100%;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }

        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-btn {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .test-btn.danger:hover {
            box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3);
        }

        .status-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .back-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s;
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }

        .tracking-numbers {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .tracking-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s;
        }

        .tracking-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }

        .tracking-card h4 {
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .tracking-card .number {
            font-family: monospace;
            font-size: 0.85rem;
            background: #f8f9fa;
            padding: 5px 8px;
            border-radius: 5px;
            margin: 5px 0;
        }

        .tracking-card .status {
            font-size: 0.8rem;
            color: #666;
        }
    </style>
</head>
<body>
    <a href="shipment-tracking.html" class="back-link">← العودة لتتبع الشحنات</a>
    
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 اختبار إصلاح تتبع الشحنات</h1>
            <p>تحقق من إصلاح مشكلة "خطأ في تحميل النظام"</p>
        </div>

        <div class="test-section">
            <h3>✅ الإصلاحات المطبقة</h3>
            <ul style="list-style: none; padding: 0;">
                <li style="padding: 8px 0; color: #666; display: flex; align-items: center; gap: 10px;">
                    <span style="color: #28a745; font-size: 1.2rem;">✅</span>
                    إصلاح مسار قاعدة البيانات (database-simple.js)
                </li>
                <li style="padding: 8px 0; color: #666; display: flex; align-items: center; gap: 10px;">
                    <span style="color: #28a745; font-size: 1.2rem;">✅</span>
                    إضافة قاعدة بيانات احتياطية
                </li>
                <li style="padding: 8px 0; color: #666; display: flex; align-items: center; gap: 10px;">
                    <span style="color: #28a745; font-size: 1.2rem;">✅</span>
                    إضافة شحنات تجريبية للاختبار
                </li>
                <li style="padding: 8px 0; color: #666; display: flex; align-items: center; gap: 10px;">
                    <span style="color: #28a745; font-size: 1.2rem;">✅</span>
                    تحسين معالجة الأخطاء
                </li>
                <li style="padding: 8px 0; color: #666; display: flex; align-items: center; gap: 10px;">
                    <span style="color: #28a745; font-size: 1.2rem;">✅</span>
                    تحديث رسالة الترحيب
                </li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 اختبار النظام</h3>
            <a href="shipment-tracking.html" class="test-btn">
                افتح صفحة تتبع الشحنات المحدثة
            </a>
            <p style="color: #666; font-size: 0.9rem; margin-top: 10px;">
                يجب أن تعمل الصفحة بدون أخطاء وتعرض رسالة ترحيب مع أرقام التتبع التجريبية
            </p>
        </div>

        <div class="test-section">
            <h3>📦 أرقام التتبع التجريبية</h3>
            <p style="margin-bottom: 15px; color: #666;">استخدم هذه الأرقام لاختبار النظام:</p>
            
            <div class="tracking-numbers">
                <div class="tracking-card" style="border-color: #3498db;">
                    <h4 style="color: #3498db;">📦 في الطريق</h4>
                    <div class="number">TRK123456789</div>
                    <div class="status">أحمد محمد → فاطمة علي</div>
                </div>
                
                <div class="tracking-card" style="border-color: #27ae60;">
                    <h4 style="color: #27ae60;">✅ تم التسليم</h4>
                    <div class="number">TRK987654321</div>
                    <div class="status">سارة أحمد → محمد خالد</div>
                </div>
                
                <div class="tracking-card" style="border-color: #f39c12;">
                    <h4 style="color: #f39c12;">⏳ معلق</h4>
                    <div class="number">TRK555666777</div>
                    <div class="status">عبدالله سعد → نورا محمد</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 خطوات الاختبار</h3>
            <ol style="color: #666; line-height: 1.6; padding-right: 20px;">
                <li>اضغط على "افتح صفحة تتبع الشحنات المحدثة"</li>
                <li>تأكد من عدم ظهور رسالة "خطأ في تحميل النظام"</li>
                <li>تأكد من ظهور رسالة الترحيب مع أرقام التتبع</li>
                <li>جرب البحث بأحد أرقام التتبع التجريبية</li>
                <li>تأكد من ظهور تفاصيل الشحنة بشكل صحيح</li>
                <li>جرب الوظائف الأخرى (طباعة، مشاركة، تصدير)</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات التشخيص</h3>
            <button onclick="checkDatabase()" class="test-btn">
                📊 فحص قاعدة البيانات
            </button>
            <button onclick="testTracking()" class="test-btn">
                🔍 اختبار البحث
            </button>
            <button onclick="clearData()" class="test-btn danger">
                🗑️ مسح البيانات التجريبية
            </button>
            
            <div class="status-box" id="statusBox" style="display: none;">
                <h4 style="color: #1976d2; margin-bottom: 10px;">📊 نتائج التشخيص</h4>
                <div id="statusContent"></div>
            </div>
        </div>
    </div>

    <script>
        // فحص قاعدة البيانات
        function checkDatabase() {
            const statusBox = document.getElementById('statusBox');
            const statusContent = document.getElementById('statusContent');
            
            let results = [];
            
            // فحص localStorage
            try {
                const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                results.push(`✅ عدد الشحنات في localStorage: ${shipments.length}`);
                
                if (shipments.length > 0) {
                    results.push(`📦 أول شحنة: ${shipments[0].trackingNumber}`);
                    results.push(`📦 آخر شحنة: ${shipments[shipments.length-1].trackingNumber}`);
                }
            } catch (error) {
                results.push(`❌ خطأ في قراءة localStorage: ${error.message}`);
            }
            
            // فحص قاعدة البيانات الخارجية
            if (typeof db !== 'undefined') {
                results.push('✅ قاعدة البيانات الخارجية متاحة');
            } else {
                results.push('⚠️ قاعدة البيانات الخارجية غير متاحة (سيتم استخدام البيانات المحفوظة)');
            }
            
            statusContent.innerHTML = results.map(r => `<p style="margin: 5px 0;">${r}</p>`).join('');
            statusBox.style.display = 'block';
        }

        // اختبار البحث
        function testTracking() {
            const statusBox = document.getElementById('statusBox');
            const statusContent = document.getElementById('statusContent');
            
            const testNumbers = ['TRK123456789', 'TRK987654321', 'TRK555666777'];
            let results = [];
            
            testNumbers.forEach(number => {
                try {
                    const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                    const found = shipments.find(s => s.trackingNumber === number);
                    
                    if (found) {
                        results.push(`✅ ${number}: ${found.status} (${found.senderName} → ${found.receiverName})`);
                    } else {
                        results.push(`❌ ${number}: غير موجود`);
                    }
                } catch (error) {
                    results.push(`❌ ${number}: خطأ في البحث`);
                }
            });
            
            statusContent.innerHTML = results.map(r => `<p style="margin: 5px 0;">${r}</p>`).join('');
            statusBox.style.display = 'block';
        }

        // مسح البيانات التجريبية
        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟\n\nسيتم إعادة إنشاؤها عند فتح صفحة التتبع مرة أخرى.')) {
                localStorage.removeItem('shipments');
                alert('✅ تم مسح البيانات التجريبية بنجاح!');
                checkDatabase();
            }
        }

        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 صفحة اختبار إصلاح تتبع الشحنات');
            console.log('✅ الإصلاحات المطبقة:');
            console.log('  - إصلاح مسار قاعدة البيانات');
            console.log('  - إضافة قاعدة بيانات احتياطية');
            console.log('  - إضافة شحنات تجريبية');
            console.log('  - تحسين معالجة الأخطاء');
            
            // فحص تلقائي للبيانات
            setTimeout(checkDatabase, 1000);
        });
    </script>
</body>
</html>
