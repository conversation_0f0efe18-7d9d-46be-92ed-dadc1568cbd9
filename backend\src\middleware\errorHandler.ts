// Error Handling Middleware
// وسطاء التعامل مع الأخطاء

import { Request, Response, NextFunction } from 'express'
import { ZodError } from 'zod'
import { Prisma } from '@prisma/client'
import { logger } from '../utils/logger'
import { config } from '../config/config'

// Custom error class
export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean
  public code?: string

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = true
    this.code = code

    Error.captureStackTrace(this, this.constructor)
  }
}

// Error response interface
interface ErrorResponse {
  success: false
  error: {
    message: string
    code?: string
    details?: any
    stack?: string
  }
  timestamp: string
  path: string
  method: string
}

// Handle Zod validation errors
function handleZodError(error: ZodError): AppError {
  const messages = error.errors.map(err => {
    const path = err.path.join('.')
    return `${path}: ${err.message}`
  })
  
  return new AppError(
    `Validation failed: ${messages.join(', ')}`,
    400,
    'VALIDATION_ERROR'
  )
}

// Handle Prisma errors
function handlePrismaError(error: Prisma.PrismaClientKnownRequestError): AppError {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target as string[]
      return new AppError(
        `Duplicate value for ${field?.join(', ') || 'field'}`,
        409,
        'DUPLICATE_ENTRY'
      )
    
    case 'P2025':
      // Record not found
      return new AppError(
        'Record not found',
        404,
        'NOT_FOUND'
      )
    
    case 'P2003':
      // Foreign key constraint violation
      return new AppError(
        'Related record not found',
        400,
        'FOREIGN_KEY_VIOLATION'
      )
    
    case 'P2014':
      // Required relation violation
      return new AppError(
        'Required relation missing',
        400,
        'REQUIRED_RELATION_MISSING'
      )
    
    default:
      return new AppError(
        'Database operation failed',
        500,
        'DATABASE_ERROR'
      )
  }
}

// Handle JWT errors
function handleJWTError(error: Error): AppError {
  if (error.name === 'JsonWebTokenError') {
    return new AppError('Invalid token', 401, 'INVALID_TOKEN')
  }
  
  if (error.name === 'TokenExpiredError') {
    return new AppError('Token expired', 401, 'TOKEN_EXPIRED')
  }
  
  return new AppError('Authentication failed', 401, 'AUTH_FAILED')
}

// Send error response
function sendErrorResponse(
  error: AppError,
  req: Request,
  res: Response
): void {
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      message: error.message,
      code: error.code,
    },
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
  }

  // Add stack trace in development
  if (config.nodeEnv === 'development') {
    errorResponse.error.stack = error.stack
  }

  // Add details for validation errors
  if (error.code === 'VALIDATION_ERROR' && error.cause) {
    errorResponse.error.details = error.cause
  }

  res.status(error.statusCode).json(errorResponse)
}

// Main error handler middleware
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  let appError: AppError

  // Handle different types of errors
  if (error instanceof AppError) {
    appError = error
  } else if (error instanceof ZodError) {
    appError = handleZodError(error)
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    appError = handlePrismaError(error)
  } else if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    appError = handleJWTError(error)
  } else {
    // Unknown error
    appError = new AppError(
      config.nodeEnv === 'production' 
        ? 'Something went wrong' 
        : error.message,
      500,
      'INTERNAL_ERROR'
    )
  }

  // Log error
  logger.error('Error occurred', {
    error: error.message,
    stack: error.stack,
    statusCode: appError.statusCode,
    code: appError.code,
    path: req.path,
    method: req.method,
    userId: (req as any).user?.id,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  })

  // Send response
  sendErrorResponse(appError, req, res)
}

// Async error wrapper
export function asyncHandler<T extends Request, U extends Response>(
  fn: (req: T, res: U, next: NextFunction) => Promise<any>
) {
  return (req: T, res: U, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// Not found handler
export function notFoundHandler(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const error = new AppError(
    `Route ${req.originalUrl} not found`,
    404,
    'NOT_FOUND'
  )
  next(error)
}

// Validation error helper
export function createValidationError(message: string, details?: any): AppError {
  const error = new AppError(message, 400, 'VALIDATION_ERROR')
  error.cause = details
  return error
}

// Authorization error helper
export function createAuthError(message: string = 'Unauthorized'): AppError {
  return new AppError(message, 401, 'UNAUTHORIZED')
}

// Forbidden error helper
export function createForbiddenError(message: string = 'Forbidden'): AppError {
  return new AppError(message, 403, 'FORBIDDEN')
}

// Not found error helper
export function createNotFoundError(message: string = 'Not found'): AppError {
  return new AppError(message, 404, 'NOT_FOUND')
}

// Conflict error helper
export function createConflictError(message: string): AppError {
  return new AppError(message, 409, 'CONFLICT')
}

// Rate limit error helper
export function createRateLimitError(message: string = 'Too many requests'): AppError {
  return new AppError(message, 429, 'RATE_LIMIT_EXCEEDED')
}
