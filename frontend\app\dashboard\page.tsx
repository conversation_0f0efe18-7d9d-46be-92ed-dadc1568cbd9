'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { t } = useLanguage()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner w-8 h-8"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-display font-bold text-gray-900">
              {t('dashboard.title')}
            </h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                {t('dashboard.welcome', { name: user?.firstName })}
              </span>
              <a
                href="/security"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm"
              >
                إعدادات الأمان
              </a>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.firstName?.charAt(0)}
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {user?.firstName} {user?.lastName}
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Card */}
          <div className="card mb-8">
            <div className="text-center">
              <h2 className="text-2xl font-display font-bold text-gray-900 mb-4">
                {t('dashboard.welcome', { name: user?.firstName })}
              </h2>
              <p className="text-gray-600 mb-6">
                {t('dashboard.overview')}
              </p>
              
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-blue-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-blue-600">0</div>
                  <div className="text-sm text-blue-800">{t('dashboard.totalShipments')}</div>
                </div>
                <div className="bg-yellow-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-yellow-600">0</div>
                  <div className="text-sm text-yellow-800">{t('dashboard.pendingShipments')}</div>
                </div>
                <div className="bg-green-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-green-600">0</div>
                  <div className="text-sm text-green-800">{t('dashboard.deliveredShipments')}</div>
                </div>
                <div className="bg-purple-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-purple-600">$0</div>
                  <div className="text-sm text-purple-800">{t('dashboard.totalRevenue')}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card mb-8">
            <h3 className="text-lg font-display font-semibold text-gray-900 mb-4">
              {t('dashboard.quickActions')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="btn-primary text-center p-4">
                <div className="text-lg font-medium">{t('dashboard.addShipment')}</div>
                <div className="text-sm opacity-75">{t('shipments.addShipment')}</div>
              </button>
              <button className="btn-secondary text-center p-4">
                <div className="text-lg font-medium">{t('dashboard.addCustomer')}</div>
                <div className="text-sm opacity-75">{t('customers.addCustomer')}</div>
              </button>
              <button className="btn-outline text-center p-4">
                <div className="text-lg font-medium">{t('dashboard.viewReports')}</div>
                <div className="text-sm opacity-75">{t('reports.title')}</div>
              </button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Shipments */}
            <div className="card">
              <h3 className="text-lg font-display font-semibold text-gray-900 mb-4">
                {t('dashboard.recentShipments')}
              </h3>
              <div className="text-center py-8 text-gray-500">
                {t('common.loading')}...
              </div>
            </div>

            {/* Top Distributors */}
            <div className="card">
              <h3 className="text-lg font-display font-semibold text-gray-900 mb-4">
                {t('dashboard.topDistributors')}
              </h3>
              <div className="text-center py-8 text-gray-500">
                {t('common.loading')}...
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="mt-8 card">
            <h3 className="text-lg font-display font-semibold text-gray-900 mb-4">
              {language === 'ar' ? 'حالة النظام' : 'System Status'}
            </h3>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full me-2"></div>
                <span className="text-sm text-gray-600">
                  {language === 'ar' ? 'النظام يعمل بشكل طبيعي' : 'System operational'}
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full me-2"></div>
                <span className="text-sm text-gray-600">
                  {language === 'ar' ? 'قاعدة البيانات متصلة' : 'Database connected'}
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full me-2"></div>
                <span className="text-sm text-gray-600">
                  {language === 'ar' ? 'API يعمل' : 'API operational'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
