'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

export default function DashboardPage() {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
    } else {
      setIsAuthenticated(true)
    }
    setLoading(false)
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    router.push('/login')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">
              لوحة التحكم - نظام إدارة الشحنات
            </h1>
            <div className="flex items-center space-x-4 space-x-reverse">
              <a
                href="/security"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm"
              >
                إعدادات الأمان
              </a>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Card */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                مرحباً بك في نظام إدارة الشحنات
              </h2>
              <p className="text-gray-600 mb-6">
                نظام متكامل لإدارة الشحنات والتوصيل مع المصادقة الثنائية
              </p>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-blue-600">✅</div>
                  <div className="text-sm text-blue-800">نظام المصادقة الثنائية</div>
                  <div className="text-xs text-blue-600">يعمل بشكل صحيح</div>
                </div>
                <div className="bg-green-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-green-600">🔒</div>
                  <div className="text-sm text-green-800">الأمان المتقدم</div>
                  <div className="text-xs text-green-600">QR Code + Google Authenticator</div>
                </div>
                <div className="bg-purple-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-purple-600">⚡</div>
                  <div className="text-sm text-purple-800">جاهز للاستخدام</div>
                  <div className="text-xs text-purple-600">Backend + Frontend</div>
                </div>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              الميزات المتاحة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">🔐 المصادقة الثنائية</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• تسجيل دخول آمن مع QR Code</li>
                  <li>• دعم Google Authenticator</li>
                  <li>• رموز احتياطية للطوارئ</li>
                  <li>• تشفير متقدم للبيانات</li>
                </ul>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">⚙️ إعدادات النظام</h4>
                <div className="space-y-2">
                  <a
                    href="/security"
                    className="block w-full text-center bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
                  >
                    إدارة المصادقة الثنائية
                  </a>
                  <button className="block w-full text-center bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700">
                    عرض سجل الدخول
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              حالة النظام
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">✓</div>
                <div className="text-sm text-gray-600">Backend API</div>
                <div className="text-xs text-gray-500">http://localhost:7000</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">✓</div>
                <div className="text-sm text-gray-600">Frontend</div>
                <div className="text-xs text-gray-500">http://localhost:3000</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">✓</div>
                <div className="text-sm text-gray-600">Database</div>
                <div className="text-xs text-gray-500">SQLite</div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
