'use client'

import { useEffect } from 'react'

export default function DashboardPage() {
  useEffect(() => {
    // Redirect to the main dashboard
    window.location.href = '/main-dashboard.html'
  }, [])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>جاري التوجيه إلى نظام إدارة الشحنات...</p>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">
              لوحة التحكم - نظام إدارة الشحنات
            </h1>
            <div className="flex items-center space-x-4 space-x-reverse">
              <a
                href="/security"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm"
              >
                إعدادات الأمان
              </a>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>


}
