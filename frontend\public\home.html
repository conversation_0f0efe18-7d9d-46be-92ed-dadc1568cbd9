<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة الشحن السريع | الصفحة الرئيسية</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* تحسين تحميل الخطوط */
        @font-display: swap;

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .top-bar {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.9rem;
        }

        .top-bar .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .contact-info {
            display: flex;
            gap: 30px;
        }

        .contact-info span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .language-switch {
            display: flex;
            gap: 10px;
        }

        .language-switch a {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 15px;
            transition: background 0.3s;
        }

        .language-switch a:hover,
        .language-switch a.active {
            background: rgba(255,255,255,0.2);
        }

        .main-nav {
            padding: 15px 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .logo-icon {
            background: white;
            color: #667eea;
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 40px;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            position: relative;
        }

        .nav-menu a:hover {
            color: #ffd700;
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: #ffd700;
            transition: width 0.3s;
        }

        .nav-menu a:hover::after {
            width: 100%;
        }

        .cta-buttons {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .btn-primary {
            background: #ffd700;
            color: #333;
        }

        .btn-primary:hover {
            background: #ffed4e;
            transform: translateY(-2px);
        }

        .btn-outline {
            border: 2px solid white;
            color: white;
        }

        .btn-outline:hover {
            background: white;
            color: #667eea;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 150px 0 100px;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="0,0 1000,100 1000,0"/></svg>');
            background-size: cover;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 700;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .hero .btn {
            font-size: 1.1rem;
            padding: 15px 35px;
        }

        /* Tracking Section */
        .tracking-section {
            background: white;
            padding: 80px 0;
            margin-top: -50px;
            position: relative;
            z-index: 3;
        }

        .tracking-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .tracking-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            text-align: center;
        }

        .tracking-card h2 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .tracking-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .tracking-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 50px;
            font-size: 1rem;
            text-align: center;
            transition: border-color 0.3s;
        }

        .tracking-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .tracking-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.3s;
        }

        .tracking-btn:hover {
            transform: translateY(-2px);
        }

        /* Services Section */
        .services {
            background: #f8f9fa;
            padding: 100px 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* تحسين عرض النصوص */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.2;
        }

        p, span, div {
            line-height: 1.6;
        }

        /* تحسين الأزرار */
        button, .btn {
            font-family: inherit;
            font-weight: 500;
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-header h2 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }

        .section-header p {
            font-size: 1.2rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .service-card {
            background: white;
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .service-card:hover {
            transform: translateY(-10px);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }

        .service-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        .service-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Stats Section */
        .stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 80px 0;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 3rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .stat-item p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Footer */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 60px 0 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h3 {
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 10px;
        }

        .footer-section ul li a {
            color: #bdc3c7;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-section ul li a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #34495e;
            padding-top: 20px;
            text-align: center;
            color: #bdc3c7;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .tracking-form {
                flex-direction: column;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .contact-info {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="top-bar">
            <div class="container">
                <div class="contact-info">
                    <span>📞 +966 11 234 5678</span>
                    <span>✉️ <EMAIL></span>
                </div>
                <div class="language-switch">
                    <a href="#" class="active">العربية</a>
                    <a href="#">English</a>
                </div>
            </div>
        </div>
        
        <nav class="main-nav">
            <div class="nav-container">
                <div class="logo">
                    <div class="logo-icon">🚚</div>
                    <span>شركة الشحن السريع</span>
                </div>
                
                <ul class="nav-menu">
                    <li><a href="#home">الرئيسية</a></li>
                    <li><a href="#services">خدماتنا</a></li>
                    <li><a href="#tracking">تتبع الشحنة</a></li>
                    <li><a href="about-us.html">من نحن</a></li>
                    <li><a href="success-partners.html">شركاء النجاح</a></li>
                    <li><a href="#contact">اتصل بنا</a></li>
                </ul>
                
                <div class="cta-buttons">
                    <a href="index.html" class="btn btn-outline">تسجيل الدخول</a>
                    <a href="#quote" class="btn btn-primary">احصل على عرض سعر</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>شحن سريع وآمن في جميع أنحاء المملكة</h1>
            <p>نوفر خدمات الشحن والتوصيل السريع مع ضمان الأمان والجودة. شريكك الموثوق في عالم الشحن والخدمات اللوجستية</p>
            <div class="hero-buttons">
                <a href="#services" class="btn btn-primary">اكتشف خدماتنا</a>
                <a href="#tracking" class="btn btn-outline">تتبع شحنتك</a>
            </div>
        </div>
    </section>

    <!-- Tracking Section -->
    <section class="tracking-section" id="tracking">
        <div class="tracking-container">
            <div class="tracking-card">
                <h2>🔍 تتبع شحنتك</h2>
                <p>أدخل رقم الشحنة لمعرفة موقعها الحالي</p>
                <div class="tracking-form">
                    <input type="text" class="tracking-input" placeholder="أدخل رقم الشحنة (مثال: SHP001)">
                    <button class="tracking-btn">تتبع الآن</button>
                </div>
                <p style="font-size: 0.9rem; color: #666;">يمكنك تتبع عدة شحنات بفصلها بفاصلة</p>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <h2>خدماتنا المتميزة</h2>
                <p>نقدم مجموعة شاملة من خدمات الشحن والتوصيل لتلبية جميع احتياجاتكم</p>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🚚</div>
                    <h3>الشحن السريع</h3>
                    <p>خدمة توصيل سريعة خلال 24-48 ساعة داخل المملكة مع ضمان الأمان والجودة</p>
                </div>

                <div class="service-card">
                    <div class="service-icon">🌍</div>
                    <h3>الشحن الدولي</h3>
                    <p>شحن إلى جميع دول العالم مع أفضل الأسعار وأسرع أوقات التسليم</p>
                </div>

                <div class="service-card">
                    <div class="service-icon">📦</div>
                    <h3>التغليف الآمن</h3>
                    <p>خدمات تغليف احترافية لضمان وصول شحنتك بأفضل حالة</p>
                </div>

                <div class="service-card">
                    <div class="service-icon">💰</div>
                    <h3>الدفع عند الاستلام</h3>
                    <p>خدمة الدفع عند الاستلام مع ضمان تحصيل المبالغ وتحويلها</p>
                </div>

                <div class="service-card">
                    <div class="service-icon">📱</div>
                    <h3>تتبع مباشر</h3>
                    <p>تتبع شحنتك لحظة بلحظة عبر موقعنا أو تطبيق الهاتف</p>
                </div>

                <div class="service-card">
                    <div class="service-icon">🏪</div>
                    <h3>خدمات التجارة الإلكترونية</h3>
                    <p>حلول شاملة لمتاجر التجارة الإلكترونية مع أسعار مخفضة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>50,000+</h3>
                    <p>شحنة شهرياً</p>
                </div>
                <div class="stat-item">
                    <h3>99.5%</h3>
                    <p>معدل التسليم الناجح</p>
                </div>
                <div class="stat-item">
                    <h3>24/7</h3>
                    <p>خدمة العملاء</p>
                </div>
                <div class="stat-item">
                    <h3>15+</h3>
                    <p>مدينة في المملكة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>شركة الشحن السريع</h3>
                    <p>شريكك الموثوق في عالم الشحن والخدمات اللوجستية. نقدم خدمات متميزة منذ أكثر من 10 سنوات.</p>
                    <div style="margin-top: 20px;">
                        <span style="margin-left: 15px;">📞 +966 11 234 5678</span>
                        <span>✉️ <EMAIL></span>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>خدماتنا</h3>
                    <ul>
                        <li><a href="#">الشحن المحلي</a></li>
                        <li><a href="#">الشحن الدولي</a></li>
                        <li><a href="#">الدفع عند الاستلام</a></li>
                        <li><a href="#">التجارة الإلكترونية</a></li>
                        <li><a href="#">الشحن السريع</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>روابط مهمة</h3>
                    <ul>
                        <li><a href="#">تتبع الشحنة</a></li>
                        <li><a href="#">احسب التكلفة</a></li>
                        <li><a href="#">مواقع الفروع</a></li>
                        <li><a href="#">الشروط والأحكام</a></li>
                        <li><a href="#">سياسة الخصوصية</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <ul>
                        <li><a href="#">خدمة العملاء</a></li>
                        <li><a href="#">الشكاوى والاقتراحات</a></li>
                        <li><a href="#">وظائف شاغرة</a></li>
                        <li><a href="#">شراكات تجارية</a></li>
                        <li><a href="index.html">نظام الإدارة</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 شركة الشحن السريع. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Tracking functionality
        function initializeTracking() {
            const trackingBtn = document.querySelector('.tracking-btn');
            const trackingInput = document.querySelector('.tracking-input');

            if (!trackingBtn || !trackingInput) {
                console.warn('عناصر التتبع غير موجودة');
                return;
            }

            trackingBtn.addEventListener('click', function() {
                const trackingNumber = trackingInput.value.trim();

                if (!trackingNumber) {
                    alert('يرجى إدخال رقم الشحنة');
                    return;
                }

                // Show loading state
                const originalText = this.textContent;
                this.textContent = 'جاري البحث...';
                this.disabled = true;

                // Simulate tracking
                setTimeout(() => {
                    try {
                        window.location.href = `shipment-tracking.html?tracking=${trackingNumber}`;
                    } catch (error) {
                        console.error('خطأ في التنقل:', error);
                        this.textContent = originalText;
                        this.disabled = false;
                        alert('حدث خطأ، يرجى المحاولة مرة أخرى');
                    }
                }, 1000);
            });
        }

        // Add enter key support for tracking input
        function initializeEnterKeySupport() {
            const trackingInput = document.querySelector('.tracking-input');
            const trackingBtn = document.querySelector('.tracking-btn');

            if (!trackingInput || !trackingBtn) {
                console.warn('عناصر التتبع غير موجودة للـ Enter key support');
                return;
            }

            trackingInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    trackingBtn.click();
                }
            });
        }

        // Animate stats on scroll
        function animateStats() {
            const stats = document.querySelectorAll('.stat-item h3');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target;
                        const finalValue = target.textContent;
                        const numericValue = parseInt(finalValue.replace(/[^0-9]/g, ''));

                        if (numericValue) {
                            let current = 0;
                            const increment = numericValue / 50;
                            const timer = setInterval(() => {
                                current += increment;
                                if (current >= numericValue) {
                                    target.textContent = finalValue;
                                    clearInterval(timer);
                                } else {
                                    target.textContent = Math.floor(current) + finalValue.replace(/[0-9]/g, '');
                                }
                            }, 30);
                        }
                        observer.unobserve(target);
                    }
                });
            });

            stats.forEach(stat => observer.observe(stat));
        }

        // Initialize all functionality when page loads
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Initialize tracking functionality
                initializeTracking();

                // Initialize enter key support
                initializeEnterKeySupport();

                // Initialize stats animation
                animateStats();

                console.log('الصفحة الرئيسية تم تحميلها بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل الصفحة:', error);

                // Fallback: try to initialize basic functionality
                setTimeout(() => {
                    try {
                        initializeTracking();
                        initializeEnterKeySupport();
                    } catch (fallbackError) {
                        console.error('خطأ في التهيئة الاحتياطية:', fallbackError);
                    }
                }, 1000);
            }
        });

        // Mobile menu toggle (if needed)
        function toggleMobileMenu() {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.toggle('active');
        }
    </script>
</body>
</html>
