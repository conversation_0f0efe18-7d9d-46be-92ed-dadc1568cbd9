<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المناديب والموزعين - نظام إدارة الشحنات</title>
    
    <!-- Apple SF Pro AR Display Font -->
    <style>
        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            color: #7f8c8d;
            font-family: 'SF Pro AR Display', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-box {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 12px 15px;
            flex: 1;
            max-width: 400px;
        }

        .search-box input {
            border: none;
            background: transparent;
            outline: none;
            font-family: 'SF Pro AR Display', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            color: #2c3e50;
        }

        .search-box input::placeholder {
            color: #95a5a6;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-family: 'SF Pro AR Display', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .distributors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .distributor-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-right: 5px solid #667eea;
        }

        .distributor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .distributor-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .distributor-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 15px;
        }

        .distributor-info h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .distributor-info p {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .distributor-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .detail-value {
            color: #2c3e50;
            font-weight: bold;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .distributor-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .distributor-actions .btn {
            flex: 1;
            min-width: 80px;
            justify-content: center;
            font-size: 0.9rem;
            padding: 8px 15px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px 30px;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #ecf0f1;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            border-radius: 0 0 20px 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-family: 'SF Pro AR Display', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        /* Performance Tab Styles */
        .performance-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .performance-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .performance-card.full-width {
            grid-column: 1 / -1;
        }

        .performance-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }

        .performers-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .performer-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #667eea;
        }

        .performer-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .performer-rank {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .performance-stats {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .performance-table-container {
            overflow-x: auto;
        }

        .performance-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
        }

        .performance-table th,
        .performance-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .performance-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .performance-table tr:hover {
            background: #f8f9fa;
        }

        /* Commissions Tab Styles */
        .commission-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .summary-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            color: white;
        }

        .summary-amount {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .summary-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .commissions-table-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow-x: auto;
        }

        .commissions-table {
            width: 100%;
            border-collapse: collapse;
        }

        .commissions-table th,
        .commissions-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .commissions-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .commission-history {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .commission-history h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .history-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #28a745;
        }

        /* Reports Tab Styles */
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .report-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .report-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            color: white;
        }

        .report-info h3 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.2rem;
        }

        .report-info p {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .report-stats {
            color: #667eea;
            font-weight: bold;
        }

        .detailed-report {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }

        .report-header h3 {
            color: #2c3e50;
            margin: 0;
        }

        .report-content {
            min-height: 200px;
        }

        /* Pay Commissions Modal Styles */
        .selected-distributors {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .selected-distributors h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .distributor-payment-item {
            margin-bottom: 10px;
        }

        .distributor-payment-item input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.2);
        }

        .payment-summary {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #667eea;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .summary-item span:last-child {
            color: #27ae60;
            font-size: 1.3rem;
        }

        /* Action Buttons Styles */
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            border: none;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e67e22, #d35400);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #7f8c8d, #34495e);
            transform: translateY(-2px);
        }

        /* Table Action Buttons */
        table .btn {
            min-width: 35px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        table .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .distributors-grid {
                grid-template-columns: 1fr;
            }

            .action-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                max-width: none;
            }

            .modal-content {
                width: 95%;
                margin: 5% auto;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .modal-header {
                padding: 20px;
            }

            .modal-body {
                padding: 20px;
            }

            .modal-footer {
                padding: 15px 20px;
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🚚 إدارة المناديب والموزعين</h1>
            <p>إدارة شاملة لفريق التوزيع والمناديب</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number" id="totalDistributors">0</div>
                <div class="stat-label">إجمالي المناديب</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-number" id="activeDistributors">0</div>
                <div class="stat-label">المناديب النشطين</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📦</div>
                <div class="stat-number" id="totalDeliveries">0</div>
                <div class="stat-label">إجمالي التوصيلات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-number" id="totalCommissions">0</div>
                <div class="stat-label">إجمالي العمولات</div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="tabs">
            <button class="tab-btn active" onclick="showTab('distributors')">📋 قائمة المناديب</button>
            <button class="tab-btn" onclick="showTab('performance')">📊 الأداء</button>
            <button class="tab-btn" onclick="showTab('commissions')">💰 العمولات</button>
            <button class="tab-btn" onclick="showTab('reports')">📈 التقارير</button>
        </div>

        <!-- Distributors Tab -->
        <div id="distributors" class="tab-content active">
            <div class="action-bar">
                <div class="search-box">
                    <span>🔍</span>
                    <input type="text" placeholder="البحث عن مندوب..." id="searchInput" onkeyup="searchDistributors()">
                </div>
                <button class="btn btn-primary" onclick="showAddDistributorModal()">
                    ➕ إضافة مندوب جديد
                </button>
            </div>

            <div class="distributors-grid" id="distributorsGrid">
                <!-- سيتم تحميل المناديب هنا -->
            </div>
        </div>

        <!-- Performance Tab -->
        <div id="performance" class="tab-content">
            <div class="action-bar">
                <h2 style="color: #2c3e50; margin: 0;">📊 تقرير الأداء</h2>
                <div style="display: flex; gap: 15px;">
                    <select id="performancePeriod" onchange="loadPerformanceData()" style="padding: 10px; border-radius: 8px; border: 2px solid #ecf0f1;">
                        <option value="week">هذا الأسبوع</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="quarter">هذا الربع</option>
                        <option value="year">هذا العام</option>
                    </select>
                    <button class="btn btn-primary" onclick="exportPerformanceReport()">📄 تصدير التقرير</button>
                </div>
            </div>

            <!-- Performance Charts -->
            <div class="performance-grid">
                <div class="performance-card">
                    <h3>🏆 أفضل المناديب أداءً</h3>
                    <div id="topPerformers" class="performers-list">
                        <!-- سيتم تحميل البيانات هنا -->
                    </div>
                </div>

                <div class="performance-card">
                    <h3>📈 إحصائيات الأداء</h3>
                    <div class="performance-stats">
                        <div class="stat-item">
                            <span class="stat-label">متوسط التوصيلات اليومية:</span>
                            <span class="stat-value" id="avgDailyDeliveries">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">متوسط معدل النجاح:</span>
                            <span class="stat-value" id="avgSuccessRate">0%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">متوسط التقييم:</span>
                            <span class="stat-value" id="avgRating">0/5</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">إجمالي العمولات المدفوعة:</span>
                            <span class="stat-value" id="totalPaidCommissions">0 ريال</span>
                        </div>
                    </div>
                </div>

                <div class="performance-card full-width">
                    <h3>📊 تفاصيل أداء المناديب</h3>
                    <div class="performance-table-container">
                        <table class="performance-table" id="performanceTable">
                            <thead>
                                <tr>
                                    <th>المندوب</th>
                                    <th>التوصيلات</th>
                                    <th>معدل النجاح</th>
                                    <th>التقييم</th>
                                    <th>العمولات</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commissions Tab -->
        <div id="commissions" class="tab-content">
            <div class="action-bar">
                <h2 style="color: #2c3e50; margin: 0;">💰 إدارة العمولات</h2>
                <div style="display: flex; gap: 15px;">
                    <select id="commissionPeriod" onchange="loadCommissionsData()" style="padding: 10px; border-radius: 8px; border: 2px solid #ecf0f1;">
                        <option value="current">الشهر الحالي</option>
                        <option value="last">الشهر الماضي</option>
                        <option value="quarter">هذا الربع</option>
                        <option value="year">هذا العام</option>
                    </select>
                    <button class="btn btn-success" onclick="showPayCommissionsModal()">💳 دفع العمولات</button>
                    <button class="btn btn-primary" onclick="exportCommissionsReport()">📄 تصدير التقرير</button>
                </div>
            </div>

            <!-- Commission Summary -->
            <div class="commission-summary">
                <div class="summary-card">
                    <div class="summary-icon">💰</div>
                    <div class="summary-info">
                        <div class="summary-amount" id="totalCommissions">0 ريال</div>
                        <div class="summary-label">إجمالي العمولات</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">✅</div>
                    <div class="summary-info">
                        <div class="summary-amount" id="paidCommissions">0 ريال</div>
                        <div class="summary-label">العمولات المدفوعة</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">⏳</div>
                    <div class="summary-info">
                        <div class="summary-amount" id="pendingCommissions">0 ريال</div>
                        <div class="summary-label">العمولات المعلقة</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">📊</div>
                    <div class="summary-info">
                        <div class="summary-amount" id="avgCommissionRate">0%</div>
                        <div class="summary-label">متوسط نسبة العمولة</div>
                    </div>
                </div>
            </div>

            <!-- Commissions Table -->
            <div class="commissions-table-container">
                <table class="commissions-table" id="commissionsTable">
                    <thead>
                        <tr>
                            <th>المندوب</th>
                            <th>التوصيلات</th>
                            <th>نسبة العمولة</th>
                            <th>العمولة المستحقة</th>
                            <th>العمولة المدفوعة</th>
                            <th>المتبقي</th>
                            <th>آخر دفعة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تحميل البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- Commission History -->
            <div class="commission-history">
                <h3>📋 سجل دفع العمولات</h3>
                <div id="commissionHistory" class="history-list">
                    <!-- سيتم تحميل السجل هنا -->
                </div>
            </div>
        </div>

        <!-- Reports Tab -->
        <div id="reports" class="tab-content">
            <div class="action-bar">
                <h2 style="color: #2c3e50; margin: 0;">📈 التقارير التفصيلية</h2>
                <div style="display: flex; gap: 15px;">
                    <select id="reportType" onchange="loadReportData()" style="padding: 10px; border-radius: 8px; border: 2px solid #ecf0f1;">
                        <option value="all">جميع التقارير</option>
                        <option value="daily">التقرير اليومي</option>
                        <option value="weekly">التقرير الأسبوعي</option>
                        <option value="monthly" selected>التقرير الشهري</option>
                        <option value="yearly">التقرير السنوي</option>
                        <option value="performance">تقارير الأداء</option>
                        <option value="financial">التقارير المالية</option>
                        <option value="regional">التقارير الجغرافية</option>
                        <option value="custom">تقرير مخصص شامل</option>
                    </select>
                    <input type="date" id="reportDate" onchange="loadReportData()" style="padding: 10px; border-radius: 8px; border: 2px solid #ecf0f1;">
                    <button class="btn btn-primary" onclick="generateDetailedReport()">📊 إنشاء تقرير</button>
                    <button class="btn btn-success" onclick="exportAllReports()">📤 تصدير جميع التقارير</button>
                </div>
            </div>

            <!-- Report Cards -->
            <div class="reports-grid">
                <div class="report-card" onclick="showDistributorReport()">
                    <div class="report-icon">👥</div>
                    <div class="report-info">
                        <h3>تقرير المناديب</h3>
                        <p>تفاصيل أداء وإحصائيات المناديب</p>
                        <div class="report-stats">
                            <span id="reportDistributorsCount">0 مندوب</span>
                        </div>
                    </div>
                </div>

                <div class="report-card" onclick="showDeliveryReport()">
                    <div class="report-icon">📦</div>
                    <div class="report-info">
                        <h3>تقرير التوصيلات</h3>
                        <p>إحصائيات التوصيلات ومعدلات النجاح</p>
                        <div class="report-stats">
                            <span id="reportDeliveriesCount">0 توصيلة</span>
                        </div>
                    </div>
                </div>

                <div class="report-card" onclick="showFinancialReport()">
                    <div class="report-icon">💰</div>
                    <div class="report-info">
                        <h3>التقرير المالي</h3>
                        <p>العمولات والمدفوعات والأرباح</p>
                        <div class="report-stats">
                            <span id="reportFinancialAmount">0 ريال</span>
                        </div>
                    </div>
                </div>

                <div class="report-card" onclick="showPerformanceReport()">
                    <div class="report-icon">📊</div>
                    <div class="report-info">
                        <h3>تقرير الأداء</h3>
                        <p>تحليل شامل لأداء الفريق</p>
                        <div class="report-stats">
                            <span id="reportPerformanceScore">0%</span>
                        </div>
                    </div>
                </div>

                <div class="report-card" onclick="showRegionalReport()">
                    <div class="report-icon">🗺️</div>
                    <div class="report-info">
                        <h3>التقرير الجغرافي</h3>
                        <p>توزيع التوصيلات حسب المناطق</p>
                        <div class="report-stats">
                            <span id="reportRegionsCount">0 منطقة</span>
                        </div>
                    </div>
                </div>

                <div class="report-card" onclick="showVehicleReport()">
                    <div class="report-icon">🚗</div>
                    <div class="report-info">
                        <h3>تقرير المركبات</h3>
                        <p>استخدام وكفاءة المركبات</p>
                        <div class="report-stats">
                            <span id="reportVehiclesCount">0 مركبة</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Report Display -->
            <div id="detailedReportContainer" class="detailed-report" style="display: none;">
                <div class="report-header">
                    <h3 id="detailedReportTitle">التقرير التفصيلي</h3>
                    <button class="btn btn-secondary" onclick="hideDetailedReport()">❌ إغلاق</button>
                </div>
                <div id="detailedReportContent" class="report-content">
                    <!-- سيتم تحميل محتوى التقرير هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لإضافة مندوب جديد -->
    <div id="addDistributorModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>➕ إضافة مندوب جديد</h2>
                <span class="close" onclick="closeAddDistributorModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addDistributorForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="distributorName">👤 اسم المندوب *</label>
                            <input type="text" id="distributorName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="distributorPhone">📱 رقم الهاتف *</label>
                            <input type="tel" id="distributorPhone" name="phone" required placeholder="+966xxxxxxxxx">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="distributorEmail">📧 البريد الإلكتروني</label>
                            <input type="email" id="distributorEmail" name="email" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="distributorArea">📍 منطقة العمل *</label>
                            <select id="distributorArea" name="area" required>
                                <option value="">اختر المنطقة</option>
                                <option value="الرياض - حي العليا">الرياض - حي العليا</option>
                                <option value="الرياض - حي النخيل">الرياض - حي النخيل</option>
                                <option value="الرياض - حي الملز">الرياض - حي الملز</option>
                                <option value="جدة - حي الروضة">جدة - حي الروضة</option>
                                <option value="جدة - حي البلد">جدة - حي البلد</option>
                                <option value="جدة - حي الحمراء">جدة - حي الحمراء</option>
                                <option value="الدمام - حي الفيصلية">الدمام - حي الفيصلية</option>
                                <option value="الدمام - حي الشاطئ">الدمام - حي الشاطئ</option>
                                <option value="مكة المكرمة - العزيزية">مكة المكرمة - العزيزية</option>
                                <option value="مكة المكرمة - العوالي">مكة المكرمة - العوالي</option>
                                <option value="المدينة المنورة - العنابس">المدينة المنورة - العنابس</option>
                                <option value="الكويت - حولي">الكويت - حولي</option>
                                <option value="الكويت - الجهراء">الكويت - الجهراء</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="vehicleType">🚗 نوع المركبة *</label>
                            <select id="vehicleType" name="vehicleType" required>
                                <option value="">اختر نوع المركبة</option>
                                <option value="دراجة نارية">دراجة نارية</option>
                                <option value="سيارة صغيرة">سيارة صغيرة</option>
                                <option value="سيارة متوسطة">سيارة متوسطة</option>
                                <option value="شاحنة صغيرة">شاحنة صغيرة</option>
                                <option value="شاحنة متوسطة">شاحنة متوسطة</option>
                                <option value="شاحنة كبيرة">شاحنة كبيرة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="vehicleNumber">🔢 رقم المركبة *</label>
                            <input type="text" id="vehicleNumber" name="vehicleNumber" required placeholder="أ ب ج 1234">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="nationalId">🆔 رقم الهوية الوطنية *</label>
                            <input type="text" id="nationalId" name="nationalId" required placeholder="1xxxxxxxxx">
                        </div>
                        <div class="form-group">
                            <label for="licenseNumber">🪪 رقم رخصة القيادة *</label>
                            <input type="text" id="licenseNumber" name="licenseNumber" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="salary">💰 الراتب الأساسي (ريال)</label>
                            <input type="number" id="salary" name="salary" min="0" placeholder="3000">
                        </div>
                        <div class="form-group">
                            <label for="commissionRate">📊 نسبة العمولة (%)</label>
                            <input type="number" id="commissionRate" name="commissionRate" min="0" max="100" step="0.1" placeholder="5.0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="joinDate">📅 تاريخ الانضمام *</label>
                            <input type="date" id="joinDate" name="joinDate" required>
                        </div>
                        <div class="form-group">
                            <label for="status">✅ الحالة</label>
                            <select id="status" name="status">
                                <option value="نشط">نشط</option>
                                <option value="غير نشط">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group full-width">
                        <label for="notes">📝 ملاحظات</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeAddDistributorModal()">❌ إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitAddDistributor()">✅ إضافة المندوب</button>
            </div>
        </div>
    </div>

    <!-- Pay Commissions Modal -->
    <div id="payCommissionsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>💳 دفع العمولات</h2>
                <span class="close" onclick="closePayCommissionsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="payCommissionsForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="paymentMethod">طريقة الدفع *</label>
                            <select id="paymentMethod" name="paymentMethod" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="cash">نقداً</option>
                                <option value="check">شيك</option>
                                <option value="digital_wallet">محفظة رقمية</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="paymentDate">تاريخ الدفع *</label>
                            <input type="date" id="paymentDate" name="paymentDate" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="paymentNotes">ملاحظات الدفع</label>
                        <textarea id="paymentNotes" name="paymentNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                    </div>

                    <div class="selected-distributors" id="selectedDistributors">
                        <h3>المناديب المحددين للدفع:</h3>
                        <div id="distributorsList">
                            <!-- سيتم تحميل قائمة المناديب هنا -->
                        </div>
                        <div class="payment-summary">
                            <div class="summary-item">
                                <span>إجمالي المبلغ:</span>
                                <span id="totalPaymentAmount">0 ريال</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closePayCommissionsModal()">❌ إلغاء</button>
                <button type="button" class="btn btn-success" onclick="processPayment()">💳 تأكيد الدفع</button>
            </div>
        </div>
    </div>

    <script src="js/database.js" onload="console.log('✅ ملف database.js تم تحميله')" onerror="console.error('❌ فشل في تحميل ملف database.js')"></script>
    <script>
        // التحقق من تحميل قاعدة البيانات
        function checkDatabase() {
            if (typeof db === 'undefined') {
                console.warn('⚠️ قاعدة البيانات الرئيسية غير محملة، استخدام النسخة الاحتياطية...');
                createFallbackDatabase();
                return true;
            }
            return true;
        }

        // قاعدة بيانات احتياطية للمناديب
        function createFallbackDatabase() {
            window.db = window.db || {};
            
            // إضافة وظائف المناديب إلى قاعدة البيانات
            window.db.getAllDistributors = function() {
                const distributors = localStorage.getItem('distributors');
                if (distributors) {
                    return JSON.parse(distributors);
                } else {
                    const defaultDistributors = [
                        {
                            id: 1,
                            name: 'أحمد محمد السعد',
                            phone: '+966501234567',
                            email: '<EMAIL>',
                            area: 'الرياض - حي العليا',
                            vehicleType: 'سيارة صغيرة',
                            vehicleNumber: 'أ ب ج 1234',
                            status: 'نشط',
                            joinDate: '2024-01-15',
                            totalDeliveries: 245,
                            successRate: 98.5,
                            totalCommissions: 12500,
                            rating: 4.8
                        },
                        {
                            id: 2,
                            name: 'فاطمة أحمد الزهراني',
                            phone: '+966502345678',
                            email: '<EMAIL>',
                            area: 'جدة - حي الروضة',
                            vehicleType: 'دراجة نارية',
                            vehicleNumber: 'د ه و 5678',
                            status: 'نشط',
                            joinDate: '2024-02-01',
                            totalDeliveries: 189,
                            successRate: 97.2,
                            totalCommissions: 9450,
                            rating: 4.6
                        },
                        {
                            id: 3,
                            name: 'محمد علي القحطاني',
                            phone: '+966503456789',
                            email: '<EMAIL>',
                            area: 'الدمام - حي الفيصلية',
                            vehicleType: 'شاحنة صغيرة',
                            vehicleNumber: 'ز ح ط 9012',
                            status: 'نشط',
                            joinDate: '2024-01-20',
                            totalDeliveries: 312,
                            successRate: 99.1,
                            totalCommissions: 15600,
                            rating: 4.9
                        },
                        {
                            id: 4,
                            name: 'نورا سعد المطيري',
                            phone: '+966504567890',
                            email: '<EMAIL>',
                            area: 'مكة المكرمة - العزيزية',
                            vehicleType: 'سيارة متوسطة',
                            vehicleNumber: 'ي ك ل 3456',
                            status: 'غير نشط',
                            joinDate: '2024-03-10',
                            totalDeliveries: 67,
                            successRate: 95.5,
                            totalCommissions: 3350,
                            rating: 4.3
                        }
                    ];
                    localStorage.setItem('distributors', JSON.stringify(defaultDistributors));
                    return defaultDistributors;
                }
            };
            
            console.log('✅ تم إنشاء قاعدة بيانات المناديب الاحتياطية');
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚚 تحميل صفحة إدارة المناديب...');

            setTimeout(() => {
                checkDatabase();
                loadDistributors();
                updateStatistics();
                console.log('✅ تم تحميل الصفحة بنجاح');
            }, 100);
        });

        // تبديل التبويبات
        function showTab(tabId) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabId).classList.add('active');

            // إضافة الفئة النشطة للزر المحدد
            event.target.classList.add('active');
        }

        // تحميل المناديب
        function loadDistributors() {
            try {
                const distributors = db.getAllDistributors();
                const grid = document.getElementById('distributorsGrid');
                grid.innerHTML = '';

                distributors.forEach(distributor => {
                    const card = createDistributorCard(distributor);
                    grid.appendChild(card);
                });
            } catch (error) {
                console.error('خطأ في تحميل المناديب:', error);
                alert('خطأ في تحميل المناديب: ' + error.message);
            }
        }

        // إنشاء بطاقة مندوب
        function createDistributorCard(distributor) {
            const card = document.createElement('div');
            card.className = 'distributor-card';
            card.innerHTML = `
                <div class="distributor-header">
                    <div class="distributor-avatar">
                        ${distributor.name.charAt(0)}
                    </div>
                    <div class="distributor-info">
                        <h3>${distributor.name}</h3>
                        <p>${distributor.area}</p>
                    </div>
                </div>

                <div class="distributor-details">
                    <div class="detail-row">
                        <span class="detail-label">📱 الهاتف:</span>
                        <span class="detail-value">${distributor.phone}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">🚗 نوع المركبة:</span>
                        <span class="detail-value">${distributor.vehicleType}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">🔢 رقم المركبة:</span>
                        <span class="detail-value">${distributor.vehicleNumber}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">📦 التوصيلات:</span>
                        <span class="detail-value">${distributor.totalDeliveries}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">✅ معدل النجاح:</span>
                        <span class="detail-value">${distributor.successRate}%</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">⭐ التقييم:</span>
                        <span class="detail-value">${distributor.rating}/5</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">الحالة:</span>
                        <span class="status-badge ${distributor.status === 'نشط' ? 'status-active' : 'status-inactive'}">
                            ${distributor.status}
                        </span>
                    </div>
                </div>

                <div class="distributor-actions">
                    <button class="btn btn-warning" onclick="editDistributor(${distributor.id})">
                        ✏️ تعديل
                    </button>
                    <button class="btn ${distributor.status === 'نشط' ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleDistributorStatus(${distributor.id})">
                        ${distributor.status === 'نشط' ? '❌ إيقاف' : '✅ تفعيل'}
                    </button>
                    <button class="btn btn-primary" onclick="viewDistributorDetails(${distributor.id})">
                        👁️ عرض
                    </button>
                </div>
            `;
            return card;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            try {
                const distributors = db.getAllDistributors();

                const totalDistributors = distributors.length;
                const activeDistributors = distributors.filter(d => d.status === 'نشط').length;
                const totalDeliveries = distributors.reduce((sum, d) => sum + d.totalDeliveries, 0);
                const totalCommissions = distributors.reduce((sum, d) => sum + d.totalCommissions, 0);

                document.getElementById('totalDistributors').textContent = totalDistributors;
                document.getElementById('activeDistributors').textContent = activeDistributors;
                document.getElementById('totalDeliveries').textContent = totalDeliveries.toLocaleString();
                document.getElementById('totalCommissions').textContent = totalCommissions.toLocaleString() + ' ريال';
            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // البحث في المناديب
        function searchDistributors() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const distributors = db.getAllDistributors();

            const filteredDistributors = distributors.filter(distributor =>
                distributor.name.toLowerCase().includes(searchTerm) ||
                distributor.phone.includes(searchTerm) ||
                distributor.area.toLowerCase().includes(searchTerm) ||
                distributor.vehicleNumber.toLowerCase().includes(searchTerm)
            );

            const grid = document.getElementById('distributorsGrid');
            grid.innerHTML = '';

            filteredDistributors.forEach(distributor => {
                const card = createDistributorCard(distributor);
                grid.appendChild(card);
            });
        }

        // إظهار نافذة إضافة مندوب جديد
        function showAddDistributorModal() {
            document.getElementById('addDistributorModal').style.display = 'block';

            // تعيين تاريخ اليوم كافتراضي
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('joinDate').value = today;
        }

        // إغلاق نافذة إضافة مندوب
        function closeAddDistributorModal() {
            document.getElementById('addDistributorModal').style.display = 'none';
            document.getElementById('addDistributorForm').reset();
            resetModalToAddMode();
        }

        // إضافة مندوب جديد
        function submitAddDistributor() {
            try {
                const form = document.getElementById('addDistributorForm');
                const formData = new FormData(form);

                // التحقق من صحة البيانات
                const requiredFields = ['name', 'phone', 'area', 'vehicleType', 'vehicleNumber', 'nationalId', 'licenseNumber', 'joinDate'];
                const missingFields = [];

                requiredFields.forEach(field => {
                    if (!formData.get(field) || formData.get(field).trim() === '') {
                        missingFields.push(field);
                    }
                });

                if (missingFields.length > 0) {
                    alert('يرجى ملء جميع الحقول المطلوبة (المميزة بـ *)');
                    return;
                }

                // التحقق من صحة رقم الهاتف
                const phone = formData.get('phone');
                if (!phone.match(/^\+966[0-9]{9}$/)) {
                    alert('يرجى إدخال رقم هاتف صحيح بالصيغة: +966xxxxxxxxx');
                    return;
                }

                // التحقق من صحة رقم الهوية
                const nationalId = formData.get('nationalId');
                if (!nationalId.match(/^[12][0-9]{9}$/)) {
                    alert('يرجى إدخال رقم هوية وطنية صحيح (10 أرقام تبدأ بـ 1 أو 2)');
                    return;
                }

                // إنشاء كائن المندوب الجديد
                const distributors = db.getAllDistributors();
                const newId = Math.max(...distributors.map(d => d.id), 0) + 1;

                const newDistributor = {
                    id: newId,
                    name: formData.get('name').trim(),
                    phone: formData.get('phone').trim(),
                    email: formData.get('email').trim() || '',
                    area: formData.get('area'),
                    vehicleType: formData.get('vehicleType'),
                    vehicleNumber: formData.get('vehicleNumber').trim(),
                    nationalId: formData.get('nationalId').trim(),
                    licenseNumber: formData.get('licenseNumber').trim(),
                    salary: parseFloat(formData.get('salary')) || 0,
                    commissionRate: parseFloat(formData.get('commissionRate')) || 0,
                    joinDate: formData.get('joinDate'),
                    status: formData.get('status') || 'نشط',
                    notes: formData.get('notes').trim() || '',
                    totalDeliveries: 0,
                    successRate: 0,
                    totalCommissions: 0,
                    rating: 0
                };

                // إضافة المندوب إلى قاعدة البيانات
                distributors.push(newDistributor);
                localStorage.setItem('distributors', JSON.stringify(distributors));

                // تحديث الواجهة
                loadDistributors();
                updateStatistics();
                closeAddDistributorModal();

                alert(`✅ تم إضافة المندوب "${newDistributor.name}" بنجاح!`);

            } catch (error) {
                console.error('خطأ في إضافة المندوب:', error);
                alert('حدث خطأ أثناء إضافة المندوب. يرجى المحاولة مرة أخرى.');
            }
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const addModal = document.getElementById('addDistributorModal');
            const payModal = document.getElementById('payCommissionsModal');

            if (event.target === addModal) {
                closeAddDistributorModal();
            } else if (event.target === payModal) {
                closePayCommissionsModal();
            }
        }

        function editDistributor(id) {
            try {
                const distributors = db.getAllDistributors();
                const distributor = distributors.find(d => d.id === id);

                if (!distributor) {
                    alert('لم يتم العثور على المندوب');
                    return;
                }

                // ملء النموذج ببيانات المندوب الحالية
                document.getElementById('distributorName').value = distributor.name;
                document.getElementById('distributorPhone').value = distributor.phone;
                document.getElementById('distributorEmail').value = distributor.email || '';
                document.getElementById('distributorArea').value = distributor.area;
                document.getElementById('vehicleType').value = distributor.vehicleType;
                document.getElementById('vehicleNumber').value = distributor.vehicleNumber;
                document.getElementById('nationalId').value = distributor.nationalId || '';
                document.getElementById('licenseNumber').value = distributor.licenseNumber || '';
                document.getElementById('salary').value = distributor.salary || '';
                document.getElementById('commissionRate').value = distributor.commissionRate || '';
                document.getElementById('joinDate').value = distributor.joinDate;
                document.getElementById('status').value = distributor.status;
                document.getElementById('notes').value = distributor.notes || '';

                // تغيير عنوان النافذة وزر الحفظ
                document.querySelector('.modal-header h2').textContent = '✏️ تعديل بيانات المندوب';
                document.querySelector('.modal-footer .btn-primary').textContent = '💾 حفظ التعديلات';
                document.querySelector('.modal-footer .btn-primary').onclick = function() {
                    submitEditDistributor(id);
                };

                // إظهار النافذة
                document.getElementById('addDistributorModal').style.display = 'block';

            } catch (error) {
                console.error('خطأ في تحميل بيانات المندوب:', error);
                alert('حدث خطأ أثناء تحميل بيانات المندوب');
            }
        }

        // حفظ تعديلات المندوب
        function submitEditDistributor(id) {
            try {
                const form = document.getElementById('addDistributorForm');
                const formData = new FormData(form);

                // التحقق من صحة البيانات
                const requiredFields = ['name', 'phone', 'area', 'vehicleType', 'vehicleNumber', 'nationalId', 'licenseNumber', 'joinDate'];
                const missingFields = [];

                requiredFields.forEach(field => {
                    if (!formData.get(field) || formData.get(field).trim() === '') {
                        missingFields.push(field);
                    }
                });

                if (missingFields.length > 0) {
                    alert('يرجى ملء جميع الحقول المطلوبة (المميزة بـ *)');
                    return;
                }

                // التحقق من صحة رقم الهاتف
                const phone = formData.get('phone');
                if (!phone.match(/^\+966[0-9]{9}$/)) {
                    alert('يرجى إدخال رقم هاتف صحيح بالصيغة: +966xxxxxxxxx');
                    return;
                }

                // التحقق من صحة رقم الهوية
                const nationalId = formData.get('nationalId');
                if (!nationalId.match(/^[12][0-9]{9}$/)) {
                    alert('يرجى إدخال رقم هوية وطنية صحيح (10 أرقام تبدأ بـ 1 أو 2)');
                    return;
                }

                // تحديث بيانات المندوب
                const distributors = db.getAllDistributors();
                const distributorIndex = distributors.findIndex(d => d.id === id);

                if (distributorIndex === -1) {
                    alert('لم يتم العثور على المندوب');
                    return;
                }

                // الاحتفاظ بالبيانات الإحصائية الحالية
                const currentDistributor = distributors[distributorIndex];

                distributors[distributorIndex] = {
                    ...currentDistributor,
                    name: formData.get('name').trim(),
                    phone: formData.get('phone').trim(),
                    email: formData.get('email').trim() || '',
                    area: formData.get('area'),
                    vehicleType: formData.get('vehicleType'),
                    vehicleNumber: formData.get('vehicleNumber').trim(),
                    nationalId: formData.get('nationalId').trim(),
                    licenseNumber: formData.get('licenseNumber').trim(),
                    salary: parseFloat(formData.get('salary')) || 0,
                    commissionRate: parseFloat(formData.get('commissionRate')) || 0,
                    joinDate: formData.get('joinDate'),
                    status: formData.get('status'),
                    notes: formData.get('notes').trim() || ''
                };

                // حفظ التحديثات
                localStorage.setItem('distributors', JSON.stringify(distributors));

                // تحديث الواجهة
                loadDistributors();
                updateStatistics();
                closeAddDistributorModal();

                // إعادة تعيين النافذة للوضع الافتراضي
                resetModalToAddMode();

                alert(`✅ تم تحديث بيانات المندوب "${distributors[distributorIndex].name}" بنجاح!`);

            } catch (error) {
                console.error('خطأ في تحديث المندوب:', error);
                alert('حدث خطأ أثناء تحديث بيانات المندوب. يرجى المحاولة مرة أخرى.');
            }
        }

        // إعادة تعيين النافذة لوضع الإضافة
        function resetModalToAddMode() {
            document.querySelector('.modal-header h2').textContent = '➕ إضافة مندوب جديد';
            document.querySelector('.modal-footer .btn-primary').textContent = '✅ إضافة المندوب';
            document.querySelector('.modal-footer .btn-primary').onclick = submitAddDistributor;
        }

        function toggleDistributorStatus(id) {
            try {
                const distributors = db.getAllDistributors();
                const distributor = distributors.find(d => d.id === id);

                if (distributor) {
                    distributor.status = distributor.status === 'نشط' ? 'غير نشط' : 'نشط';
                    localStorage.setItem('distributors', JSON.stringify(distributors));
                    loadDistributors();
                    updateStatistics();

                    alert(`تم ${distributor.status === 'نشط' ? 'تفعيل' : 'إيقاف'} المندوب بنجاح`);
                }
            } catch (error) {
                console.error('خطأ في تغيير حالة المندوب:', error);
                alert('خطأ في تغيير حالة المندوب');
            }
        }

        function viewDistributorDetails(id) {
            const distributors = db.getAllDistributors();
            const distributor = distributors.find(d => d.id === id);

            if (!distributor) {
                alert('لم يتم العثور على المندوب');
                return;
            }

            const detailsHtml = `
                <div style="padding: 20px; text-align: right; direction: rtl;">
                    <h3 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">👤 تفاصيل المندوب</h3>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #3498db;">
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">📋 البيانات الأساسية</h4>
                            <p><strong>الاسم:</strong> ${distributor.name}</p>
                            <p><strong>الهاتف:</strong> ${distributor.phone}</p>
                            <p><strong>المنطقة:</strong> ${distributor.area}</p>
                            <p><strong>نوع المركبة:</strong> ${distributor.vehicleType}</p>
                            <p><strong>تاريخ الانضمام:</strong> ${distributor.joinDate}</p>
                            <p><strong>الحالة:</strong> <span style="color: ${distributor.status === 'نشط' ? '#27ae60' : '#e74c3c'};">${distributor.status}</span></p>
                        </div>

                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #27ae60;">
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">📊 إحصائيات الأداء</h4>
                            <p><strong>عدد التوصيلات:</strong> ${distributor.totalDeliveries}</p>
                            <p><strong>معدل النجاح:</strong> ${distributor.successRate}%</p>
                            <p><strong>التقييم:</strong> ${distributor.rating}/5 ⭐</p>
                            <p><strong>نقاط القوة:</strong> ${distributor.successRate > 90 ? 'ممتاز' : distributor.successRate > 80 ? 'جيد جداً' : distributor.successRate > 70 ? 'جيد' : 'يحتاج تحسين'}</p>
                        </div>

                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #f39c12;">
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">💰 المعلومات المالية</h4>
                            <p><strong>إجمالي العمولات:</strong> ${distributor.totalCommissions.toLocaleString()} ريال</p>
                            <p><strong>نسبة العمولة:</strong> ${distributor.commissionRate || 5}%</p>
                            <p><strong>العمولة المدفوعة:</strong> ${Math.round(distributor.totalCommissions * 0.8).toLocaleString()} ريال</p>
                            <p><strong>العمولة المعلقة:</strong> ${Math.round(distributor.totalCommissions * 0.2).toLocaleString()} ريال</p>
                        </div>
                    </div>
                </div>
            `;

            showDetailedReport('تفاصيل المندوب', detailsHtml);
        }

        function editDistributor(distributorId) {
            const distributor = db.getAllDistributors().find(d => d.id === distributorId);
            if (!distributor) {
                alert('لم يتم العثور على المندوب');
                return;
            }

            // إنشاء نافذة التعديل
            const editModal = document.createElement('div');
            editModal.className = 'modal';
            editModal.style.display = 'block';
            editModal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h2>✏️ تعديل بيانات المندوب</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="editDistributorForm" style="text-align: right; direction: rtl;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <div class="form-group">
                                    <label for="editName">اسم المندوب *</label>
                                    <input type="text" id="editName" value="${distributor.name}" required>
                                </div>

                                <div class="form-group">
                                    <label for="editPhone">رقم الهاتف *</label>
                                    <input type="tel" id="editPhone" value="${distributor.phone}" required>
                                </div>

                                <div class="form-group">
                                    <label for="editArea">المنطقة *</label>
                                    <select id="editArea" required>
                                        <option value="">اختر المنطقة</option>
                                        <option value="الرياض - الملز" ${distributor.area === 'الرياض - الملز' ? 'selected' : ''}>الرياض - الملز</option>
                                        <option value="الرياض - العليا" ${distributor.area === 'الرياض - العليا' ? 'selected' : ''}>الرياض - العليا</option>
                                        <option value="جدة - البلد" ${distributor.area === 'جدة - البلد' ? 'selected' : ''}>جدة - البلد</option>
                                        <option value="جدة - الروضة" ${distributor.area === 'جدة - الروضة' ? 'selected' : ''}>جدة - الروضة</option>
                                        <option value="الدمام - الفيصلية" ${distributor.area === 'الدمام - الفيصلية' ? 'selected' : ''}>الدمام - الفيصلية</option>
                                        <option value="الكويت - حولي" ${distributor.area === 'الكويت - حولي' ? 'selected' : ''}>الكويت - حولي</option>
                                        <option value="الكويت - الجهراء" ${distributor.area === 'الكويت - الجهراء' ? 'selected' : ''}>الكويت - الجهراء</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="editVehicleType">نوع المركبة *</label>
                                    <select id="editVehicleType" required>
                                        <option value="">اختر نوع المركبة</option>
                                        <option value="سيارة صغيرة" ${distributor.vehicleType === 'سيارة صغيرة' ? 'selected' : ''}>سيارة صغيرة</option>
                                        <option value="دراجة نارية" ${distributor.vehicleType === 'دراجة نارية' ? 'selected' : ''}>دراجة نارية</option>
                                        <option value="شاحنة صغيرة" ${distributor.vehicleType === 'شاحنة صغيرة' ? 'selected' : ''}>شاحنة صغيرة</option>
                                        <option value="فان" ${distributor.vehicleType === 'فان' ? 'selected' : ''}>فان</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="editCommissionRate">نسبة العمولة (%)</label>
                                    <input type="number" id="editCommissionRate" value="${distributor.commissionRate || 5}" min="1" max="20" step="0.5">
                                </div>

                                <div class="form-group">
                                    <label for="editStatus">الحالة</label>
                                    <select id="editStatus">
                                        <option value="نشط" ${distributor.status === 'نشط' ? 'selected' : ''}>نشط</option>
                                        <option value="غير نشط" ${distributor.status === 'غير نشط' ? 'selected' : ''}>غير نشط</option>
                                        <option value="متاح" ${distributor.status === 'متاح' ? 'selected' : ''}>متاح</option>
                                        <option value="مشغول" ${distributor.status === 'مشغول' ? 'selected' : ''}>مشغول</option>
                                    </select>
                                </div>
                            </div>

                            <div style="margin-top: 20px; text-align: center;">
                                <button type="submit" class="btn btn-primary" style="margin-left: 10px;">💾 حفظ التعديلات</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(editModal);

            // معالج إرسال النموذج
            document.getElementById('editDistributorForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const updatedData = {
                    name: document.getElementById('editName').value.trim(),
                    phone: document.getElementById('editPhone').value.trim(),
                    area: document.getElementById('editArea').value,
                    vehicleType: document.getElementById('editVehicleType').value,
                    commissionRate: parseFloat(document.getElementById('editCommissionRate').value) || 5,
                    status: document.getElementById('editStatus').value
                };

                // التحقق من صحة البيانات
                if (!updatedData.name || !updatedData.phone || !updatedData.area || !updatedData.vehicleType) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                // تحديث بيانات المندوب
                const distributors = db.getAllDistributors();
                const distributorIndex = distributors.findIndex(d => d.id === distributorId);

                if (distributorIndex !== -1) {
                    distributors[distributorIndex] = { ...distributors[distributorIndex], ...updatedData };
                    localStorage.setItem('distributors', JSON.stringify(distributors));

                    alert('✅ تم تحديث بيانات المندوب بنجاح!');
                    editModal.remove();

                    // إعادة تحميل البيانات
                    loadPerformanceData();
                    loadCommissionsData();
                } else {
                    alert('حدث خطأ أثناء تحديث البيانات');
                }
            });
        }

        function toggleDistributorStatus(distributorId) {
            const distributors = db.getAllDistributors();
            const distributorIndex = distributors.findIndex(d => d.id === distributorId);

            if (distributorIndex === -1) {
                alert('لم يتم العثور على المندوب');
                return;
            }

            const distributor = distributors[distributorIndex];
            const currentStatus = distributor.status;
            const newStatus = currentStatus === 'نشط' ? 'غير نشط' : 'نشط';

            const confirmMessage = currentStatus === 'نشط'
                ? `هل تريد إلغاء تفعيل المندوب "${distributor.name}"؟`
                : `هل تريد تفعيل المندوب "${distributor.name}"؟`;

            if (confirm(confirmMessage)) {
                distributors[distributorIndex].status = newStatus;
                localStorage.setItem('distributors', JSON.stringify(distributors));

                const statusMessage = newStatus === 'نشط'
                    ? `✅ تم تفعيل المندوب "${distributor.name}" بنجاح!`
                    : `⏸️ تم إلغاء تفعيل المندوب "${distributor.name}" بنجاح!`;

                alert(statusMessage);

                // إعادة تحميل البيانات
                loadPerformanceData();
                loadCommissionsData();
            }
        }

        // === وظائف تقرير الأداء ===
        function loadPerformanceData() {
            try {
                const distributors = db.getAllDistributors();
                const period = document.getElementById('performancePeriod').value;

                // تحميل أفضل المناديب
                loadTopPerformers(distributors);

                // تحميل إحصائيات الأداء
                loadPerformanceStats(distributors);

                // تحميل جدول الأداء
                loadPerformanceTable(distributors);

            } catch (error) {
                console.error('خطأ في تحميل بيانات الأداء:', error);
            }
        }

        function loadTopPerformers(distributors) {
            const topPerformers = distributors
                .sort((a, b) => b.successRate - a.successRate)
                .slice(0, 5);

            const container = document.getElementById('topPerformers');
            container.innerHTML = '';

            topPerformers.forEach((distributor, index) => {
                const item = document.createElement('div');
                item.className = 'performer-item';
                item.innerHTML = `
                    <div class="performer-info">
                        <div class="performer-rank">${index + 1}</div>
                        <div>
                            <div style="font-weight: bold; color: #2c3e50;">${distributor.name}</div>
                            <div style="color: #7f8c8d; font-size: 0.9rem;">${distributor.area}</div>
                        </div>
                    </div>
                    <div style="text-align: left;">
                        <div style="font-weight: bold; color: #27ae60;">${distributor.successRate}%</div>
                        <div style="color: #7f8c8d; font-size: 0.9rem;">${distributor.totalDeliveries} توصيلة</div>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        function loadPerformanceStats(distributors) {
            const activeDistributors = distributors.filter(d => d.status === 'نشط');

            const avgDailyDeliveries = Math.round(
                activeDistributors.reduce((sum, d) => sum + d.totalDeliveries, 0) / 30
            );

            const avgSuccessRate = (
                activeDistributors.reduce((sum, d) => sum + d.successRate, 0) / activeDistributors.length
            ).toFixed(1);

            const avgRating = (
                activeDistributors.reduce((sum, d) => sum + d.rating, 0) / activeDistributors.length
            ).toFixed(1);

            const totalPaidCommissions = activeDistributors.reduce((sum, d) => sum + d.totalCommissions, 0);

            document.getElementById('avgDailyDeliveries').textContent = avgDailyDeliveries;
            document.getElementById('avgSuccessRate').textContent = avgSuccessRate + '%';
            document.getElementById('avgRating').textContent = avgRating + '/5';
            document.getElementById('totalPaidCommissions').textContent = totalPaidCommissions.toLocaleString() + ' ريال';
        }

        function loadPerformanceTable(distributors) {
            const tbody = document.querySelector('#performanceTable tbody');
            tbody.innerHTML = '';

            distributors.forEach(distributor => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${distributor.name}</td>
                    <td>${distributor.totalDeliveries}</td>
                    <td>${distributor.successRate}%</td>
                    <td>${distributor.rating}/5</td>
                    <td>${distributor.totalCommissions.toLocaleString()} ريال</td>
                    <td><span class="status-badge ${distributor.status === 'نشط' ? 'status-active' : 'status-inactive'}">${distributor.status}</span></td>
                    <td>
                        <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                            <button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 8px;" onclick="viewDistributorDetails(${distributor.id})" title="عرض التفاصيل">
                                👁️
                            </button>
                            <button class="btn btn-warning" style="font-size: 0.8rem; padding: 5px 8px;" onclick="editDistributor(${distributor.id})" title="تعديل البيانات">
                                ✏️
                            </button>
                            <button class="btn ${distributor.status === 'نشط' ? 'btn-secondary' : 'btn-success'}" style="font-size: 0.8rem; padding: 5px 8px;" onclick="toggleDistributorStatus(${distributor.id})" title="${distributor.status === 'نشط' ? 'إلغاء التفعيل' : 'تفعيل'}">
                                ${distributor.status === 'نشط' ? '⏸️' : '✅'}
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function exportPerformanceReport() {
            try {
                console.log('بدء تصدير تقرير الأداء...');
                const distributors = db.getAllDistributors();
                console.log('عدد المناديب:', distributors.length);

                const periodElement = document.getElementById('performancePeriod');
                const period = periodElement ? periodElement.value : 'شهري';
                console.log('الفترة المحددة:', period);

                // إنشاء محتوى Excel بتنسيق HTML
                let htmlContent = `
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; }
                            table { border-collapse: collapse; width: 100%; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                            th { background-color: #f2f2f2; font-weight: bold; }
                            .header { background-color: #4CAF50; color: white; text-align: center; padding: 10px; }
                            .section-title { background-color: #2196F3; color: white; padding: 5px; margin-top: 20px; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h2>تقرير الأداء التفصيلي</h2>
                            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                        </div>

                        <div class="section-title">البيانات الأساسية للمناديب</div>
                        <table>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المندوب</th>
                                <th>الهاتف</th>
                                <th>المنطقة</th>
                                <th>نوع المركبة</th>
                                <th>التوصيلات</th>
                                <th>معدل النجاح</th>
                                <th>التقييم</th>
                                <th>العمولات</th>
                                <th>الحالة</th>
                            </tr>
                `;

                distributors.forEach((distributor, index) => {
                    const name = distributor.name || 'غير محدد';
                    const phone = distributor.phone || 'غير محدد';
                    const area = distributor.area || 'غير محدد';
                    const vehicleType = distributor.vehicleType || 'غير محدد';
                    const totalDeliveries = distributor.totalDeliveries || 0;
                    const successRate = distributor.successRate || 0;
                    const rating = distributor.rating || 0;
                    const totalCommissions = distributor.totalCommissions || 0;
                    const status = distributor.status || 'غير محدد';

                    htmlContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${name}</td>
                            <td>${phone}</td>
                            <td>${area}</td>
                            <td>${vehicleType}</td>
                            <td>${totalDeliveries}</td>
                            <td>${successRate}%</td>
                            <td>${rating}/5</td>
                            <td>${totalCommissions.toLocaleString()} ريال</td>
                            <td>${status}</td>
                        </tr>
                    `;
                });

                htmlContent += `
                        </table>

                        <div class="section-title">الإحصائيات العامة</div>
                        <table>
                            <tr><th>المؤشر</th><th>القيمة</th></tr>
                            <tr><td>إجمالي المناديب</td><td>${distributors.length}</td></tr>
                            <tr><td>المناديب النشطين</td><td>${distributors.filter(d => d.status === 'نشط').length}</td></tr>
                            <tr><td>إجمالي التوصيلات</td><td>${distributors.reduce((sum, d) => sum + (d.totalDeliveries || 0), 0).toLocaleString()}</td></tr>
                            <tr><td>متوسط معدل النجاح</td><td>${distributors.length > 0 ? (distributors.reduce((sum, d) => sum + (d.successRate || 0), 0) / distributors.length).toFixed(1) : 0}%</td></tr>
                            <tr><td>إجمالي العمولات</td><td>${distributors.reduce((sum, d) => sum + (d.totalCommissions || 0), 0).toLocaleString()} ريال</td></tr>
                        </table>

                        <div class="section-title">أفضل 5 مناديب أداءً</div>
                        <table>
                            <tr><th>الترتيب</th><th>اسم المندوب</th><th>معدل النجاح</th><th>التوصيلات</th><th>العمولات</th></tr>
                `;

                const topPerformers = distributors
                    .filter(d => d.successRate && d.successRate > 0)
                    .sort((a, b) => (b.successRate || 0) - (a.successRate || 0))
                    .slice(0, 5);

                topPerformers.forEach((distributor, index) => {
                    const name = distributor.name || 'غير محدد';
                    const successRate = distributor.successRate || 0;
                    const totalDeliveries = distributor.totalDeliveries || 0;
                    const totalCommissions = distributor.totalCommissions || 0;

                    htmlContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${name}</td>
                            <td>${successRate}%</td>
                            <td>${totalDeliveries}</td>
                            <td>${totalCommissions.toLocaleString()} ريال</td>
                        </tr>
                    `;
                });

                htmlContent += `
                        </table>
                    </body>
                    </html>
                `;

                // تحويل إلى Blob وتنزيل كملف Excel
                const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `تقرير_الأداء_${new Date().toISOString().split('T')[0]}.xls`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert('✅ تم تصدير تقرير الأداء بنجاح!\n\nالملف سيفتح في Excel مع النص العربي بشكل صحيح');

            } catch (error) {
                console.error('خطأ في تصدير التقرير:', error);
                alert('حدث خطأ أثناء تصدير التقرير');
            }
        }

        // === وظائف إدارة العمولات ===
        function loadCommissionsData() {
            try {
                const distributors = db.getAllDistributors();
                const period = document.getElementById('commissionPeriod').value;

                // تحميل ملخص العمولات
                loadCommissionsSummary(distributors);

                // تحميل جدول العمولات
                loadCommissionsTable(distributors);

                // تحميل سجل العمولات
                loadCommissionHistory();

            } catch (error) {
                console.error('خطأ في تحميل بيانات العمولات:', error);
            }
        }

        function loadCommissionsSummary(distributors) {
            const totalCommissions = distributors.reduce((sum, d) => sum + d.totalCommissions, 0);
            const paidCommissions = Math.round(totalCommissions * 0.8); // 80% مدفوعة
            const pendingCommissions = totalCommissions - paidCommissions;
            const avgCommissionRate = (
                distributors.reduce((sum, d) => sum + (d.commissionRate || 5), 0) / distributors.length
            ).toFixed(1);

            document.getElementById('totalCommissions').textContent = totalCommissions.toLocaleString() + ' ريال';
            document.getElementById('paidCommissions').textContent = paidCommissions.toLocaleString() + ' ريال';
            document.getElementById('pendingCommissions').textContent = pendingCommissions.toLocaleString() + ' ريال';
            document.getElementById('avgCommissionRate').textContent = avgCommissionRate + '%';
        }

        function loadCommissionsTable(distributors) {
            const tbody = document.querySelector('#commissionsTable tbody');
            tbody.innerHTML = '';

            distributors.forEach(distributor => {
                const commissionRate = distributor.commissionRate || 5;
                const totalCommission = distributor.totalCommissions;
                const paidCommission = Math.round(totalCommission * 0.8);
                const remainingCommission = totalCommission - paidCommission;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${distributor.name}</td>
                    <td>${distributor.totalDeliveries}</td>
                    <td>${commissionRate}%</td>
                    <td>${totalCommission.toLocaleString()} ريال</td>
                    <td>${paidCommission.toLocaleString()} ريال</td>
                    <td>${remainingCommission.toLocaleString()} ريال</td>
                    <td>${new Date().toLocaleDateString('ar-SA')}</td>
                    <td>
                        <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                            <button class="btn btn-success" style="font-size: 0.8rem; padding: 5px 8px;" onclick="payCommission(${distributor.id})" title="دفع العمولة">
                                💳
                            </button>
                            <button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 8px;" onclick="viewDistributorDetails(${distributor.id})" title="عرض التفاصيل">
                                👁️
                            </button>
                            <button class="btn btn-warning" style="font-size: 0.8rem; padding: 5px 8px;" onclick="editDistributor(${distributor.id})" title="تعديل البيانات">
                                ✏️
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        function loadCommissionHistory() {
            const historyContainer = document.getElementById('commissionHistory');
            const sampleHistory = [
                { distributor: 'أحمد محمد السعد', amount: 2500, date: '2024-06-15', method: 'تحويل بنكي' },
                { distributor: 'فاطمة أحمد الزهراني', amount: 1890, date: '2024-06-14', method: 'نقداً' },
                { distributor: 'محمد علي القحطاني', amount: 3120, date: '2024-06-13', method: 'تحويل بنكي' }
            ];

            historyContainer.innerHTML = '';
            sampleHistory.forEach(item => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                historyItem.innerHTML = `
                    <div>
                        <div style="font-weight: bold; color: #2c3e50;">${item.distributor}</div>
                        <div style="color: #7f8c8d; font-size: 0.9rem;">${item.method} - ${item.date}</div>
                    </div>
                    <div style="font-weight: bold; color: #27ae60;">${item.amount.toLocaleString()} ريال</div>
                `;
                historyContainer.appendChild(historyItem);
            });
        }

        function showPayCommissionsModal() {
            try {
                const distributors = db.getAllDistributors();
                const distributorsWithCommissions = distributors.filter(d => {
                    const remainingCommission = d.totalCommissions - Math.round(d.totalCommissions * 0.8);
                    return remainingCommission > 0;
                });

                if (distributorsWithCommissions.length === 0) {
                    alert('لا توجد عمولات معلقة للدفع حالياً');
                    return;
                }

                // تحميل قائمة المناديب
                loadDistributorsForPayment(distributorsWithCommissions);

                // تعيين تاريخ اليوم
                document.getElementById('paymentDate').value = new Date().toISOString().split('T')[0];

                // إظهار النافذة
                document.getElementById('payCommissionsModal').style.display = 'block';

            } catch (error) {
                console.error('خطأ في إظهار نافذة دفع العمولات:', error);
                alert('حدث خطأ أثناء تحميل بيانات العمولات');
            }
        }

        function loadDistributorsForPayment(distributors) {
            const container = document.getElementById('distributorsList');
            container.innerHTML = '';
            let totalAmount = 0;

            distributors.forEach(distributor => {
                const remainingCommission = distributor.totalCommissions - Math.round(distributor.totalCommissions * 0.8);
                totalAmount += remainingCommission;

                const distributorItem = document.createElement('div');
                distributorItem.className = 'distributor-payment-item';
                distributorItem.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background: #f8f9fa; border-radius: 10px; margin-bottom: 10px;">
                        <div>
                            <input type="checkbox" id="distributor_${distributor.id}" value="${distributor.id}" checked onchange="updatePaymentTotal()">
                            <label for="distributor_${distributor.id}" style="margin-right: 10px; font-weight: bold;">${distributor.name}</label>
                            <div style="color: #7f8c8d; font-size: 0.9rem;">${distributor.area} - ${distributor.phone}</div>
                        </div>
                        <div style="text-align: left;">
                            <div style="font-weight: bold; color: #e74c3c;">${remainingCommission.toLocaleString()} ريال</div>
                            <div style="color: #7f8c8d; font-size: 0.9rem;">العمولة المعلقة</div>
                        </div>
                    </div>
                `;
                container.appendChild(distributorItem);
            });

            document.getElementById('totalPaymentAmount').textContent = totalAmount.toLocaleString() + ' ريال';
        }

        function updatePaymentTotal() {
            const checkboxes = document.querySelectorAll('#distributorsList input[type="checkbox"]:checked');
            const distributors = db.getAllDistributors();
            let total = 0;

            checkboxes.forEach(checkbox => {
                const distributorId = parseInt(checkbox.value);
                const distributor = distributors.find(d => d.id === distributorId);
                if (distributor) {
                    const remainingCommission = distributor.totalCommissions - Math.round(distributor.totalCommissions * 0.8);
                    total += remainingCommission;
                }
            });

            document.getElementById('totalPaymentAmount').textContent = total.toLocaleString() + ' ريال';
        }

        function closePayCommissionsModal() {
            document.getElementById('payCommissionsModal').style.display = 'none';
            document.getElementById('payCommissionsForm').reset();
        }

        function processPayment() {
            try {
                const form = document.getElementById('payCommissionsForm');
                const formData = new FormData(form);

                // التحقق من الحقول المطلوبة
                if (!formData.get('paymentMethod') || !formData.get('paymentDate')) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                // الحصول على المناديب المحددين
                const selectedDistributors = [];
                const checkboxes = document.querySelectorAll('#distributorsList input[type="checkbox"]:checked');

                if (checkboxes.length === 0) {
                    alert('يرجى تحديد مندوب واحد على الأقل للدفع');
                    return;
                }

                checkboxes.forEach(checkbox => {
                    selectedDistributors.push(parseInt(checkbox.value));
                });

                // تأكيد الدفع
                const totalAmount = document.getElementById('totalPaymentAmount').textContent;
                const paymentMethod = formData.get('paymentMethod');
                const paymentDate = formData.get('paymentDate');

                const confirmMessage = `هل أنت متأكد من دفع العمولات؟\n\nالمبلغ الإجمالي: ${totalAmount}\nطريقة الدفع: ${getPaymentMethodText(paymentMethod)}\nتاريخ الدفع: ${paymentDate}\nعدد المناديب: ${selectedDistributors.length}`;

                if (confirm(confirmMessage)) {
                    // محاكاة عملية الدفع
                    setTimeout(() => {
                        alert(`✅ تم دفع العمولات بنجاح!\n\nالمبلغ المدفوع: ${totalAmount}\nعدد المناديب: ${selectedDistributors.length}\nطريقة الدفع: ${getPaymentMethodText(paymentMethod)}`);

                        // إغلاق النافذة وتحديث البيانات
                        closePayCommissionsModal();
                        loadCommissionsData();
                    }, 1000);
                }

            } catch (error) {
                console.error('خطأ في معالجة الدفع:', error);
                alert('حدث خطأ أثناء معالجة الدفع. يرجى المحاولة مرة أخرى.');
            }
        }

        function getPaymentMethodText(method) {
            const methods = {
                'bank_transfer': 'تحويل بنكي',
                'cash': 'نقداً',
                'check': 'شيك',
                'digital_wallet': 'محفظة رقمية'
            };
            return methods[method] || method;
        }

        function payCommission(distributorId) {
            if (confirm('هل أنت متأكد من دفع العمولة لهذا المندوب؟')) {
                alert('تم دفع العمولة بنجاح!');
                loadCommissionsData();
            }
        }

        function exportCommissionsReport() {
            try {
                console.log('بدء تصدير تقرير العمولات...');
                const distributors = db.getAllDistributors();
                console.log('عدد المناديب:', distributors.length);

                const periodElement = document.getElementById('commissionPeriod');
                const period = periodElement ? periodElement.value : 'شهري';
                console.log('الفترة المحددة:', period);

                // إنشاء محتوى Excel بتنسيق HTML
                let htmlContent = `
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; }
                            table { border-collapse: collapse; width: 100%; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                            th { background-color: #f2f2f2; font-weight: bold; }
                            .header { background-color: #FF9800; color: white; text-align: center; padding: 10px; }
                            .section-title { background-color: #2196F3; color: white; padding: 5px; margin-top: 20px; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h2>تقرير العمولات التفصيلي</h2>
                            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                        </div>

                        <div class="section-title">تفاصيل عمولات المناديب</div>
                        <table>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المندوب</th>
                                <th>عدد التوصيلات</th>
                                <th>نسبة العمولة</th>
                                <th>إجمالي العمولة</th>
                                <th>العمولة المدفوعة</th>
                                <th>العمولة المعلقة</th>
                                <th>آخر دفعة</th>
                            </tr>
                `;

                distributors.forEach((distributor, index) => {
                    const name = distributor.name || 'غير محدد';
                    const totalDeliveries = distributor.totalDeliveries || 0;
                    const commissionRate = distributor.commissionRate || 5;
                    const totalCommission = distributor.totalCommissions || 0;
                    const paidCommission = Math.round(totalCommission * 0.8);
                    const remainingCommission = totalCommission - paidCommission;

                    htmlContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${name}</td>
                            <td>${totalDeliveries}</td>
                            <td>${commissionRate}%</td>
                            <td>${totalCommission.toLocaleString()} ريال</td>
                            <td>${paidCommission.toLocaleString()} ريال</td>
                            <td>${remainingCommission.toLocaleString()} ريال</td>
                            <td>${new Date().toLocaleDateString('ar-SA')}</td>
                        </tr>
                    `;
                });

                // إضافة ملخص العمولات
                const totalCommissions = distributors.reduce((sum, d) => sum + (d.totalCommissions || 0), 0);
                const paidCommissions = Math.round(totalCommissions * 0.8);
                const pendingCommissions = totalCommissions - paidCommissions;

                htmlContent += `
                        </table>

                        <div class="section-title">ملخص العمولات</div>
                        <table>
                            <tr><th>المؤشر</th><th>القيمة</th></tr>
                            <tr><td>إجمالي العمولات</td><td>${totalCommissions.toLocaleString()} ريال</td></tr>
                            <tr><td>العمولات المدفوعة</td><td>${paidCommissions.toLocaleString()} ريال</td></tr>
                            <tr><td>العمولات المعلقة</td><td>${pendingCommissions.toLocaleString()} ريال</td></tr>
                            <tr><td>متوسط نسبة العمولة</td><td>${distributors.length > 0 ? (distributors.reduce((sum, d) => sum + (d.commissionRate || 5), 0) / distributors.length).toFixed(1) : 0}%</td></tr>
                            <tr><td>عدد المناديب</td><td>${distributors.length}</td></tr>
                            <tr><td>متوسط العمولة للمندوب</td><td>${distributors.length > 0 ? Math.round(totalCommissions / distributors.length).toLocaleString() : 0} ريال</td></tr>
                        </table>
                    </body>
                    </html>
                `;

                // تحويل إلى Blob وتنزيل كملف Excel
                const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `تقرير_العمولات_${new Date().toISOString().split('T')[0]}.xls`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert('✅ تم تصدير تقرير العمولات بنجاح!\n\nالملف سيفتح في Excel مع النص العربي بشكل صحيح');

            } catch (error) {
                console.error('خطأ في تصدير التقرير:', error);
                alert('حدث خطأ أثناء تصدير التقرير');
            }
        }

        // === وظائف التقارير التفصيلية ===
        function loadReportData() {
            try {
                const distributors = db.getAllDistributors();
                const reportType = document.getElementById('reportType').value;
                const reportDate = document.getElementById('reportDate').value;

                // تحديث إحصائيات البطاقات
                updateReportCards(distributors);

            } catch (error) {
                console.error('خطأ في تحميل بيانات التقارير:', error);
            }
        }

        function updateReportCards(distributors) {
            const totalDistributors = distributors.length;
            const totalDeliveries = distributors.reduce((sum, d) => sum + d.totalDeliveries, 0);
            const totalFinancial = distributors.reduce((sum, d) => sum + d.totalCommissions, 0);
            const avgPerformance = (distributors.reduce((sum, d) => sum + d.successRate, 0) / distributors.length).toFixed(1);
            const uniqueRegions = [...new Set(distributors.map(d => d.area.split(' - ')[0]))].length;
            const uniqueVehicles = [...new Set(distributors.map(d => d.vehicleType))].length;

            document.getElementById('reportDistributorsCount').textContent = totalDistributors + ' مندوب';
            document.getElementById('reportDeliveriesCount').textContent = totalDeliveries.toLocaleString() + ' توصيلة';
            document.getElementById('reportFinancialAmount').textContent = totalFinancial.toLocaleString() + ' ريال';
            document.getElementById('reportPerformanceScore').textContent = avgPerformance + '%';
            document.getElementById('reportRegionsCount').textContent = uniqueRegions + ' منطقة';
            document.getElementById('reportVehiclesCount').textContent = uniqueVehicles + ' نوع';
        }

        // وظائف عرض التقارير التفصيلية
        function showDistributorReport() {
            showDetailedReport('تقرير المناديب التفصيلي', generateDistributorReportContent());
        }

        function showDeliveryReport() {
            showDetailedReport('تقرير التوصيلات التفصيلي', generateDeliveryReportContent());
        }

        function showFinancialReport() {
            showDetailedReport('التقرير المالي التفصيلي', generateFinancialReportContent());
        }

        function showPerformanceReport() {
            showDetailedReport('تقرير الأداء التفصيلي', generatePerformanceReportContent());
        }

        function showRegionalReport() {
            showDetailedReport('التقرير الجغرافي التفصيلي', generateRegionalReportContent());
        }

        function showVehicleReport() {
            showDetailedReport('تقرير المركبات التفصيلي', generateVehicleReportContent());
        }

        function showDetailedReport(title, content) {
            document.getElementById('detailedReportTitle').textContent = title;
            document.getElementById('detailedReportContent').innerHTML = content;
            document.getElementById('detailedReportContainer').style.display = 'block';
        }

        function hideDetailedReport() {
            document.getElementById('detailedReportContainer').style.display = 'none';
        }

        function generateDistributorReportContent() {
            const distributors = db.getAllDistributors();
            let content = '<div style="display: grid; gap: 20px;">';

            distributors.forEach(distributor => {
                content += `
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #667eea;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">${distributor.name}</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div><strong>الهاتف:</strong> ${distributor.phone}</div>
                            <div><strong>المنطقة:</strong> ${distributor.area}</div>
                            <div><strong>المركبة:</strong> ${distributor.vehicleType}</div>
                            <div><strong>التوصيلات:</strong> ${distributor.totalDeliveries}</div>
                            <div><strong>معدل النجاح:</strong> ${distributor.successRate}%</div>
                            <div><strong>العمولات:</strong> ${distributor.totalCommissions.toLocaleString()} ريال</div>
                        </div>
                    </div>
                `;
            });

            content += '</div>';
            return content;
        }

        function generateDeliveryReportContent() {
            return '<p style="color: #7f8c8d; text-align: center; padding: 40px;">سيتم إضافة تقرير التوصيلات التفصيلي قريباً...</p>';
        }

        function generateFinancialReportContent() {
            return '<p style="color: #7f8c8d; text-align: center; padding: 40px;">سيتم إضافة التقرير المالي التفصيلي قريباً...</p>';
        }

        function generatePerformanceReportContent() {
            return '<p style="color: #7f8c8d; text-align: center; padding: 40px;">سيتم إضافة تقرير الأداء التفصيلي قريباً...</p>';
        }

        function generateRegionalReportContent() {
            return '<p style="color: #7f8c8d; text-align: center; padding: 40px;">سيتم إضافة التقرير الجغرافي التفصيلي قريباً...</p>';
        }

        function generateVehicleReportContent() {
            return '<p style="color: #7f8c8d; text-align: center; padding: 40px;">سيتم إضافة تقرير المركبات التفصيلي قريباً...</p>';
        }

        function generateDetailedReport() {
            try {
                console.log('بدء إنشاء التقرير المخصص...');

                const reportTypeElement = document.getElementById('reportType');
                const reportDateElement = document.getElementById('reportDate');

                const reportType = reportTypeElement ? reportTypeElement.value : 'comprehensive';
                const reportDate = reportDateElement ? reportDateElement.value : new Date().toISOString().split('T')[0];

                console.log('نوع التقرير:', reportType);
                console.log('تاريخ التقرير:', reportDate);

                const distributors = db.getAllDistributors();
                console.log('عدد المناديب:', distributors.length);

                if (!reportType || !reportDate) {
                    alert('يرجى تحديد نوع التقرير والتاريخ');
                    return;
                }

                let reportContent = '';
                let reportTitle = '';

                switch(reportType) {
                    case 'daily':
                        reportTitle = 'التقرير اليومي المخصص';
                        reportContent = generateDailyCustomReport(distributors, reportDate);
                        break;
                    case 'weekly':
                        reportTitle = 'التقرير الأسبوعي المخصص';
                        reportContent = generateWeeklyCustomReport(distributors, reportDate);
                        break;
                    case 'monthly':
                        reportTitle = 'التقرير الشهري المخصص';
                        reportContent = generateMonthlyCustomReport(distributors, reportDate);
                        break;
                    case 'custom':
                        reportTitle = 'التقرير المخصص الشامل';
                        reportContent = generateComprehensiveCustomReport(distributors, reportDate);
                        break;
                    default:
                        reportTitle = 'التقرير العام المخصص';
                        reportContent = generateGeneralCustomReport(distributors, reportDate);
                }

                showDetailedReport(reportTitle, reportContent);

            } catch (error) {
                console.error('خطأ في إنشاء التقرير المخصص:', error);
                alert('حدث خطأ أثناء إنشاء التقرير المخصص');
            }
        }

        function generateDailyCustomReport(distributors, date) {
            return `
                <div style="padding: 20px;">
                    <h3 style="color: #2c3e50; border-bottom: 2px solid #667eea; padding-bottom: 10px;">📅 التقرير اليومي - ${date}</h3>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px;">
                            <h4>📊 إحصائيات اليوم</h4>
                            <p>المناديب النشطين: ${distributors.filter(d => d.status === 'نشط').length}</p>
                            <p>متوسط التوصيلات: ${Math.round(distributors.reduce((sum, d) => sum + (d.totalDeliveries || 0), 0) / 30)}</p>
                            <p>معدل النجاح العام: ${distributors.length > 0 ? (distributors.reduce((sum, d) => sum + (d.successRate || 0), 0) / distributors.length).toFixed(1) : 0}%</p>
                        </div>

                        <div style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 20px; border-radius: 10px;">
                            <h4>💰 الإحصائيات المالية</h4>
                            <p>إجمالي العمولات: ${distributors.reduce((sum, d) => sum + d.totalCommissions, 0).toLocaleString()} ريال</p>
                            <p>العمولات المعلقة: ${Math.round(distributors.reduce((sum, d) => sum + d.totalCommissions, 0) * 0.2).toLocaleString()} ريال</p>
                        </div>
                    </div>

                    <div style="margin-top: 30px;">
                        <h4 style="color: #2c3e50;">🏆 أفضل 3 مناديب اليوم</h4>
                        ${distributors.sort((a, b) => b.successRate - a.successRate).slice(0, 3).map((d, i) => `
                            <div style="background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 10px; border-right: 4px solid ${i === 0 ? '#f1c40f' : i === 1 ? '#95a5a6' : '#cd7f32'};">
                                <strong>${i + 1}. ${d.name}</strong> - معدل النجاح: ${d.successRate}% - التوصيلات: ${d.totalDeliveries}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        function generateWeeklyCustomReport(distributors, date) {
            return `
                <div style="padding: 20px;">
                    <h3 style="color: #2c3e50; border-bottom: 2px solid #667eea; padding-bottom: 10px;">📅 التقرير الأسبوعي - أسبوع ${date}</h3>
                    <p style="color: #7f8c8d; margin-bottom: 30px;">تقرير شامل لأداء المناديب خلال الأسبوع</p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        ${distributors.map(d => `
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-top: 4px solid #667eea;">
                                <h4 style="color: #2c3e50; margin-bottom: 15px;">${d.name || 'غير محدد'}</h4>
                                <div style="color: #7f8c8d; line-height: 1.6;">
                                    <div>📱 ${d.phone || 'غير محدد'}</div>
                                    <div>📍 ${d.area || 'غير محدد'}</div>
                                    <div>🚚 ${d.vehicleType || 'غير محدد'}</div>
                                    <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #ecf0f1;">
                                        <div style="color: #27ae60; font-weight: bold;">✅ ${d.successRate || 0}% نجاح</div>
                                        <div style="color: #3498db;">📦 ${d.totalDeliveries || 0} توصيلة</div>
                                        <div style="color: #e74c3c;">💰 ${(d.totalCommissions || 0).toLocaleString()} ريال</div>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        function generateMonthlyCustomReport(distributors, date) {
            const monthlyStats = {
                totalDeliveries: distributors.reduce((sum, d) => sum + (d.totalDeliveries || 0), 0),
                avgSuccessRate: distributors.length > 0 ? (distributors.reduce((sum, d) => sum + (d.successRate || 0), 0) / distributors.length).toFixed(1) : 0,
                totalCommissions: distributors.reduce((sum, d) => sum + (d.totalCommissions || 0), 0),
                activeDistributors: distributors.filter(d => d.status === 'نشط').length
            };

            return `
                <div style="padding: 20px;">
                    <h3 style="color: #2c3e50; border-bottom: 2px solid #667eea; padding-bottom: 10px;">📅 التقرير الشهري - ${date}</h3>

                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; border-radius: 15px; margin: 20px 0;">
                        <h4 style="margin-bottom: 20px;">📊 ملخص الشهر</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                            <div style="text-align: center;">
                                <div style="font-size: 2rem; font-weight: bold;">${monthlyStats.totalDeliveries.toLocaleString()}</div>
                                <div>إجمالي التوصيلات</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 2rem; font-weight: bold;">${monthlyStats.avgSuccessRate}%</div>
                                <div>متوسط معدل النجاح</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 2rem; font-weight: bold;">${monthlyStats.totalCommissions.toLocaleString()}</div>
                                <div>إجمالي العمولات (ريال)</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 2rem; font-weight: bold;">${monthlyStats.activeDistributors}</div>
                                <div>المناديب النشطين</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 30px;">
                        <h4 style="color: #2c3e50;">📈 تحليل الأداء الشهري</h4>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <div style="display: grid; gap: 15px;">
                                <div>🏆 أفضل مندوب: ${distributors.sort((a, b) => b.successRate - a.successRate)[0].name} (${distributors.sort((a, b) => b.successRate - a.successRate)[0].successRate}%)</div>
                                <div>📦 أكثر توصيلاً: ${distributors.sort((a, b) => b.totalDeliveries - a.totalDeliveries)[0].name} (${distributors.sort((a, b) => b.totalDeliveries - a.totalDeliveries)[0].totalDeliveries} توصيلة)</div>
                                <div>💰 أعلى عمولة: ${distributors.sort((a, b) => b.totalCommissions - a.totalCommissions)[0].name} (${distributors.sort((a, b) => b.totalCommissions - a.totalCommissions)[0].totalCommissions.toLocaleString()} ريال)</div>
                                <div>🗺️ أكثر المناطق نشاطاً: ${[...new Set(distributors.map(d => d.area.split(' - ')[0]))].slice(0, 3).join(', ')}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateComprehensiveCustomReport(distributors, date) {
            return `
                <div style="padding: 20px;">
                    <h3 style="color: #2c3e50; border-bottom: 2px solid #667eea; padding-bottom: 10px;">📋 التقرير الشامل المخصص - ${date}</h3>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #667eea;">
                            <h4 style="color: #2c3e50;">👥 إحصائيات المناديب</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>📊 إجمالي المناديب: ${distributors.length}</li>
                                <li>✅ المناديب النشطين: ${distributors.filter(d => d.status === 'نشط').length}</li>
                                <li>⏸️ المناديب غير النشطين: ${distributors.filter(d => d.status === 'غير نشط').length}</li>
                                <li>⭐ متوسط التقييم: ${distributors.length > 0 ? (distributors.reduce((sum, d) => sum + (d.rating || 0), 0) / distributors.length).toFixed(1) : 0}/5</li>
                            </ul>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #27ae60;">
                            <h4 style="color: #2c3e50;">📦 إحصائيات التوصيل</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>📈 إجمالي التوصيلات: ${distributors.reduce((sum, d) => sum + (d.totalDeliveries || 0), 0).toLocaleString()}</li>
                                <li>📊 متوسط التوصيلات: ${distributors.length > 0 ? Math.round(distributors.reduce((sum, d) => sum + (d.totalDeliveries || 0), 0) / distributors.length) : 0}</li>
                                <li>✅ متوسط معدل النجاح: ${distributors.length > 0 ? (distributors.reduce((sum, d) => sum + (d.successRate || 0), 0) / distributors.length).toFixed(1) : 0}%</li>
                                <li>🚚 أنواع المركبات: ${[...new Set(distributors.map(d => d.vehicleType || 'غير محدد'))].length}</li>
                            </ul>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #e74c3c;">
                            <h4 style="color: #2c3e50;">💰 الإحصائيات المالية</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>💵 إجمالي العمولات: ${distributors.reduce((sum, d) => sum + (d.totalCommissions || 0), 0).toLocaleString()} ريال</li>
                                <li>✅ العمولات المدفوعة: ${Math.round(distributors.reduce((sum, d) => sum + (d.totalCommissions || 0), 0) * 0.8).toLocaleString()} ريال</li>
                                <li>⏳ العمولات المعلقة: ${Math.round(distributors.reduce((sum, d) => sum + (d.totalCommissions || 0), 0) * 0.2).toLocaleString()} ريال</li>
                                <li>📊 متوسط العمولة: ${distributors.length > 0 ? Math.round(distributors.reduce((sum, d) => sum + (d.totalCommissions || 0), 0) / distributors.length).toLocaleString() : 0} ريال</li>
                            </ul>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #f39c12;">
                            <h4 style="color: #2c3e50;">🗺️ التوزيع الجغرافي</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>🏙️ عدد المناطق: ${[...new Set(distributors.map(d => (d.area || 'غير محدد').split(' - ')[0]))].length}</li>
                                <li>📍 أكثر المناطق: ${[...new Set(distributors.map(d => (d.area || 'غير محدد').split(' - ')[0]))].slice(0, 2).join(', ')}</li>
                                <li>🚚 توزيع المركبات: متنوع</li>
                                <li>📊 كثافة التغطية: ممتازة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateGeneralCustomReport(distributors, date) {
            return generateComprehensiveCustomReport(distributors, date);
        }

        function exportAllReports() {
            try {
                console.log('بدء تصدير جميع التقارير...');
                const distributors = db.getAllDistributors();
                console.log('عدد المناديب:', distributors.length);
                const currentDate = new Date().toLocaleDateString('ar-SA');

                // إنشاء تقرير شامل بتنسيق HTML لـ Excel
                let htmlContent = `
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; }
                            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                            th { background-color: #f2f2f2; font-weight: bold; }
                            .header { background-color: #9C27B0; color: white; text-align: center; padding: 15px; margin-bottom: 20px; }
                            .section-title { background-color: #2196F3; color: white; padding: 8px; margin-top: 20px; font-weight: bold; }
                            .summary-box { background-color: #E8F5E8; padding: 10px; margin: 10px 0; border-right: 4px solid #4CAF50; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1>التقرير الشامل لجميع بيانات المناديب</h1>
                            <h3>تاريخ التقرير: ${currentDate}</h3>
                        </div>

                        <div class="section-title">📊 البيانات الأساسية للمناديب</div>
                        <table>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المندوب</th>
                                <th>الهاتف</th>
                                <th>المنطقة</th>
                                <th>نوع المركبة</th>
                                <th>تاريخ الانضمام</th>
                                <th>الحالة</th>
                                <th>التقييم</th>
                            </tr>
                `;

                distributors.forEach((distributor, index) => {
                    const name = distributor.name || 'غير محدد';
                    const phone = distributor.phone || 'غير محدد';
                    const area = distributor.area || 'غير محدد';
                    const vehicleType = distributor.vehicleType || 'غير محدد';
                    const joinDate = distributor.joinDate || 'غير محدد';
                    const status = distributor.status || 'غير محدد';
                    const rating = distributor.rating || 0;

                    htmlContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${name}</td>
                            <td>${phone}</td>
                            <td>${area}</td>
                            <td>${vehicleType}</td>
                            <td>${joinDate}</td>
                            <td>${status}</td>
                            <td>${rating}/5</td>
                        </tr>
                    `;
                });

                htmlContent += `
                        </table>

                        <div class="section-title">📈 إحصائيات الأداء</div>
                        <table>
                            <tr>
                                <th>اسم المندوب</th>
                                <th>عدد التوصيلات</th>
                                <th>معدل النجاح</th>
                                <th>التقييم</th>
                                <th>تقييم الأداء</th>
                            </tr>
                `;

                distributors.forEach(distributor => {
                    const name = distributor.name || 'غير محدد';
                    const totalDeliveries = distributor.totalDeliveries || 0;
                    const successRate = distributor.successRate || 0;
                    const rating = distributor.rating || 0;
                    const performanceLevel = successRate > 90 ? 'ممتاز' :
                                           successRate > 80 ? 'جيد جداً' :
                                           successRate > 70 ? 'جيد' : 'يحتاج تحسين';

                    htmlContent += `
                        <tr>
                            <td>${name}</td>
                            <td>${totalDeliveries}</td>
                            <td>${successRate}%</td>
                            <td>${rating}/5</td>
                            <td>${performanceLevel}</td>
                        </tr>
                    `;
                });

                htmlContent += `
                        </table>

                        <div class="section-title">💰 تفاصيل العمولات</div>
                        <table>
                            <tr>
                                <th>اسم المندوب</th>
                                <th>نسبة العمولة</th>
                                <th>إجمالي العمولة</th>
                                <th>العمولة المدفوعة</th>
                                <th>العمولة المعلقة</th>
                                <th>حالة الدفع</th>
                            </tr>
                `;

                distributors.forEach(distributor => {
                    const commissionRate = distributor.commissionRate || 5;
                    const totalCommission = distributor.totalCommissions;
                    const paidCommission = Math.round(totalCommission * 0.8);
                    const remainingCommission = totalCommission - paidCommission;

                    htmlContent += `
                        <tr>
                            <td>${distributor.name}</td>
                            <td>${commissionRate}%</td>
                            <td>${totalCommission.toLocaleString()} ريال</td>
                            <td>${paidCommission.toLocaleString()} ريال</td>
                            <td>${remainingCommission.toLocaleString()} ريال</td>
                            <td>${remainingCommission > 0 ? 'معلقة' : 'مكتملة'}</td>
                        </tr>
                    `;
                });

                // الإحصائيات العامة
                const totalCommissions = distributors.reduce((sum, d) => sum + (d.totalCommissions || 0), 0);
                const totalDeliveries = distributors.reduce((sum, d) => sum + (d.totalDeliveries || 0), 0);
                const avgSuccessRate = distributors.length > 0 ? (distributors.reduce((sum, d) => sum + (d.successRate || 0), 0) / distributors.length).toFixed(1) : 0;
                const avgRating = distributors.length > 0 ? (distributors.reduce((sum, d) => sum + (d.rating || 0), 0) / distributors.length).toFixed(1) : 0;

                htmlContent += `
                        </table>

                        <div class="section-title">📋 الإحصائيات العامة</div>
                        <table>
                            <tr><th>المؤشر</th><th>القيمة</th></tr>
                            <tr><td>إجمالي المناديب</td><td>${distributors.length}</td></tr>
                            <tr><td>المناديب النشطين</td><td>${distributors.filter(d => d.status === 'نشط').length}</td></tr>
                            <tr><td>المناديب غير النشطين</td><td>${distributors.filter(d => d.status === 'غير نشط').length}</td></tr>
                            <tr><td>إجمالي التوصيلات</td><td>${totalDeliveries.toLocaleString()}</td></tr>
                            <tr><td>متوسط التوصيلات</td><td>${distributors.length > 0 ? Math.round(totalDeliveries / distributors.length) : 0}</td></tr>
                            <tr><td>متوسط معدل النجاح</td><td>${avgSuccessRate}%</td></tr>
                            <tr><td>متوسط التقييم</td><td>${avgRating}/5</td></tr>
                            <tr><td>إجمالي العمولات</td><td>${totalCommissions.toLocaleString()} ريال</td></tr>
                            <tr><td>العمولات المدفوعة</td><td>${Math.round(totalCommissions * 0.8).toLocaleString()} ريال</td></tr>
                            <tr><td>العمولات المعلقة</td><td>${Math.round(totalCommissions * 0.2).toLocaleString()} ريال</td></tr>
                            <tr><td>عدد المناطق المغطاة</td><td>${[...new Set(distributors.map(d => d.area.split(' - ')[0]))].length}</td></tr>
                            <tr><td>أنواع المركبات</td><td>${[...new Set(distributors.map(d => d.vehicleType))].length}</td></tr>
                        </table>

                        <div class="section-title">🏆 أفضل 5 مناديب أداءً</div>
                        <table>
                            <tr><th>الترتيب</th><th>اسم المندوب</th><th>معدل النجاح</th><th>عدد التوصيلات</th><th>العمولات</th></tr>
                `;

                const topPerformers = distributors
                    .filter(d => d.successRate && d.successRate > 0)
                    .sort((a, b) => (b.successRate || 0) - (a.successRate || 0))
                    .slice(0, 5);

                topPerformers.forEach((distributor, index) => {
                    const name = distributor.name || 'غير محدد';
                    const successRate = distributor.successRate || 0;
                    const totalDeliveries = distributor.totalDeliveries || 0;
                    const totalCommissions = distributor.totalCommissions || 0;

                    htmlContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${name}</td>
                            <td>${successRate}%</td>
                            <td>${totalDeliveries}</td>
                            <td>${totalCommissions.toLocaleString()} ريال</td>
                        </tr>
                    `;
                });

                htmlContent += `
                        </table>

                        <div class="summary-box">
                            <h3>📝 ملخص التقرير</h3>
                            <p>هذا التقرير يحتوي على جميع بيانات المناديب بما في ذلك البيانات الأساسية وإحصائيات الأداء وتفاصيل العمولات والإحصائيات العامة.</p>
                            <p>تم إنشاء هذا التقرير في: ${currentDate}</p>
                        </div>
                    </body>
                    </html>
                `;

                // تحويل إلى Blob وتنزيل كملف Excel
                const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `التقرير_الشامل_للمناديب_${new Date().toISOString().split('T')[0]}.xls`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert('✅ تم تصدير التقرير الشامل بنجاح!\n\n📋 يحتوي التقرير على:\n• البيانات الأساسية\n• إحصائيات الأداء\n• تفاصيل العمولات\n• الإحصائيات العامة\n• أفضل المناديب\n\n🎯 الملف سيفتح في Excel مع النص العربي بشكل صحيح');

            } catch (error) {
                console.error('خطأ في تصدير التقارير:', error);
                console.error('تفاصيل الخطأ:', error.message);
                console.error('مكان الخطأ:', error.stack);
                alert('حدث خطأ أثناء تصدير التقارير: ' + error.message);
            }
        }

        // تحميل البيانات عند تغيير التبويبات
        function showTab(tabId) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabId).classList.add('active');

            // إضافة الفئة النشطة للزر المحدد
            event.target.classList.add('active');

            // تحميل البيانات حسب التبويب
            if (tabId === 'performance') {
                loadPerformanceData();
            } else if (tabId === 'commissions') {
                loadCommissionsData();
            } else if (tabId === 'reports') {
                loadReportData();
                // تعيين تاريخ اليوم
                document.getElementById('reportDate').value = new Date().toISOString().split('T')[0];
            }
        }
    </script>

    <!-- تطبيق خط SF Pro Arabic Display على جميع العناصر -->
    <style>
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, textarea, select, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }
    </style>
</body>
</html>
