<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار قاعدة البيانات</h1>
        <p>هذه الصفحة تختبر تحميل وعمل قاعدة البيانات</p>
        
        <div id="results"></div>
        
        <button onclick="runTests()">🔄 تشغيل الاختبارات</button>
        <button onclick="clearResults()">🗑️ مسح النتائج</button>
        <button onclick="showData()">📊 عرض البيانات</button>
    </div>

    <script src="js/database-simple.js"></script>
    <script>
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        function runTests() {
            clearResults();
            addResult('🚀 بدء الاختبارات...', 'info');

            // اختبار 1: تحميل قاعدة البيانات
            try {
                if (typeof db !== 'undefined' && db !== null) {
                    addResult('✅ تم تحميل قاعدة البيانات بنجاح', 'success');
                } else {
                    addResult('❌ فشل في تحميل قاعدة البيانات', 'error');
                    return;
                }
            } catch (error) {
                addResult('❌ خطأ في تحميل قاعدة البيانات: ' + error.message, 'error');
                return;
            }

            // اختبار 2: الشحنات
            try {
                const shipments = db.getAllShipments();
                if (shipments && shipments.length > 0) {
                    addResult(`✅ تم تحميل ${shipments.length} شحنة`, 'success');
                } else {
                    addResult('⚠️ لا توجد شحنات', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في تحميل الشحنات: ' + error.message, 'error');
            }

            // اختبار 3: البحث عن شحنة
            try {
                const shipment = db.findShipmentByTracking('SHP001');
                if (shipment) {
                    addResult('✅ تم العثور على الشحنة SHP001', 'success');
                } else {
                    addResult('⚠️ لم يتم العثور على الشحنة SHP001', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في البحث عن الشحنة: ' + error.message, 'error');
            }

            // اختبار 4: المستخدمين
            try {
                const users = db.getAllUsers();
                if (users && users.length > 0) {
                    addResult(`✅ تم تحميل ${users.length} مستخدم`, 'success');
                } else {
                    addResult('⚠️ لا توجد مستخدمين', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في تحميل المستخدمين: ' + error.message, 'error');
            }

            // اختبار 5: الأدوار
            try {
                const roles = db.getAllRoles();
                if (roles && roles.length > 0) {
                    addResult(`✅ تم تحميل ${roles.length} دور`, 'success');
                } else {
                    addResult('⚠️ لا توجد أدوار', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في تحميل الأدوار: ' + error.message, 'error');
            }

            addResult('🏁 انتهت الاختبارات', 'info');
        }

        function showData() {
            clearResults();
            addResult('📊 عرض البيانات المحفوظة:', 'info');

            try {
                // عرض الشحنات
                const shipments = db.getAllShipments();
                addResult(`الشحنات (${shipments.length}):`, 'info');
                shipments.forEach(shipment => {
                    addResult(`  - ${shipment.trackingNumber}: ${shipment.senderName} → ${shipment.receiverName} (${shipment.status})`, 'info');
                });

                // عرض المستخدمين
                const users = db.getAllUsers();
                addResult(`المستخدمين (${users.length}):`, 'info');
                users.forEach(user => {
                    addResult(`  - ${user.name} (${user.email}) - ${user.role}`, 'info');
                });

                // عرض الأدوار
                const roles = db.getAllRoles();
                addResult(`الأدوار (${roles.length}):`, 'info');
                roles.forEach(role => {
                    addResult(`  - ${role.name}: ${role.permissions.length} صلاحية`, 'info');
                });

            } catch (error) {
                addResult('❌ خطأ في عرض البيانات: ' + error.message, 'error');
            }
        }

        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
