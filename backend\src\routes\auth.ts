// Authentication Routes
// مسارات المصادقة

import { Router } from 'express'
import bcrypt from 'bcrypt'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { asyncHand<PERSON>, AppError, createValidationError } from '../middleware/errorHandler'
import { authenticate, generateToken } from '../middleware/auth'
import { config } from '../config/config'
import { loggers } from '../utils/logger'

const router = Router()
const prisma = new PrismaClient()

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
})

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(config.constants.minPasswordLength, `Password must be at least ${config.constants.minPasswordLength} characters`),
  firstName: z.string().min(1, 'First name is required').max(config.constants.maxNameLength),
  lastName: z.string().min(1, 'Last name is required').max(config.constants.maxNameLength),
  phone: z.string().optional(),
  language: z.enum(['ar', 'en']).default('ar'),
})

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(config.constants.minPasswordLength, `Password must be at least ${config.constants.minPasswordLength} characters`),
})

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', asyncHandler(async (req, res) => {
  const { email, password } = loginSchema.parse(req.body)
  
  // Find user
  const user = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
    select: {
      id: true,
      email: true,
      passwordHash: true,
      firstName: true,
      lastName: true,
      role: true,
      language: true,
      isActive: true,
    },
  })
  
  if (!user) {
    loggers.auth.loginFailed(email, 'User not found', req.ip)
    throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS')
  }
  
  if (!user.isActive) {
    loggers.auth.loginFailed(email, 'Account deactivated', req.ip)
    throw new AppError('Account is deactivated', 401, 'ACCOUNT_DEACTIVATED')
  }
  
  // Verify password
  const isPasswordValid = await bcrypt.compare(password, user.passwordHash)
  if (!isPasswordValid) {
    loggers.auth.loginFailed(email, 'Invalid password', req.ip)
    throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS')
  }
  
  // Generate token
  const token = generateToken({
    id: user.id,
    email: user.email,
    role: user.role,
  })
  
  // Log successful login
  loggers.auth.login(user.id, user.email, req.ip)
  
  res.json({
    success: true,
    data: {
      token,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        language: user.language,
      },
    },
    message: 'Login successful',
  })
}))

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: User registration
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phone:
 *                 type: string
 *               language:
 *                 type: string
 *                 enum: [ar, en]
 *     responses:
 *       201:
 *         description: Registration successful
 *       409:
 *         description: Email already exists
 */
router.post('/register', asyncHandler(async (req, res) => {
  const userData = registerSchema.parse(req.body)
  
  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: userData.email.toLowerCase() },
  })
  
  if (existingUser) {
    throw new AppError('Email already registered', 409, 'EMAIL_EXISTS')
  }
  
  // Hash password
  const passwordHash = await bcrypt.hash(userData.password, config.security.bcryptRounds)
  
  // Create user
  const user = await prisma.user.create({
    data: {
      email: userData.email.toLowerCase(),
      passwordHash,
      firstName: userData.firstName,
      lastName: userData.lastName,
      phone: userData.phone,
      language: userData.language,
      role: 'USER', // Default role
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      language: true,
    },
  })
  
  // Generate token
  const token = generateToken({
    id: user.id,
    email: user.email,
    role: user.role,
  })
  
  // Log successful registration
  loggers.auth.register(user.id, user.email)
  
  res.status(201).json({
    success: true,
    data: {
      token,
      user,
    },
    message: 'Registration successful',
  })
}))

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved
 *       401:
 *         description: Unauthorized
 */
router.get('/me', authenticate, asyncHandler(async (req, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      phone: true,
      role: true,
      language: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  })
  
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND')
  }
  
  res.json({
    success: true,
    data: { user },
  })
}))

/**
 * @swagger
 * /api/auth/change-password:
 *   post:
 *     summary: Change user password
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password changed successfully
 *       401:
 *         description: Invalid current password
 */
router.post('/change-password', authenticate, asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = changePasswordSchema.parse(req.body)
  
  // Get user with password hash
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      passwordHash: true,
    },
  })
  
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND')
  }
  
  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash)
  if (!isCurrentPasswordValid) {
    throw new AppError('Current password is incorrect', 401, 'INVALID_CURRENT_PASSWORD')
  }
  
  // Hash new password
  const newPasswordHash = await bcrypt.hash(newPassword, config.security.bcryptRounds)
  
  // Update password
  await prisma.user.update({
    where: { id: user.id },
    data: { passwordHash: newPasswordHash },
  })
  
  res.json({
    success: true,
    message: 'Password changed successfully',
  })
}))

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: User logout
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.post('/logout', authenticate, asyncHandler(async (req, res) => {
  // Log logout
  loggers.auth.logout(req.user!.id, req.user!.email)
  
  res.json({
    success: true,
    message: 'Logout successful',
  })
}))

export default router
