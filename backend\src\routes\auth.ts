// Authentication Routes
// مسارات المصادقة

import { Router } from 'express'
import bcrypt from 'bcrypt'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { asyncHandler, AppError, createValidationError } from '../middleware/errorHandler'
import { authenticate, generateToken } from '../middleware/auth'
import { config } from '../config/config'
import { loggers } from '../utils/logger'
import {
  generateSecret,
  generateQRCode,
  verifyToken,
  generateBackupCodes,
  hashBackupCodes,
  verifyHashedBackupCode,
  validate2FASetup
} from '../utils/twoFactor'

const router = Router()
const prisma = new PrismaClient()

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
})

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(config.constants.minPasswordLength, `Password must be at least ${config.constants.minPasswordLength} characters`),
  firstName: z.string().min(1, 'First name is required').max(config.constants.maxNameLength),
  lastName: z.string().min(1, 'Last name is required').max(config.constants.maxNameLength),
  phone: z.string().optional(),
  language: z.enum(['ar', 'en']).default('ar'),
})

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(config.constants.minPasswordLength, `Password must be at least ${config.constants.minPasswordLength} characters`),
})

// 2FA validation schemas
const enable2FASchema = z.object({
  token: z.string().length(6, 'Token must be 6 digits'),
})

const verify2FASchema = z.object({
  token: z.string().min(6, 'Token must be at least 6 characters'),
})

const loginWith2FASchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
  token: z.string().min(6, 'Token is required'),
})

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', asyncHandler(async (req, res) => {
  const { email, password } = loginSchema.parse(req.body)
  
  // Find user
  const user = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
    select: {
      id: true,
      email: true,
      passwordHash: true,
      firstName: true,
      lastName: true,
      role: true,
      language: true,
      isActive: true,
    },
  })
  
  if (!user) {
    loggers.auth.loginFailed(email, 'User not found', req.ip)
    throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS')
  }
  
  if (!user.isActive) {
    loggers.auth.loginFailed(email, 'Account deactivated', req.ip)
    throw new AppError('Account is deactivated', 401, 'ACCOUNT_DEACTIVATED')
  }
  
  // Verify password
  const isPasswordValid = await bcrypt.compare(password, user.passwordHash)
  if (!isPasswordValid) {
    loggers.auth.loginFailed(email, 'Invalid password', req.ip)
    throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS')
  }
  
  // Generate token
  const token = generateToken({
    id: user.id,
    email: user.email,
    role: user.role,
  })
  
  // Log successful login
  loggers.auth.login(user.id, user.email, req.ip)
  
  res.json({
    success: true,
    data: {
      token,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        language: user.language,
      },
    },
    message: 'Login successful',
  })
}))

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: User registration
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phone:
 *                 type: string
 *               language:
 *                 type: string
 *                 enum: [ar, en]
 *     responses:
 *       201:
 *         description: Registration successful
 *       409:
 *         description: Email already exists
 */
router.post('/register', asyncHandler(async (req, res) => {
  const userData = registerSchema.parse(req.body)
  
  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: userData.email.toLowerCase() },
  })
  
  if (existingUser) {
    throw new AppError('Email already registered', 409, 'EMAIL_EXISTS')
  }
  
  // Hash password
  const passwordHash = await bcrypt.hash(userData.password, config.security.bcryptRounds)
  
  // Create user
  const user = await prisma.user.create({
    data: {
      email: userData.email.toLowerCase(),
      passwordHash,
      firstName: userData.firstName,
      lastName: userData.lastName,
      phone: userData.phone,
      language: userData.language,
      role: 'USER', // Default role
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      language: true,
    },
  })
  
  // Generate token
  const token = generateToken({
    id: user.id,
    email: user.email,
    role: user.role,
  })
  
  // Log successful registration
  loggers.auth.register(user.id, user.email)
  
  res.status(201).json({
    success: true,
    data: {
      token,
      user,
    },
    message: 'Registration successful',
  })
}))

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved
 *       401:
 *         description: Unauthorized
 */
router.get('/me', authenticate, asyncHandler(async (req, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      phone: true,
      role: true,
      language: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  })
  
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND')
  }
  
  res.json({
    success: true,
    data: { user },
  })
}))

/**
 * @swagger
 * /api/auth/change-password:
 *   post:
 *     summary: Change user password
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password changed successfully
 *       401:
 *         description: Invalid current password
 */
router.post('/change-password', authenticate, asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = changePasswordSchema.parse(req.body)
  
  // Get user with password hash
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      passwordHash: true,
    },
  })
  
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND')
  }
  
  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash)
  if (!isCurrentPasswordValid) {
    throw new AppError('Current password is incorrect', 401, 'INVALID_CURRENT_PASSWORD')
  }
  
  // Hash new password
  const newPasswordHash = await bcrypt.hash(newPassword, config.security.bcryptRounds)
  
  // Update password
  await prisma.user.update({
    where: { id: user.id },
    data: { passwordHash: newPasswordHash },
  })
  
  res.json({
    success: true,
    message: 'Password changed successfully',
  })
}))

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: User logout
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.post('/logout', authenticate, asyncHandler(async (req, res) => {
  // Log logout
  loggers.auth.logout(req.user!.id, req.user!.email)

  res.json({
    success: true,
    message: 'Logout successful',
  })
}))

// ===== Two-Factor Authentication Routes =====

/**
 * @swagger
 * /api/auth/2fa/setup:
 *   post:
 *     summary: Setup 2FA for user
 *     tags: [Two-Factor Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 2FA setup initiated
 */
router.post('/2fa/setup', authenticate, asyncHandler(async (req, res) => {
  const user = req.user!

  // Check if 2FA is already enabled
  const existingUser = await prisma.user.findUnique({
    where: { id: user.id },
    select: { twoFactorEnabled: true },
  })

  if (existingUser?.twoFactorEnabled) {
    throw new AppError('Two-factor authentication is already enabled', 400, 'TWO_FACTOR_ALREADY_ENABLED')
  }

  // Generate secret and QR code
  const { secret, otpauthUrl } = generateSecret(user.email)
  const qrCode = await generateQRCode(otpauthUrl)

  res.json({
    success: true,
    data: {
      secret,
      qrCode,
      manualEntryKey: secret,
    },
    message: 'Scan the QR code with your authenticator app',
  })
}))

/**
 * @swagger
 * /api/auth/2fa/enable:
 *   post:
 *     summary: Enable 2FA after verification
 *     tags: [Two-Factor Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - secret
 *             properties:
 *               token:
 *                 type: string
 *               secret:
 *                 type: string
 *     responses:
 *       200:
 *         description: 2FA enabled successfully
 */
router.post('/2fa/enable', authenticate, asyncHandler(async (req, res) => {
  const user = req.user!
  const { token, secret } = req.body

  if (!token || !secret) {
    throw new AppError('Token and secret are required', 400, 'MISSING_REQUIRED_FIELDS')
  }

  // Verify the token
  if (!validate2FASetup(secret, token)) {
    throw new AppError('Invalid token', 400, 'INVALID_TOKEN')
  }

  // Generate backup codes
  const backupCodes = generateBackupCodes()
  const hashedBackupCodes = hashBackupCodes(backupCodes)

  // Enable 2FA for the user
  await prisma.user.update({
    where: { id: user.id },
    data: {
      twoFactorEnabled: true,
      twoFactorSecret: secret,
      backupCodes: JSON.stringify(hashedBackupCodes),
    },
  })

  res.json({
    success: true,
    data: {
      backupCodes,
    },
    message: 'Two-factor authentication enabled successfully. Save these backup codes in a safe place.',
  })
}))

/**
 * @swagger
 * /api/auth/2fa/disable:
 *   post:
 *     summary: Disable 2FA
 *     tags: [Two-Factor Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *     responses:
 *       200:
 *         description: 2FA disabled successfully
 */
router.post('/2fa/disable', authenticate, asyncHandler(async (req, res) => {
  const user = req.user!
  const { token } = verify2FASchema.parse(req.body)

  // Get user's 2FA settings
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: {
      twoFactorEnabled: true,
      twoFactorSecret: true,
      backupCodes: true,
    },
  })

  if (!userData?.twoFactorEnabled) {
    throw new AppError('Two-factor authentication is not enabled', 400, 'TWO_FACTOR_NOT_ENABLED')
  }

  // Verify token or backup code
  let isValid = false
  let remainingBackupCodes = userData.backupCodes ? JSON.parse(userData.backupCodes) : []

  if (userData.twoFactorSecret && verifyToken(userData.twoFactorSecret, token)) {
    isValid = true
  } else if (userData.backupCodes) {
    const backupResult = verifyHashedBackupCode(JSON.parse(userData.backupCodes), token)
    if (backupResult.isValid) {
      isValid = true
      remainingBackupCodes = backupResult.remainingCodes
    }
  }

  if (!isValid) {
    throw new AppError('Invalid token or backup code', 400, 'INVALID_TOKEN')
  }

  // Disable 2FA
  await prisma.user.update({
    where: { id: user.id },
    data: {
      twoFactorEnabled: false,
      twoFactorSecret: null,
      backupCodes: null,
    },
  })

  res.json({
    success: true,
    message: 'Two-factor authentication disabled successfully',
  })
}))

/**
 * @swagger
 * /api/auth/2fa/verify:
 *   post:
 *     summary: Verify 2FA token
 *     tags: [Two-Factor Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token verified successfully
 */
router.post('/2fa/verify', authenticate, asyncHandler(async (req, res) => {
  const user = req.user!
  const { token } = verify2FASchema.parse(req.body)

  // Get user's 2FA settings
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: {
      twoFactorEnabled: true,
      twoFactorSecret: true,
      backupCodes: true,
    },
  })

  if (!userData?.twoFactorEnabled) {
    throw new AppError('Two-factor authentication is not enabled', 400, 'TWO_FACTOR_NOT_ENABLED')
  }

  // Verify token or backup code
  let isValid = false
  let usedBackupCode = false
  let remainingBackupCodes = userData.backupCodes ? JSON.parse(userData.backupCodes) : []

  if (userData.twoFactorSecret && verifyToken(userData.twoFactorSecret, token)) {
    isValid = true
  } else if (userData.backupCodes) {
    const backupResult = verifyHashedBackupCode(JSON.parse(userData.backupCodes), token)
    if (backupResult.isValid) {
      isValid = true
      usedBackupCode = true
      remainingBackupCodes = backupResult.remainingCodes

      // Update remaining backup codes
      await prisma.user.update({
        where: { id: user.id },
        data: { backupCodes: JSON.stringify(remainingBackupCodes) },
      })
    }
  }

  if (!isValid) {
    throw new AppError('Invalid token or backup code', 400, 'INVALID_TOKEN')
  }

  res.json({
    success: true,
    data: {
      verified: true,
      usedBackupCode,
      remainingBackupCodes: usedBackupCode ? remainingBackupCodes.length : undefined,
    },
    message: 'Token verified successfully',
  })
}))

export default router
