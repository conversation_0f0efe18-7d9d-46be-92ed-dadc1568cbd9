<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل صلاحيات المستخدم - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #ecf0f1;
            --font-arabic: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic) !important;
            font-weight: 600 !important;
        }

        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic) !important;
            font-weight: 600 !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1400px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .permissions-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .user-info-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid var(--secondary-color);
        }

        .user-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-group {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .info-group h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.1rem;
            border-bottom: 2px solid var(--light-color);
            padding-bottom: 8px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            font-family: var(--font-arabic);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .permission-category {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .permission-category:hover {
            border-color: var(--secondary-color);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.1);
        }

        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #dee2e6;
        }

        .category-icon {
            font-size: 2rem;
            margin-left: 15px;
        }

        .category-title {
            flex: 1;
        }

        .category-title h3 {
            color: var(--primary-color);
            margin-bottom: 5px;
            font-size: 1.3rem;
        }

        .category-title p {
            color: #666;
            font-size: 0.9rem;
        }

        .select-all-btn {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .select-all-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .permission-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            border-radius: 8px;
            transition: background 0.3s ease;
            cursor: pointer;
        }

        .permission-item:hover {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .permission-checkbox {
            margin-left: 12px;
            transform: scale(1.2);
            cursor: pointer;
        }

        .permission-label {
            flex: 1;
            cursor: pointer;
        }

        .permission-name {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 3px;
        }

        .permission-desc {
            font-size: 0.85rem;
            color: #666;
            line-height: 1.4;
        }

        .permission-level {
            background: var(--warning-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .level-basic { background: var(--success-color); }
        .level-advanced { background: var(--warning-color); }
        .level-admin { background: var(--danger-color); }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid var(--light-color);
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-arabic);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #229954);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .quick-templates {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border: 2px solid var(--warning-color);
        }

        .quick-templates h3 {
            color: var(--warning-color);
            margin-bottom: 15px;
            text-align: center;
        }

        .template-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .template-btn {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--warning-color);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .template-btn:hover {
            background: var(--warning-color);
            color: white;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        @media (max-width: 768px) {
            .permissions-grid {
                grid-template-columns: 1fr;
            }

            .user-info-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .template-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🔐</span>
                <span>نظام إدارة الشحنات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="user-management.html">إدارة المستخدمين</a>
                <a href="user-permissions-advanced.html" class="active">صلاحيات المستخدم</a>
                <a href="reports.html">التقارير</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1>🔐 تعديل صلاحيات المستخدم</h1>
            <p>إدارة تفصيلية لصلاحيات المستخدم في النظام</p>
        </div>

        <div class="permissions-container">
            <!-- معلومات المستخدم -->
            <div class="user-info-section">
                <h2 style="color: var(--primary-color); margin-bottom: 20px;">👤 بيانات المستخدم</h2>
                <div class="user-info-grid">
                    <div class="info-group">
                        <h3>المعلومات الأساسية</h3>
                        <div class="form-group">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="userName" value="محمد علي">
                        </div>
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="userEmail" value="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="info-group">
                        <h3>الدور والحالة</h3>
                        <div class="form-group">
                            <label class="form-label">الدور الحالي</label>
                            <select class="form-control" id="userRole">
                                <option value="admin">مدير النظام</option>
                                <option value="manager">مدير</option>
                                <option value="distributor" selected>موزع</option>
                                <option value="employee">موظف</option>
                                <option value="viewer">مشاهد</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">حالة المستخدم</label>
                            <div class="status-indicator status-active">
                                <span>🟢</span>
                                <span>مستخدم نشط</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-group">
                        <h3>معلومات إضافية</h3>
                        <div class="form-group">
                            <label class="form-label">الفرع</label>
                            <select class="form-control" id="userBranch">
                                <option value="main">الفرع الرئيسي</option>
                                <option value="riyadh">فرع الرياض</option>
                                <option value="jeddah" selected>فرع جدة</option>
                                <option value="dammam">فرع الدمام</option>
                                <option value="kuwait">فرع الكويت</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="userPhone" value="+966509876543">
                        </div>
                    </div>
                </div>
            </div>

            <!-- قوالب سريعة -->
            <div class="quick-templates">
                <h3>⚡ قوالب الصلاحيات السريعة</h3>
                <div class="template-buttons">
                    <button class="template-btn" onclick="applyTemplate('admin')">🔴 مدير النظام - جميع الصلاحيات</button>
                    <button class="template-btn" onclick="applyTemplate('manager')">🟠 مدير - صلاحيات إدارية</button>
                    <button class="template-btn" onclick="applyTemplate('distributor')">🟣 موزع - إدارة الشحنات والمدفوعات</button>
                    <button class="template-btn" onclick="applyTemplate('employee')">🟡 موظف - العمليات الأساسية</button>
                    <button class="template-btn" onclick="applyTemplate('viewer')">🟢 مشاهد - عرض فقط</button>
                    <button class="template-btn" onclick="clearAllPermissions()">⚪ مسح جميع الصلاحيات</button>
                </div>
            </div>

            <!-- الصلاحيات التفصيلية -->
            <div class="permissions-grid" id="permissionsGrid">
                <!-- سيتم ملء الصلاحيات بواسطة JavaScript -->
            </div>

            <!-- أزرار الحفظ -->
            <div class="action-buttons">
                <button class="btn btn-success" onclick="savePermissions()">
                    💾 حفظ الصلاحيات
                </button>
                <button class="btn btn-primary" onclick="previewPermissions()">
                    👁️ معاينة الصلاحيات
                </button>
                <button class="btn btn-secondary" onclick="goBack()">
                    ↩️ العودة
                </button>
            </div>
        </div>
    </main>

    <script src="js/database-simple.js"></script>
    <script>
        // الصلاحيات المتقدمة للنظام
        const advancedPermissions = {
            general: {
                name: 'الصلاحيات العامة',
                icon: '⚙️',
                description: 'إدارة النظام والإعدادات العامة',
                permissions: [
                    { id: 'system_admin', name: 'إدارة البرنامج', desc: 'الوصول الكامل لإدارة النظام', level: 'admin' },
                    { id: 'backup_create', name: 'عمل نسخة احتياطية', desc: 'إنشاء نسخ احتياطية من البيانات', level: 'advanced' },
                    { id: 'backup_restore', name: 'استرجاع نسخة احتياطية', desc: 'استعادة البيانات من النسخ الاحتياطية', level: 'admin' },
                    { id: 'datetime_edit', name: 'تعديل التاريخ والوقت', desc: 'تعديل إعدادات التاريخ والوقت في النظام', level: 'admin' },
                    { id: 'user_active', name: 'مستخدم نشط', desc: 'تفعيل وإلغاء تفعيل المستخدمين', level: 'advanced' }
                ]
            },
            shipments: {
                name: 'إدارة الشحنات',
                icon: '📦',
                description: 'صلاحيات التعامل مع الشحنات',
                permissions: [
                    { id: 'shipments_view', name: 'عرض الشحنات', desc: 'عرض قائمة الشحنات وتفاصيلها', level: 'basic' },
                    { id: 'shipments_create', name: 'إنشاء شحنة جديدة', desc: 'إضافة شحنات جديدة للنظام', level: 'basic' },
                    { id: 'shipments_edit', name: 'تعديل الشحنات', desc: 'تعديل بيانات الشحنات الموجودة', level: 'basic' },
                    { id: 'shipments_delete', name: 'حذف الشحنات', desc: 'حذف الشحنات من النظام', level: 'advanced' },
                    { id: 'shipments_today_only', name: 'عرض شحنات اليوم فقط', desc: 'قصر العرض على شحنات اليوم الحالي', level: 'basic' },
                    { id: 'shipments_track', name: 'تتبع الشحنات', desc: 'تتبع حالة الشحنات ومواقعها', level: 'basic' },
                    { id: 'shipments_print', name: 'طباعة الشحنات', desc: 'طباعة تفاصيل وإيصالات الشحنات', level: 'basic' },
                    { id: 'shipments_returns', name: 'مرتجعات الشحنات', desc: 'إدارة مرتجعات الشحنات', level: 'advanced' },
                    { id: 'shipments_follow', name: 'متابعة الشحنات', desc: 'متابعة حالة الشحنات والتحديثات', level: 'basic' },
                    { id: 'shipments_electronic', name: 'إرسال الشحنات الإلكترونية', desc: 'إرسال تفاصيل الشحنات إلكترونياً', level: 'advanced' }
                ]
            },
            customers: {
                name: 'إدارة العملاء',
                icon: '👥',
                description: 'صلاحيات التعامل مع العملاء',
                permissions: [
                    { id: 'customers_view', name: 'عرض العملاء', desc: 'عرض قائمة العملاء وبياناتهم', level: 'basic' },
                    { id: 'customers_create', name: 'إضافة عميل جديد', desc: 'إضافة عملاء جدد للنظام', level: 'basic' },
                    { id: 'customers_edit', name: 'تعديل بيانات العملاء', desc: 'تعديل معلومات العملاء الموجودين', level: 'basic' },
                    { id: 'customers_delete', name: 'حذف العملاء', desc: 'حذف العملاء من النظام', level: 'advanced' },
                    { id: 'customers_balance', name: 'رصيد العميل', desc: 'عرض وإدارة أرصدة العملاء', level: 'basic' },
                    { id: 'customers_statement', name: 'كشف حساب العميل', desc: 'عرض كشف حساب مفصل للعملاء', level: 'basic' },
                    { id: 'customers_types', name: 'أنواع العملاء', desc: 'إدارة أنواع العملاء (عميل، مورد، مندوب، أخرى)', level: 'advanced' }
                ]
            },
            financial: {
                name: 'الإدارة المالية',
                icon: '💰',
                description: 'صلاحيات التعامل مع الأمور المالية',
                permissions: [
                    { id: 'treasury_view', name: 'عرض حركة الخزينة ورصيدها', desc: 'عرض تفاصيل الخزينة والأرصدة', level: 'basic' },
                    { id: 'treasury_receipts', name: 'تحليل المقبوضات', desc: 'تحليل وعرض المقبوضات المالية', level: 'basic' },
                    { id: 'treasury_expenses', name: 'تحليل المصروفات', desc: 'تحليل وعرض المصروفات المالية', level: 'basic' },
                    { id: 'treasury_transfer', name: 'تحويل من خزينة لأخرى', desc: 'تحويل الأموال بين الخزائن', level: 'advanced' },
                    { id: 'treasury_allowed', name: 'الخزائن المسموح بها', desc: 'تحديد الخزائن المسموح للمستخدم بالوصول إليها', level: 'advanced' },
                    { id: 'payment_methods', name: 'طرق الدفع المسموحة', desc: 'تحديد طرق الدفع (نقدي، كي نت، تحويل)', level: 'basic' },
                    { id: 'installments', name: 'التقسيط', desc: 'إدارة عمليات التقسيط والأقساط', level: 'advanced' },
                    { id: 'checks_follow', name: 'متابعة سداد الشيكات والأقساط', desc: 'متابعة حالة الشيكات والأقساط', level: 'advanced' },
                    { id: 'treasury_cancel', name: 'إلغاء الخزينة في الصرف أو القبض', desc: 'إلغاء تأثير العمليات على الخزينة', level: 'admin' }
                ]
            },
            pricing: {
                name: 'إدارة الأسعار والخصومات',
                icon: '💲',
                description: 'صلاحيات التحكم في الأسعار والخصومات',
                permissions: [
                    { id: 'prices_edit', name: 'تعديل أسعار البيع', desc: 'تعديل أسعار الخدمات والمنتجات', level: 'advanced' },
                    { id: 'discount_add', name: 'إضافة خصم للفاتورة', desc: 'إضافة خصومات على الفواتير', level: 'basic' },
                    { id: 'discount_max_value', name: 'أعلى قيمة خصم', desc: 'تحديد الحد الأقصى لقيمة الخصم', level: 'advanced' },
                    { id: 'discount_max_percent', name: 'أعلى نسبة خصم', desc: 'تحديد الحد الأقصى لنسبة الخصم', level: 'advanced' },
                    { id: 'sell_below_min', name: 'البيع بأقل من أقل سعر بيع', desc: 'السماح بالبيع بأسعار أقل من الحد الأدنى', level: 'admin' },
                    { id: 'sell_below_cost', name: 'البيع بأقل من سعر الشراء', desc: 'السماح بالبيع بأسعار أقل من التكلفة', level: 'admin' },
                    { id: 'tax_edit_sale', name: 'تعديل الضريبة في البيع', desc: 'تعديل نسبة الضريبة في عمليات البيع', level: 'advanced' },
                    { id: 'tax_edit_purchase', name: 'تعديل الضريبة في الشراء', desc: 'تعديل نسبة الضريبة في عمليات الشراء', level: 'advanced' }
                ]
            },
            reports: {
                name: 'التقارير المتقدمة',
                icon: '📊',
                description: 'صلاحيات الوصول للتقارير والإحصائيات',
                permissions: [
                    { id: 'reports_daily', name: 'تقرير الحركة اليومية', desc: 'عرض تقرير مفصل للحركة اليومية', level: 'basic' },
                    { id: 'reports_sales', name: 'تقرير تحليل المبيعات', desc: 'تحليل مفصل لبيانات المبيعات', level: 'basic' },
                    { id: 'reports_purchases', name: 'تقرير تحليل المشتريات', desc: 'تحليل مفصل لبيانات المشتريات', level: 'basic' },
                    { id: 'reports_profits', name: 'عرض أرباح الفواتير', desc: 'عرض تفاصيل الأرباح والخسائر', level: 'advanced' },
                    { id: 'reports_current_profit', name: 'عرض ربح الفاتورة الحالية', desc: 'عرض ربح الفاتورة أثناء إنشائها', level: 'advanced' },
                    { id: 'reports_advanced', name: 'التقارير المتقدمة', desc: 'الوصول لجميع التقارير المتقدمة', level: 'advanced' }
                ]
            },
            branches: {
                name: 'إدارة الفروع',
                icon: '🏢',
                description: 'صلاحيات التعامل مع الفروع والمخازن',
                permissions: [
                    { id: 'branches_allowed', name: 'الفروع المسموح بها', desc: 'تحديد الفروع المسموح للمستخدم بالوصول إليها', level: 'advanced' },
                    { id: 'warehouses_allowed', name: 'المخازن المسموح بها', desc: 'تحديد المخازن المسموح للمستخدم بالوصول إليها', level: 'advanced' },
                    { id: 'branches_transfer', name: 'عرض تحويلات الفروع', desc: 'عرض وإدارة التحويلات بين الفروع', level: 'basic' },
                    { id: 'inventory_view', name: 'عرض الجرد', desc: 'عرض تفاصيل الجرد والمخزون', level: 'basic' },
                    { id: 'inventory_adjustments', name: 'عرض التسويات', desc: 'عرض وإدارة تسويات المخزون', level: 'advanced' }
                ]
            }
        };

        // المتغيرات العامة
        let currentUserPermissions = [];
        let selectedUserId = 'user3'; // افتراضي للموزع

        // الحصول على معرف المستخدم من الرابط أو التخزين المؤقت
        function getUserIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const userIdFromUrl = urlParams.get('userId');
            const userIdFromSession = sessionStorage.getItem('editingUserId');

            return userIdFromUrl || userIdFromSession || 'user3';
        }

        // انتظار تحميل قاعدة البيانات
        function waitForDatabase() {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 50; // 5 ثوان

                const checkDatabase = () => {
                    attempts++;

                    if (typeof db !== 'undefined' && db !== null) {
                        console.log('✅ تم تحميل قاعدة البيانات بنجاح');
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        console.error('❌ فشل في تحميل قاعدة البيانات');
                        reject(new Error('قاعدة البيانات غير متاحة'));
                    } else {
                        setTimeout(checkDatabase, 100);
                    }
                };

                checkDatabase();
            });
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📍 تحميل صفحة صلاحيات المستخدم المتقدمة...');

            try {
                // انتظار تحميل قاعدة البيانات
                await waitForDatabase();

                // تحديد المستخدم المراد تعديله
                selectedUserId = getUserIdFromUrl();
                console.log('🔍 تحرير صلاحيات المستخدم:', selectedUserId);

                // تحميل الصلاحيات
                loadPermissionsGrid();

                // تحميل بيانات المستخدم
                loadUserData();

                // إظهار رسالة ترحيب
                showWelcomeMessage();

                console.log('✅ تم تحميل الصفحة بنجاح');

            } catch (error) {
                console.error('خطأ في تحميل الصفحة:', error);
                alert('خطأ: ' + error.message + '\n\nيرجى إعادة تحميل الصفحة أو التحقق من الاتصال.');

                // إظهار رسالة خطأ
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = 'text-align: center; padding: 40px; color: #e74c3c; font-size: 1.2rem;';
                errorDiv.innerHTML = '❌ خطأ في تحميل النظام<br><br>يرجى إعادة تحميل الصفحة';
                document.querySelector('.permissions-container').appendChild(errorDiv);
            }
        });

        // تحميل شبكة الصلاحيات
        function loadPermissionsGrid() {
            const grid = document.getElementById('permissionsGrid');
            grid.innerHTML = '';

            Object.keys(advancedPermissions).forEach(categoryKey => {
                const category = advancedPermissions[categoryKey];
                const categoryDiv = createPermissionCategory(categoryKey, category);
                grid.appendChild(categoryDiv);
            });
        }

        // إنشاء فئة صلاحيات
        function createPermissionCategory(categoryKey, category) {
            const div = document.createElement('div');
            div.className = 'permission-category';

            let permissionsHtml = '';
            category.permissions.forEach(permission => {
                const isChecked = currentUserPermissions.includes(permission.id) ? 'checked' : '';
                permissionsHtml += `
                    <div class="permission-item" onclick="togglePermission('${permission.id}')">
                        <input type="checkbox" class="permission-checkbox" id="perm_${permission.id}" ${isChecked}>
                        <div class="permission-label">
                            <div class="permission-name">${permission.name}</div>
                            <div class="permission-desc">${permission.desc}</div>
                        </div>
                        <span class="permission-level level-${permission.level}">${getLevelText(permission.level)}</span>
                    </div>
                `;
            });

            div.innerHTML = `
                <div class="category-header">
                    <div class="category-icon">${category.icon}</div>
                    <div class="category-title">
                        <h3>${category.name}</h3>
                        <p>${category.description}</p>
                    </div>
                    <button class="select-all-btn" onclick="toggleCategoryPermissions('${categoryKey}')">
                        تحديد الكل
                    </button>
                </div>
                ${permissionsHtml}
            `;

            return div;
        }

        // الحصول على نص المستوى
        function getLevelText(level) {
            const levels = {
                'basic': 'أساسي',
                'advanced': 'متقدم',
                'admin': 'إداري'
            };
            return levels[level] || level;
        }

        // تبديل صلاحية واحدة
        function togglePermission(permissionId) {
            const checkbox = document.getElementById('perm_' + permissionId);
            checkbox.checked = !checkbox.checked;

            if (checkbox.checked) {
                if (!currentUserPermissions.includes(permissionId)) {
                    currentUserPermissions.push(permissionId);
                }
            } else {
                currentUserPermissions = currentUserPermissions.filter(id => id !== permissionId);
            }

            updatePermissionCount();
        }

        // تبديل جميع صلاحيات فئة
        function toggleCategoryPermissions(categoryKey) {
            const category = advancedPermissions[categoryKey];
            const allChecked = category.permissions.every(p => currentUserPermissions.includes(p.id));

            category.permissions.forEach(permission => {
                const checkbox = document.getElementById('perm_' + permission.id);
                if (allChecked) {
                    // إلغاء تحديد الكل
                    checkbox.checked = false;
                    currentUserPermissions = currentUserPermissions.filter(id => id !== permission.id);
                } else {
                    // تحديد الكل
                    checkbox.checked = true;
                    if (!currentUserPermissions.includes(permission.id)) {
                        currentUserPermissions.push(permission.id);
                    }
                }
            });

            updatePermissionCount();
        }

        // تطبيق قالب صلاحيات
        function applyTemplate(templateType) {
            // مسح الصلاحيات الحالية
            currentUserPermissions = [];

            // تطبيق القالب
            switch(templateType) {
                case 'admin':
                    // جميع الصلاحيات
                    Object.values(advancedPermissions).forEach(category => {
                        category.permissions.forEach(permission => {
                            currentUserPermissions.push(permission.id);
                        });
                    });
                    break;

                case 'manager':
                    // صلاحيات إدارية محدودة
                    currentUserPermissions = [
                        'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                        'customers_view', 'customers_create', 'customers_edit', 'customers_balance', 'customers_statement',
                        'treasury_view', 'treasury_receipts', 'treasury_expenses', 'payment_methods',
                        'prices_edit', 'discount_add', 'tax_edit_sale',
                        'reports_daily', 'reports_sales', 'reports_purchases', 'reports_profits',
                        'branches_transfer', 'inventory_view'
                    ];
                    break;

                case 'distributor':
                    // صلاحيات الموزعين
                    currentUserPermissions = [
                        'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print', 'shipments_follow',
                        'customers_view', 'customers_create', 'customers_edit', 'customers_balance',
                        'treasury_view', 'treasury_receipts', 'payment_methods', 'installments',
                        'discount_add', 'prices_edit',
                        'reports_daily', 'reports_sales',
                        'inventory_view'
                    ];
                    break;

                case 'employee':
                    // صلاحيات أساسية
                    currentUserPermissions = [
                        'shipments_view', 'shipments_create', 'shipments_edit', 'shipments_track', 'shipments_print',
                        'customers_view', 'customers_create', 'customers_edit',
                        'treasury_view', 'payment_methods',
                        'discount_add',
                        'reports_daily',
                        'inventory_view'
                    ];
                    break;

                case 'viewer':
                    // عرض فقط
                    currentUserPermissions = [
                        'shipments_view', 'shipments_track',
                        'customers_view', 'customers_balance',
                        'treasury_view',
                        'reports_daily', 'reports_sales',
                        'inventory_view'
                    ];
                    break;
            }

            // تحديث الواجهة
            updatePermissionsDisplay();
            updatePermissionCount();

            alert(`✅ تم تطبيق قالب "${getTemplateText(templateType)}" بنجاح!`);
        }

        // مسح جميع الصلاحيات
        function clearAllPermissions() {
            if (confirm('هل أنت متأكد من مسح جميع الصلاحيات؟')) {
                currentUserPermissions = [];
                updatePermissionsDisplay();
                updatePermissionCount();
                alert('✅ تم مسح جميع الصلاحيات');
            }
        }

        // تحديث عرض الصلاحيات
        function updatePermissionsDisplay() {
            Object.values(advancedPermissions).forEach(category => {
                category.permissions.forEach(permission => {
                    const checkbox = document.getElementById('perm_' + permission.id);
                    if (checkbox) {
                        checkbox.checked = currentUserPermissions.includes(permission.id);
                    }
                });
            });
        }

        // تحديث عداد الصلاحيات
        function updatePermissionCount() {
            const totalPermissions = Object.values(advancedPermissions)
                .reduce((total, category) => total + category.permissions.length, 0);

            console.log(`الصلاحيات المحددة: ${currentUserPermissions.length} من ${totalPermissions}`);
        }

        // الحصول على نص القالب
        function getTemplateText(templateType) {
            const templates = {
                'admin': 'مدير النظام',
                'manager': 'مدير',
                'distributor': 'موزع',
                'employee': 'موظف',
                'viewer': 'مشاهد'
            };
            return templates[templateType] || templateType;
        }

        // تحميل بيانات المستخدم
        function loadUserData() {
            try {
                if (typeof db !== 'undefined') {
                    const user = db.getUserById(selectedUserId);
                    if (user) {
                        document.getElementById('userName').value = user.name;
                        document.getElementById('userEmail').value = user.email;
                        document.getElementById('userRole').value = user.role;
                        document.getElementById('userBranch').value = user.branch || 'main';
                        document.getElementById('userPhone').value = user.phone || '';

                        // تحميل الصلاحيات المخصصة
                        currentUserPermissions = user.customPermissions || [];

                        // إذا لم توجد صلاحيات مخصصة، تطبيق قالب الدور
                        if (currentUserPermissions.length === 0) {
                            applyTemplate(user.role);
                        } else {
                            updatePermissionsDisplay();
                        }

                        console.log('✅ تم تحميل بيانات المستخدم:', user.name);
                    } else {
                        console.warn('⚠️ لم يتم العثور على المستخدم، استخدام بيانات افتراضية');
                        loadDefaultUserData();
                    }
                } else {
                    console.warn('⚠️ قاعدة البيانات غير متاحة، استخدام بيانات افتراضية');
                    loadDefaultUserData();
                }
            } catch (error) {
                console.error('خطأ في تحميل بيانات المستخدم:', error);
                loadDefaultUserData();
            }
        }

        // تحميل بيانات افتراضية للمستخدم
        function loadDefaultUserData() {
            const defaultUsers = {
                'user1': { name: 'أحمد محمد', email: '<EMAIL>', role: 'admin', branch: 'main', phone: '+966501234567' },
                'user2': { name: 'فاطمة أحمد', email: '<EMAIL>', role: 'manager', branch: 'riyadh', phone: '+966507654321' },
                'user3': { name: 'محمد علي', email: '<EMAIL>', role: 'distributor', branch: 'jeddah', phone: '+966509876543' },
                'user4': { name: 'سارة خالد', email: '<EMAIL>', role: 'employee', branch: 'dammam', phone: '+966502468135' },
                'user5': { name: 'عبدالله سالم', email: '<EMAIL>', role: 'viewer', branch: 'main', phone: '+966505555555' }
            };

            const user = defaultUsers[selectedUserId] || defaultUsers['user3'];

            document.getElementById('userName').value = user.name;
            document.getElementById('userEmail').value = user.email;
            document.getElementById('userRole').value = user.role;
            document.getElementById('userBranch').value = user.branch;
            document.getElementById('userPhone').value = user.phone;

            // تطبيق قالب الدور الافتراضي
            applyTemplate(user.role);

            console.log('✅ تم تحميل البيانات الافتراضية للمستخدم:', user.name);
        }

        // إظهار رسالة ترحيب
        function showWelcomeMessage() {
            const welcomeDiv = document.createElement('div');
            welcomeDiv.id = 'welcomeMessage';
            welcomeDiv.style.cssText = `
                background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
                border: 2px solid #27ae60;
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 30px;
                text-align: center;
                color: #2c3e50;
                animation: slideIn 0.5s ease-out;
            `;

            welcomeDiv.innerHTML = `
                <h3 style="color: #27ae60; margin-bottom: 15px;">🔐 مرحباً بك في نظام الصلاحيات المتقدم</h3>
                <p style="margin-bottom: 15px;">يمكنك تعديل صلاحيات المستخدم بدقة أو استخدام القوالب السريعة</p>
                <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-bottom: 15px;">
                    <span style="background: #3498db; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8rem;">💡 نصيحة: استخدم القوالب السريعة لتوفير الوقت</span>
                </div>
                <button onclick="hideWelcomeMessage()" style="background: #95a5a6; color: white; border: none; padding: 5px 15px; border-radius: 15px; cursor: pointer; font-size: 0.8rem; font-family: inherit;">إخفاء</button>
            `;

            const container = document.querySelector('.permissions-container');
            const userSection = document.querySelector('.user-info-section');
            container.insertBefore(welcomeDiv, userSection);
        }

        // إخفاء رسالة الترحيب
        function hideWelcomeMessage() {
            const welcomeDiv = document.getElementById('welcomeMessage');
            if (welcomeDiv) {
                welcomeDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => welcomeDiv.remove(), 300);
            }
        }

        // حفظ الصلاحيات
        function savePermissions() {
            try {
                const userName = document.getElementById('userName').value;
                const userEmail = document.getElementById('userEmail').value;
                const userRole = document.getElementById('userRole').value;
                const userBranch = document.getElementById('userBranch').value;
                const userPhone = document.getElementById('userPhone').value;

                if (typeof db !== 'undefined') {
                    const updateData = {
                        name: userName,
                        email: userEmail,
                        role: userRole,
                        branch: userBranch,
                        phone: userPhone,
                        customPermissions: currentUserPermissions
                    };

                    const result = db.updateUser(selectedUserId, updateData);
                    if (result) {
                        alert('✅ تم حفظ صلاحيات المستخدم بنجاح!');

                        // تسجيل النشاط
                        if (typeof permissionManager !== 'undefined') {
                            permissionManager.logUserActivity('تعديل صلاحيات مستخدم', `تم تعديل صلاحيات المستخدم: ${userName}`);
                        }
                    } else {
                        alert('❌ حدث خطأ أثناء حفظ الصلاحيات');
                    }
                } else {
                    alert('❌ قاعدة البيانات غير متاحة');
                }
            } catch (error) {
                console.error('خطأ في حفظ الصلاحيات:', error);
                alert('❌ حدث خطأ أثناء حفظ الصلاحيات: ' + error.message);
            }
        }

        // معاينة الصلاحيات
        function previewPermissions() {
            let previewText = 'معاينة صلاحيات المستخدم:\n\n';

            Object.keys(advancedPermissions).forEach(categoryKey => {
                const category = advancedPermissions[categoryKey];
                const categoryPermissions = category.permissions.filter(p => currentUserPermissions.includes(p.id));

                if (categoryPermissions.length > 0) {
                    previewText += `${category.name}:\n`;
                    categoryPermissions.forEach(permission => {
                        previewText += `  ✓ ${permission.name}\n`;
                    });
                    previewText += '\n';
                }
            });

            if (currentUserPermissions.length === 0) {
                previewText += 'لا توجد صلاحيات محددة للمستخدم.';
            }

            alert(previewText);
        }

        // العودة للصفحة السابقة
        function goBack() {
            if (confirm('هل أنت متأكد من العودة؟ قد تفقد التغييرات غير المحفوظة.')) {
                window.location.href = 'user-management.html';
            }
        }

        // تحديث الدور عند التغيير
        document.addEventListener('DOMContentLoaded', function() {
            const roleSelect = document.getElementById('userRole');
            if (roleSelect) {
                roleSelect.addEventListener('change', function() {
                    if (confirm('هل تريد تطبيق صلاحيات الدور الجديد؟')) {
                        applyTemplate(this.value);
                    }
                });
            }
        });
    </script>

    <!-- تطبيق خط SF Pro Arabic Display Semibold -->
    <script src="js/sf-pro-arabic-font.js"></script>
</body>
</html>
