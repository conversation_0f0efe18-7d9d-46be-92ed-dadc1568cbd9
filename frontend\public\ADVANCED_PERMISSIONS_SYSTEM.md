# نظام الصلاحيات المتقدم - Advanced Permissions System

## 🎯 نظرة عامة

تم إنشاء نظام صلاحيات متقدم وشامل لإدارة المستخدمين والمناديب والسائقين وموظفي الشركة في نظام إدارة الشحنات.

## 🔐 الأدوار المتاحة

### 1. مدير النظام (Admin)
- **الصلاحيات**: جميع الصلاحيات
- **الوصف**: صلاحيات كاملة لجميع أجزاء النظام
- **اللون**: أحمر (#dc3545)

### 2. مدير (Manager)
- **الصلاحيات**: صلاحيات إدارية محدودة
- **الوصف**: إدارة الشحنات، العملاء، التقارير، الموارد البشرية
- **اللون**: برتقالي (#fd7e14)

### 3. موظف (Employee)
- **الصلاحيات**: صلاحيات أساسية للعمليات اليومية
- **الوصف**: إدارة الشحنات والعملاء الأساسية
- **اللون**: أخضر فاتح (#20c997)

### 4. موزع (Distributor)
- **الصلاحيات**: صلاحيات الموزعين والشركاء
- **الوصف**: عرض وإنشاء الشحنات، إدارة العملاء
- **اللون**: بنفسجي (#6f42c1)

### 5. سائق (Driver)
- **الصلاحيات**: صلاحيات السائقين
- **الوصف**: عرض وتتبع الشحنات، تسجيل الحضور
- **اللون**: أزرق فاتح (#0dcaf0)

### 6. مندوب (Representative)
- **الصلاحيات**: صلاحيات المناديب
- **الوصف**: إدارة الشحنات والعملاء، تسجيل الحضور
- **اللون**: أخضر (#198754)

## ⚙️ فئات الصلاحيات

### 📦 إدارة الشحنات
- `shipments_view` - عرض الشحنات
- `shipments_create` - إنشاء شحنة
- `shipments_edit` - تعديل الشحنات
- `shipments_delete` - حذف الشحنات
- `shipments_track` - تتبع الشحنات
- `shipments_print` - طباعة البوليصات

### 👥 إدارة العملاء
- `customers_view` - عرض العملاء
- `customers_create` - إضافة عملاء
- `customers_edit` - تعديل العملاء
- `customers_delete` - حذف العملاء
- `customers_balance` - إدارة الأرصدة

### 💰 النظام المالي
- `financial_view` - عرض المالية
- `financial_payments` - إدارة المدفوعات
- `financial_invoices` - إدارة الفواتير
- `financial_commissions` - إدارة العمولات

### 👤 إدارة المستخدمين
- `users_view` - عرض المستخدمين
- `users_create` - إضافة مستخدمين
- `users_edit` - تعديل المستخدمين
- `users_delete` - حذف المستخدمين
- `users_permissions` - إدارة الصلاحيات
- `users_roles` - إدارة الأدوار

### 🏢 الموارد البشرية
- `hr_employees` - إدارة الموظفين
- `hr_drivers` - إدارة السائقين
- `hr_representatives` - إدارة المناديب
- `hr_attendance` - الحضور والانصراف
- `hr_payroll` - إدارة الرواتب

### 📊 التقارير
- `reports_view` - عرض التقارير
- `reports_advanced` - التقارير المتقدمة
- `reports_export` - تصدير التقارير
- `reports_analytics` - التحليلات

### ⚙️ إعدادات النظام
- `system_settings` - الإعدادات العامة
- `system_backup` - النسخ الاحتياطي
- `system_logs` - سجلات النظام
- `system_maintenance` - صيانة النظام

## 🛠️ الملفات الرئيسية

### 1. صفحة الإعدادات المتقدمة
- **الملف**: `advanced-settings.html`
- **الوصف**: واجهة شاملة لإدارة الصلاحيات والأدوار
- **الرابط**: http://localhost:3000/advanced-settings.html

### 2. نظام الصلاحيات المتقدم
- **الملف**: `js/advanced-permissions.js`
- **الوصف**: مكتبة JavaScript لإدارة الصلاحيات
- **الفئة**: `AdvancedPermissionsManager`

### 3. صفحة اختبار النظام
- **الملف**: `test-advanced-permissions.html`
- **الوصف**: اختبار شامل لجميع مكونات النظام
- **الرابط**: http://localhost:3000/test-advanced-permissions.html

## 🔧 كيفية الاستخدام

### 1. تحميل النظام
```javascript
// النظام يتم تحميله تلقائياً
const permissionsManager = window.permissionsManager;
```

### 2. التحقق من الصلاحيات
```javascript
// التحقق من صلاحية واحدة
if (permissionsManager.hasPermission('shipments_create')) {
    // المستخدم يمكنه إنشاء شحنات
}

// التحقق من صلاحيات متعددة
if (permissionsManager.hasAnyPermission(['users_view', 'users_edit'])) {
    // المستخدم يمكنه عرض أو تعديل المستخدمين
}
```

### 3. حفظ الصلاحيات
```javascript
const permissions = ['shipments_view', 'customers_view'];
permissionsManager.saveRolePermissions('employee', permissions);
```

### 4. تطبيق الصلاحيات على الواجهة
```html
<!-- إخفاء العنصر إذا لم تكن الصلاحية متاحة -->
<button data-permission="shipments_create">إنشاء شحنة</button>

<!-- تعطيل الزر إذا لم تكن الصلاحيات متاحة -->
<button data-permission-required="users_edit,users_delete">تعديل المستخدم</button>
```

## 📊 مصفوفة الصلاحيات

| الصلاحية | مدير النظام | مدير | موظف | موزع | سائق | مندوب |
|---------|------------|------|------|------|------|-------|
| عرض الشحنات | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| إنشاء شحنة | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ |
| حذف الشحنات | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| إدارة المستخدمين | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| النظام المالي | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ |

## 🔄 التكامل مع الأنظمة الأخرى

### 1. نظام الموارد البشرية
- ربط مع `hr-management.html`
- إدارة الموظفين والمناديب والسائقين
- تتبع الحضور والانصراف

### 2. نظام إدارة المستخدمين
- ربط مع `user-management.html`
- إدارة الأدوار والصلاحيات
- سجل النشاطات

### 3. لوحة التحكم الرئيسية
- ربط مع `main-dashboard.html`
- قائمة جانبية محدثة
- وصول سريع للإعدادات

## 📝 سجل العمليات

النظام يسجل جميع العمليات المتعلقة بالصلاحيات:
- تعديل صلاحيات الأدوار
- تغيير أدوار المستخدمين
- محاولات الوصول غير المصرح بها
- عمليات النسخ الاحتياطي والاستعادة

## 🔒 الأمان

- تشفير البيانات في localStorage
- تسجيل جميع العمليات
- التحقق من الصلاحيات في كل عملية
- حماية من الوصول غير المصرح به

## 🚀 الميزات المتقدمة

1. **النسخ الاحتياطي التلقائي**
2. **استعادة الإعدادات**
3. **اختبار شامل للنظام**
4. **واجهة مستخدم متجاوبة**
5. **دعم اللغة العربية**
6. **تكامل مع جميع أجزاء النظام**

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- استخدم صفحة الاختبار: `test-advanced-permissions.html`
- راجع سجل العمليات في الإعدادات المتقدمة
- تحقق من وحدة التحكم في المتصفح للأخطاء
