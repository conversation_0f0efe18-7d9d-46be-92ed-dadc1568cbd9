<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركاء النجاح | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            font-weight: 600;
            line-height: 1.6;
            color: #333;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.5rem;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
        }

        .logo-icon {
            background: white;
            color: #667eea;
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #ffd700;
        }

        .back-home {
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 20px;
            text-decoration: none;
            color: white;
            transition: background 0.3s;
        }

        .back-home:hover {
            background: rgba(255,255,255,0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 100px 0;
            color: white;
            text-align: center;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .hero p {
            font-size: 1.3rem;
            opacity: 0.9;
            line-height: 1.8;
        }

        /* Main Content */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            padding: 80px 0;
        }

        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .section-subtitle {
            font-size: 1.2rem;
            text-align: center;
            color: #666;
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Partners Grid */
        .partners-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-bottom: 80px;
        }

        .partner-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            border: 2px solid transparent;
        }

        .partner-card:hover {
            transform: translateY(-10px);
            border-color: #667eea;
        }

        .partner-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 3rem;
            color: white;
        }

        .partner-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .partner-type {
            color: #667eea;
            font-weight: 500;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .partner-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .partner-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        /* Categories */
        .categories {
            background: #f8f9fa;
        }

        .category-tabs {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 50px;
            flex-wrap: wrap;
        }

        .category-tab {
            background: white;
            border: 2px solid #e0e0e0;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .category-tab:hover,
        .category-tab.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* Success Stories */
        .success-stories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }

        .story-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .story-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .story-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .story-info h4 {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: #333;
        }

        .story-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .story-content {
            color: #666;
            line-height: 1.6;
            font-style: italic;
        }

        .story-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #667eea;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #666;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .cta-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .cta-section h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .cta-section p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 35px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .btn-primary {
            background: #ffd700;
            color: #333;
        }

        .btn-primary:hover {
            background: #ffed4e;
            transform: translateY(-2px);
        }

        .btn-outline {
            border: 2px solid white;
            color: white;
        }

        .btn-outline:hover {
            background: white;
            color: #667eea;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .category-tabs {
                flex-direction: column;
                align-items: center;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                <div class="logo-icon">🚚</div>
                <span>شركة الشحن السريع</span>
            </div>
            
            <ul class="nav-menu">
                <li><a href="home.html">الرئيسية</a></li>
                <li><a href="about-us.html">من نحن</a></li>
                <li><a href="success-partners.html">شركاء النجاح</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            
            <a href="home.html" class="back-home">← العودة للرئيسية</a>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>شركاء النجاح</h1>
            <p>نفخر بشراكاتنا الاستراتيجية مع أفضل الشركات والمؤسسات التي تساعدنا في تقديم خدمات متميزة وحلول مبتكرة لعملائنا</p>
        </div>
    </section>

    <!-- Main Partners Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">شركاؤنا الاستراتيجيون</h2>
            <p class="section-subtitle">نتعاون مع أفضل الشركات في مختلف القطاعات لضمان تقديم خدمات عالية الجودة</p>
            
            <div class="partners-grid">
                <div class="partner-card">
                    <div class="partner-logo">🏪</div>
                    <h3 class="partner-name">مجموعة التجارة الذكية</h3>
                    <p class="partner-type">شريك التجارة الإلكترونية</p>
                    <p class="partner-description">أكبر منصة تجارة إلكترونية في المملكة، نوفر لهم خدمات الشحن والتوصيل لأكثر من 50,000 طلب شهرياً</p>
                    <div class="partner-stats">
                        <div class="stat">
                            <div class="stat-number">50K+</div>
                            <div class="stat-label">طلب شهرياً</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">98%</div>
                            <div class="stat-label">معدل النجاح</div>
                        </div>
                    </div>
                </div>
                
                <div class="partner-card">
                    <div class="partner-logo">🏭</div>
                    <h3 class="partner-name">الشركة الوطنية للصناعات</h3>
                    <p class="partner-type">شريك صناعي</p>
                    <p class="partner-description">شراكة استراتيجية لنقل المنتجات الصناعية والمواد الخام بين المصانع والموزعين</p>
                    <div class="partner-stats">
                        <div class="stat">
                            <div class="stat-number">15</div>
                            <div class="stat-label">مصنع</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">شحنة شهرياً</div>
                        </div>
                    </div>
                </div>
                
                <div class="partner-card">
                    <div class="partner-logo">🏥</div>
                    <h3 class="partner-name">مجمع الرعاية الطبية</h3>
                    <p class="partner-type">شريك طبي</p>
                    <p class="partner-description">نقل المستلزمات الطبية والأدوية بأعلى معايير الأمان والسرعة للمستشفيات والصيدليات</p>
                    <div class="partner-stats">
                        <div class="stat">
                            <div class="stat-number">25</div>
                            <div class="stat-label">مستشفى</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">99.8%</div>
                            <div class="stat-label">دقة التسليم</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="section categories">
        <div class="container">
            <h2 class="section-title">شركاء حسب القطاع</h2>
            <p class="section-subtitle">اكتشف شركاءنا في مختلف القطاعات والصناعات</p>

            <div class="category-tabs">
                <div class="category-tab active" data-category="all">جميع الشركاء</div>
                <div class="category-tab" data-category="ecommerce">التجارة الإلكترونية</div>
                <div class="category-tab" data-category="retail">التجزئة</div>
                <div class="category-tab" data-category="manufacturing">الصناعة</div>
                <div class="category-tab" data-category="healthcare">الرعاية الصحية</div>
            </div>

            <div class="partners-grid" id="partnersContainer">
                <div class="partner-card" data-category="ecommerce">
                    <div class="partner-logo">🛒</div>
                    <h3 class="partner-name">سوق الخليج</h3>
                    <p class="partner-type">منصة تجارة إلكترونية</p>
                    <p class="partner-description">منصة رائدة للتسوق الإلكتروني تخدم ملايين العملاء في دول الخليج</p>
                </div>

                <div class="partner-card" data-category="retail">
                    <div class="partner-logo">🏬</div>
                    <h3 class="partner-name">مراكز التسوق الحديثة</h3>
                    <p class="partner-type">سلسلة متاجر</p>
                    <p class="partner-description">أكبر سلسلة مراكز تسوق في المملكة مع أكثر من 50 فرع</p>
                </div>

                <div class="partner-card" data-category="manufacturing">
                    <div class="partner-logo">⚙️</div>
                    <h3 class="partner-name">مصانع الأجهزة الذكية</h3>
                    <p class="partner-type">شركة تصنيع</p>
                    <p class="partner-description">متخصصة في تصنيع الأجهزة الإلكترونية والذكية للسوق المحلي والتصدير</p>
                </div>

                <div class="partner-card" data-category="healthcare">
                    <div class="partner-logo">💊</div>
                    <h3 class="partner-name">شبكة الصيدليات الوطنية</h3>
                    <p class="partner-type">سلسلة صيدليات</p>
                    <p class="partner-description">أكبر شبكة صيدليات في المملكة مع أكثر من 200 فرع</p>
                </div>

                <div class="partner-card" data-category="ecommerce">
                    <div class="partner-logo">📱</div>
                    <h3 class="partner-name">تطبيق التوصيل السريع</h3>
                    <p class="partner-type">تطبيق توصيل</p>
                    <p class="partner-description">تطبيق رائد لتوصيل الطعام والمنتجات اليومية في أقل من 30 دقيقة</p>
                </div>

                <div class="partner-card" data-category="retail">
                    <div class="partner-logo">👗</div>
                    <h3 class="partner-name">بوتيك الأزياء العصرية</h3>
                    <p class="partner-type">متاجر أزياء</p>
                    <p class="partner-description">سلسلة متاجر أزياء راقية تقدم أحدث صيحات الموضة العالمية</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Stories Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">قصص نجاح ملهمة</h2>
            <p class="section-subtitle">اكتشف كيف ساعدنا شركاءنا في تحقيق أهدافهم وتطوير أعمالهم</p>

            <div class="success-stories">
                <div class="story-card">
                    <div class="story-header">
                        <div class="story-logo">🛍️</div>
                        <div class="story-info">
                            <h4>متجر الهدايا الذكية</h4>
                            <p>شريك منذ 2020</p>
                        </div>
                    </div>
                    <div class="story-content">
                        "بفضل خدمات الشحن السريع والموثوق، تمكنا من زيادة مبيعاتنا بنسبة 300% خلال عامين. فريق العمل محترف والخدمة ممتازة."
                    </div>
                    <div class="story-metrics">
                        <div class="metric">
                            <div class="metric-value">300%</div>
                            <div class="metric-label">زيادة المبيعات</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">99.5%</div>
                            <div class="metric-label">رضا العملاء</div>
                        </div>
                    </div>
                </div>

                <div class="story-card">
                    <div class="story-header">
                        <div class="story-logo">🏥</div>
                        <div class="story-info">
                            <h4>مستشفى الرعاية المتقدمة</h4>
                            <p>شريك منذ 2019</p>
                        </div>
                    </div>
                    <div class="story-content">
                        "الدقة في مواعيد التسليم والحفاظ على سلسلة التبريد للأدوية أمر بالغ الأهمية بالنسبة لنا. شركة الشحن السريع تفهم احتياجاتنا تماماً."
                    </div>
                    <div class="story-metrics">
                        <div class="metric">
                            <div class="metric-value">100%</div>
                            <div class="metric-label">دقة التسليم</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">24/7</div>
                            <div class="metric-label">خدمة طوارئ</div>
                        </div>
                    </div>
                </div>

                <div class="story-card">
                    <div class="story-header">
                        <div class="story-logo">🏭</div>
                        <div class="story-info">
                            <h4>مصنع الإلكترونيات الحديثة</h4>
                            <p>شريك منذ 2021</p>
                        </div>
                    </div>
                    <div class="story-content">
                        "التعامل مع المنتجات الحساسة يتطلب خبرة ومهارة. فريق الشحن السريع يتعامل مع منتجاتنا بعناية فائقة ويضمن وصولها سليمة."
                    </div>
                    <div class="story-metrics">
                        <div class="metric">
                            <div class="metric-value">0.1%</div>
                            <div class="metric-label">معدل التلف</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">50+</div>
                            <div class="metric-label">مدينة تغطية</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>انضم إلى شركاء النجاح</h2>
                <p>هل تريد أن تكون جزءاً من قصة نجاحنا؟ تواصل معنا اليوم واكتشف كيف يمكننا مساعدتك في تطوير أعمالك</p>
                <div class="cta-buttons">
                    <a href="#contact" class="btn btn-primary">تواصل معنا</a>
                    <a href="index.html" class="btn btn-outline">نظام الإدارة</a>
                </div>
            </div>
        </div>
    </section>

    <script>
        // تحسين وظائف صفحة شركاء النجاح
        function initializePartnersPage() {
            try {
                initializeCategoryFiltering();
                initializeAnimations();
                initializeSmoothScrolling();
                console.log('تم تحميل صفحة شركاء النجاح بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل صفحة شركاء النجاح:', error);
            }
        }

        // تصفية الفئات
        function initializeCategoryFiltering() {
            const categoryTabs = document.querySelectorAll('.category-tab');
            if (!categoryTabs.length) {
                console.warn('لم يتم العثور على تبويبات الفئات');
                return;
            }

            categoryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    try {
                        // Remove active class from all tabs
                        categoryTabs.forEach(t => t.classList.remove('active'));
                        // Add active class to clicked tab
                        this.classList.add('active');

                        const category = this.dataset.category;
                        const partners = document.querySelectorAll('.partner-card');

                        partners.forEach((partner, index) => {
                            if (category === 'all' || partner.dataset.category === category) {
                                partner.style.display = 'block';
                                partner.style.animation = `fadeIn 0.5s ease ${index * 0.1}s forwards`;
                            } else {
                                partner.style.display = 'none';
                            }
                        });

                        console.log(`تم تصفية الشركاء حسب الفئة: ${category}`);
                    } catch (error) {
                        console.error('خطأ في تصفية الفئات:', error);
                    }
                });
            });
        }

        // تهيئة الرسوم المتحركة
        function initializeAnimations() {
            // Add fade in animation styles
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                @keyframes slideInLeft {
                    from { opacity: 0; transform: translateX(-30px); }
                    to { opacity: 1; transform: translateX(0); }
                }
                @keyframes slideInRight {
                    from { opacity: 0; transform: translateX(30px); }
                    to { opacity: 1; transform: translateX(0); }
                }
            `;
            document.head.appendChild(style);

            // Animate cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        const animationType = index % 2 === 0 ? 'slideInLeft' : 'slideInRight';
                        entry.target.style.animation = `${animationType} 0.6s ease forwards`;
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe all cards
            const cards = document.querySelectorAll('.partner-card, .story-card');
            cards.forEach(card => {
                card.style.opacity = '0';
                observer.observe(card);
            });
        }

        // تهيئة التمرير السلس
        function initializeSmoothScrolling() {
            const anchors = document.querySelectorAll('a[href^="#"]');
            if (!anchors.length) {
                console.warn('لم يتم العثور على روابط التمرير');
                return;
            }

            anchors.forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // تحريك الإحصائيات
        function animateStats() {
            const stats = document.querySelectorAll('.stat-number');
            if (!stats.length) return;

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target;
                        const finalValue = target.textContent;
                        const numericValue = parseInt(finalValue.replace(/[^0-9]/g, ''));

                        if (numericValue) {
                            let current = 0;
                            const increment = numericValue / 30;
                            const timer = setInterval(() => {
                                current += increment;
                                if (current >= numericValue) {
                                    target.textContent = finalValue;
                                    clearInterval(timer);
                                } else {
                                    target.textContent = Math.floor(current) + finalValue.replace(/[0-9]/g, '');
                                }
                            }, 50);
                        }
                        observer.unobserve(target);
                    }
                });
            }, { threshold: 0.5 });

            stats.forEach(stat => observer.observe(stat));
        }

        // تهيئة الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            try {
                initializePartnersPage();
                animateStats();
            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);

                // تهيئة احتياطية
                setTimeout(() => {
                    try {
                        initializeCategoryFiltering();
                        initializeAnimations();
                    } catch (fallbackError) {
                        console.error('خطأ في التهيئة الاحتياطية:', fallbackError);
                    }
                }, 1000);
            }
        });
    </script>

    <style>
        /* تطبيق خط SF Pro Arabic Display على جميع العناصر */
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, select, textarea, button, .btn {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }

        .nav a, .nav-links a {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }
    </style>
</body>
</html>
