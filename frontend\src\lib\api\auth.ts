import { api, ApiResponse } from './client'
import {
  User,
  LoginCredentials,
  LoginWith2FACredentials,
  RegisterData,
  AuthResponse,
  TwoFactorSetup,
  TwoFactorStatus,
  BackupCodesResponse,
  TwoFactorVerification,
  Enable2FARequest
} from '@/types/auth'

export const authApi = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/auth/login', credentials)
    return response.data
  },

  // Register user
  register: async (data: RegisterData): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/auth/register', data)
    return response.data
  },

  // Get current user profile
  getProfile: async (): Promise<User> => {
    const response = await api.get<{ user: User }>('/auth/me')
    return response.data.user
  },

  // Change password
  changePassword: async (data: {
    currentPassword: string
    newPassword: string
  }): Promise<void> => {
    await api.post('/auth/change-password', data)
  },

  // Logout user
  logout: async (): Promise<void> => {
    await api.post('/auth/logout')
  },

  // Refresh token (for future implementation)
  refreshToken: async (): Promise<{ token: string }> => {
    const response = await api.post<{ token: string }>('/auth/refresh')
    return response.data
  },

  // Two-Factor Authentication
  loginWith2FA: async (credentials: LoginWith2FACredentials): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/auth/login/2fa', credentials)
    return response.data
  },

  setup2FA: async (): Promise<TwoFactorSetup> => {
    const response = await api.post<TwoFactorSetup>('/auth/2fa/setup')
    return response.data
  },

  enable2FA: async (data: Enable2FARequest): Promise<BackupCodesResponse> => {
    const response = await api.post<BackupCodesResponse>('/auth/2fa/enable', data)
    return response.data
  },

  disable2FA: async (data: TwoFactorVerification): Promise<void> => {
    await api.post('/auth/2fa/disable', data)
  },

  verify2FA: async (data: TwoFactorVerification): Promise<{ verified: boolean; usedBackupCode?: boolean; remainingBackupCodes?: number }> => {
    const response = await api.post<{ verified: boolean; usedBackupCode?: boolean; remainingBackupCodes?: number }>('/auth/2fa/verify', data)
    return response.data
  },

  get2FAStatus: async (): Promise<TwoFactorStatus> => {
    const response = await api.get<TwoFactorStatus>('/auth/2fa/status')
    return response.data
  },

  generateBackupCodes: async (data: TwoFactorVerification): Promise<BackupCodesResponse> => {
    const response = await api.post<BackupCodesResponse>('/auth/2fa/backup-codes', data)
    return response.data
  },
}
