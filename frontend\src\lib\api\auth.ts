import { api, ApiResponse } from './client'
import { User, LoginCredentials, RegisterData, AuthResponse } from '@/types/auth'

export const authApi = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/auth/login', credentials)
    return response.data
  },

  // Register user
  register: async (data: RegisterData): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/auth/register', data)
    return response.data
  },

  // Get current user profile
  getProfile: async (): Promise<User> => {
    const response = await api.get<{ user: User }>('/auth/me')
    return response.data.user
  },

  // Change password
  changePassword: async (data: {
    currentPassword: string
    newPassword: string
  }): Promise<void> => {
    await api.post('/auth/change-password', data)
  },

  // Logout user
  logout: async (): Promise<void> => {
    await api.post('/auth/logout')
  },

  // Refresh token (for future implementation)
  refreshToken: async (): Promise<{ token: string }> => {
    const response = await api.post<{ token: string }>('/auth/refresh')
    return response.data
  },
}
