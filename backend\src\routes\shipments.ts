// Shipment Routes
// مسارات الشحنات

import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient, ShipmentStatus } from '@prisma/client'
import { asyncHandler, AppError } from '../middleware/errorHandler'
import { authenticate, authorize, requireManagerOrAdmin } from '../middleware/auth'
import { config } from '../config/config'
import { loggers } from '../utils/logger'

const router = Router()
const prisma = new PrismaClient()

// Validation schemas
const createShipmentSchema = z.object({
  senderId: z.string().uuid('Invalid sender ID'),
  receiverId: z.string().uuid('Invalid receiver ID'),
  distributorId: z.string().uuid('Invalid distributor ID').optional(),
  weight: z.number().positive('Weight must be positive').max(config.constants.maxShipmentWeight),
  length: z.number().positive('Length must be positive').max(config.constants.maxShipmentDimension).optional(),
  width: z.number().positive('Width must be positive').max(config.constants.maxShipmentDimension).optional(),
  height: z.number().positive('Height must be positive').max(config.constants.maxShipmentDimension).optional(),
  contents: z.string().max(config.constants.maxNotesLength).optional(),
  pickupAddress: z.string().min(1, 'Pickup address is required').max(config.constants.maxAddressLength),
  pickupCity: z.string().max(config.constants.maxNameLength).optional(),
  pickupCountry: z.string().max(config.constants.maxNameLength).optional(),
  deliveryAddress: z.string().min(1, 'Delivery address is required').max(config.constants.maxAddressLength),
  deliveryCity: z.string().max(config.constants.maxNameLength).optional(),
  deliveryCountry: z.string().max(config.constants.maxNameLength).optional(),
  estimatedDelivery: z.string().datetime().optional(),
  cost: z.number().positive('Cost must be positive'),
  currencyId: z.string().uuid('Invalid currency ID'),
  notes: z.string().max(config.constants.maxNotesLength).optional(),
  specialInstructions: z.string().max(config.constants.maxNotesLength).optional(),
})

const updateShipmentSchema = createShipmentSchema.partial()

const updateStatusSchema = z.object({
  status: z.enum(['PENDING', 'PICKED_UP', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED', 'RETURNED']),
  location: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  notes: z.string().max(config.constants.maxNotesLength).optional(),
})

// Generate tracking number
function generateTrackingNumber(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `${config.constants.trackingNumberPrefix}${timestamp}${random}`.toUpperCase()
}

/**
 * @swagger
 * /api/shipments:
 *   get:
 *     summary: Get shipments list
 *     tags: [Shipments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Shipments retrieved successfully
 */
router.get('/', authenticate, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1
  const limit = Math.min(parseInt(req.query.limit as string) || config.constants.defaultPageSize, config.constants.maxPageSize)
  const status = req.query.status as ShipmentStatus
  const search = req.query.search as string
  const skip = (page - 1) * limit

  // Build where clause
  const where: any = {}
  
  // Role-based filtering
  if (req.user!.role === 'DISTRIBUTOR') {
    const distributor = await prisma.distributor.findUnique({
      where: { userId: req.user!.id },
      select: { id: true },
    })
    
    if (distributor) {
      where.distributorId = distributor.id
    } else {
      return res.json({
        success: true,
        data: {
          shipments: [],
          pagination: { page, limit, total: 0, totalPages: 0 },
        },
      })
    }
  }
  
  if (status) {
    where.status = status
  }
  
  if (search) {
    where.OR = [
      { trackingNumber: { contains: search, mode: 'insensitive' } },
      { contents: { contains: search, mode: 'insensitive' } },
    ]
  }

  const [shipments, total] = await Promise.all([
    prisma.shipment.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        sender: { select: { id: true, name: true, email: true, phone: true, city: true, country: true } },
        receiver: { select: { id: true, name: true, email: true, phone: true, city: true, country: true } },
        distributor: { select: { id: true, name: true, phone: true, vehicleType: true, rating: true } },
        currency: { select: { id: true, code: true, symbol: true } },
        creator: { select: { id: true, firstName: true, lastName: true } },
      },
    }),
    prisma.shipment.count({ where }),
  ])

  const totalPages = Math.ceil(total / limit)

  res.json({
    success: true,
    data: {
      shipments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    },
  })
}))

/**
 * @swagger
 * /api/shipments:
 *   post:
 *     summary: Create new shipment
 *     tags: [Shipments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: Shipment created successfully
 */
router.post('/', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const shipmentData = createShipmentSchema.parse(req.body)
  
  // Verify sender and receiver exist
  const [sender, receiver, currency] = await Promise.all([
    prisma.customer.findUnique({ where: { id: shipmentData.senderId } }),
    prisma.customer.findUnique({ where: { id: shipmentData.receiverId } }),
    prisma.currency.findUnique({ where: { id: shipmentData.currencyId } }),
  ])
  
  if (!sender) {
    throw new AppError('Sender not found', 404, 'SENDER_NOT_FOUND')
  }
  
  if (!receiver) {
    throw new AppError('Receiver not found', 404, 'RECEIVER_NOT_FOUND')
  }
  
  if (!currency) {
    throw new AppError('Currency not found', 404, 'CURRENCY_NOT_FOUND')
  }
  
  // Verify distributor if provided
  if (shipmentData.distributorId) {
    const distributor = await prisma.distributor.findUnique({
      where: { id: shipmentData.distributorId },
    })
    
    if (!distributor) {
      throw new AppError('Distributor not found', 404, 'DISTRIBUTOR_NOT_FOUND')
    }
  }
  
  // Generate tracking number
  let trackingNumber: string
  let isUnique = false
  let attempts = 0
  
  do {
    trackingNumber = generateTrackingNumber()
    const existing = await prisma.shipment.findUnique({
      where: { trackingNumber },
    })
    isUnique = !existing
    attempts++
  } while (!isUnique && attempts < 10)
  
  if (!isUnique) {
    throw new AppError('Failed to generate unique tracking number', 500, 'TRACKING_NUMBER_GENERATION_FAILED')
  }
  
  // Set estimated delivery if not provided
  const estimatedDelivery = shipmentData.estimatedDelivery 
    ? new Date(shipmentData.estimatedDelivery)
    : new Date(Date.now() + config.constants.defaultEstimatedDeliveryDays * 24 * 60 * 60 * 1000)
  
  // Create shipment
  const shipment = await prisma.shipment.create({
    data: {
      ...shipmentData,
      trackingNumber,
      estimatedDelivery,
      createdBy: req.user!.id,
    },
    include: {
      sender: { select: { id: true, name: true, email: true, phone: true, city: true, country: true } },
      receiver: { select: { id: true, name: true, email: true, phone: true, city: true, country: true } },
      distributor: { select: { id: true, name: true, phone: true, vehicleType: true, rating: true } },
      currency: { select: { id: true, code: true, symbol: true } },
      creator: { select: { id: true, firstName: true, lastName: true } },
    },
  })
  
  // Log shipment creation
  loggers.shipment.created(shipment.id, shipment.trackingNumber, req.user!.id)
  
  res.status(201).json({
    success: true,
    data: { shipment },
    message: 'Shipment created successfully',
  })
}))

export default router
