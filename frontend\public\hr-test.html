<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الموارد البشرية</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .btn {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { transform: translateY(-2px); opacity: 0.9; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: monospace; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام الموارد البشرية</h1>
        <p>اختبار وظائف الإضافة والتعديل في نظام الموارد البشرية</p>

        <div class="test-section">
            <h3>🔗 روابط سريعة</h3>
            <a href="hr-management.html" class="btn btn-primary">🏢 نظام الموارد البشرية</a>
            <a href="main-dashboard.html" class="btn btn-info">🏠 لوحة التحكم</a>
            <a href="user-management.html" class="btn btn-success">👥 إدارة المستخدمين</a>
        </div>

        <div class="test-section">
            <h3>📊 اختبار البيانات</h3>
            <button class="btn btn-success" onclick="testAddEmployee()">➕ اختبار إضافة موظف</button>
            <button class="btn btn-warning" onclick="testEditEmployee()">✏️ اختبار تعديل موظف</button>
            <button class="btn btn-info" onclick="checkEmployeeData()">📋 فحص البيانات</button>
            <button class="btn btn-primary" onclick="clearEmployeeData()">🗑️ مسح البيانات</button>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>🔍 فحص النماذج</h3>
            <button class="btn btn-success" onclick="checkAddForm()">🔍 فحص نموذج الإضافة</button>
            <button class="btn btn-warning" onclick="checkEditForm()">🔍 فحص نموذج التعديل</button>
            <button class="btn btn-info" onclick="checkEventListeners()">🔍 فحص معالجات الأحداث</button>
            <div id="formResults"></div>
        </div>

        <div class="test-section">
            <h3>📝 سجل الأحداث</h3>
            <button class="btn btn-info" onclick="clearLog()">🗑️ مسح السجل</button>
            <div id="eventLog" class="log"></div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        let logContainer = null;

        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('eventLog');
            log('✅ تم تحميل صفحة الاختبار');
        });

        function log(message) {
            if (logContainer) {
                const timestamp = new Date().toLocaleTimeString('ar-SA');
                logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            console.log(message);
        }

        function clearLog() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }

        function testAddEmployee() {
            log('🔄 اختبار إضافة موظف...');
            
            // إضافة موظف تجريبي
            const testEmployee = {
                id: 'test' + Date.now(),
                name: 'موظف تجريبي ' + Math.floor(Math.random() * 1000),
                position: 'مطور تجريبي',
                department: 'تقنية المعلومات',
                phone: '0501234567',
                email: '<EMAIL>',
                salary: 5000,
                status: 'نشط',
                joinDate: new Date().toISOString().split('T')[0],
                avatar: 'م',
                createdAt: new Date().toISOString()
            };

            try {
                let employees = JSON.parse(localStorage.getItem('employees') || '[]');
                employees.push(testEmployee);
                localStorage.setItem('employees', JSON.stringify(employees));
                
                showResult('testResults', 'success', `✅ تم إضافة موظف تجريبي: ${testEmployee.name}`);
                log(`✅ تم إضافة موظف تجريبي: ${testEmployee.name}`);
            } catch (error) {
                showResult('testResults', 'error', `❌ خطأ في إضافة الموظف: ${error.message}`);
                log(`❌ خطأ في إضافة الموظف: ${error.message}`);
            }
        }

        function testEditEmployee() {
            log('🔄 اختبار تعديل موظف...');
            
            try {
                let employees = JSON.parse(localStorage.getItem('employees') || '[]');
                
                if (employees.length === 0) {
                    showResult('testResults', 'error', '❌ لا توجد موظفين للتعديل');
                    log('❌ لا توجد موظفين للتعديل');
                    return;
                }

                // تعديل أول موظف
                const employee = employees[0];
                const oldName = employee.name;
                employee.name = employee.name + ' (محدث)';
                employee.updatedAt = new Date().toISOString();

                localStorage.setItem('employees', JSON.stringify(employees));
                
                showResult('testResults', 'success', `✅ تم تعديل الموظف: ${oldName} → ${employee.name}`);
                log(`✅ تم تعديل الموظف: ${oldName} → ${employee.name}`);
            } catch (error) {
                showResult('testResults', 'error', `❌ خطأ في تعديل الموظف: ${error.message}`);
                log(`❌ خطأ في تعديل الموظف: ${error.message}`);
            }
        }

        function checkEmployeeData() {
            log('🔄 فحص بيانات الموظفين...');
            
            try {
                const employees = JSON.parse(localStorage.getItem('employees') || '[]');
                const count = employees.length;
                
                let result = `📊 عدد الموظفين: ${count}<br>`;
                
                if (count > 0) {
                    result += `👤 أول موظف: ${employees[0].name}<br>`;
                    result += `📧 البريد: ${employees[0].email}<br>`;
                    result += `💼 المنصب: ${employees[0].position}`;
                }
                
                showResult('testResults', 'info', result);
                log(`📊 تم فحص البيانات: ${count} موظف`);
            } catch (error) {
                showResult('testResults', 'error', `❌ خطأ في فحص البيانات: ${error.message}`);
                log(`❌ خطأ في فحص البيانات: ${error.message}`);
            }
        }

        function clearEmployeeData() {
            log('🔄 مسح بيانات الموظفين...');
            
            if (confirm('هل أنت متأكد من مسح جميع بيانات الموظفين؟')) {
                localStorage.removeItem('employees');
                showResult('testResults', 'success', '✅ تم مسح جميع بيانات الموظفين');
                log('✅ تم مسح جميع بيانات الموظفين');
            } else {
                log('❌ تم إلغاء عملية المسح');
            }
        }

        function checkAddForm() {
            log('🔄 فحص نموذج الإضافة...');
            
            // فتح نافذة جديدة للتحقق من النموذج
            const hrWindow = window.open('hr-management.html', '_blank');
            
            setTimeout(() => {
                try {
                    const addModal = hrWindow.document.getElementById('addEmployeeModal');
                    const addForm = hrWindow.document.getElementById('addEmployeeForm');
                    
                    let result = '';
                    if (addModal) result += '✅ نموذج الإضافة موجود<br>';
                    else result += '❌ نموذج الإضافة غير موجود<br>';
                    
                    if (addForm) result += '✅ نموذج الإضافة يعمل<br>';
                    else result += '❌ نموذج الإضافة لا يعمل<br>';
                    
                    showResult('formResults', addModal && addForm ? 'success' : 'error', result);
                    log('✅ تم فحص نموذج الإضافة');
                } catch (error) {
                    showResult('formResults', 'error', `❌ خطأ في فحص النموذج: ${error.message}`);
                    log(`❌ خطأ في فحص النموذج: ${error.message}`);
                }
            }, 2000);
        }

        function checkEditForm() {
            log('🔄 فحص نموذج التعديل...');
            
            const hrWindow = window.open('hr-management.html', '_blank');
            
            setTimeout(() => {
                try {
                    const editModal = hrWindow.document.getElementById('editEmployeeModal');
                    const editForm = hrWindow.document.getElementById('editEmployeeForm');
                    
                    let result = '';
                    if (editModal) result += '✅ نموذج التعديل موجود<br>';
                    else result += '❌ نموذج التعديل غير موجود<br>';
                    
                    if (editForm) result += '✅ نموذج التعديل يعمل<br>';
                    else result += '❌ نموذج التعديل لا يعمل<br>';
                    
                    showResult('formResults', editModal && editForm ? 'success' : 'error', result);
                    log('✅ تم فحص نموذج التعديل');
                } catch (error) {
                    showResult('formResults', 'error', `❌ خطأ في فحص النموذج: ${error.message}`);
                    log(`❌ خطأ في فحص النموذج: ${error.message}`);
                }
            }, 2000);
        }

        function checkEventListeners() {
            log('🔄 فحص معالجات الأحداث...');
            
            // فحص وجود الوظائف
            const hrWindow = window.open('hr-management.html', '_blank');
            
            setTimeout(() => {
                try {
                    let result = '';
                    
                    if (typeof hrWindow.addEmployee === 'function') {
                        result += '✅ وظيفة addEmployee موجودة<br>';
                    } else {
                        result += '❌ وظيفة addEmployee غير موجودة<br>';
                    }
                    
                    if (typeof hrWindow.editEmployee === 'function') {
                        result += '✅ وظيفة editEmployee موجودة<br>';
                    } else {
                        result += '❌ وظيفة editEmployee غير موجودة<br>';
                    }
                    
                    showResult('formResults', 'info', result);
                    log('✅ تم فحص معالجات الأحداث');
                } catch (error) {
                    showResult('formResults', 'error', `❌ خطأ في فحص المعالجات: ${error.message}`);
                    log(`❌ خطأ في فحص المعالجات: ${error.message}`);
                }
            }, 2000);
        }

        function showResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `<div class="status status-${type}">${message}</div>`;
            }
        }
    </script>
</body>
</html>
