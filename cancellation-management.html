<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أسباب الإلغاء - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display);
            font-weight: 600;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
            min-height: calc(100vh - 140px);
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 14px 28px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s ease;
            font-size: 1rem;
            min-width: 120px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e0a800 0%, #e8590c 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            font-weight: bold;
            font-size: 1.1rem;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #c0392b 100%);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #138496 0%, #5a32a3 100%);
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 35px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 30px 25px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
        }

        .stat-card.total {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #fff 0%, #ffe6e6 100%);
        }

        .stat-card.delivery {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fff 0%, #fff8e1 100%);
        }

        .stat-card.rejection {
            border-left-color: #6f42c1;
            background: linear-gradient(135deg, #fff 0%, #f3e5f5 100%);
        }

        .stat-card.recent {
            border-left-color: #17a2b8;
            background: linear-gradient(135deg, #fff 0%, #e0f7fa 100%);
        }

        .card-icon {
            font-size: 3.5rem;
            margin-bottom: 18px;
            opacity: 0.9;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card-amount {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 12px;
            font-family: var(--font-english-display);
            color: #2c3e50;
            text-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .card-label {
            color: #6c757d;
            font-weight: 600;
            font-size: 1.1rem;
            letter-spacing: 0.5px;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .category-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

        .category-header {
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 4px solid;
            position: relative;
            overflow: hidden;
        }

        .category-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
        }

        .category-header.delivery {
            border-bottom-color: #ffc107;
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        }

        .category-header.rejection {
            border-bottom-color: #6f42c1;
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .reasons-list {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .reason-item {
            padding: 18px 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .reason-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(5px);
        }

        .reason-item:last-child {
            border-bottom: none;
        }

        .reason-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .reason-text {
            font-weight: 600;
            font-size: 1.05rem;
            color: #2c3e50;
            line-height: 1.4;
        }

        .reason-details {
            margin-top: 3px;
        }

        .reason-badges {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .reason-id {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: bold;
            margin-left: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .reason-usage {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-width: 40px;
            text-align: center;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.show {
            opacity: 1;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid #e9ecef;
            transform: scale(0.7);
            transition: transform 0.3s ease;
        }

        .modal.show .modal-content {
            transform: scale(1);
        }

        .modal-header {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 35px;
            background: #ffffff;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-input,
        .form-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            transition: border-color 0.3s ease;
        }

        .form-input:focus,
        .form-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 35px;
            padding: 25px 0 10px 0;
            border-top: 2px solid #e9ecef;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 0 0 15px 15px;
            margin-left: -30px;
            margin-right: -30px;
            padding-left: 30px;
            padding-right: 30px;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 1200px) {
            .categories-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 0 15px;
                margin: 15px auto;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .categories-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .reason-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                padding: 15px 20px;
            }

            .reason-text {
                font-size: 1rem;
            }

            .category-title {
                font-size: 1.3rem;
            }

            .card-amount {
                font-size: 2.5rem;
            }

            .card-icon {
                font-size: 3rem;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .category-header {
                padding: 20px;
            }

            .reason-item {
                padding: 12px 15px;
            }

            .modal-content {
                width: 98%;
                margin: 2% auto;
                max-height: 95vh;
            }

            .modal-body {
                padding: 20px;
            }

            .form-actions {
                flex-direction: column;
                gap: 10px;
                padding: 20px;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>❌</span>
                <span>إدارة أسباب الإلغاء</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="shipments.html">الشحنات</a>
                <a href="cancellation-management.html" class="active">أسباب الإلغاء</a>
                <a href="reports.html">التقارير</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">❌ إدارة أسباب الإلغاء</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-danger" onclick="showCancellationModal()" style="font-size: 1.1rem; font-weight: bold;">
                    🗑️ إلغاء شحنة
                </button>
                <a href="cancellation-reports.html" class="btn btn-success">
                    📊 تقارير الإلغاء
                </a>
                <button class="btn" onclick="loadCancellationData()">
                    🔄 تحديث البيانات
                </button>
            </div>
        </div>

        <!-- إحصائيات الإلغاء -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="card-icon">❌</div>
                <div class="card-amount" id="totalCancelled">0</div>
                <div class="card-label">إجمالي الملغية</div>
            </div>
            
            <div class="stat-card delivery">
                <div class="card-icon">🚚</div>
                <div class="card-amount" id="deliveryIssues">0</div>
                <div class="card-label">مشاكل التوصيل</div>
            </div>
            
            <div class="stat-card rejection">
                <div class="card-icon">🚫</div>
                <div class="card-amount" id="rejectionIssues">0</div>
                <div class="card-label">رفض الشحنات</div>
            </div>
            
            <div class="stat-card recent">
                <div class="card-icon">📅</div>
                <div class="card-amount" id="recentCancellations">0</div>
                <div class="card-label">إلغاءات حديثة</div>
            </div>
        </div>

        <!-- فئات أسباب الإلغاء -->
        <div class="categories-grid" id="categoriesContainer">
            <!-- سيتم ملء البيانات هنا -->
        </div>
    </main>

    <!-- نافذة إلغاء الشحنة -->
    <div id="cancellationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إلغاء شحنة</h2>
                <span class="close" onclick="closeCancellationModal()">&times;</span>
            </div>
            
            <div class="modal-body">
                <form id="cancellationForm">
                    <div class="form-group">
                        <label class="form-label">رقم الشحنة *</label>
                        <select class="form-select" id="shipmentSelect" required>
                            <option value="">اختر الشحنة</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">فئة سبب الإلغاء *</label>
                        <select class="form-select" id="categorySelect" required>
                            <option value="">اختر الفئة</option>
                            <option value="اسباب عدم التوصيل">أسباب عدم التوصيل</option>
                            <option value="اسباب رفض الشحنات">أسباب رفض الشحنات</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">سبب الإلغاء *</label>
                        <select class="form-select" id="reasonSelect" required>
                            <option value="">اختر السبب</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-input" id="cancellationNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn" onclick="closeCancellationModal()" style="background: #6c757d;">
                            ❌ إغلاق النافذة
                        </button>
                        <button type="submit" class="btn btn-danger" style="font-size: 1.1rem; padding: 16px 32px;">
                            🗑️ تأكيد إلغاء الشحنة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('❌ تحميل صفحة إدارة أسباب الإلغاء...');

            try {
                loadCancellationData();
                loadShipments();
                setupEventListeners();

                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('cancellationForm').addEventListener('submit', cancelShipment);
            document.getElementById('categorySelect').addEventListener('change', loadReasonsByCategory);
        }

        // تحميل بيانات الإلغاء
        function loadCancellationData() {
            try {
                loadCancellationStats();
                loadCancellationCategories();
                console.log('📊 تم تحميل بيانات الإلغاء');
            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات الإلغاء:', error);
            }
        }

        // تحميل إحصائيات الإلغاء
        function loadCancellationStats() {
            try {
                const stats = db.getCancellationStats();

                document.getElementById('totalCancelled').textContent = stats.totalCancelled;
                document.getElementById('deliveryIssues').textContent = stats.byCategory['اسباب عدم التوصيل'] || 0;
                document.getElementById('rejectionIssues').textContent = stats.byCategory['اسباب رفض الشحنات'] || 0;
                document.getElementById('recentCancellations').textContent = stats.recentCancellations.length;

            } catch (error) {
                console.error('❌ خطأ في تحميل إحصائيات الإلغاء:', error);
            }
        }

        // تحميل فئات أسباب الإلغاء
        function loadCancellationCategories() {
            try {
                const categories = db.getCancellationCategories();
                const container = document.getElementById('categoriesContainer');

                container.innerHTML = categories.map(category => {
                    const reasons = db.getCancellationReasonsByCategory(category);
                    const stats = db.getCancellationStats();

                    return `
                        <div class="category-card">
                            <div class="category-header ${category.includes('التوصيل') ? 'delivery' : 'rejection'}">
                                <h3 class="category-title">
                                    <span style="font-size: 1.8rem;">${category.includes('التوصيل') ? '🚚' : '🚫'}</span>
                                    <div style="flex: 1;">
                                        <div>${category}</div>
                                        <div style="font-size: 0.9rem; font-weight: normal; color: #6c757d; margin-top: 3px;">
                                            ${reasons.length} سبب متاح
                                        </div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="background: rgba(255,255,255,0.3); padding: 8px 12px; border-radius: 8px; font-size: 1.1rem; font-weight: bold;">
                                            ${stats.byCategory[category] || 0}
                                        </div>
                                        <div style="font-size: 0.8rem; font-weight: normal; margin-top: 2px; opacity: 0.8;">
                                            استخدام
                                        </div>
                                    </div>
                                </h3>
                            </div>
                            <ul class="reasons-list">
                                ${reasons.map(reason => `
                                    <li class="reason-item">
                                        <div class="reason-content">
                                            <span class="reason-text">${reason.reason}</span>
                                            <div class="reason-details">
                                                <small style="color: #6c757d; font-size: 0.9rem;">
                                                    الكود: #${reason.id} |
                                                    ${reason.category === 'اسباب عدم التوصيل' ? 'مشاكل التوصيل' : 'رفض الشحنات'}
                                                </small>
                                            </div>
                                        </div>
                                        <div class="reason-badges">
                                            <span class="reason-usage" title="عدد مرات الاستخدام">${stats.byReason[reason.reason] || 0}</span>
                                            <span class="reason-id" title="رقم السبب">#${reason.id}</span>
                                        </div>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    `;
                }).join('');

            } catch (error) {
                console.error('❌ خطأ في تحميل فئات الإلغاء:', error);
            }
        }

        // تحميل الشحنات النشطة
        function loadShipments() {
            try {
                const shipments = db.getAllShipments();
                const activeShipments = shipments.filter(s => s.status !== 'ملغي' && s.status !== 'مسلم');
                const shipmentSelect = document.getElementById('shipmentSelect');

                shipmentSelect.innerHTML = '<option value="">اختر الشحنة</option>';

                activeShipments.forEach(shipment => {
                    const option = document.createElement('option');
                    option.value = shipment.id;
                    option.textContent = `${shipment.trackingNumber} - ${shipment.receiverName}`;
                    shipmentSelect.appendChild(option);
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل الشحنات:', error);
            }
        }

        // تحميل أسباب الإلغاء حسب الفئة
        function loadReasonsByCategory() {
            try {
                const category = document.getElementById('categorySelect').value;
                const reasonSelect = document.getElementById('reasonSelect');

                reasonSelect.innerHTML = '<option value="">اختر السبب</option>';

                if (category) {
                    const reasons = db.getCancellationReasonsByCategory(category);

                    reasons.forEach(reason => {
                        const option = document.createElement('option');
                        option.value = reason.id;
                        option.textContent = reason.reason;
                        reasonSelect.appendChild(option);
                    });
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل أسباب الإلغاء:', error);
            }
        }

        // إظهار نافذة إلغاء الشحنة
        function showCancellationModal() {
            const modal = document.getElementById('cancellationModal');
            modal.style.display = 'block';

            // تأثير الظهور التدريجي
            setTimeout(() => {
                modal.style.opacity = '1';
                const modalContent = modal.querySelector('.modal-content');
                modalContent.style.transform = 'scale(1)';
            }, 10);

            loadShipments(); // تحديث قائمة الشحنات

            // التركيز على أول حقل
            setTimeout(() => {
                const firstSelect = document.getElementById('shipmentSelect');
                if (firstSelect) {
                    firstSelect.focus();
                }
            }, 300);
        }

        // إغلاق نافذة إلغاء الشحنة
        function closeCancellationModal() {
            const modal = document.getElementById('cancellationModal');
            const modalContent = modal.querySelector('.modal-content');

            // تأثير الاختفاء التدريجي
            modal.style.opacity = '0';
            modalContent.style.transform = 'scale(0.7)';

            setTimeout(() => {
                modal.style.display = 'none';
                document.getElementById('cancellationForm').reset();
            }, 300);
        }

        // إلغاء شحنة
        function cancelShipment(e) {
            e.preventDefault();

            try {
                const shipmentId = document.getElementById('shipmentSelect').value;
                const reasonId = document.getElementById('reasonSelect').value;
                const notes = document.getElementById('cancellationNotes').value.trim();

                if (!shipmentId || !reasonId) {
                    alert('يرجى اختيار الشحنة وسبب الإلغاء');
                    return;
                }

                const shipment = db.getShipmentById(shipmentId);
                const reason = db.getCancellationReasonById(reasonId);

                if (!shipment) {
                    alert('لم يتم العثور على الشحنة');
                    return;
                }

                if (!reason) {
                    alert('لم يتم العثور على سبب الإلغاء');
                    return;
                }

                if (confirm(`هل أنت متأكد من إلغاء الشحنة؟\n\nالشحنة: ${shipment.trackingNumber}\nالسبب: ${reason.reason}`)) {
                    const cancelledShipment = db.cancelShipment(shipmentId, reasonId, notes);

                    if (cancelledShipment) {
                        alert(`تم إلغاء الشحنة بنجاح\n\nرقم الشحنة: ${shipment.trackingNumber}\nسبب الإلغاء: ${reason.reason}`);
                        closeCancellationModal();
                        loadCancellationData();
                    } else {
                        alert('خطأ في إلغاء الشحنة');
                    }
                }

            } catch (error) {
                console.error('❌ خطأ في إلغاء الشحنة:', error);
                alert('خطأ في إلغاء الشحنة: ' + error.message);
            }
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('cancellationModal');
            if (event.target === modal) {
                closeCancellationModal();
            }
        }
    </script>
</body>
</html>
