<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع الشحنات - نظام إدارة الشحنات</title>
    <style>
        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #ecf0f1;
            --font-arabic: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic);
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .tracking-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            position: relative;
        }

        .tracking-form {
            text-align: center;
            margin-bottom: 40px;
        }

        .tracking-form h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .search-container {
            display: flex;
            max-width: 600px;
            margin: 0 auto;
            gap: 15px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            font-size: 1.1rem;
            font-family: var(--font-arabic);
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-arabic);
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .tracking-result {
            display: none;
            margin-top: 30px;
        }

        .shipment-info {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid var(--secondary-color);
        }

        .shipment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .tracking-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .status-badge {
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-in-transit { background: #d1ecf1; color: #0c5460; }
        .status-delivered { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }

        .shipment-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .detail-group {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .detail-group h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.1rem;
            border-bottom: 2px solid var(--light-color);
            padding-bottom: 8px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }

        .detail-label {
            font-weight: 600;
            color: #666;
        }

        .detail-value {
            color: var(--primary-color);
            font-weight: 500;
        }

        .no-result {
            text-align: center;
            padding: 40px;
            color: #666;
            display: none;
        }

        .no-result-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .tracking-actions {
            display: none;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-arabic);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            color: white;
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, var(--success-color), #229954);
            color: white;
        }

        .action-btn.info {
            background: linear-gradient(135deg, var(--info-color), #138496);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* تصميم مبسط للعملاء */
        .shipment-route {
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 2px solid #dee2e6;
        }

        .route-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .route-from, .route-to {
            padding: 10px 20px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            color: #495057;
        }

        .route-arrow {
            font-size: 1.5rem;
            color: #007bff;
            font-weight: 700;
        }

        .shipment-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            margin-top: 20px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            font-weight: 600;
            color: #6c757d;
            flex: 1;
        }

        .summary-value {
            color: #495057;
            font-weight: 500;
            text-align: left;
            flex: 1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .tracking-container {
                padding: 20px;
            }

            .search-container {
                flex-direction: column;
            }

            .search-input {
                min-width: auto;
            }

            .shipment-header {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>📦</span>
                <span>نظام إدارة الشحنات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="shipments.html">الشحنات</a>
                <a href="shipment-tracking.html" class="active">تتبع الشحنات</a>
                <a href="customers.html">العملاء</a>
                <a href="reports.html">التقارير</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1>📍 تتبع الشحنات</h1>
            <p>تتبع شحنتك في الوقت الفعلي واطلع على جميع التحديثات</p>
        </div>

        <div class="tracking-container">
            <div class="tracking-form">
                <h2>🔍 ابحث عن شحنتك</h2>
                <div class="search-container">
                    <input type="text" id="trackingInput" class="search-input" 
                           placeholder="أدخل رقم التتبع أو رقم الشحنة (مثال: SHP001)">
                    <button class="search-btn" id="searchBtn">
                        🔍 تتبع الشحنة
                    </button>
                </div>
            </div>

            <div id="trackingResult" class="tracking-result">
                <!-- سيتم عرض نتائج التتبع هنا -->
            </div>

            <div id="trackingActions" class="tracking-actions">
                <button class="action-btn primary" id="printBtn">
                    🖨️ طباعة التفاصيل
                </button>
                <button class="action-btn secondary" id="shareBtn">
                    📤 مشاركة التتبع
                </button>
                <button class="action-btn info" id="exportBtn">
                    💾 تصدير البيانات
                </button>
            </div>

            <div id="noResult" class="no-result">
                <div class="no-result-icon">📦</div>
                <h3>لم يتم العثور على الشحنة</h3>
                <p>يرجى التأكد من رقم التتبع والمحاولة مرة أخرى</p>
            </div>
        </div>
    </main>

    <script src="js/database-simple.js"></script>
    <script>
        // إنشاء قاعدة بيانات احتياطية إذا لم يتم تحميل الملف
        if (typeof db === 'undefined') {
            console.log('⚠️ إنشاء قاعدة بيانات احتياطية لتتبع الشحنات...');

            window.db = {
                findShipmentByTracking: function(trackingNumber) {
                    try {
                        const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                        return shipments.find(s => s.trackingNumber === trackingNumber) || null;
                    } catch (error) {
                        console.error('خطأ في البحث عن الشحنة:', error);
                        return null;
                    }
                },

                getAllShipments: function() {
                    try {
                        return JSON.parse(localStorage.getItem('shipments') || '[]');
                    } catch (error) {
                        console.error('خطأ في تحميل الشحنات:', error);
                        return [];
                    }
                }
            };

            // إضافة شحنات تجريبية إذا لم تكن موجودة
            const existingShipments = JSON.parse(localStorage.getItem('shipments') || '[]');
            if (existingShipments.length === 0) {
                const sampleShipments = [
                    {
                        id: 'SHIP001',
                        trackingNumber: 'TRK123456789',
                        senderName: 'أحمد محمد',
                        senderPhone: '0501234567',
                        receiverName: 'فاطمة علي',
                        receiverPhone: '0509876543',
                        receiverCity: 'الرياض',
                        receiverAddress: 'حي النخيل، شارع الملك فهد',
                        status: 'في الطريق',
                        contents: 'هدايا شخصية',
                        weight: 2.5,
                        cost: 45,
                        currency: 'ريال',
                        createdAt: new Date().toISOString(),
                        statusHistory: [
                            { status: 'تم الاستلام', date: new Date(Date.now() - 2*24*60*60*1000).toISOString(), location: 'مركز الرياض' },
                            { status: 'في الطريق', date: new Date(Date.now() - 1*24*60*60*1000).toISOString(), location: 'مركز التوزيع' }
                        ]
                    },
                    {
                        id: 'SHIP002',
                        trackingNumber: 'TRK987654321',
                        senderName: 'سارة أحمد',
                        senderPhone: '0551234567',
                        receiverName: 'محمد خالد',
                        receiverPhone: '0559876543',
                        receiverCity: 'جدة',
                        receiverAddress: 'حي الصفا، طريق الملك عبدالعزيز',
                        status: 'تم التسليم',
                        contents: 'مستندات مهمة',
                        weight: 0.5,
                        cost: 25,
                        currency: 'ريال',
                        createdAt: new Date(Date.now() - 3*24*60*60*1000).toISOString(),
                        statusHistory: [
                            { status: 'تم الاستلام', date: new Date(Date.now() - 3*24*60*60*1000).toISOString(), location: 'مركز جدة' },
                            { status: 'في الطريق', date: new Date(Date.now() - 2*24*60*60*1000).toISOString(), location: 'مركز التوزيع' },
                            { status: 'تم التسليم', date: new Date(Date.now() - 1*24*60*60*1000).toISOString(), location: 'العنوان المحدد' }
                        ]
                    },
                    {
                        id: 'SHIP003',
                        trackingNumber: 'TRK555666777',
                        senderName: 'عبدالله سعد',
                        senderPhone: '0561234567',
                        receiverName: 'نورا محمد',
                        receiverPhone: '0569876543',
                        receiverCity: 'الدمام',
                        receiverAddress: 'حي الفيصلية، شارع الأمير محمد',
                        status: 'معلق',
                        contents: 'قطع غيار إلكترونية',
                        weight: 1.2,
                        cost: 35,
                        currency: 'ريال',
                        createdAt: new Date().toISOString(),
                        statusHistory: [
                            { status: 'تم الاستلام', date: new Date().toISOString(), location: 'مركز الدمام' }
                        ]
                    }
                ];

                localStorage.setItem('shipments', JSON.stringify(sampleShipments));
                console.log('✅ تم إنشاء شحنات تجريبية للاختبار');
            }

            console.log('✅ تم إنشاء قاعدة البيانات الاحتياطية لتتبع الشحنات');
        }

        // متغيرات عامة
        let currentShipment = null;

        // عناصر DOM
        const trackingInput = document.getElementById('trackingInput');
        const searchBtn = document.getElementById('searchBtn');
        const printBtn = document.getElementById('printBtn');
        const shareBtn = document.getElementById('shareBtn');
        const exportBtn = document.getElementById('exportBtn');
        const trackingResult = document.getElementById('trackingResult');
        const trackingActions = document.getElementById('trackingActions');
        const noResult = document.getElementById('noResult');

        // وظيفة البحث عن الشحنة
        function trackShipment() {
            const trackingNumber = trackingInput.value.trim();

            if (!trackingNumber) {
                alert('يرجى إدخال رقم التتبع');
                trackingInput.focus();
                return;
            }

            console.log('البحث عن الشحنة:', trackingNumber);

            // البحث في قاعدة البيانات
            const shipment = findShipmentByTracking(trackingNumber);

            if (shipment) {
                console.log('تم العثور على الشحنة:', shipment);
                displayShipmentInfo(shipment);
            } else {
                console.log('لم يتم العثور على الشحنة');
                showNoResult();
            }
        }

        // البحث عن الشحنة في قاعدة البيانات
        function findShipmentByTracking(trackingNumber) {
            try {
                // التحقق من وجود قاعدة البيانات
                if (typeof db === 'undefined' || !db) {
                    console.warn('⚠️ قاعدة البيانات غير متاحة، استخدام البيانات المحفوظة');

                    // البحث المباشر في localStorage
                    const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                    return shipments.find(s => s.trackingNumber === trackingNumber) || null;
                }

                // استخدام دالة البحث من قاعدة البيانات
                const result = db.findShipmentByTracking(trackingNumber);
                console.log('🔍 نتيجة البحث:', result ? 'تم العثور على الشحنة' : 'لم يتم العثور على الشحنة');
                return result;
            } catch (error) {
                console.error('خطأ في البحث:', error);

                // محاولة البحث المباشر كبديل
                try {
                    const shipments = JSON.parse(localStorage.getItem('shipments') || '[]');
                    return shipments.find(s => s.trackingNumber === trackingNumber) || null;
                } catch (fallbackError) {
                    console.error('خطأ في البحث البديل:', fallbackError);
                    return null;
                }
            }
        }

        // عرض معلومات الشحنة
        function displayShipmentInfo(shipment) {
            currentShipment = shipment;

            // إخفاء رسالة عدم وجود نتائج
            noResult.style.display = 'none';

            // إظهار النتائج والأدوات
            trackingResult.style.display = 'block';
            trackingActions.style.display = 'flex';

            // إنشاء HTML للنتائج
            const statusClass = getStatusClass(shipment.status);
            const statusText = getStatusText(shipment.status);

            trackingResult.innerHTML = createShipmentHTML(shipment, statusClass, statusText);
        }

        // إنشاء HTML لمعلومات الشحنة (مبسط للعملاء)
        function createShipmentHTML(shipment, statusClass, statusText) {
            return '<div class="shipment-info">' +
                '<div class="shipment-header">' +
                '<div class="tracking-number">📦 ' + (shipment.trackingNumber || 'غير محدد') + '</div>' +
                '<div class="status-badge ' + statusClass + '">' + statusText + '</div>' +
                '</div>' +
                '<div class="shipment-route">' +
                '<div class="route-info">' +
                '<span class="route-from">' + (shipment.senderName || 'المرسل') + '</span>' +
                '<span class="route-arrow">→</span>' +
                '<span class="route-to">' + (shipment.receiverName || 'المستلم') + '</span>' +
                '</div>' +
                '</div>' +
                '<div class="shipment-summary">' +
                '<div class="summary-item">' +
                '<span class="summary-label">📅 تاريخ الإرسال:</span>' +
                '<span class="summary-value">' + formatDate(shipment.createdDate) + '</span>' +
                '</div>' +
                '<div class="summary-item">' +
                '<span class="summary-label">📍 الموقع الحالي:</span>' +
                '<span class="summary-value">' + getCurrentLocation(shipment.status) + '</span>' +
                '</div>' +
                '<div class="summary-item">' +
                '<span class="summary-label">⏰ التسليم المتوقع:</span>' +
                '<span class="summary-value">' + formatDate(shipment.estimatedDelivery) + '</span>' +
                '</div>' +
                '</div>' +
                '</div>';
        }

        // الحصول على الموقع الحالي بناءً على الحالة
        function getCurrentLocation(status) {
            const locationMap = {
                'معلق': 'مركز الفرز',
                'في الطريق': 'في الطريق للتسليم',
                'مسلم': 'تم التسليم',
                'ملغي': 'تم الإلغاء',
                'في التحويل': 'في الطريق',
                'في الفرع': 'في الفرع'
            };
            return locationMap[status] || 'غير محدد';
        }

        // عرض رسالة عدم وجود نتائج
        function showNoResult() {
            trackingResult.style.display = 'none';
            trackingActions.style.display = 'none';
            noResult.style.display = 'block';
            currentShipment = null;
        }

        // الحصول على فئة CSS للحالة
        function getStatusClass(status) {
            const statusMap = {
                'معلق': 'status-pending',
                'في الطريق': 'status-in-transit',
                'مسلم': 'status-delivered',
                'ملغي': 'status-cancelled',
                'في التحويل': 'status-in-transit',
                'في الفرع': 'status-pending'
            };
            return statusMap[status] || 'status-pending';
        }

        // الحصول على نص الحالة
        function getStatusText(status) {
            const statusMap = {
                'معلق': '⏳ معلق',
                'في الطريق': '🚛 في الطريق',
                'مسلم': '✅ مسلم',
                'ملغي': '❌ ملغي',
                'في التحويل': '🔄 في التحويل',
                'في الفرع': '🏢 في الفرع'
            };
            return statusMap[status] || '⏳ ' + status;
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            if (!dateString) return 'غير محدد';

            try {
                const date = new Date(dateString);
                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    weekday: 'long'
                };

                return date.toLocaleDateString('ar-SA', options);
            } catch (error) {
                console.error('خطأ في تنسيق التاريخ:', error);
                return 'تاريخ غير صحيح';
            }
        }

        // طباعة معلومات التتبع
        function printTrackingInfo() {
            if (!currentShipment) {
                alert('لا توجد شحنة للطباعة');
                return;
            }

            // إنشاء نافذة طباعة بسيطة
            const printWindow = window.open('', '_blank');

            printWindow.document.write('<html dir="rtl"><head><meta charset="UTF-8"><title>تتبع الشحنة</title></head><body style="font-family:\'SF Pro AR Display\', -apple-system, BlinkMacSystemFont, Arial, sans-serif;margin:20px;font-weight:600;">');
            printWindow.document.write('<h1>تتبع الشحنة ' + currentShipment.trackingNumber + '</h1>');
            printWindow.document.write('<p>الحالة: ' + getStatusText(currentShipment.status) + '</p>');
            printWindow.document.write('<h3>معلومات المرسل</h3>');
            printWindow.document.write('<p>الاسم: ' + (currentShipment.senderName || 'غير محدد') + '</p>');
            printWindow.document.write('<p>الهاتف: ' + (currentShipment.senderPhone || 'غير محدد') + '</p>');
            printWindow.document.write('<p>العنوان: ' + (currentShipment.senderAddress || 'غير محدد') + '</p>');
            printWindow.document.write('<h3>معلومات المستلم</h3>');
            printWindow.document.write('<p>الاسم: ' + (currentShipment.receiverName || 'غير محدد') + '</p>');
            printWindow.document.write('<p>الهاتف: ' + (currentShipment.receiverPhone || 'غير محدد') + '</p>');
            printWindow.document.write('<p>العنوان: ' + (currentShipment.receiverAddress || 'غير محدد') + '</p>');
            printWindow.document.write('<h3>تفاصيل الشحنة</h3>');
            printWindow.document.write('<p>المحتويات: ' + (currentShipment.contents || 'غير محدد') + '</p>');
            printWindow.document.write('<p>الوزن: ' + (currentShipment.weight || 0) + ' كيلو</p>');
            printWindow.document.write('<p>التكلفة: ' + (currentShipment.cost || 0) + ' ' + (currentShipment.currency || 'ريال') + '</p>');
            printWindow.document.write('<script>window.onload=function(){setTimeout(function(){window.print();window.close();},500);}<\/script>');
            printWindow.document.write('</body></html>');
            printWindow.document.close();
        }

        // مشاركة معلومات التتبع
        function shareTrackingInfo() {
            if (!currentShipment) {
                alert('لا توجد شحنة للمشاركة');
                return;
            }

            const shareText = '🚚 تتبع الشحنة ' + currentShipment.trackingNumber + '\n\n' +
                '📦 الحالة: ' + getStatusText(currentShipment.status) + '\n' +
                '👤 من: ' + (currentShipment.senderName || 'غير محدد') + '\n' +
                '📍 إلى: ' + (currentShipment.receiverName || 'غير محدد') + '\n' +
                '📅 التسليم المتوقع: ' + formatDate(currentShipment.estimatedDelivery) + '\n\n' +
                'للتتبع المباشر: ' + window.location.href;

            if (navigator.share) {
                navigator.share({
                    title: 'تتبع الشحنة ' + currentShipment.trackingNumber,
                    text: shareText,
                    url: window.location.href
                }).catch(console.error);
            } else {
                navigator.clipboard.writeText(shareText).then(function() {
                    alert('✅ تم نسخ معلومات التتبع للحافظة');
                }).catch(function() {
                    alert('تم إعداد النص للمشاركة:\n\n' + shareText);
                });
            }
        }

        // تصدير معلومات التتبع
        function exportTrackingInfo() {
            if (!currentShipment) {
                alert('لا توجد شحنة للتصدير');
                return;
            }

            const trackingInfo = 'تفاصيل تتبع الشحنة\n' +
                '==================\n\n' +
                'رقم التتبع: ' + currentShipment.trackingNumber + '\n' +
                'الحالة: ' + currentShipment.status + '\n' +
                'تاريخ التصدير: ' + new Date().toLocaleDateString('ar-SA') + '\n\n' +
                'معلومات المرسل:\n' +
                '- الاسم: ' + (currentShipment.senderName || 'غير محدد') + '\n' +
                '- الهاتف: ' + (currentShipment.senderPhone || 'غير محدد') + '\n' +
                '- العنوان: ' + (currentShipment.senderAddress || 'غير محدد') + '\n\n' +
                'معلومات المستلم:\n' +
                '- الاسم: ' + (currentShipment.receiverName || 'غير محدد') + '\n' +
                '- الهاتف: ' + (currentShipment.receiverPhone || 'غير محدد') + '\n' +
                '- العنوان: ' + (currentShipment.receiverAddress || 'غير محدد') + '\n\n' +
                'تفاصيل الشحنة:\n' +
                '- المحتويات: ' + (currentShipment.contents || 'غير محدد') + '\n' +
                '- الوزن: ' + (currentShipment.weight || 0) + ' كيلو\n' +
                '- التكلفة: ' + (currentShipment.cost || 0) + ' ' + (currentShipment.currency || 'ريال') + '\n\n' +
                'التواريخ:\n' +
                '- تاريخ الإنشاء: ' + formatDate(currentShipment.createdDate) + '\n' +
                '- التسليم المتوقع: ' + formatDate(currentShipment.estimatedDelivery) + '\n' +
                (currentShipment.actualDelivery ? '- التسليم الفعلي: ' + formatDate(currentShipment.actualDelivery) + '\n' : '') +
                '\nالملاحظات: ' + (currentShipment.notes || 'لا توجد ملاحظات') + '\n\n' +
                '---\n' +
                'تم إنشاء هذا التقرير من نظام إدارة الشحنات';

            const blob = new Blob([trackingInfo], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'tracking_' + currentShipment.trackingNumber + '_' + new Date().toISOString().split('T')[0] + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // زر البحث
            searchBtn.addEventListener('click', trackShipment);

            // مفتاح Enter في حقل البحث
            trackingInput.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    trackShipment();
                }
            });

            // زر الطباعة
            printBtn.addEventListener('click', printTrackingInfo);

            // زر المشاركة
            shareBtn.addEventListener('click', shareTrackingInfo);

            // زر التصدير
            exportBtn.addEventListener('click', exportTrackingInfo);

            // اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(event) {
                // Ctrl+F للتركيز على البحث
                if (event.ctrlKey && event.key === 'f') {
                    event.preventDefault();
                    trackingInput.focus();
                }

                // Escape لإخفاء النتائج
                if (event.key === 'Escape') {
                    if (trackingResult.style.display === 'block') {
                        showNoResult();
                        trackingInput.value = '';
                        trackingInput.focus();
                    }
                }
            });
        }

        // انتظار تحميل قاعدة البيانات
        function waitForDatabase() {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 30; // 3 ثوان

                const checkDatabase = () => {
                    attempts++;

                    if (typeof db !== 'undefined' && db !== null) {
                        console.log('✅ تم تحميل قاعدة البيانات بنجاح');

                        // اختبار وظيفة البحث
                        try {
                            const testResult = db.findShipmentByTracking('TEST');
                            console.log('✅ وظيفة البحث تعمل بشكل صحيح');
                            resolve();
                        } catch (error) {
                            console.warn('⚠️ مشكلة في وظيفة البحث، استخدام قاعدة البيانات الاحتياطية');
                            resolve(); // نستمر بقاعدة البيانات الاحتياطية
                        }
                    } else if (attempts >= maxAttempts) {
                        console.warn('⚠️ انتهت محاولات تحميل قاعدة البيانات، استخدام قاعدة البيانات الاحتياطية');
                        resolve(); // نستمر بقاعدة البيانات الاحتياطية بدلاً من reject
                    } else {
                        setTimeout(checkDatabase, 100);
                    }
                };

                checkDatabase();
            });
        }

        // إظهار رسالة التحميل
        function showLoadingMessage() {
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'loadingMessage';
            loadingDiv.style.cssText = 'text-align: center; padding: 40px; color: #3498db; font-size: 1.2rem;';
            loadingDiv.innerHTML = '⏳ جاري تحميل النظام...<br><br>يرجى الانتظار';
            document.querySelector('.tracking-container').appendChild(loadingDiv);
        }

        // إخفاء رسالة التحميل
        function hideLoadingMessage() {
            const loadingDiv = document.getElementById('loadingMessage');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }

        // إظهار رسالة ترحيب مع أرقام التتبع التجريبية
        function showWelcomeMessage() {
            const welcomeDiv = document.createElement('div');
            welcomeDiv.style.cssText = `
                background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
                border: 2px solid #27ae60;
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 30px;
                text-align: center;
                color: #2c3e50;
            `;

            welcomeDiv.innerHTML = `
                <h3 style="color: #27ae60; margin-bottom: 15px;">🎉 مرحباً بك في نظام تتبع الشحنات</h3>
                <p style="margin-bottom: 15px;">جرب البحث باستخدام أرقام التتبع التجريبية التالية:</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: rgba(52, 152, 219, 0.1); padding: 15px; border-radius: 10px; border: 1px solid #3498db;">
                        <h4 style="color: #3498db; margin-bottom: 8px;">📦 في الطريق</h4>
                        <button onclick="quickTrack('TRK123456789')" style="background: #3498db; color: white; border: none; padding: 10px 15px; border-radius: 8px; cursor: pointer; font-size: 0.9rem; width: 100%; font-family: inherit;">TRK123456789</button>
                        <p style="font-size: 0.8rem; margin-top: 5px; color: #666;">أحمد محمد → فاطمة علي</p>
                    </div>
                    <div style="background: rgba(39, 174, 96, 0.1); padding: 15px; border-radius: 10px; border: 1px solid #27ae60;">
                        <h4 style="color: #27ae60; margin-bottom: 8px;">✅ تم التسليم</h4>
                        <button onclick="quickTrack('TRK987654321')" style="background: #27ae60; color: white; border: none; padding: 10px 15px; border-radius: 8px; cursor: pointer; font-size: 0.9rem; width: 100%; font-family: inherit;">TRK987654321</button>
                        <p style="font-size: 0.8rem; margin-top: 5px; color: #666;">سارة أحمد → محمد خالد</p>
                    </div>
                    <div style="background: rgba(243, 156, 18, 0.1); padding: 15px; border-radius: 10px; border: 1px solid #f39c12;">
                        <h4 style="color: #f39c12; margin-bottom: 8px;">⏳ معلق</h4>
                        <button onclick="quickTrack('TRK555666777')" style="background: #f39c12; color: white; border: none; padding: 10px 15px; border-radius: 8px; cursor: pointer; font-size: 0.9rem; width: 100%; font-family: inherit;">TRK555666777</button>
                        <p style="font-size: 0.8rem; margin-top: 5px; color: #666;">عبدالله سعد → نورا محمد</p>
                    </div>
                </div>
                <p style="margin-top: 15px; font-size: 0.9rem; color: #7f8c8d;">💡 اضغط على أي رقم تتبع لتجربة البحث الفوري</p>
                <button onclick="hideWelcomeMessage()" style="background: #95a5a6; color: white; border: none; padding: 8px 15px; border-radius: 20px; cursor: pointer; font-size: 0.8rem; margin-top: 10px; font-family: inherit;">إخفاء هذه الرسالة</button>
            `;

            const container = document.querySelector('.tracking-container');
            const form = document.querySelector('.tracking-form');
            container.insertBefore(welcomeDiv, form.nextSibling);
        }

        // البحث السريع برقم تتبع
        function quickTrack(trackingNumber) {
            trackingInput.value = trackingNumber;
            searchShipment();
        }

        // إخفاء رسالة الترحيب
        function hideWelcomeMessage() {
            const welcomeDiv = document.querySelector('.tracking-container > div');
            if (welcomeDiv && welcomeDiv.innerHTML.includes('مرحباً بك في نظام تتبع الشحنات')) {
                welcomeDiv.style.display = 'none';
            }
        }

        // إخفاء رسالة الترحيب
        function hideWelcomeMessage() {
            const welcomeDiv = document.querySelector('.tracking-container > div[style*="linear-gradient"]');
            if (welcomeDiv) {
                welcomeDiv.remove();
            }
        }

        // تتبع سريع
        function quickTrack(trackingNumber) {
            trackingInput.value = trackingNumber;
            trackShipment();
            hideWelcomeMessage();
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📍 تحميل صفحة تتبع الشحنات...');

            // إظهار رسالة التحميل
            showLoadingMessage();

            try {
                // انتظار تحميل قاعدة البيانات
                await waitForDatabase();

                // إخفاء رسالة التحميل
                hideLoadingMessage();

                // إعداد مستمعي الأحداث
                setupEventListeners();

                // التركيز على حقل البحث
                trackingInput.focus();

                // إظهار رسالة ترحيب مع أرقام التتبع التجريبية
                showWelcomeMessage();

                console.log('✅ تم تحميل صفحة التتبع بنجاح');

            } catch (error) {
                console.error('خطأ في تحميل الصفحة:', error);

                // إخفاء رسالة التحميل
                hideLoadingMessage();

                alert('خطأ: ' + error.message + '\n\nيرجى إعادة تحميل الصفحة أو التحقق من الاتصال.');

                // إخفاء عناصر البحث
                document.querySelector('.tracking-form').style.display = 'none';

                // إظهار رسالة خطأ
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = 'text-align: center; padding: 40px; color: #e74c3c; font-size: 1.2rem;';
                errorDiv.innerHTML = '❌ خطأ في تحميل النظام<br><br>يرجى إعادة تحميل الصفحة';
                document.querySelector('.tracking-container').appendChild(errorDiv);
            }
        });
    </script>
</body>
</html>
