<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة تسعير الشحنات - نظام إدارة الشحنات</title>

    <link rel="stylesheet" href="css/style.css">

    <style>
        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #333;
            padding: 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #333;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: #667eea;
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            font-size: 2.5rem;
            color: white;
            margin: 0 0 10px 0;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
        }

        .pricing-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .tab-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .tab-btn.active,
        .tab-btn:hover {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .pricing-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .pricing-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid #667eea;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
        }

        .detail-value {
            font-weight: bold;
            color: #2c3e50;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .add-pricing-btn {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 5px 20px rgba(40, 167, 69, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .add-pricing-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 30px rgba(40, 167, 69, 0.6);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }

            .pricing-tabs {
                flex-direction: column;
                align-items: center;
            }

            .tab-btn {
                width: 100%;
                max-width: 300px;
            }

            .page-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>💰</span>
                <span>إدارة تسعير الشحنات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="shipments.html">الشحنات</a>
                <a href="shipment-tracking.html">تتبع الشحنات</a>
                <a href="pricing-management.html" class="active">التسعير</a>
                <a href="financial-system.html">النظام المالي</a>
                <a href="reports.html">التقارير</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">💰 إدارة تسعير الشحنات</h1>
            <p class="page-subtitle">نظام تسعير شامل ومرن لجميع أنواع الشحنات والمناطق</p>
        </div>

        <!-- تبويبات التسعير -->
        <div class="pricing-tabs">
            <button class="tab-btn active" onclick="showTab('weight-pricing')">📏 التسعير حسب الوزن</button>
            <button class="tab-btn" onclick="showTab('zone-pricing')">🗺️ التسعير حسب المنطقة</button>
            <button class="tab-btn" onclick="showTab('service-pricing')">🚚 تسعير الخدمات</button>
            <button class="tab-btn" onclick="showTab('special-pricing')">⭐ التسعير الخاص</button>
        </div>

        <!-- التسعير حسب الوزن -->
        <div id="weight-pricing" class="tab-content active">
            <h2 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">📏 التسعير حسب الوزن</h2>
            <div class="pricing-grid" id="weightPricingGrid">
                <!-- سيتم ملء البيانات هنا -->
            </div>
        </div>

        <!-- التسعير حسب المنطقة -->
        <div id="zone-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">🗺️ التسعير حسب المنطقة</h2>
            <div class="pricing-grid" id="zonePricingGrid">
                <!-- سيتم ملء البيانات هنا -->
            </div>
        </div>

        <!-- تسعير الخدمات -->
        <div id="service-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">🚚 تسعير الخدمات</h2>
            <div class="pricing-grid" id="servicePricingGrid">
                <!-- سيتم ملء البيانات هنا -->
            </div>
        </div>

        <!-- التسعير الخاص -->
        <div id="special-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 25px; text-align: center;">⭐ التسعير الخاص</h2>
            <div class="pricing-grid" id="specialPricingGrid">
                <!-- سيتم ملء البيانات هنا -->
            </div>
        </div>
    </main>

    <!-- زر إضافة تسعير جديد -->
    <button class="add-pricing-btn" onclick="showAddPricingModal()" title="إضافة تسعير جديد">
        ➕
    </button>

    <!-- نافذة تعديل التسعير حسب الوزن -->
    <div id="editWeightModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); backdrop-filter: blur(8px);">
        <div class="modal-content" style="background-color: white; margin: 5% auto; padding: 0; border-radius: 15px; width: 95%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">📏 تعديل التسعير حسب الوزن</h2>
                <span class="close" onclick="closeEditWeightModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer; line-height: 1;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="editWeightForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم الفئة *</label>
                            <input type="text" id="editWeightName" required placeholder="مثال: طرد صغير" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الحد الأدنى للوزن (كيلو) *</label>
                            <input type="number" id="editWeightMin" required min="0" step="0.1" placeholder="0.5" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الحد الأقصى للوزن (كيلو) *</label>
                            <input type="number" id="editWeightMax" required min="0" step="0.1" placeholder="2.0" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">السعر الأساسي (ريال) *</label>
                            <input type="number" id="editWeightBasePrice" required min="0" step="0.01" placeholder="15.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">سعر الكيلو (ريال) *</label>
                            <input type="number" id="editWeightPerKgPrice" required min="0" step="0.01" placeholder="5.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الحالة</label>
                            <select id="editWeightActive" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                <option value="true">نشط</option>
                                <option value="false">معطل</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeEditWeightModal()" style="background: #6c757d;">إلغاء</button>
                        <button type="submit" class="btn" style="background: #28a745;">💾 حفظ التعديلات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل التسعير حسب المنطقة -->
    <div id="editZoneModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); backdrop-filter: blur(8px);">
        <div class="modal-content" style="background-color: white; margin: 5% auto; padding: 0; border-radius: 15px; width: 95%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">🗺️ تعديل التسعير حسب المنطقة</h2>
                <span class="close" onclick="closeEditZoneModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer; line-height: 1;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="editZoneForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم المنطقة/المدينة *</label>
                            <input type="text" id="editZoneName" required placeholder="مثال: الرياض" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">نوع المنطقة *</label>
                            <select id="editZoneType" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                <option value="">اختر النوع</option>
                                <option value="محلي">محلي</option>
                                <option value="دولي">دولي</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">السعر الأساسي (ريال) *</label>
                            <input type="number" id="editZoneBasePrice" required min="0" step="0.01" placeholder="15.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الرسوم الإضافية (ريال)</label>
                            <input type="number" id="editZoneExtraPrice" min="0" step="0.01" placeholder="0.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">وقت التسليم المتوقع</label>
                            <input type="text" id="editZoneDeliveryTime" placeholder="مثال: 1-2 أيام" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الحالة</label>
                            <select id="editZoneActive" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                <option value="true">نشط</option>
                                <option value="false">معطل</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeEditZoneModal()" style="background: #6c757d;">إلغاء</button>
                        <button type="submit" class="btn" style="background: #28a745;">💾 حفظ التعديلات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل تسعير الخدمات -->
    <div id="editServiceModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); backdrop-filter: blur(8px);">
        <div class="modal-content" style="background-color: white; margin: 5% auto; padding: 0; border-radius: 15px; width: 95%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">🚚 تعديل تسعير الخدمة</h2>
                <span class="close" onclick="closeEditServiceModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer; line-height: 1;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="editServiceForm">
                    <div style="display: grid; grid-template-columns: 1fr; gap: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم الخدمة *</label>
                            <input type="text" id="editServiceName" required placeholder="مثال: التوصيل السريع" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">وصف الخدمة</label>
                            <textarea id="editServiceDescription" rows="3" placeholder="وصف مفصل للخدمة..." style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; resize: vertical;"></textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">السعر (ريال) *</label>
                                <input type="number" id="editServicePrice" required min="0" step="0.01" placeholder="0.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">نوع الخدمة *</label>
                                <select id="editServiceType" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                    <option value="">اختر النوع</option>
                                    <option value="مجاني">مجاني</option>
                                    <option value="إضافي">إضافي</option>
                                    <option value="اختياري">اختياري</option>
                                </select>
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">الحالة</label>
                                <select id="editServiceActive" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                    <option value="true">نشط</option>
                                    <option value="false">معطل</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeEditServiceModal()" style="background: #6c757d;">إلغاء</button>
                        <button type="submit" class="btn" style="background: #28a745;">💾 حفظ التعديلات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل التسعير الخاص -->
    <div id="editSpecialModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); backdrop-filter: blur(8px);">
        <div class="modal-content" style="background-color: white; margin: 5% auto; padding: 0; border-radius: 15px; width: 95%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">⭐ تعديل التسعير الخاص</h2>
                <span class="close" onclick="closeEditSpecialModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer; line-height: 1;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="editSpecialForm">
                    <div style="display: grid; grid-template-columns: 1fr; gap: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم العرض/الخصم *</label>
                            <input type="text" id="editSpecialName" required placeholder="مثال: عملاء VIP" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">وصف العرض</label>
                            <textarea id="editSpecialDescription" rows="3" placeholder="وصف مفصل للعرض أو الخصم..." style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; resize: vertical;"></textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">قيمة الخصم *</label>
                                <input type="number" id="editSpecialDiscount" required min="0" step="0.01" placeholder="15" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">نوع الخصم *</label>
                                <select id="editSpecialType" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                    <option value="">اختر النوع</option>
                                    <option value="نسبة مئوية">نسبة مئوية (%)</option>
                                    <option value="مبلغ ثابت">مبلغ ثابت (ريال)</option>
                                </select>
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">الحالة</label>
                                <select id="editSpecialActive" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                    <option value="true">نشط</option>
                                    <option value="false">معطل</option>
                                </select>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">الحد الأدنى للطلبات</label>
                                <input type="number" id="editSpecialMinOrders" min="0" placeholder="50" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">الحد الأدنى للوزن (كيلو)</label>
                                <input type="number" id="editSpecialMinWeight" min="0" step="0.1" placeholder="20" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">الحد الأقصى للاستخدام</label>
                                <input type="number" id="editSpecialMaxUses" min="0" placeholder="1" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeEditSpecialModal()" style="background: #6c757d;">إلغاء</button>
                        <button type="submit" class="btn" style="background: #28a745;">💾 حفظ التعديلات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة تسعير جديد -->
    <div id="addPricingModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); backdrop-filter: blur(8px);">
        <div class="modal-content" style="background-color: white; margin: 3% auto; padding: 0; border-radius: 15px; width: 95%; max-width: 700px; max-height: 95vh; overflow-y: auto; box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">➕ إضافة تسعير جديد</h2>
                <span class="close" onclick="closeAddPricingModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer; line-height: 1;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <!-- اختيار نوع التسعير -->
                <div style="margin-bottom: 25px;">
                    <label style="font-weight: 600; color: #495057; margin-bottom: 10px; display: block;">نوع التسعير *</label>
                    <select id="addPricingType" onchange="showAddPricingForm()" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; width: 100%;">
                        <option value="">اختر نوع التسعير</option>
                        <option value="weight">📏 التسعير حسب الوزن</option>
                        <option value="zone">🗺️ التسعير حسب المنطقة</option>
                        <option value="service">🚚 تسعير الخدمات</option>
                        <option value="special">⭐ التسعير الخاص</option>
                    </select>
                </div>

                <!-- نموذج التسعير حسب الوزن -->
                <div id="addWeightForm" class="pricing-form" style="display: none;">
                    <h3 style="color: #667eea; margin-bottom: 20px;">📏 إضافة تسعير حسب الوزن</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم الفئة *</label>
                            <input type="text" id="addWeightName" required placeholder="مثال: طرد متوسط" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الحد الأدنى للوزن (كيلو) *</label>
                            <input type="number" id="addWeightMin" required min="0" step="0.1" placeholder="2.0" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الحد الأقصى للوزن (كيلو) *</label>
                            <input type="number" id="addWeightMax" required min="0" step="0.1" placeholder="10.0" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">السعر الأساسي (ريال) *</label>
                            <input type="number" id="addWeightBasePrice" required min="0" step="0.01" placeholder="25.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">سعر الكيلو (ريال) *</label>
                            <input type="number" id="addWeightPerKgPrice" required min="0" step="0.01" placeholder="8.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>
                    </div>
                </div>

                <!-- نموذج التسعير حسب المنطقة -->
                <div id="addZoneForm" class="pricing-form" style="display: none;">
                    <h3 style="color: #28a745; margin-bottom: 20px;">🗺️ إضافة تسعير حسب المنطقة</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم المنطقة/المدينة *</label>
                            <input type="text" id="addZoneName" required placeholder="مثال: أبها" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">نوع المنطقة *</label>
                            <select id="addZoneType" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                <option value="">اختر النوع</option>
                                <option value="محلي">محلي</option>
                                <option value="دولي">دولي</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">السعر الأساسي (ريال) *</label>
                            <input type="number" id="addZoneBasePrice" required min="0" step="0.01" placeholder="20.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الرسوم الإضافية (ريال)</label>
                            <input type="number" id="addZoneExtraPrice" min="0" step="0.01" placeholder="5.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px; grid-column: 1 / -1;">
                            <label style="font-weight: 600; color: #495057;">وقت التسليم المتوقع</label>
                            <input type="text" id="addZoneDeliveryTime" placeholder="مثال: 3-4 أيام" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>
                    </div>
                </div>

                <!-- نموذج تسعير الخدمات -->
                <div id="addServiceForm" class="pricing-form" style="display: none;">
                    <h3 style="color: #ffc107; margin-bottom: 20px;">🚚 إضافة تسعير خدمة</h3>
                    <div style="display: grid; grid-template-columns: 1fr; gap: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم الخدمة *</label>
                            <input type="text" id="addServiceName" required placeholder="مثال: التوصيل المبرد" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">وصف الخدمة</label>
                            <textarea id="addServiceDescription" rows="3" placeholder="وصف مفصل للخدمة..." style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; resize: vertical;"></textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">السعر (ريال) *</label>
                                <input type="number" id="addServicePrice" required min="0" step="0.01" placeholder="30.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">نوع الخدمة *</label>
                                <select id="addServiceType" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                    <option value="">اختر النوع</option>
                                    <option value="مجاني">مجاني</option>
                                    <option value="إضافي">إضافي</option>
                                    <option value="اختياري">اختياري</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج التسعير الخاص -->
                <div id="addSpecialForm" class="pricing-form" style="display: none;">
                    <h3 style="color: #6f42c1; margin-bottom: 20px;">⭐ إضافة تسعير خاص</h3>
                    <div style="display: grid; grid-template-columns: 1fr; gap: 20px;">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم العرض/الخصم *</label>
                            <input type="text" id="addSpecialName" required placeholder="مثال: خصم الطلاب" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">وصف العرض</label>
                            <textarea id="addSpecialDescription" rows="3" placeholder="وصف مفصل للعرض أو الخصم..." style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; resize: vertical;"></textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">قيمة الخصم *</label>
                                <input type="number" id="addSpecialDiscount" required min="0" step="0.01" placeholder="20" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">نوع الخصم *</label>
                                <select id="addSpecialType" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                    <option value="">اختر النوع</option>
                                    <option value="نسبة مئوية">نسبة مئوية (%)</option>
                                    <option value="مبلغ ثابت">مبلغ ثابت (ريال)</option>
                                </select>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">الحد الأدنى للطلبات</label>
                                <input type="number" id="addSpecialMinOrders" min="0" placeholder="10" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">الحد الأدنى للوزن (كيلو)</label>
                                <input type="number" id="addSpecialMinWeight" min="0" step="0.1" placeholder="5" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <label style="font-weight: 600; color: #495057;">الحد الأقصى للاستخدام</label>
                                <input type="number" id="addSpecialMaxUses" min="0" placeholder="5" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px;">
                            </div>
                        </div>
                    </div>
                </div>

                <div id="addPricingActions" style="display: none; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                    <div style="display: flex; gap: 15px; justify-content: flex-end;">
                        <button type="button" class="btn" onclick="closeAddPricingModal()" style="background: #6c757d;">إلغاء</button>
                        <button type="button" class="btn" onclick="submitAddPricing()" style="background: #28a745;">➕ إضافة التسعير</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/database.js" onload="console.log('✅ ملف database.js تم تحميله')" onerror="console.error('❌ فشل في تحميل ملف database.js')"></script>
    <script>
        // قاعدة بيانات احتياطية مبسطة
        function createFallbackDatabase() {
            console.log('🔧 إنشاء قاعدة بيانات احتياطية...');

            window.db = {
                getPricingByType: function(type) {
                    const pricing = JSON.parse(localStorage.getItem('pricing') || '{}');
                    return pricing[type] || this.getDefaultPricing(type);
                },

                updatePricingItem: function(type, id, updateData) {
                    try {
                        const pricing = JSON.parse(localStorage.getItem('pricing') || '{}');
                        if (!pricing[type]) pricing[type] = this.getDefaultPricing(type);

                        const itemIndex = pricing[type].findIndex(item => item.id === id);
                        if (itemIndex !== -1) {
                            pricing[type][itemIndex] = { ...pricing[type][itemIndex], ...updateData };
                            localStorage.setItem('pricing', JSON.stringify(pricing));
                            return true;
                        }
                        return false;
                    } catch (error) {
                        console.error('خطأ في تحديث التسعير:', error);
                        return false;
                    }
                },

                addPricingItem: function(type, itemData) {
                    try {
                        const pricing = JSON.parse(localStorage.getItem('pricing') || '{}');
                        if (!pricing[type]) pricing[type] = this.getDefaultPricing(type);

                        const newId = Math.max(...pricing[type].map(item => item.id), 0) + 1;
                        const newItem = { id: newId, ...itemData };
                        pricing[type].push(newItem);
                        localStorage.setItem('pricing', JSON.stringify(pricing));
                        return true;
                    } catch (error) {
                        console.error('خطأ في إضافة التسعير:', error);
                        return false;
                    }
                },

                getDefaultPricing: function(type) {
                    const defaults = {
                        weightPricing: [
                            { id: 1, name: 'مستندات', minWeight: 0, maxWeight: 0.5, basePrice: 10, perKgPrice: 2, active: true },
                            { id: 2, name: 'طرد صغير', minWeight: 0.5, maxWeight: 2, basePrice: 15, perKgPrice: 5, active: true },
                            { id: 3, name: 'طرد متوسط', minWeight: 2, maxWeight: 10, basePrice: 25, perKgPrice: 8, active: true },
                            { id: 4, name: 'طرد كبير', minWeight: 10, maxWeight: 30, basePrice: 50, perKgPrice: 12, active: true },
                            { id: 5, name: 'شحنة ثقيلة', minWeight: 30, maxWeight: 100, basePrice: 100, perKgPrice: 15, active: true }
                        ],
                        zonePricing: [
                            { id: 1, name: 'الرياض', type: 'محلي', basePrice: 15, extraPrice: 0, deliveryTime: '1-2 أيام', active: true },
                            { id: 2, name: 'جدة', type: 'محلي', basePrice: 18, extraPrice: 3, deliveryTime: '2-3 أيام', active: true },
                            { id: 3, name: 'الدمام', type: 'محلي', basePrice: 20, extraPrice: 5, deliveryTime: '2-3 أيام', active: true },
                            { id: 4, name: 'مكة المكرمة', type: 'محلي', basePrice: 22, extraPrice: 7, deliveryTime: '2-4 أيام', active: true },
                            { id: 5, name: 'المدينة المنورة', type: 'محلي', basePrice: 25, extraPrice: 10, deliveryTime: '3-4 أيام', active: true },
                            { id: 6, name: 'الكويت', type: 'دولي', basePrice: 50, extraPrice: 35, deliveryTime: '5-7 أيام', active: true },
                            { id: 7, name: 'حولي', type: 'دولي', basePrice: 55, extraPrice: 40, deliveryTime: '5-7 أيام', active: true }
                        ],
                        servicePricing: [
                            { id: 1, name: 'التوصيل العادي', description: 'خدمة التوصيل الاعتيادية', price: 0, type: 'مجاني', active: true },
                            { id: 2, name: 'التوصيل السريع', description: 'توصيل في نفس اليوم', price: 20, type: 'إضافي', active: true },
                            { id: 3, name: 'التوصيل الطارئ', description: 'توصيل خلال ساعتين', price: 50, type: 'إضافي', active: true },
                            { id: 4, name: 'التأمين على الشحنة', description: 'تأمين ضد الفقدان والتلف', price: 15, type: 'اختياري', active: true },
                            { id: 5, name: 'التغليف الخاص', description: 'تغليف احترافي للشحنات الحساسة', price: 25, type: 'اختياري', active: true },
                            { id: 6, name: 'التتبع المتقدم', description: 'تتبع مباشر مع إشعارات SMS', price: 10, type: 'اختياري', active: true }
                        ],
                        specialPricing: [
                            { id: 1, name: 'عملاء VIP', description: 'خصم للعملاء المميزين', discount: 15, type: 'نسبة مئوية', minOrders: 50, active: true },
                            { id: 2, name: 'الشحنات الكبيرة', description: 'خصم للشحنات أكثر من 20 كيلو', discount: 10, type: 'نسبة مئوية', minWeight: 20, active: true },
                            { id: 3, name: 'العملاء الجدد', description: 'خصم ترحيبي للعملاء الجدد', discount: 20, type: 'مبلغ ثابت', maxUses: 1, active: true },
                            { id: 4, name: 'الطلبات الشهرية', description: 'خصم للعملاء مع أكثر من 10 شحنات شهرياً', discount: 12, type: 'نسبة مئوية', minMonthlyOrders: 10, active: true },
                            { id: 5, name: 'المناسبات الخاصة', description: 'خصم في المناسبات والأعياد', discount: 25, type: 'نسبة مئوية', seasonal: true, active: false }
                        ]
                    };
                    return defaults[type] || [];
                }
            };

            console.log('✅ تم إنشاء قاعدة البيانات الاحتياطية');
        }

        // التحقق من تحميل قاعدة البيانات
        function checkDatabase() {
            console.log('🔍 فحص قاعدة البيانات...');
            console.log('نوع db:', typeof db);

            if (typeof db === 'undefined') {
                console.warn('⚠️ قاعدة البيانات الرئيسية غير محملة، استخدام النسخة الاحتياطية...');
                createFallbackDatabase();
                return true;
            }

            console.log('✅ قاعدة البيانات محملة بنجاح');
            return true;
        }

        // انتظار تحميل قاعدة البيانات
        function waitForDatabase(callback, maxAttempts = 5) {
            let attempts = 0;

            function tryLoad() {
                attempts++;
                console.log(`محاولة ${attempts} من ${maxAttempts} لتحميل قاعدة البيانات...`);

                if (typeof db !== 'undefined') {
                    console.log('✅ تم العثور على قاعدة البيانات الرئيسية');
                    callback();
                } else if (attempts < maxAttempts) {
                    console.log('⏳ انتظار تحميل قاعدة البيانات...');
                    setTimeout(tryLoad, 100);
                } else {
                    console.warn('⚠️ لم يتم تحميل قاعدة البيانات الرئيسية، استخدام النسخة الاحتياطية...');
                    createFallbackDatabase();
                    callback();
                }
            }

            tryLoad();
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💰 تحميل صفحة إدارة التسعير...');

            // انتظار تحميل قاعدة البيانات ثم تحميل البيانات
            waitForDatabase(function() {
                try {
                    loadAllPricing();
                    console.log('✅ تم تحميل الصفحة بنجاح');
                } catch (error) {
                    console.error('❌ خطأ في تحميل الصفحة:', error);
                    alert('خطأ في تحميل الصفحة: ' + error.message);
                }
            });
        });

            // إضافة دعم اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(event) {
                // Escape لإغلاق النوافذ
                if (event.key === 'Escape') {
                    closeAddPricingModal();
                    closeEditWeightModal();
                    closeEditZoneModal();
                    closeEditServiceModal();
                    closeEditSpecialModal();
                }

                // Ctrl+N لإضافة تسعير جديد
                if (event.ctrlKey && event.key === 'n') {
                    event.preventDefault();
                    showAddPricingModal();
                }

                // Enter لحفظ النماذج المفتوحة
                if (event.key === 'Enter' && event.target.tagName !== 'TEXTAREA') {
                    const addModal = document.getElementById('addPricingModal');
                    const editWeightModal = document.getElementById('editWeightModal');
                    const editZoneModal = document.getElementById('editZoneModal');
                    const editServiceModal = document.getElementById('editServiceModal');
                    const editSpecialModal = document.getElementById('editSpecialModal');

                    if (addModal.style.display === 'block' && document.getElementById('addPricingType').value) {
                        event.preventDefault();
                        submitAddPricing();
                    } else if (editWeightModal.style.display === 'block') {
                        event.preventDefault();
                        submitWeightEdit();
                    } else if (editZoneModal.style.display === 'block') {
                        event.preventDefault();
                        submitZoneEdit();
                    } else if (editServiceModal.style.display === 'block') {
                        event.preventDefault();
                        submitServiceEdit();
                    } else if (editSpecialModal.style.display === 'block') {
                        event.preventDefault();
                        submitSpecialEdit();
                    }
                }
            });
        });

        // تبديل التبويبات
        function showTab(tabId) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabId).classList.add('active');
            
            // إضافة الفئة النشطة للزر المحدد
            event.target.classList.add('active');
        }

        // تحميل جميع أنواع التسعير
        function loadAllPricing() {
            if (!checkDatabase()) return;

            loadWeightPricing();
            loadZonePricing();
            loadServicePricing();
            loadSpecialPricing();
        }

        // تحميل التسعير حسب الوزن
        function loadWeightPricing() {
            try {
                if (!checkDatabase()) return;

                const weightPricing = db.getPricingByType('weightPricing');

            const grid = document.getElementById('weightPricingGrid');
            grid.innerHTML = '';

                weightPricing.forEach(pricing => {
                    const card = document.createElement('div');
                    card.className = 'pricing-card';
                    card.innerHTML = `
                        <div class="card-header">
                            <div class="card-title">
                                <span>📦</span>
                                ${pricing.name}
                            </div>
                            <div class="card-actions">
                                <button class="btn btn-warning" onclick="editWeightPricing(${pricing.id})">✏️ تعديل</button>
                                <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                                        onclick="toggleWeightPricing(${pricing.id})">
                                    ${pricing.active ? '❌ إيقاف' : '✅ تفعيل'}
                                </button>
                            </div>
                        </div>
                        <div class="pricing-details">
                            <div class="detail-item">
                                <span class="detail-label">نطاق الوزن:</span>
                                <span class="detail-value">${pricing.minWeight} - ${pricing.maxWeight} كيلو</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">السعر الأساسي:</span>
                                <span class="detail-value">${pricing.basePrice} ريال</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">سعر الكيلو:</span>
                                <span class="detail-value">${pricing.perKgPrice} ريال/كيلو</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">الحالة:</span>
                                <span class="detail-value" style="color: ${pricing.active ? '#28a745' : '#dc3545'}">
                                    ${pricing.active ? '✅ نشط' : '❌ معطل'}
                                </span>
                            </div>
                        </div>
                    `;
                    grid.appendChild(card);
                });
            } catch (error) {
                console.error('خطأ في تحميل التسعير حسب الوزن:', error);
                alert('خطأ في تحميل التسعير: ' + error.message);
            }
        }

        // تحميل التسعير حسب المنطقة
        function loadZonePricing() {
            try {
                if (!checkDatabase()) return;

                const zonePricing = db.getPricingByType('zonePricing');

            const grid = document.getElementById('zonePricingGrid');
            grid.innerHTML = '';

            zonePricing.forEach(pricing => {
                const card = document.createElement('div');
                card.className = 'pricing-card';
                card.style.borderLeftColor = pricing.type === 'دولي' ? '#dc3545' : '#28a745';
                card.innerHTML = `
                    <div class="card-header">
                        <div class="card-title">
                            <span>${pricing.type === 'دولي' ? '🌍' : '🏙️'}</span>
                            ${pricing.name}
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-warning" onclick="editZonePricing(${pricing.id})">✏️ تعديل</button>
                            <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                                    onclick="toggleZonePricing(${pricing.id})">
                                ${pricing.active ? '❌ إيقاف' : '✅ تفعيل'}
                            </button>
                        </div>
                    </div>
                    <div class="pricing-details">
                        <div class="detail-item">
                            <span class="detail-label">نوع المنطقة:</span>
                            <span class="detail-value">${pricing.type}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">السعر الأساسي:</span>
                            <span class="detail-value">${pricing.basePrice} ريال</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">رسوم إضافية:</span>
                            <span class="detail-value">${pricing.extraPrice} ريال</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">وقت التسليم:</span>
                            <span class="detail-value">${pricing.deliveryTime}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">الحالة:</span>
                            <span class="detail-value" style="color: ${pricing.active ? '#28a745' : '#dc3545'}">
                                ${pricing.active ? '✅ نشط' : '❌ معطل'}
                            </span>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
            } catch (error) {
                console.error('خطأ في تحميل التسعير حسب المنطقة:', error);
                alert('خطأ في تحميل التسعير: ' + error.message);
            }
        }

        // تحميل تسعير الخدمات
        function loadServicePricing() {
            try {
                if (!checkDatabase()) return;

                const servicePricing = db.getPricingByType('servicePricing');

            const grid = document.getElementById('servicePricingGrid');
            grid.innerHTML = '';

            servicePricing.forEach(pricing => {
                const card = document.createElement('div');
                card.className = 'pricing-card';
                card.style.borderLeftColor = pricing.type === 'مجاني' ? '#28a745' : pricing.type === 'إضافي' ? '#ffc107' : '#17a2b8';
                card.innerHTML = `
                    <div class="card-header">
                        <div class="card-title">
                            <span>${pricing.type === 'مجاني' ? '🆓' : pricing.type === 'إضافي' ? '⚡' : '🎯'}</span>
                            ${pricing.name}
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-warning" onclick="editServicePricing(${pricing.id})">✏️ تعديل</button>
                            <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                                    onclick="toggleServicePricing(${pricing.id})">
                                ${pricing.active ? '❌ إيقاف' : '✅ تفعيل'}
                            </button>
                        </div>
                    </div>
                    <div class="pricing-details">
                        <div class="detail-item" style="grid-column: 1 / -1;">
                            <span class="detail-label">الوصف:</span>
                            <span class="detail-value">${pricing.description}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">السعر:</span>
                            <span class="detail-value">${pricing.price === 0 ? 'مجاني' : pricing.price + ' ريال'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">نوع الخدمة:</span>
                            <span class="detail-value">${pricing.type}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">الحالة:</span>
                            <span class="detail-value" style="color: ${pricing.active ? '#28a745' : '#dc3545'}">
                                ${pricing.active ? '✅ نشط' : '❌ معطل'}
                            </span>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
            } catch (error) {
                console.error('خطأ في تحميل تسعير الخدمات:', error);
                alert('خطأ في تحميل التسعير: ' + error.message);
            }
        }

        // تحميل التسعير الخاص
        function loadSpecialPricing() {
            try {
                if (!checkDatabase()) return;

                const specialPricing = db.getPricingByType('specialPricing');

            const grid = document.getElementById('specialPricingGrid');
            grid.innerHTML = '';

            specialPricing.forEach(pricing => {
                const card = document.createElement('div');
                card.className = 'pricing-card';
                card.style.borderLeftColor = '#6f42c1';
                card.innerHTML = `
                    <div class="card-header">
                        <div class="card-title">
                            <span>⭐</span>
                            ${pricing.name}
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-warning" onclick="editSpecialPricing(${pricing.id})">✏️ تعديل</button>
                            <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                                    onclick="toggleSpecialPricing(${pricing.id})">
                                ${pricing.active ? '❌ إيقاف' : '✅ تفعيل'}
                            </button>
                        </div>
                    </div>
                    <div class="pricing-details">
                        <div class="detail-item" style="grid-column: 1 / -1;">
                            <span class="detail-label">الوصف:</span>
                            <span class="detail-value">${pricing.description}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">الخصم:</span>
                            <span class="detail-value">${pricing.discount} ${pricing.type === 'نسبة مئوية' ? '%' : 'ريال'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">نوع الخصم:</span>
                            <span class="detail-value">${pricing.type}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">الحالة:</span>
                            <span class="detail-value" style="color: ${pricing.active ? '#28a745' : '#dc3545'}">
                                ${pricing.active ? '✅ نشط' : '❌ معطل'}
                            </span>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
            } catch (error) {
                console.error('خطأ في تحميل التسعير الخاص:', error);
                alert('خطأ في تحميل التسعير: ' + error.message);
            }
        }

        // متغيرات لحفظ معرف العنصر المحرر
        let currentEditId = null;

        // === وظائف تعديل التسعير حسب الوزن ===
        function editWeightPricing(id) {
            try {
                if (!checkDatabase()) return;

                const pricing = db.getPricingByType('weightPricing');
                const item = pricing.find(p => p.id === id);

                if (!item) {
                    alert('لم يتم العثور على عنصر التسعير');
                    return;
                }

                // ملء النموذج بالبيانات الحالية
                document.getElementById('editWeightName').value = item.name;
                document.getElementById('editWeightMin').value = item.minWeight;
                document.getElementById('editWeightMax').value = item.maxWeight;
                document.getElementById('editWeightBasePrice').value = item.basePrice;
                document.getElementById('editWeightPerKgPrice').value = item.perKgPrice;
                document.getElementById('editWeightActive').value = item.active.toString();

                currentEditId = id;
                document.getElementById('editWeightModal').style.display = 'block';

                // إعداد مستمع الأحداث للنموذج
                document.getElementById('editWeightForm').onsubmit = function(e) {
                    e.preventDefault();
                    submitWeightEdit();
                };

            } catch (error) {
                console.error('خطأ في فتح نافذة تعديل التسعير:', error);
                alert('خطأ في فتح نافذة التعديل: ' + error.message);
            }
        }

        function closeEditWeightModal() {
            document.getElementById('editWeightModal').style.display = 'none';
            document.getElementById('editWeightForm').reset();
            currentEditId = null;
        }

        function submitWeightEdit() {
            try {
                if (!checkDatabase()) return;

                const updateData = {
                    name: document.getElementById('editWeightName').value.trim(),
                    minWeight: parseFloat(document.getElementById('editWeightMin').value),
                    maxWeight: parseFloat(document.getElementById('editWeightMax').value),
                    basePrice: parseFloat(document.getElementById('editWeightBasePrice').value),
                    perKgPrice: parseFloat(document.getElementById('editWeightPerKgPrice').value),
                    active: document.getElementById('editWeightActive').value === 'true'
                };

                // التحقق من صحة البيانات
                if (!updateData.name || updateData.minWeight < 0 || updateData.maxWeight <= updateData.minWeight ||
                    updateData.basePrice < 0 || updateData.perKgPrice < 0) {
                    alert('يرجى التأكد من صحة جميع البيانات المدخلة');
                    return;
                }

                const result = db.updatePricingItem('weightPricing', currentEditId, updateData);

                if (result) {
                    alert('✅ تم تحديث التسعير بنجاح!');
                    closeEditWeightModal();
                    loadWeightPricing();
                } else {
                    alert('❌ خطأ في تحديث التسعير');
                }

            } catch (error) {
                console.error('خطأ في تحديث التسعير:', error);
                alert('خطأ في تحديث التسعير: ' + error.message);
            }
        }

        function toggleWeightPricing(id) {
            try {
                if (!checkDatabase()) return;

                const pricing = db.getPricingByType('weightPricing');
                const item = pricing.find(p => p.id === id);

                if (!item) {
                    alert('لم يتم العثور على عنصر التسعير');
                    return;
                }

                const newStatus = !item.active;
                const result = db.updatePricingItem('weightPricing', id, { active: newStatus });

                if (result) {
                    alert(`✅ تم ${newStatus ? 'تفعيل' : 'إيقاف'} التسعير بنجاح!`);
                    loadWeightPricing();
                } else {
                    alert('❌ خطأ في تغيير حالة التسعير');
                }

            } catch (error) {
                console.error('خطأ في تغيير حالة التسعير:', error);
                alert('خطأ في تغيير حالة التسعير: ' + error.message);
            }
        }

        // === وظائف تعديل التسعير حسب المنطقة ===
        function editZonePricing(id) {
            try {
                if (!checkDatabase()) return;

                const pricing = db.getPricingByType('zonePricing');
                const item = pricing.find(p => p.id === id);

                if (!item) {
                    alert('لم يتم العثور على عنصر التسعير');
                    return;
                }

                // ملء النموذج بالبيانات الحالية
                document.getElementById('editZoneName').value = item.name;
                document.getElementById('editZoneType').value = item.type;
                document.getElementById('editZoneBasePrice').value = item.basePrice;
                document.getElementById('editZoneExtraPrice').value = item.extraPrice;
                document.getElementById('editZoneDeliveryTime').value = item.deliveryTime;
                document.getElementById('editZoneActive').value = item.active.toString();

                currentEditId = id;
                document.getElementById('editZoneModal').style.display = 'block';

                // إعداد مستمع الأحداث للنموذج
                document.getElementById('editZoneForm').onsubmit = function(e) {
                    e.preventDefault();
                    submitZoneEdit();
                };

            } catch (error) {
                console.error('خطأ في فتح نافذة تعديل التسعير:', error);
                alert('خطأ في فتح نافذة التعديل: ' + error.message);
            }
        }

        function closeEditZoneModal() {
            document.getElementById('editZoneModal').style.display = 'none';
            document.getElementById('editZoneForm').reset();
            currentEditId = null;
        }

        function submitZoneEdit() {
            try {
                if (!checkDatabase()) return;

                const updateData = {
                    name: document.getElementById('editZoneName').value.trim(),
                    type: document.getElementById('editZoneType').value,
                    basePrice: parseFloat(document.getElementById('editZoneBasePrice').value),
                    extraPrice: parseFloat(document.getElementById('editZoneExtraPrice').value) || 0,
                    deliveryTime: document.getElementById('editZoneDeliveryTime').value.trim(),
                    active: document.getElementById('editZoneActive').value === 'true'
                };

                // التحقق من صحة البيانات
                if (!updateData.name || !updateData.type || updateData.basePrice < 0 || updateData.extraPrice < 0) {
                    alert('يرجى التأكد من صحة جميع البيانات المدخلة');
                    return;
                }

                const result = db.updatePricingItem('zonePricing', currentEditId, updateData);

                if (result) {
                    alert('✅ تم تحديث التسعير بنجاح!');
                    closeEditZoneModal();
                    loadZonePricing();
                } else {
                    alert('❌ خطأ في تحديث التسعير');
                }

            } catch (error) {
                console.error('خطأ في تحديث التسعير:', error);
                alert('خطأ في تحديث التسعير: ' + error.message);
            }
        }

        function toggleZonePricing(id) {
            try {
                if (!checkDatabase()) return;

                const pricing = db.getPricingByType('zonePricing');
                const item = pricing.find(p => p.id === id);

                if (!item) {
                    alert('لم يتم العثور على عنصر التسعير');
                    return;
                }

                const newStatus = !item.active;
                const result = db.updatePricingItem('zonePricing', id, { active: newStatus });

                if (result) {
                    alert(`✅ تم ${newStatus ? 'تفعيل' : 'إيقاف'} التسعير بنجاح!`);
                    loadZonePricing();
                } else {
                    alert('❌ خطأ في تغيير حالة التسعير');
                }

            } catch (error) {
                console.error('خطأ في تغيير حالة التسعير:', error);
                alert('خطأ في تغيير حالة التسعير: ' + error.message);
            }
        }

        // === وظائف تعديل تسعير الخدمات ===
        function editServicePricing(id) {
            try {
                if (!checkDatabase()) return;

                const pricing = db.getPricingByType('servicePricing');
                const item = pricing.find(p => p.id === id);

                if (!item) {
                    alert('لم يتم العثور على عنصر التسعير');
                    return;
                }

                // ملء النموذج بالبيانات الحالية
                document.getElementById('editServiceName').value = item.name;
                document.getElementById('editServiceDescription').value = item.description;
                document.getElementById('editServicePrice').value = item.price;
                document.getElementById('editServiceType').value = item.type;
                document.getElementById('editServiceActive').value = item.active.toString();

                currentEditId = id;
                document.getElementById('editServiceModal').style.display = 'block';

                // إعداد مستمع الأحداث للنموذج
                document.getElementById('editServiceForm').onsubmit = function(e) {
                    e.preventDefault();
                    submitServiceEdit();
                };

            } catch (error) {
                console.error('خطأ في فتح نافذة تعديل التسعير:', error);
                alert('خطأ في فتح نافذة التعديل: ' + error.message);
            }
        }

        function closeEditServiceModal() {
            document.getElementById('editServiceModal').style.display = 'none';
            document.getElementById('editServiceForm').reset();
            currentEditId = null;
        }

        function submitServiceEdit() {
            try {
                if (!checkDatabase()) return;

                const updateData = {
                    name: document.getElementById('editServiceName').value.trim(),
                    description: document.getElementById('editServiceDescription').value.trim(),
                    price: parseFloat(document.getElementById('editServicePrice').value) || 0,
                    type: document.getElementById('editServiceType').value,
                    active: document.getElementById('editServiceActive').value === 'true'
                };

                // التحقق من صحة البيانات
                if (!updateData.name || !updateData.type || updateData.price < 0) {
                    alert('يرجى التأكد من صحة جميع البيانات المدخلة');
                    return;
                }

                const result = db.updatePricingItem('servicePricing', currentEditId, updateData);

                if (result) {
                    alert('✅ تم تحديث التسعير بنجاح!');
                    closeEditServiceModal();
                    loadServicePricing();
                } else {
                    alert('❌ خطأ في تحديث التسعير');
                }

            } catch (error) {
                console.error('خطأ في تحديث التسعير:', error);
                alert('خطأ في تحديث التسعير: ' + error.message);
            }
        }

        function toggleServicePricing(id) {
            try {
                if (!checkDatabase()) return;

                const pricing = db.getPricingByType('servicePricing');
                const item = pricing.find(p => p.id === id);

                if (!item) {
                    alert('لم يتم العثور على عنصر التسعير');
                    return;
                }

                const newStatus = !item.active;
                const result = db.updatePricingItem('servicePricing', id, { active: newStatus });

                if (result) {
                    alert(`✅ تم ${newStatus ? 'تفعيل' : 'إيقاف'} التسعير بنجاح!`);
                    loadServicePricing();
                } else {
                    alert('❌ خطأ في تغيير حالة التسعير');
                }

            } catch (error) {
                console.error('خطأ في تغيير حالة التسعير:', error);
                alert('خطأ في تغيير حالة التسعير: ' + error.message);
            }
        }

        // === وظائف تعديل التسعير الخاص ===
        function editSpecialPricing(id) {
            try {
                if (!checkDatabase()) return;

                const pricing = db.getPricingByType('specialPricing');
                const item = pricing.find(p => p.id === id);

                if (!item) {
                    alert('لم يتم العثور على عنصر التسعير');
                    return;
                }

                // ملء النموذج بالبيانات الحالية
                document.getElementById('editSpecialName').value = item.name;
                document.getElementById('editSpecialDescription').value = item.description;
                document.getElementById('editSpecialDiscount').value = item.discount;
                document.getElementById('editSpecialType').value = item.type;
                document.getElementById('editSpecialActive').value = item.active.toString();
                document.getElementById('editSpecialMinOrders').value = item.minOrders || '';
                document.getElementById('editSpecialMinWeight').value = item.minWeight || '';
                document.getElementById('editSpecialMaxUses').value = item.maxUses || '';

                currentEditId = id;
                document.getElementById('editSpecialModal').style.display = 'block';

                // إعداد مستمع الأحداث للنموذج
                document.getElementById('editSpecialForm').onsubmit = function(e) {
                    e.preventDefault();
                    submitSpecialEdit();
                };

            } catch (error) {
                console.error('خطأ في فتح نافذة تعديل التسعير:', error);
                alert('خطأ في فتح نافذة التعديل: ' + error.message);
            }
        }

        function closeEditSpecialModal() {
            document.getElementById('editSpecialModal').style.display = 'none';
            document.getElementById('editSpecialForm').reset();
            currentEditId = null;
        }

        function submitSpecialEdit() {
            try {
                if (!checkDatabase()) return;

                const updateData = {
                    name: document.getElementById('editSpecialName').value.trim(),
                    description: document.getElementById('editSpecialDescription').value.trim(),
                    discount: parseFloat(document.getElementById('editSpecialDiscount').value),
                    type: document.getElementById('editSpecialType').value,
                    active: document.getElementById('editSpecialActive').value === 'true'
                };

                // إضافة الحقول الاختيارية
                const minOrders = document.getElementById('editSpecialMinOrders').value;
                const minWeight = document.getElementById('editSpecialMinWeight').value;
                const maxUses = document.getElementById('editSpecialMaxUses').value;

                if (minOrders) updateData.minOrders = parseInt(minOrders);
                if (minWeight) updateData.minWeight = parseFloat(minWeight);
                if (maxUses) updateData.maxUses = parseInt(maxUses);

                // التحقق من صحة البيانات
                if (!updateData.name || !updateData.type || updateData.discount <= 0) {
                    alert('يرجى التأكد من صحة جميع البيانات المدخلة');
                    return;
                }

                const result = db.updatePricingItem('specialPricing', currentEditId, updateData);

                if (result) {
                    alert('✅ تم تحديث التسعير بنجاح!');
                    closeEditSpecialModal();
                    loadSpecialPricing();
                } else {
                    alert('❌ خطأ في تحديث التسعير');
                }

            } catch (error) {
                console.error('خطأ في تحديث التسعير:', error);
                alert('خطأ في تحديث التسعير: ' + error.message);
            }
        }

        function toggleSpecialPricing(id) {
            try {
                if (!checkDatabase()) return;

                const pricing = db.getPricingByType('specialPricing');
                const item = pricing.find(p => p.id === id);

                if (!item) {
                    alert('لم يتم العثور على عنصر التسعير');
                    return;
                }

                const newStatus = !item.active;
                const result = db.updatePricingItem('specialPricing', id, { active: newStatus });

                if (result) {
                    alert(`✅ تم ${newStatus ? 'تفعيل' : 'إيقاف'} التسعير بنجاح!`);
                    loadSpecialPricing();
                } else {
                    alert('❌ خطأ في تغيير حالة التسعير');
                }

            } catch (error) {
                console.error('خطأ في تغيير حالة التسعير:', error);
                alert('خطأ في تغيير حالة التسعير: ' + error.message);
            }
        }

        // === وظائف إضافة تسعير جديد ===
        function showAddPricingModal() {
            try {
                // إعادة تعيين النموذج
                document.getElementById('addPricingType').value = '';
                hideAllAddForms();

                // إظهار النافذة
                document.getElementById('addPricingModal').style.display = 'block';

                // التركيز على قائمة النوع
                document.getElementById('addPricingType').focus();

            } catch (error) {
                console.error('خطأ في فتح نافذة إضافة التسعير:', error);
                alert('خطأ في فتح نافذة الإضافة: ' + error.message);
            }
        }

        function closeAddPricingModal() {
            document.getElementById('addPricingModal').style.display = 'none';
            resetAllAddForms();
        }

        function showAddPricingForm() {
            const type = document.getElementById('addPricingType').value;

            // إخفاء جميع النماذج
            hideAllAddForms();

            // إظهار النموذج المحدد
            if (type) {
                document.getElementById(`add${capitalizeFirst(type)}Form`).style.display = 'block';
                document.getElementById('addPricingActions').style.display = 'block';
            } else {
                document.getElementById('addPricingActions').style.display = 'none';
            }
        }

        function hideAllAddForms() {
            document.getElementById('addWeightForm').style.display = 'none';
            document.getElementById('addZoneForm').style.display = 'none';
            document.getElementById('addServiceForm').style.display = 'none';
            document.getElementById('addSpecialForm').style.display = 'none';
            document.getElementById('addPricingActions').style.display = 'none';
        }

        function resetAllAddForms() {
            // إعادة تعيين جميع النماذج
            document.getElementById('addPricingType').value = '';

            // نموذج الوزن
            document.getElementById('addWeightName').value = '';
            document.getElementById('addWeightMin').value = '';
            document.getElementById('addWeightMax').value = '';
            document.getElementById('addWeightBasePrice').value = '';
            document.getElementById('addWeightPerKgPrice').value = '';

            // نموذج المنطقة
            document.getElementById('addZoneName').value = '';
            document.getElementById('addZoneType').value = '';
            document.getElementById('addZoneBasePrice').value = '';
            document.getElementById('addZoneExtraPrice').value = '';
            document.getElementById('addZoneDeliveryTime').value = '';

            // نموذج الخدمة
            document.getElementById('addServiceName').value = '';
            document.getElementById('addServiceDescription').value = '';
            document.getElementById('addServicePrice').value = '';
            document.getElementById('addServiceType').value = '';

            // نموذج التسعير الخاص
            document.getElementById('addSpecialName').value = '';
            document.getElementById('addSpecialDescription').value = '';
            document.getElementById('addSpecialDiscount').value = '';
            document.getElementById('addSpecialType').value = '';
            document.getElementById('addSpecialMinOrders').value = '';
            document.getElementById('addSpecialMinWeight').value = '';
            document.getElementById('addSpecialMaxUses').value = '';

            hideAllAddForms();
        }

        function capitalizeFirst(str) {
            const mapping = {
                'weight': 'Weight',
                'zone': 'Zone',
                'service': 'Service',
                'special': 'Special'
            };
            return mapping[str] || str;
        }

        function submitAddPricing() {
            try {
                if (!checkDatabase()) return;

                const type = document.getElementById('addPricingType').value;

                if (!type) {
                    alert('يرجى اختيار نوع التسعير');
                    return;
                }

                let newItem = null;
                let typeName = '';

                switch (type) {
                    case 'weight':
                        newItem = addWeightPricing();
                        typeName = 'التسعير حسب الوزن';
                        break;
                    case 'zone':
                        newItem = addZonePricing();
                        typeName = 'التسعير حسب المنطقة';
                        break;
                    case 'service':
                        newItem = addServicePricing();
                        typeName = 'تسعير الخدمة';
                        break;
                    case 'special':
                        newItem = addSpecialPricing();
                        typeName = 'التسعير الخاص';
                        break;
                    default:
                        alert('نوع تسعير غير صحيح');
                        return;
                }

                if (newItem) {
                    alert(`✅ تم إضافة ${typeName} بنجاح!\n\nالاسم: ${newItem.name}\nتم تفعيل التسعير الجديد تلقائياً.`);
                    closeAddPricingModal();
                    loadAllPricing(); // إعادة تحميل جميع البيانات
                } else {
                    alert('❌ خطأ في إضافة التسعير');
                }

            } catch (error) {
                console.error('خطأ في إضافة التسعير:', error);
                alert('خطأ في إضافة التسعير: ' + error.message);
            }
        }

        // === وظائف إضافة أنواع التسعير المحددة ===
        function addWeightPricing() {
            try {
                const itemData = {
                    name: document.getElementById('addWeightName').value.trim(),
                    minWeight: parseFloat(document.getElementById('addWeightMin').value),
                    maxWeight: parseFloat(document.getElementById('addWeightMax').value),
                    basePrice: parseFloat(document.getElementById('addWeightBasePrice').value),
                    perKgPrice: parseFloat(document.getElementById('addWeightPerKgPrice').value)
                };

                // التحقق من صحة البيانات
                if (!itemData.name || itemData.minWeight < 0 || itemData.maxWeight <= itemData.minWeight ||
                    itemData.basePrice < 0 || itemData.perKgPrice < 0) {
                    alert('يرجى التأكد من صحة جميع البيانات المدخلة:\n- الاسم مطلوب\n- الحد الأقصى للوزن يجب أن يكون أكبر من الحد الأدنى\n- جميع الأسعار يجب أن تكون موجبة');
                    return null;
                }

                // التحقق من عدم تداخل الأوزان
                const existingPricing = db.getPricingByType('weightPricing');
                const hasOverlap = existingPricing.some(p =>
                    p.active && (
                        (itemData.minWeight >= p.minWeight && itemData.minWeight < p.maxWeight) ||
                        (itemData.maxWeight > p.minWeight && itemData.maxWeight <= p.maxWeight) ||
                        (itemData.minWeight <= p.minWeight && itemData.maxWeight >= p.maxWeight)
                    )
                );

                if (hasOverlap) {
                    alert('⚠️ تحذير: يوجد تداخل في نطاق الوزن مع تسعير موجود مسبقاً.\nيرجى التأكد من عدم تداخل الأوزان.');
                    return null;
                }

                return db.addPricingItem('weightPricing', itemData);

            } catch (error) {
                console.error('خطأ في إضافة التسعير حسب الوزن:', error);
                alert('خطأ في إضافة التسعير: ' + error.message);
                return null;
            }
        }

        function addZonePricing() {
            try {
                const itemData = {
                    name: document.getElementById('addZoneName').value.trim(),
                    type: document.getElementById('addZoneType').value,
                    basePrice: parseFloat(document.getElementById('addZoneBasePrice').value),
                    extraPrice: parseFloat(document.getElementById('addZoneExtraPrice').value) || 0,
                    deliveryTime: document.getElementById('addZoneDeliveryTime').value.trim() || 'غير محدد'
                };

                // التحقق من صحة البيانات
                if (!itemData.name || !itemData.type || itemData.basePrice < 0 || itemData.extraPrice < 0) {
                    alert('يرجى التأكد من صحة جميع البيانات المدخلة:\n- اسم المنطقة مطلوب\n- نوع المنطقة مطلوب\n- جميع الأسعار يجب أن تكون موجبة');
                    return null;
                }

                // التحقق من عدم وجود منطقة بنفس الاسم
                const existingPricing = db.getPricingByType('zonePricing');
                const nameExists = existingPricing.some(p =>
                    p.active && p.name.toLowerCase() === itemData.name.toLowerCase()
                );

                if (nameExists) {
                    alert('⚠️ تحذير: يوجد تسعير لمنطقة بنفس الاسم مسبقاً.\nيرجى استخدام اسم مختلف أو تعديل التسعير الموجود.');
                    return null;
                }

                return db.addPricingItem('zonePricing', itemData);

            } catch (error) {
                console.error('خطأ في إضافة التسعير حسب المنطقة:', error);
                alert('خطأ في إضافة التسعير: ' + error.message);
                return null;
            }
        }

        function addServicePricing() {
            try {
                const itemData = {
                    name: document.getElementById('addServiceName').value.trim(),
                    description: document.getElementById('addServiceDescription').value.trim() || 'لا يوجد وصف',
                    price: parseFloat(document.getElementById('addServicePrice').value) || 0,
                    type: document.getElementById('addServiceType').value
                };

                // التحقق من صحة البيانات
                if (!itemData.name || !itemData.type || itemData.price < 0) {
                    alert('يرجى التأكد من صحة جميع البيانات المدخلة:\n- اسم الخدمة مطلوب\n- نوع الخدمة مطلوب\n- السعر يجب أن يكون موجباً');
                    return null;
                }

                // التحقق من عدم وجود خدمة بنفس الاسم
                const existingPricing = db.getPricingByType('servicePricing');
                const nameExists = existingPricing.some(p =>
                    p.active && p.name.toLowerCase() === itemData.name.toLowerCase()
                );

                if (nameExists) {
                    alert('⚠️ تحذير: يوجد خدمة بنفس الاسم مسبقاً.\nيرجى استخدام اسم مختلف أو تعديل الخدمة الموجودة.');
                    return null;
                }

                return db.addPricingItem('servicePricing', itemData);

            } catch (error) {
                console.error('خطأ في إضافة تسعير الخدمة:', error);
                alert('خطأ في إضافة التسعير: ' + error.message);
                return null;
            }
        }

        function addSpecialPricing() {
            try {
                const itemData = {
                    name: document.getElementById('addSpecialName').value.trim(),
                    description: document.getElementById('addSpecialDescription').value.trim() || 'لا يوجد وصف',
                    discount: parseFloat(document.getElementById('addSpecialDiscount').value),
                    type: document.getElementById('addSpecialType').value
                };

                // إضافة الحقول الاختيارية
                const minOrders = document.getElementById('addSpecialMinOrders').value;
                const minWeight = document.getElementById('addSpecialMinWeight').value;
                const maxUses = document.getElementById('addSpecialMaxUses').value;

                if (minOrders) itemData.minOrders = parseInt(minOrders);
                if (minWeight) itemData.minWeight = parseFloat(minWeight);
                if (maxUses) itemData.maxUses = parseInt(maxUses);

                // التحقق من صحة البيانات
                if (!itemData.name || !itemData.type || itemData.discount <= 0) {
                    alert('يرجى التأكد من صحة جميع البيانات المدخلة:\n- اسم العرض مطلوب\n- نوع الخصم مطلوب\n- قيمة الخصم يجب أن تكون أكبر من صفر');
                    return null;
                }

                // التحقق من منطقية قيمة الخصم
                if (itemData.type === 'نسبة مئوية' && itemData.discount > 100) {
                    alert('⚠️ تحذير: نسبة الخصم لا يمكن أن تكون أكثر من 100%');
                    return null;
                }

                // التحقق من عدم وجود عرض بنفس الاسم
                const existingPricing = db.getPricingByType('specialPricing');
                const nameExists = existingPricing.some(p =>
                    p.active && p.name.toLowerCase() === itemData.name.toLowerCase()
                );

                if (nameExists) {
                    alert('⚠️ تحذير: يوجد عرض بنفس الاسم مسبقاً.\nيرجى استخدام اسم مختلف أو تعديل العرض الموجود.');
                    return null;
                }

                return db.addPricingItem('specialPricing', itemData);

            } catch (error) {
                console.error('خطأ في إضافة التسعير الخاص:', error);
                alert('خطأ في إضافة التسعير: ' + error.message);
                return null;
            }
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const editWeightModal = document.getElementById('editWeightModal');
            const editZoneModal = document.getElementById('editZoneModal');
            const editServiceModal = document.getElementById('editServiceModal');
            const editSpecialModal = document.getElementById('editSpecialModal');
            const addPricingModal = document.getElementById('addPricingModal');

            if (event.target === editWeightModal) {
                closeEditWeightModal();
            }
            if (event.target === editZoneModal) {
                closeEditZoneModal();
            }
            if (event.target === editServiceModal) {
                closeEditServiceModal();
            }
            if (event.target === editSpecialModal) {
                closeEditSpecialModal();
            }
            if (event.target === addPricingModal) {
                closeAddPricingModal();
            }
        }
    </script>

    <!-- تطبيق خط SF Pro Arabic Display على جميع العناصر -->
    <style>
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, textarea, select, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }
    </style>
</body>
</html>
