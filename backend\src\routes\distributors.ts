// Distributor Routes
// مسارات الموزعين

import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { asyncHandler, AppError } from '../middleware/errorHandler'
import { authenticate, requireManagerOrAdmin } from '../middleware/auth'
import { config } from '../config/config'

const router = Router()
const prisma = new PrismaClient()

// Validation schemas
const createDistributorSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  name: z.string().min(1, 'Name is required').max(config.constants.maxNameLength),
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().min(1, 'Phone is required'),
  vehicleType: z.string().optional(),
  vehicleNumber: z.string().optional(),
  licenseNumber: z.string().optional(),
  area: z.string().optional(),
})

const updateDistributorSchema = createDistributorSchema.partial()

/**
 * @swagger
 * /api/distributors:
 *   get:
 *     summary: Get distributors list
 *     tags: [Distributors]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Distributors retrieved successfully
 */
router.get('/', authenticate, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1
  const limit = Math.min(parseInt(req.query.limit as string) || config.constants.defaultPageSize, config.constants.maxPageSize)
  const search = req.query.search as string
  const available = req.query.available === 'true'
  const skip = (page - 1) * limit

  const where: any = {}
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search, mode: 'insensitive' } },
      { area: { contains: search, mode: 'insensitive' } },
    ]
  }
  
  if (available) {
    where.isAvailable = true
  }

  const [distributors, total] = await Promise.all([
    prisma.distributor.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            isActive: true,
          },
        },
        _count: {
          select: {
            shipments: true,
          },
        },
      },
    }),
    prisma.distributor.count({ where }),
  ])

  const totalPages = Math.ceil(total / limit)

  res.json({
    success: true,
    data: {
      distributors,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    },
  })
}))

/**
 * @swagger
 * /api/distributors:
 *   post:
 *     summary: Create new distributor
 *     tags: [Distributors]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: Distributor created successfully
 */
router.post('/', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const distributorData = createDistributorSchema.parse(req.body)
  
  // Check if user exists and has DISTRIBUTOR role
  const user = await prisma.user.findUnique({
    where: { id: distributorData.userId },
    select: { id: true, role: true, email: true },
  })
  
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND')
  }
  
  if (user.role !== 'DISTRIBUTOR') {
    throw new AppError('User must have DISTRIBUTOR role', 400, 'INVALID_USER_ROLE')
  }
  
  // Check if distributor already exists for this user
  const existingDistributor = await prisma.distributor.findUnique({
    where: { userId: distributorData.userId },
  })
  
  if (existingDistributor) {
    throw new AppError('Distributor already exists for this user', 409, 'DISTRIBUTOR_EXISTS')
  }
  
  const distributor = await prisma.distributor.create({
    data: distributorData,
    include: {
      user: {
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          isActive: true,
        },
      },
    },
  })
  
  res.status(201).json({
    success: true,
    data: { distributor },
    message: 'Distributor created successfully',
  })
}))

export default router
