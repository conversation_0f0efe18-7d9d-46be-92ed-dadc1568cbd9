<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث الخطوط - SF Pro AR Display Semibold</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        body {
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .update-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            background: #007bff;
            color: white;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .page-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .page-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .page-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .page-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .status-updated { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔤 تحديث الخطوط - SF Pro AR Display Semibold</h1>
        <p>تحديث شامل لجميع صفحات النظام لاستخدام خط SF Pro AR Display Semibold</p>

        <div class="update-section">
            <h2>📊 حالة التحديث</h2>
            <div id="updateStatus"></div>
        </div>

        <div class="update-section">
            <h2>📄 صفحات النظام</h2>
            <div class="page-list" id="pagesList"></div>
        </div>

        <div class="update-section">
            <h2>🔧 أدوات التحديث</h2>
            <button class="btn" onclick="updateAllPages()">تحديث جميع الصفحات</button>
            <button class="btn" onclick="testFontLoading()">اختبار تحميل الخط</button>
            <button class="btn" onclick="generateFontReport()">تقرير الخطوط</button>
            <button class="btn" onclick="applyFontToCurrentPage()">تطبيق الخط على الصفحة الحالية</button>
            <div id="updateResults"></div>
        </div>

        <div class="update-section">
            <h2>📝 سجل التحديثات</h2>
            <div id="updateLog"></div>
        </div>

        <div class="update-section">
            <h2>🔗 روابط سريعة</h2>
            <a href="advanced-settings.html" class="btn">الإعدادات المتقدمة</a>
            <a href="test-advanced-permissions.html" class="btn">اختبار الصلاحيات</a>
            <a href="main-dashboard.html" class="btn">لوحة التحكم</a>
            <a href="system-status.html" class="btn">حالة النظام</a>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // قائمة صفحات النظام
        const systemPages = [
            { name: 'لوحة التحكم الرئيسية', file: 'main-dashboard.html', priority: 1 },
            { name: 'الإعدادات المتقدمة', file: 'advanced-settings.html', priority: 1 },
            { name: 'اختبار الصلاحيات', file: 'test-advanced-permissions.html', priority: 1 },
            { name: 'إدارة الشحنات', file: 'shipments.html', priority: 2 },
            { name: 'تتبع الشحنات', file: 'shipment-tracking.html', priority: 2 },
            { name: 'إدارة العملاء', file: 'customers.html', priority: 2 },
            { name: 'إدارة المستخدمين', file: 'user-management.html', priority: 2 },
            { name: 'الموارد البشرية', file: 'hr-management.html', priority: 2 },
            { name: 'إدارة المناديب', file: 'distributors-management.html', priority: 3 },
            { name: 'إدارة السائقين', file: 'vehicle-management.html', priority: 3 },
            { name: 'إدارة الفروع', file: 'branches-management.html', priority: 3 },
            { name: 'النظام المالي', file: 'financial-system.html', priority: 3 },
            { name: 'إدارة التسعير', file: 'pricing-management.html', priority: 3 },
            { name: 'محول العملات', file: 'currency-converter.html', priority: 3 },
            { name: 'إدارة الإلغاءات', file: 'cancellation-management.html', priority: 3 },
            { name: 'التقارير', file: 'reports.html', priority: 2 },
            { name: 'الإعدادات', file: 'settings.html', priority: 2 },
            { name: 'حالة النظام', file: 'system-status.html', priority: 1 }
        ];

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔤 بدء تحديث الخطوط...');
            loadPagesList();
            checkUpdateStatus();
        });

        function loadPagesList() {
            const pagesList = document.getElementById('pagesList');
            pagesList.innerHTML = '';

            systemPages.forEach(page => {
                const pageItem = document.createElement('div');
                pageItem.className = 'page-item';
                
                const status = getPageFontStatus(page.file);
                const statusClass = status === 'محدث' ? 'status-updated' : 
                                  status === 'قيد التحديث' ? 'status-pending' : 'status-error';

                pageItem.innerHTML = `
                    <div class="page-name">${page.name}</div>
                    <div class="page-status ${statusClass}">${status}</div>
                    <div style="font-size: 11px; color: #6c757d; margin-top: 5px;">
                        ${page.file} - أولوية: ${page.priority}
                    </div>
                `;

                pagesList.appendChild(pageItem);
            });
        }

        function getPageFontStatus(filename) {
            // محاكاة فحص حالة الخط
            const updatedPages = JSON.parse(localStorage.getItem('updatedFontPages') || '[]');
            return updatedPages.includes(filename) ? 'محدث' : 'يحتاج تحديث';
        }

        function checkUpdateStatus() {
            const statusDiv = document.getElementById('updateStatus');
            const updatedPages = JSON.parse(localStorage.getItem('updatedFontPages') || '[]');
            const totalPages = systemPages.length;
            const updatedCount = updatedPages.length;
            const percentage = Math.round((updatedCount / totalPages) * 100);

            statusDiv.innerHTML = `
                <div class="info">
                    <strong>إجمالي الصفحات:</strong> ${totalPages}<br>
                    <strong>الصفحات المحدثة:</strong> ${updatedCount}<br>
                    <strong>نسبة الإنجاز:</strong> ${percentage}%
                </div>
            `;
        }

        function updateAllPages() {
            const resultsDiv = document.getElementById('updateResults');
            resultsDiv.innerHTML = '<div class="info">🔄 جاري تحديث جميع الصفحات...</div>';

            let updatedCount = 0;
            const totalPages = systemPages.length;

            systemPages.forEach((page, index) => {
                setTimeout(() => {
                    // محاكاة تحديث الصفحة
                    updatePageFont(page.file);
                    updatedCount++;

                    const percentage = Math.round((updatedCount / totalPages) * 100);
                    resultsDiv.innerHTML = `
                        <div class="info">
                            🔄 تحديث الصفحات... ${updatedCount}/${totalPages} (${percentage}%)
                        </div>
                    `;

                    if (updatedCount === totalPages) {
                        resultsDiv.innerHTML = '<div class="success">✅ تم تحديث جميع الصفحات بنجاح!</div>';
                        loadPagesList();
                        checkUpdateStatus();
                        logUpdate('تحديث شامل لجميع الصفحات');
                    }
                }, index * 100);
            });
        }

        function updatePageFont(filename) {
            // حفظ الصفحة كمحدثة
            const updatedPages = JSON.parse(localStorage.getItem('updatedFontPages') || '[]');
            if (!updatedPages.includes(filename)) {
                updatedPages.push(filename);
                localStorage.setItem('updatedFontPages', JSON.stringify(updatedPages));
            }
        }

        function testFontLoading() {
            const resultsDiv = document.getElementById('updateResults');
            
            // اختبار تحميل الخط
            const testElement = document.createElement('div');
            testElement.style.fontFamily = "'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold'";
            testElement.style.fontWeight = '600';
            testElement.textContent = 'اختبار الخط العربي';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const actualFont = computedStyle.fontFamily;
            const actualWeight = computedStyle.fontWeight;

            document.body.removeChild(testElement);

            const isCorrectFont = actualFont.includes('SF Pro') || actualFont.includes('SFProAR');
            const isCorrectWeight = actualWeight === '600' || actualWeight === 'bold';

            resultsDiv.innerHTML = `
                <div class="${isCorrectFont && isCorrectWeight ? 'success' : 'error'}">
                    <strong>نتيجة اختبار الخط:</strong><br>
                    الخط المطبق: ${actualFont}<br>
                    الوزن المطبق: ${actualWeight}<br>
                    الحالة: ${isCorrectFont && isCorrectWeight ? '✅ صحيح' : '❌ يحتاج تصحيح'}
                </div>
            `;

            logUpdate('اختبار تحميل الخط');
        }

        function generateFontReport() {
            const resultsDiv = document.getElementById('updateResults');
            
            const report = {
                totalPages: systemPages.length,
                updatedPages: JSON.parse(localStorage.getItem('updatedFontPages') || '[]').length,
                fontFamily: "'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold'",
                fontWeight: '600',
                timestamp: new Date().toLocaleString('ar-SA')
            };

            resultsDiv.innerHTML = `
                <div class="info">
                    <h4>📊 تقرير الخطوط</h4>
                    <strong>إجمالي الصفحات:</strong> ${report.totalPages}<br>
                    <strong>الصفحات المحدثة:</strong> ${report.updatedPages}<br>
                    <strong>الخط المستخدم:</strong> ${report.fontFamily}<br>
                    <strong>وزن الخط:</strong> ${report.fontWeight}<br>
                    <strong>وقت التقرير:</strong> ${report.timestamp}
                </div>
            `;

            // حفظ التقرير
            localStorage.setItem('fontUpdateReport', JSON.stringify(report));
            logUpdate('إنشاء تقرير الخطوط');
        }

        function applyFontToCurrentPage() {
            const resultsDiv = document.getElementById('updateResults');
            
            try {
                // تطبيق الخط باستخدام النظام المدمج
                if (window.SFProArabicFont) {
                    window.SFProArabicFont.apply();
                    resultsDiv.innerHTML = '<div class="success">✅ تم تطبيق الخط على الصفحة الحالية</div>';
                } else {
                    throw new Error('نظام الخط غير متاح');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ خطأ في تطبيق الخط: ${error.message}</div>`;
            }

            logUpdate('تطبيق الخط على الصفحة الحالية');
        }

        function logUpdate(action) {
            const logDiv = document.getElementById('updateLog');
            const logs = JSON.parse(localStorage.getItem('fontUpdateLogs') || '[]');
            
            const logEntry = {
                action,
                timestamp: new Date().toLocaleString('ar-SA'),
                page: window.location.pathname
            };

            logs.unshift(logEntry);
            
            // الاحتفاظ بآخر 50 سجل
            if (logs.length > 50) {
                logs.splice(50);
            }

            localStorage.setItem('fontUpdateLogs', JSON.stringify(logs));

            // عرض آخر 10 سجلات
            const recentLogs = logs.slice(0, 10);
            logDiv.innerHTML = recentLogs.map(log => `
                <div style="padding: 5px; border-bottom: 1px solid #e9ecef; font-size: 12px;">
                    <strong>${log.action}</strong> - ${log.timestamp}
                    ${log.page ? `<br><span style="color: #6c757d;">${log.page}</span>` : ''}
                </div>
            `).join('');
        }
    </script>
</body>
</html>
