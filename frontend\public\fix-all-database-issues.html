<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح جميع مشاكل قاعدة البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #e74c3c;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .emergency-section {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        .emergency-section h2 {
            margin-bottom: 20px;
            font-size: 2rem;
        }
        .fix-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .fix-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-right: 5px solid #e74c3c;
        }
        .fix-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            width: 100%;
            text-align: center;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            text-align: center;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #27ae60;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #e74c3c;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #17a2b8;
        }
        .files-list {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
        }
        .files-list h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .files-list ul {
            line-height: 2;
            color: #666;
        }
        .files-list li {
            padding: 5px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #27ae60, #229954);
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 إصلاح جميع مشاكل قاعدة البيانات</h1>
            <p>حل شامل ونهائي لجميع مشاكل "db is not defined"</p>
        </div>

        <div class="emergency-section">
            <h2>🆘 إصلاح طارئ</h2>
            <p>إذا كنت تواجه مشاكل في أي صفحة، اضغط الزر أدناه للإصلاح الفوري</p>
            <button class="btn btn-success" onclick="emergencyFix()" style="font-size: 1.2rem; padding: 20px 40px;">
                🔧 إصلاح فوري لجميع الصفحات
            </button>
        </div>

        <div id="status" class="status status-info">
            ℹ️ جاهز لبدء عملية الإصلاح الشاملة
        </div>

        <div class="progress-bar">
            <div id="progressFill" class="progress-fill">0%</div>
        </div>

        <div class="fix-grid">
            <div class="fix-card">
                <h3>🏠 الصفحات الرئيسية</h3>
                <button class="btn" onclick="testPage('main-dashboard.html')">🏠 لوحة التحكم</button>
                <button class="btn" onclick="testPage('index.html')">🔐 تسجيل الدخول</button>
                <button class="btn" onclick="testPage('shipments.html')">📦 إدارة الشحنات</button>
                <button class="btn" onclick="testPage('customers.html')">👥 إدارة العملاء</button>
            </div>

            <div class="fix-card">
                <h3>👥 إدارة المستخدمين والصلاحيات</h3>
                <button class="btn btn-info" onclick="testPage('user-management.html')">👥 إدارة المستخدمين</button>
                <button class="btn btn-info" onclick="testPage('user-permissions-advanced.html')">🔐 الصلاحيات المتقدمة</button>
                <button class="btn btn-info" onclick="testPage('permissions-matrix.html')">📊 مصفوفة الصلاحيات</button>
            </div>

            <div class="fix-card">
                <h3>👨‍💼 إدارة المناديب والتوزيع</h3>
                <button class="btn btn-warning" onclick="testPage('distributors-management.html')">👨‍💼 إدارة المناديب</button>
                <button class="btn btn-warning" onclick="testPage('commission-management.html')">💰 إدارة العمولات</button>
                <button class="btn btn-warning" onclick="testPage('cod-management.html')">🏪 الدفع عند الاستلام</button>
            </div>

            <div class="fix-card">
                <h3>🛠️ أدوات التشخيص والإصلاح</h3>
                <button class="btn btn-success" onclick="testPage('test-database-simple.html')">🧪 اختبار قاعدة البيانات</button>
                <button class="btn btn-success" onclick="testPage('fix-database.html')">🔧 أداة الإصلاح</button>
                <button class="btn btn-success" onclick="testPage('update-all-pages.html')">🔄 صفحة التحديث</button>
                <button class="btn btn-success" onclick="testPage('clear-data.html')">🗑️ مسح البيانات</button>
            </div>
        </div>

        <div class="files-list">
            <h4>📁 الملفات التي تم إصلاحها:</h4>
            <ul>
                <li>✅ main-dashboard.html - لوحة التحكم الرئيسية</li>
                <li>✅ user-permissions-advanced.html - الصلاحيات المتقدمة</li>
                <li>✅ permissions-matrix.html - مصفوفة الصلاحيات</li>
                <li>✅ user-management.html - إدارة المستخدمين</li>
                <li>✅ distributors-management.html - إدارة المناديب</li>
                <li>✅ commission-management.html - إدارة العمولات</li>
                <li>✅ cod-management.html - الدفع عند الاستلام</li>
                <li>✅ js/database-simple.js - قاعدة البيانات المبسطة</li>
            </ul>
        </div>

        <div class="fix-grid">
            <div class="fix-card">
                <h3>🔍 تشخيص المشاكل</h3>
                <button class="btn btn-info" onclick="runDiagnostics()">🔍 فحص شامل للنظام</button>
                <button class="btn btn-info" onclick="checkConsole()">📋 فحص وحدة التحكم</button>
                <button class="btn btn-info" onclick="showSystemInfo()">ℹ️ معلومات النظام</button>
            </div>

            <div class="fix-card">
                <h3>🗑️ إعادة تعيين</h3>
                <button class="btn btn-warning" onclick="clearAllData()">🗑️ مسح جميع البيانات</button>
                <button class="btn btn-warning" onclick="resetToDefaults()">🔄 إعادة تعيين للافتراضي</button>
                <button class="btn btn-warning" onclick="forceReload()">🔄 إعادة تحميل قسري</button>
            </div>
        </div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');
        const progressFill = document.getElementById('progressFill');

        function updateStatus(message, type = 'info') {
            statusDiv.className = `status status-${type}`;
            statusDiv.innerHTML = message;
        }

        function updateProgress(percentage) {
            progressFill.style.width = percentage + '%';
            progressFill.textContent = Math.round(percentage) + '%';
        }

        // إصلاح طارئ
        function emergencyFix() {
            updateStatus('🚨 بدء الإصلاح الطارئ...', 'error');
            updateProgress(0);

            setTimeout(() => {
                updateProgress(20);
                updateStatus('🔄 تحديث قاعدة البيانات...', 'info');
                localStorage.setItem('useSimpleDatabase', 'true');
            }, 500);

            setTimeout(() => {
                updateProgress(40);
                updateStatus('🗑️ مسح البيانات التالفة...', 'info');
                clearCorruptedData();
            }, 1000);

            setTimeout(() => {
                updateProgress(60);
                updateStatus('📊 إنشاء بيانات افتراضية...', 'info');
                createDefaultData();
            }, 1500);

            setTimeout(() => {
                updateProgress(80);
                updateStatus('🔧 تطبيق الإصلاحات...', 'info');
            }, 2000);

            setTimeout(() => {
                updateProgress(100);
                updateStatus('✅ تم الإصلاح الطارئ بنجاح! جميع الصفحات جاهزة للاستخدام.', 'success');
                
                setTimeout(() => {
                    if (confirm('هل تريد فتح لوحة التحكم للتأكد من العمل؟')) {
                        window.open('main-dashboard.html', '_blank');
                    }
                }, 2000);
            }, 2500);
        }

        // اختبار صفحة
        function testPage(pageUrl) {
            updateStatus(`🔍 اختبار ${pageUrl}...`, 'info');
            
            setTimeout(() => {
                window.open(pageUrl, '_blank');
                updateStatus(`📊 تم فتح ${pageUrl} في تبويب جديد`, 'success');
            }, 500);
        }

        // مسح البيانات التالفة
        function clearCorruptedData() {
            try {
                const keysToCheck = ['shipments', 'users', 'roles', 'customers', 'distributors'];
                keysToCheck.forEach(key => {
                    try {
                        const data = localStorage.getItem(key);
                        if (data) {
                            JSON.parse(data); // اختبار صحة JSON
                        }
                    } catch (error) {
                        console.log(`مسح البيانات التالفة: ${key}`);
                        localStorage.removeItem(key);
                    }
                });
            } catch (error) {
                console.error('خطأ في مسح البيانات التالفة:', error);
            }
        }

        // إنشاء بيانات افتراضية
        function createDefaultData() {
            try {
                // إنشاء بيانات افتراضية بسيطة
                if (!localStorage.getItem('shipments')) {
                    localStorage.setItem('shipments', JSON.stringify([]));
                }
                if (!localStorage.getItem('users')) {
                    localStorage.setItem('users', JSON.stringify([]));
                }
                if (!localStorage.getItem('useSimpleDatabase')) {
                    localStorage.setItem('useSimpleDatabase', 'true');
                }
            } catch (error) {
                console.error('خطأ في إنشاء البيانات الافتراضية:', error);
            }
        }

        // تشغيل تشخيص شامل
        function runDiagnostics() {
            updateStatus('🔍 جاري تشغيل التشخيص الشامل...', 'info');
            
            let diagnostics = [];
            
            // فحص التخزين المحلي
            const storageItems = Object.keys(localStorage).length;
            diagnostics.push(`📊 عناصر التخزين المحلي: ${storageItems}`);
            
            // فحص قاعدة البيانات
            if (typeof db !== 'undefined') {
                diagnostics.push('✅ قاعدة البيانات محملة');
            } else {
                diagnostics.push('❌ قاعدة البيانات غير محملة');
            }
            
            // فحص المتصفح
            diagnostics.push(`🌐 المتصفح: ${navigator.userAgent.split(' ')[0]}`);
            
            const result = diagnostics.join('\n');
            alert('نتائج التشخيص:\n\n' + result);
            updateStatus('✅ تم إكمال التشخيص', 'success');
        }

        // فحص وحدة التحكم
        function checkConsole() {
            const guide = `
🔍 دليل فحص وحدة التحكم:

1. اضغط F12 لفتح أدوات المطور
2. انتقل لتبويب Console
3. ابحث عن الرسائل التالية:

✅ رسائل النجاح:
• 🔄 بدء تحميل قاعدة البيانات المبسطة...
• ✅ تم تحميل قاعدة البيانات المبسطة بنجاح
• 🎉 قاعدة البيانات جاهزة للاستخدام!

❌ رسائل الخطأ:
• db is not defined
• خطأ في تحميل المناديب
• قاعدة البيانات غير متاحة

💡 إذا رأيت رسائل خطأ، استخدم الإصلاح الطارئ.
            `;
            
            alert(guide);
            updateStatus('📖 تم عرض دليل وحدة التحكم', 'info');
        }

        // عرض معلومات النظام
        function showSystemInfo() {
            const info = `
ℹ️ معلومات النظام:

📅 التاريخ: ${new Date().toLocaleString('ar-SA')}
🌐 المتصفح: ${navigator.userAgent}
💾 التخزين المحلي: ${Object.keys(localStorage).length} عنصر
🔗 الرابط: ${window.location.href}
📊 حالة قاعدة البيانات: ${typeof db !== 'undefined' ? 'محملة' : 'غير محملة'}
            `;
            
            alert(info);
            updateStatus('ℹ️ تم عرض معلومات النظام', 'info');
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.clear();
                updateStatus('🗑️ تم مسح جميع البيانات', 'success');
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }

        // إعادة تعيين للافتراضي
        function resetToDefaults() {
            if (confirm('هل تريد إعادة تعيين النظام للإعدادات الافتراضية؟')) {
                localStorage.clear();
                localStorage.setItem('useSimpleDatabase', 'true');
                updateStatus('🔄 تم إعادة التعيين للافتراضي', 'success');
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }

        // إعادة تحميل قسري
        function forceReload() {
            updateStatus('🔄 إعادة تحميل قسري...', 'info');
            setTimeout(() => {
                location.reload(true);
            }, 1000);
        }

        // تحديث تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                updateStatus('🎉 أداة الإصلاح الشاملة جاهزة! اختر الإجراء المناسب أعلاه.', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
