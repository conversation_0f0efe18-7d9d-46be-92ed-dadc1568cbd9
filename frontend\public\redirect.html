<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>توجيه تلقائي | نظام إدارة الشحنات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }

        .container {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            max-width: 500px;
            width: 100%;
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        p {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .options {
            display: grid;
            gap: 15px;
            margin-top: 30px;
        }

        .option-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            font-weight: 600;
        }

        .option-btn:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
        }

        .countdown {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffd700;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚚</div>
        <h1>مرحباً بك في نظام إدارة الشحنات</h1>
        <p>جاري توجيهك للصفحة المناسبة...</p>
        
        <div class="loading"></div>
        
        <div class="countdown" id="countdown">5</div>
        
        <div class="options">
            <a href="index.html" class="option-btn">🏠 الصفحة الرئيسية</a>
            <a href="unified-login.html" class="option-btn">🔐 تسجيل الدخول</a>
            <a href="customer-dashboard.html" class="option-btn">👤 لوحة العملاء</a>
            <a href="main-dashboard.html" class="option-btn">👨‍💼 لوحة المديرين</a>
            <a href="start-here.html" class="option-btn">🚀 نقطة البداية</a>
        </div>
    </div>

    <script>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        // تحديث العد التنازلي
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                redirect();
            }
        }, 1000);
        
        function redirect() {
            // فحص حالة تسجيل الدخول
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userType = localStorage.getItem('userType');
            
            if (isLoggedIn === 'true') {
                // المستخدم مسجل دخول
                if (userType === 'customer') {
                    window.location.href = 'customer-dashboard.html';
                } else if (userType === 'admin') {
                    window.location.href = 'main-dashboard.html';
                } else {
                    window.location.href = 'unified-login.html';
                }
            } else {
                // المستخدم غير مسجل دخول
                window.location.href = 'index.html';
            }
        }
        
        // إيقاف العد التنازلي عند النقر على أي زر
        document.querySelectorAll('.option-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                clearInterval(timer);
            });
        });
        
        // فحص الملفات المتاحة
        async function checkFiles() {
            const files = [
                'index.html',
                'unified-login.html', 
                'customer-dashboard.html',
                'main-dashboard.html',
                'start-here.html'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (!response.ok) {
                        // إخفاء الزر إذا كان الملف غير موجود
                        const btn = document.querySelector(`[href="${file}"]`);
                        if (btn) {
                            btn.style.display = 'none';
                        }
                    }
                } catch (error) {
                    // إخفاء الزر في حالة الخطأ
                    const btn = document.querySelector(`[href="${file}"]`);
                    if (btn) {
                        btn.style.display = 'none';
                    }
                }
            }
        }
        
        // تشغيل فحص الملفات
        checkFiles();
        
        console.log('🚀 صفحة التوجيه التلقائي جاهزة');
    </script>
</body>
</html>
