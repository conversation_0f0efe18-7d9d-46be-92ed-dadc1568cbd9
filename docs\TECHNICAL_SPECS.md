# المواصفات الفنية | Technical Specifications

## نظرة عامة | Overview

نظام إدارة الشحنات هو تطبيق ويب متكامل يدعم اللغتين العربية والإنجليزية، مصمم لتبسيط عمليات إدارة الشحنات والتوصيل.

## هيكل المشروع | Project Structure

```
برنامج توصيل/
├── frontend/          # Next.js React Application
├── backend/           # Node.js API Server
├── mobile/            # React Native Mobile App
├── database/          # Database Schema & Migrations
├── docs/              # Documentation
└── shared/            # Shared Resources (fonts, styles, i18n)
```

## التقنيات المستخدمة | Technology Stack

### Frontend
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Custom CSS
- **UI Components**: Shadcn/ui
- **State Management**: Zustand + React Query
- **Internationalization**: next-i18next
- **Forms**: React Hook Form + Zod validation
- **Charts**: Recharts
- **Maps**: Google Maps API

### Backend
- **Runtime**: Node.js 18+
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT + bcrypt
- **Validation**: Zod
- **File Upload**: Multer
- **Email**: Nodemailer
- **Real-time**: Socket.io
- **API Documentation**: Swagger/OpenAPI

### Mobile
- **Framework**: React Native with Expo
- **Navigation**: React Navigation 6
- **State Management**: Zustand
- **UI**: NativeBase + Custom Components
- **Maps**: React Native Maps
- **Push Notifications**: Expo Notifications

### Database
- **Primary**: PostgreSQL 14+
- **Hosting**: Supabase
- **ORM**: Prisma
- **Migrations**: Prisma Migrate
- **Backup**: Automated daily backups

### DevOps & Deployment
- **Frontend Hosting**: Vercel
- **Backend Hosting**: Railway/Heroku
- **Database**: Supabase
- **CDN**: Cloudflare
- **Monitoring**: Sentry
- **CI/CD**: GitHub Actions

## قاعدة البيانات | Database Design

### الجداول الرئيسية | Main Tables

1. **users** - المستخدمين
2. **customers** - العملاء
3. **distributors** - الموزعين
4. **shipments** - الشحنات
5. **shipment_tracking** - تتبع الشحنات
6. **currencies** - العملات
7. **notifications** - الإشعارات
8. **invoices** - الفواتير

### العلاقات | Relationships

- User → Distributor (1:1)
- Customer → Shipments (1:N)
- Distributor → Shipments (1:N)
- Shipment → Tracking (1:N)
- Currency → Shipments (1:N)

## واجهات API | API Endpoints

### Authentication
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/register` - إنشاء حساب
- `POST /api/auth/logout` - تسجيل الخروج
- `GET /api/auth/me` - معلومات المستخدم

### Shipments
- `GET /api/shipments` - قائمة الشحنات
- `POST /api/shipments` - إنشاء شحنة
- `GET /api/shipments/:id` - تفاصيل الشحنة
- `PUT /api/shipments/:id` - تحديث الشحنة
- `DELETE /api/shipments/:id` - حذف الشحنة
- `GET /api/shipments/:id/tracking` - تتبع الشحنة

### Customers
- `GET /api/customers` - قائمة العملاء
- `POST /api/customers` - إضافة عميل
- `PUT /api/customers/:id` - تحديث عميل
- `DELETE /api/customers/:id` - حذف عميل

### Distributors
- `GET /api/distributors` - قائمة الموزعين
- `POST /api/distributors` - إضافة موزع
- `PUT /api/distributors/:id` - تحديث موزع
- `GET /api/distributors/:id/shipments` - شحنات الموزع

### Currencies
- `GET /api/currencies` - قائمة العملات
- `POST /api/currencies/update-rates` - تحديث أسعار الصرف

### Reports
- `GET /api/reports/dashboard` - إحصائيات لوحة التحكم
- `GET /api/reports/sales` - تقرير المبيعات
- `GET /api/reports/deliveries` - تقرير التوصيل

## الأمان | Security

### Authentication & Authorization
- JWT tokens with refresh mechanism
- Role-based access control (RBAC)
- Password hashing with bcrypt
- Rate limiting on API endpoints

### Data Protection
- Input validation with Zod
- SQL injection prevention with Prisma
- XSS protection with sanitization
- CORS configuration
- HTTPS enforcement in production

### Privacy
- GDPR compliance considerations
- Data encryption at rest
- Secure file upload handling
- Audit logging for sensitive operations

## الأداء | Performance

### Frontend Optimization
- Next.js App Router for optimal loading
- Image optimization with next/image
- Code splitting and lazy loading
- Service Worker for offline support
- CDN for static assets

### Backend Optimization
- Database indexing strategy
- Query optimization with Prisma
- Caching with Redis (production)
- Connection pooling
- Compression middleware

### Mobile Optimization
- Expo optimizations
- Image caching
- Offline data synchronization
- Background sync for tracking updates

## الاختبار | Testing

### Frontend Testing
- Unit tests with Jest + React Testing Library
- E2E tests with Playwright
- Component testing with Storybook
- Accessibility testing

### Backend Testing
- Unit tests with Jest
- Integration tests with Supertest
- Database testing with test containers
- API documentation testing

### Mobile Testing
- Unit tests with Jest
- Component testing with React Native Testing Library
- E2E testing with Detox

## النشر | Deployment

### Environment Setup
- Development: Local with Docker Compose
- Staging: Vercel + Railway + Supabase
- Production: Vercel + Railway + Supabase

### CI/CD Pipeline
1. Code push to GitHub
2. Automated testing
3. Build and deploy to staging
4. Manual approval for production
5. Deploy to production
6. Health checks and monitoring

## المراقبة | Monitoring

### Application Monitoring
- Error tracking with Sentry
- Performance monitoring
- Uptime monitoring
- Database performance metrics

### Business Metrics
- Shipment volume tracking
- Delivery performance metrics
- Customer satisfaction scores
- Revenue analytics

## التوثيق | Documentation

### API Documentation
- OpenAPI/Swagger specifications
- Postman collections
- Code examples in multiple languages

### User Documentation
- User manual in Arabic and English
- Video tutorials
- FAQ section
- Troubleshooting guide

## الصيانة | Maintenance

### Regular Tasks
- Database backups (daily)
- Security updates (monthly)
- Performance optimization (quarterly)
- Feature updates (as needed)

### Monitoring & Alerts
- System health checks
- Error rate monitoring
- Performance degradation alerts
- Security incident notifications
