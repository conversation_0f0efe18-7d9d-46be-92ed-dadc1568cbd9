<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير الضريبية | نظام الامتثال لهيئة الزكاة والضريبة</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 25px 35px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #666;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.8);
            color: #333;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .report-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .report-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
        }

        .card-subtitle {
            color: #666;
            font-size: 0.9rem;
        }

        .report-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            color: #666;
            font-weight: 500;
        }

        .info-value {
            color: #333;
            font-weight: 600;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 1rem;
            width: 100%;
            justify-content: center;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 126, 52, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 126, 52, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .filters-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.4rem;
            color: #333;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-title i {
            color: #28a745;
        }

        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .compliance-status {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .status-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .status-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .status-icon.success {
            color: #28a745;
        }

        .status-icon.warning {
            color: #ffc107;
        }

        .status-icon.error {
            color: #dc3545;
        }

        .status-title {
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .status-description {
            color: #666;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-form {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 15px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>التقارير الضريبية</h1>
            <a href="zatca-compliance.html" class="back-link">
                <i class="fas fa-arrow-right"></i>
                العودة لنظام الامتثال
            </a>
        </div>

        <!-- حالة الامتثال -->
        <div class="compliance-status">
            <h2 class="section-title">
                <i class="fas fa-shield-alt"></i>
                حالة الامتثال الحالية
            </h2>
            
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="status-title">الفوترة الإلكترونية</div>
                    <div class="status-description">متوافق 100%</div>
                </div>
                
                <div class="status-item">
                    <div class="status-icon success">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="status-title">ضريبة القيمة المضافة</div>
                    <div class="status-description">محدث</div>
                </div>
                
                <div class="status-item">
                    <div class="status-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="status-title">التقارير الشهرية</div>
                    <div class="status-description">مستحق خلال 5 أيام</div>
                </div>
                
                <div class="status-item">
                    <div class="status-icon success">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="status-title">حفظ السجلات</div>
                    <div class="status-description">آمن ومحفوظ</div>
                </div>
            </div>
        </div>

        <!-- مرشحات التقارير -->
        <div class="filters-section">
            <h2 class="section-title">
                <i class="fas fa-filter"></i>
                مرشحات التقارير
            </h2>
            
            <div class="filters-form">
                <div class="form-group">
                    <label class="form-label">نوع التقرير</label>
                    <select id="report-type" class="form-control">
                        <option value="all">جميع التقارير</option>
                        <option value="vat">ضريبة القيمة المضافة</option>
                        <option value="sales">تقارير المبيعات</option>
                        <option value="invoices">تقارير الفواتير</option>
                        <option value="compliance">تقارير الامتثال</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">الفترة الزمنية</label>
                    <select id="time-period" class="form-control">
                        <option value="current-month">الشهر الحالي</option>
                        <option value="last-month">الشهر الماضي</option>
                        <option value="current-quarter">الربع الحالي</option>
                        <option value="last-quarter">الربع الماضي</option>
                        <option value="current-year">السنة الحالية</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" id="start-date" class="form-control">
                </div>
                
                <div class="form-group">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" id="end-date" class="form-control">
                </div>
                
                <div class="form-group">
                    <button class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-search"></i>
                        تطبيق المرشحات
                    </button>
                </div>
            </div>
        </div>

        <!-- بطاقات التقارير -->
        <div class="reports-grid">
            <!-- تقرير ضريبة القيمة المضافة الشهري -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div>
                        <div class="card-title">تقرير ضريبة القيمة المضافة</div>
                        <div class="card-subtitle">التقرير الشهري المطلوب لهيئة الزكاة والضريبة</div>
                    </div>
                </div>
                
                <div class="report-info">
                    <div class="info-item">
                        <span class="info-label">الفترة:</span>
                        <span class="info-value" id="vat-period">يناير 2024</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">إجمالي الضريبة:</span>
                        <span class="info-value" id="vat-total">1,875.00 ريال</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">جاهز للإرسال</span>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="generateVATReport()">
                    <i class="fas fa-file-alt"></i>
                    إنشاء التقرير
                </button>
                <button class="btn btn-secondary" onclick="previewVATReport()">
                    <i class="fas fa-eye"></i>
                    معاينة
                </button>
            </div>

            <!-- تقرير المبيعات -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div>
                        <div class="card-title">تقرير المبيعات</div>
                        <div class="card-subtitle">تقرير مفصل لجميع المبيعات والإيرادات</div>
                    </div>
                </div>
                
                <div class="report-info">
                    <div class="info-item">
                        <span class="info-label">إجمالي المبيعات:</span>
                        <span class="info-value" id="sales-total">12,500.00 ريال</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">عدد الفواتير:</span>
                        <span class="info-value" id="invoices-count">45</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">متوسط الفاتورة:</span>
                        <span class="info-value" id="average-invoice">277.78 ريال</span>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="generateSalesReport()">
                    <i class="fas fa-chart-line"></i>
                    إنشاء التقرير
                </button>
                <button class="btn btn-secondary" onclick="previewSalesReport()">
                    <i class="fas fa-eye"></i>
                    معاينة
                </button>
            </div>

            <!-- تقرير الفواتير الإلكترونية -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div>
                        <div class="card-title">تقرير الفواتير الإلكترونية</div>
                        <div class="card-subtitle">سجل شامل لجميع الفواتير الإلكترونية</div>
                    </div>
                </div>
                
                <div class="report-info">
                    <div class="info-item">
                        <span class="info-label">الفواتير المرسلة:</span>
                        <span class="info-value" id="sent-invoices">42</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الفواتير المقبولة:</span>
                        <span class="info-value" id="accepted-invoices">40</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">معدل القبول:</span>
                        <span class="info-value" id="acceptance-rate">95.2%</span>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="generateInvoicesReport()">
                    <i class="fas fa-list-alt"></i>
                    إنشاء التقرير
                </button>
                <button class="btn btn-secondary" onclick="previewInvoicesReport()">
                    <i class="fas fa-eye"></i>
                    معاينة
                </button>
            </div>

            <!-- تقرير الامتثال -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <div class="card-title">تقرير الامتثال</div>
                        <div class="card-subtitle">تقييم شامل لحالة الامتثال للمتطلبات</div>
                    </div>
                </div>
                
                <div class="report-info">
                    <div class="info-item">
                        <span class="info-label">نسبة الامتثال:</span>
                        <span class="info-value" id="compliance-rate">98.5%</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المتطلبات المكتملة:</span>
                        <span class="info-value" id="completed-requirements">12/12</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">آخر تحديث:</span>
                        <span class="info-value" id="last-update">اليوم</span>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="generateComplianceReport()">
                    <i class="fas fa-clipboard-check"></i>
                    إنشاء التقرير
                </button>
                <button class="btn btn-secondary" onclick="previewComplianceReport()">
                    <i class="fas fa-eye"></i>
                    معاينة
                </button>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 تحميل نظام التقارير الضريبية...');
            initializeReportsSystem();
            loadReportsData();
            setDefaultDates();
        });

        // تهيئة نظام التقارير
        function initializeReportsSystem() {
            // تهيئة بيانات التقارير الأساسية
            if (!localStorage.getItem('reportsData')) {
                const initialReportsData = {
                    vatReports: [],
                    salesReports: [],
                    invoiceReports: [],
                    complianceReports: [],
                    lastGenerated: null
                };
                localStorage.setItem('reportsData', JSON.stringify(initialReportsData));
            }

            // تهيئة إعدادات التقارير
            if (!localStorage.getItem('reportsSettings')) {
                const reportsSettings = {
                    autoGenerate: true,
                    emailNotifications: true,
                    reportFormat: 'pdf',
                    language: 'ar',
                    companyInfo: {
                        name: 'شركة الشحن السريع',
                        taxNumber: '300000000000003',
                        address: 'الرياض، المملكة العربية السعودية',
                        phone: '+966501234567',
                        email: '<EMAIL>'
                    }
                };
                localStorage.setItem('reportsSettings', JSON.stringify(reportsSettings));
            }
        }

        // تحميل بيانات التقارير
        function loadReportsData() {
            // تحميل بيانات ضريبة القيمة المضافة
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const electronicInvoices = JSON.parse(localStorage.getItem('electronicInvoices') || '[]');

            // حساب إحصائيات الشهر الحالي
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            // تحديث بيانات ضريبة القيمة المضافة
            updateVATReportData(vatRecords, currentMonth, currentYear);

            // تحديث بيانات المبيعات
            updateSalesReportData(electronicInvoices, currentMonth, currentYear);

            // تحديث بيانات الفواتير الإلكترونية
            updateInvoicesReportData(electronicInvoices);

            // تحديث بيانات الامتثال
            updateComplianceReportData();
        }

        // تحديث بيانات تقرير ضريبة القيمة المضافة
        function updateVATReportData(vatRecords, currentMonth, currentYear) {
            const currentPeriod = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;
            const currentMonthRecord = vatRecords.find(record => record.period === currentPeriod);

            const vatTotal = currentMonthRecord ? currentMonthRecord.vatAmount : 0;
            const periodName = getMonthName(currentMonth) + ' ' + currentYear;

            document.getElementById('vat-period').textContent = periodName;
            document.getElementById('vat-total').textContent = formatCurrency(vatTotal);
        }

        // تحديث بيانات تقرير المبيعات
        function updateSalesReportData(invoices, currentMonth, currentYear) {
            const currentMonthInvoices = invoices.filter(invoice => {
                const invoiceDate = new Date(invoice.issueDate);
                return invoiceDate.getMonth() + 1 === currentMonth &&
                       invoiceDate.getFullYear() === currentYear;
            });

            const totalSales = currentMonthInvoices.reduce((sum, invoice) => sum + invoice.baseAmount, 0);
            const invoicesCount = currentMonthInvoices.length;
            const averageInvoice = invoicesCount > 0 ? totalSales / invoicesCount : 0;

            document.getElementById('sales-total').textContent = formatCurrency(totalSales);
            document.getElementById('invoices-count').textContent = invoicesCount.toString();
            document.getElementById('average-invoice').textContent = formatCurrency(averageInvoice);
        }

        // تحديث بيانات تقرير الفواتير الإلكترونية
        function updateInvoicesReportData(invoices) {
            const sentInvoices = invoices.length;
            const acceptedInvoices = invoices.filter(invoice => invoice.status === 'issued' || invoice.status === 'paid').length;
            const acceptanceRate = sentInvoices > 0 ? (acceptedInvoices / sentInvoices * 100).toFixed(1) : 0;

            document.getElementById('sent-invoices').textContent = sentInvoices.toString();
            document.getElementById('accepted-invoices').textContent = acceptedInvoices.toString();
            document.getElementById('acceptance-rate').textContent = acceptanceRate + '%';
        }

        // تحديث بيانات تقرير الامتثال
        function updateComplianceReportData() {
            // حساب نسبة الامتثال بناءً على المتطلبات المكتملة
            const complianceRate = 98.5; // يمكن حسابها ديناميكياً
            const completedRequirements = '12/12';
            const lastUpdate = 'اليوم';

            document.getElementById('compliance-rate').textContent = complianceRate + '%';
            document.getElementById('completed-requirements').textContent = completedRequirements;
            document.getElementById('last-update').textContent = lastUpdate;
        }

        // تعيين التواريخ الافتراضية
        function setDefaultDates() {
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

            document.getElementById('start-date').value = firstDayOfMonth.toISOString().split('T')[0];
            document.getElementById('end-date').value = today.toISOString().split('T')[0];
        }

        // تطبيق المرشحات
        function applyFilters() {
            const reportType = document.getElementById('report-type').value;
            const timePeriod = document.getElementById('time-period').value;
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            console.log('🔍 تطبيق المرشحات:', {
                reportType,
                timePeriod,
                startDate,
                endDate
            });

            // تحديث البيانات بناءً على المرشحات
            filterReportsData(reportType, timePeriod, startDate, endDate);

            showAlert('تم تطبيق المرشحات بنجاح', 'success');
        }

        // تصفية بيانات التقارير
        function filterReportsData(reportType, timePeriod, startDate, endDate) {
            // تحديد الفترة الزمنية
            let filterStartDate, filterEndDate;

            if (timePeriod === 'custom') {
                filterStartDate = new Date(startDate);
                filterEndDate = new Date(endDate);
            } else {
                const dates = getTimePeriodDates(timePeriod);
                filterStartDate = dates.start;
                filterEndDate = dates.end;
            }

            // تطبيق التصفية حسب نوع التقرير
            switch (reportType) {
                case 'vat':
                    filterVATData(filterStartDate, filterEndDate);
                    break;
                case 'sales':
                    filterSalesData(filterStartDate, filterEndDate);
                    break;
                case 'invoices':
                    filterInvoicesData(filterStartDate, filterEndDate);
                    break;
                case 'compliance':
                    filterComplianceData(filterStartDate, filterEndDate);
                    break;
                default:
                    // تحديث جميع البيانات
                    loadReportsData();
            }
        }

        // الحصول على تواريخ الفترة الزمنية
        function getTimePeriodDates(period) {
            const today = new Date();
            let start, end;

            switch (period) {
                case 'current-month':
                    start = new Date(today.getFullYear(), today.getMonth(), 1);
                    end = today;
                    break;
                case 'last-month':
                    start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    end = new Date(today.getFullYear(), today.getMonth(), 0);
                    break;
                case 'current-quarter':
                    const quarterStart = Math.floor(today.getMonth() / 3) * 3;
                    start = new Date(today.getFullYear(), quarterStart, 1);
                    end = today;
                    break;
                case 'last-quarter':
                    const lastQuarterStart = Math.floor(today.getMonth() / 3) * 3 - 3;
                    start = new Date(today.getFullYear(), lastQuarterStart, 1);
                    end = new Date(today.getFullYear(), lastQuarterStart + 3, 0);
                    break;
                case 'current-year':
                    start = new Date(today.getFullYear(), 0, 1);
                    end = today;
                    break;
                default:
                    start = new Date(today.getFullYear(), today.getMonth(), 1);
                    end = today;
            }

            return { start, end };
        }

        // إنشاء تقرير ضريبة القيمة المضافة
        function generateVATReport() {
            const vatRecords = JSON.parse(localStorage.getItem('vatRecords') || '[]');
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            const filteredRecords = filterRecordsByDate(vatRecords, startDate, endDate, 'createdAt');

            const report = {
                id: 'VAT-REPORT-' + Date.now(),
                type: 'vat',
                title: 'تقرير ضريبة القيمة المضافة',
                period: `${startDate} إلى ${endDate}`,
                generatedAt: new Date().toISOString(),
                data: {
                    records: filteredRecords,
                    totalRecords: filteredRecords.length,
                    totalTaxableSales: filteredRecords.reduce((sum, record) => sum + record.taxableSales, 0),
                    totalVAT: filteredRecords.reduce((sum, record) => sum + record.vatAmount, 0),
                    paidRecords: filteredRecords.filter(record => record.status === 'paid').length,
                    pendingRecords: filteredRecords.filter(record => record.status === 'pending').length,
                    overdueRecords: filteredRecords.filter(record => record.status === 'overdue').length
                }
            };

            // حفظ التقرير
            saveReport(report);

            // عرض التقرير
            showVATReportModal(report);
        }

        // إنشاء تقرير المبيعات
        function generateSalesReport() {
            const invoices = JSON.parse(localStorage.getItem('electronicInvoices') || '[]');
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            const filteredInvoices = filterRecordsByDate(invoices, startDate, endDate, 'issueDate');

            const report = {
                id: 'SALES-REPORT-' + Date.now(),
                type: 'sales',
                title: 'تقرير المبيعات',
                period: `${startDate} إلى ${endDate}`,
                generatedAt: new Date().toISOString(),
                data: {
                    invoices: filteredInvoices,
                    totalInvoices: filteredInvoices.length,
                    totalSales: filteredInvoices.reduce((sum, invoice) => sum + invoice.baseAmount, 0),
                    totalVAT: filteredInvoices.reduce((sum, invoice) => sum + invoice.vatAmount, 0),
                    totalAmount: filteredInvoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0),
                    averageInvoice: filteredInvoices.length > 0 ?
                        filteredInvoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0) / filteredInvoices.length : 0,
                    topCustomers: getTopCustomers(filteredInvoices),
                    salesByService: getSalesByService(filteredInvoices)
                }
            };

            // حفظ التقرير
            saveReport(report);

            // عرض التقرير
            showSalesReportModal(report);
        }

        // إنشاء تقرير الفواتير الإلكترونية
        function generateInvoicesReport() {
            const invoices = JSON.parse(localStorage.getItem('electronicInvoices') || '[]');
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            const filteredInvoices = filterRecordsByDate(invoices, startDate, endDate, 'issueDate');

            const report = {
                id: 'INVOICES-REPORT-' + Date.now(),
                type: 'invoices',
                title: 'تقرير الفواتير الإلكترونية',
                period: `${startDate} إلى ${endDate}`,
                generatedAt: new Date().toISOString(),
                data: {
                    invoices: filteredInvoices,
                    totalInvoices: filteredInvoices.length,
                    issuedInvoices: filteredInvoices.filter(inv => inv.status === 'issued').length,
                    paidInvoices: filteredInvoices.filter(inv => inv.status === 'paid').length,
                    cancelledInvoices: filteredInvoices.filter(inv => inv.status === 'cancelled').length,
                    complianceRate: filteredInvoices.length > 0 ?
                        (filteredInvoices.filter(inv => inv.zatcaCompliant).length / filteredInvoices.length * 100).toFixed(1) : 0,
                    averageProcessingTime: calculateAverageProcessingTime(filteredInvoices),
                    errorRate: calculateErrorRate(filteredInvoices)
                }
            };

            // حفظ التقرير
            saveReport(report);

            // عرض التقرير
            showInvoicesReportModal(report);
        }

        // إنشاء تقرير الامتثال
        function generateComplianceReport() {
            const complianceData = getComplianceData();
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            const report = {
                id: 'COMPLIANCE-REPORT-' + Date.now(),
                type: 'compliance',
                title: 'تقرير الامتثال',
                period: `${startDate} إلى ${endDate}`,
                generatedAt: new Date().toISOString(),
                data: complianceData
            };

            // حفظ التقرير
            saveReport(report);

            // عرض التقرير
            showComplianceReportModal(report);
        }

        // معاينة تقرير ضريبة القيمة المضافة
        function previewVATReport() {
            showAlert('معاينة تقرير ضريبة القيمة المضافة...', 'info');
            // يمكن إضافة معاينة سريعة هنا
            setTimeout(() => {
                generateVATReport();
            }, 1000);
        }

        // معاينة تقرير المبيعات
        function previewSalesReport() {
            showAlert('معاينة تقرير المبيعات...', 'info');
            setTimeout(() => {
                generateSalesReport();
            }, 1000);
        }

        // معاينة تقرير الفواتير الإلكترونية
        function previewInvoicesReport() {
            showAlert('معاينة تقرير الفواتير الإلكترونية...', 'info');
            setTimeout(() => {
                generateInvoicesReport();
            }, 1000);
        }

        // معاينة تقرير الامتثال
        function previewComplianceReport() {
            showAlert('معاينة تقرير الامتثال...', 'info');
            setTimeout(() => {
                generateComplianceReport();
            }, 1000);
        }

        // دوال مساعدة
        function filterRecordsByDate(records, startDate, endDate, dateField) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            end.setHours(23, 59, 59, 999); // نهاية اليوم

            return records.filter(record => {
                const recordDate = new Date(record[dateField]);
                return recordDate >= start && recordDate <= end;
            });
        }

        function getTopCustomers(invoices) {
            const customerSales = {};

            invoices.forEach(invoice => {
                const customer = invoice.customerName || 'عميل غير محدد';
                if (!customerSales[customer]) {
                    customerSales[customer] = 0;
                }
                customerSales[customer] += invoice.totalAmount;
            });

            return Object.entries(customerSales)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([name, amount]) => ({ name, amount }));
        }

        function getSalesByService(invoices) {
            const serviceSales = {};

            invoices.forEach(invoice => {
                const service = invoice.serviceType || 'خدمة غير محددة';
                if (!serviceSales[service]) {
                    serviceSales[service] = 0;
                }
                serviceSales[service] += invoice.totalAmount;
            });

            return Object.entries(serviceSales)
                .map(([service, amount]) => ({ service, amount }));
        }

        function calculateAverageProcessingTime(invoices) {
            // محاكاة حساب متوسط وقت المعالجة
            return '2.5 دقيقة';
        }

        function calculateErrorRate(invoices) {
            // محاكاة حساب معدل الأخطاء
            const errorCount = invoices.filter(inv => inv.status === 'error').length;
            return invoices.length > 0 ? (errorCount / invoices.length * 100).toFixed(1) + '%' : '0%';
        }

        function getComplianceData() {
            return {
                overallCompliance: 98.5,
                eInvoicingCompliance: 100,
                vatCompliance: 98,
                recordKeepingCompliance: 99,
                reportingCompliance: 97,
                requirements: [
                    { name: 'الفوترة الإلكترونية', status: 'compliant', score: 100 },
                    { name: 'ضريبة القيمة المضافة', status: 'compliant', score: 98 },
                    { name: 'حفظ السجلات', status: 'compliant', score: 99 },
                    { name: 'التقارير الدورية', status: 'warning', score: 97 },
                    { name: 'التوقيع الرقمي', status: 'compliant', score: 100 },
                    { name: 'رمز QR', status: 'compliant', score: 100 }
                ],
                lastAudit: '2024-01-15',
                nextAuditDue: '2024-07-15',
                recommendations: [
                    'تحديث نظام التقارير الدورية',
                    'مراجعة إعدادات النسخ الاحتياطي',
                    'تدريب الموظفين على التحديثات الجديدة'
                ]
            };
        }

        function saveReport(report) {
            try {
                const reports = JSON.parse(localStorage.getItem('generatedReports') || '[]');
                reports.push(report);
                localStorage.setItem('generatedReports', JSON.stringify(reports));
                console.log('✅ تم حفظ التقرير:', report.id);
            } catch (error) {
                console.error('❌ خطأ في حفظ التقرير:', error);
            }
        }

        function formatCurrency(amount) {
            return amount.toFixed(2) + ' ريال';
        }

        function getMonthName(month) {
            const months = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            return months[month - 1] || '';
        }

        // عرض تقرير ضريبة القيمة المضافة
        function showVATReportModal(report) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 900px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #333; font-size: 1.4rem;">${report.title}</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #333; margin-bottom: 15px;">ملخص التقرير</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #28a745;">${report.data.totalRecords}</div>
                                <div style="color: #666;">عدد السجلات</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #007bff;">${formatCurrency(report.data.totalTaxableSales)}</div>
                                <div style="color: #666;">المبيعات الخاضعة</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #ffc107;">${formatCurrency(report.data.totalVAT)}</div>
                                <div style="color: #666;">إجمالي الضريبة</div>
                            </div>
                        </div>
                    </div>

                    <div style="background: #e3f2fd; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #1976d2; margin-bottom: 15px;">حالة السداد</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #28a745;">${report.data.paidRecords}</div>
                                <div style="color: #666;">مسددة</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #ffc107;">${report.data.pendingRecords}</div>
                                <div style="color: #666;">معلقة</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #dc3545;">${report.data.overdueRecords}</div>
                                <div style="color: #666;">متأخرة</div>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button class="btn btn-primary" onclick="downloadReport('${report.id}')">
                            <i class="fas fa-download"></i>
                            تحميل التقرير
                        </button>
                        <button class="btn btn-secondary" onclick="emailReport('${report.id}')">
                            <i class="fas fa-envelope"></i>
                            إرسال بالبريد
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            إغلاق
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // عرض تقرير المبيعات
        function showSalesReportModal(report) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 1000px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #333; font-size: 1.4rem;">${report.title}</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #333; margin-bottom: 15px;">إحصائيات المبيعات</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #28a745;">${report.data.totalInvoices}</div>
                                <div style="color: #666;">عدد الفواتير</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #007bff;">${formatCurrency(report.data.totalSales)}</div>
                                <div style="color: #666;">إجمالي المبيعات</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #ffc107;">${formatCurrency(report.data.totalVAT)}</div>
                                <div style="color: #666;">إجمالي الضريبة</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #17a2b8;">${formatCurrency(report.data.averageInvoice)}</div>
                                <div style="color: #666;">متوسط الفاتورة</div>
                            </div>
                        </div>
                    </div>

                    ${report.data.topCustomers.length > 0 ? `
                    <div style="background: #fff3cd; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #856404; margin-bottom: 15px;">أفضل العملاء</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                            ${report.data.topCustomers.map((customer, index) => `
                                <div style="display: flex; justify-content: space-between; padding: 10px; background: white; border-radius: 6px;">
                                    <span>${index + 1}. ${customer.name}</span>
                                    <span style="font-weight: 700;">${formatCurrency(customer.amount)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button class="btn btn-primary" onclick="downloadReport('${report.id}')">
                            <i class="fas fa-download"></i>
                            تحميل التقرير
                        </button>
                        <button class="btn btn-secondary" onclick="emailReport('${report.id}')">
                            <i class="fas fa-envelope"></i>
                            إرسال بالبريد
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            إغلاق
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // عرض تقرير الفواتير الإلكترونية
        function showInvoicesReportModal(report) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 900px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #333; font-size: 1.4rem;">${report.title}</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #333; margin-bottom: 15px;">إحصائيات الفواتير</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px;">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #28a745;">${report.data.totalInvoices}</div>
                                <div style="color: #666;">إجمالي الفواتير</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #007bff;">${report.data.issuedInvoices}</div>
                                <div style="color: #666;">فواتير صادرة</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #28a745;">${report.data.paidInvoices}</div>
                                <div style="color: #666;">فواتير مدفوعة</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #dc3545;">${report.data.cancelledInvoices}</div>
                                <div style="color: #666;">فواتير ملغاة</div>
                            </div>
                        </div>
                    </div>

                    <div style="background: #e3f2fd; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #1976d2; margin-bottom: 15px;">مؤشرات الأداء</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #28a745;">${report.data.complianceRate}%</div>
                                <div style="color: #666;">معدل الامتثال</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #17a2b8;">${report.data.averageProcessingTime}</div>
                                <div style="color: #666;">متوسط وقت المعالجة</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #ffc107;">${report.data.errorRate}</div>
                                <div style="color: #666;">معدل الأخطاء</div>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button class="btn btn-primary" onclick="downloadReport('${report.id}')">
                            <i class="fas fa-download"></i>
                            تحميل التقرير
                        </button>
                        <button class="btn btn-secondary" onclick="emailReport('${report.id}')">
                            <i class="fas fa-envelope"></i>
                            إرسال بالبريد
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            إغلاق
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // عرض تقرير الامتثال
        function showComplianceReportModal(report) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 20px; padding: 30px; max-width: 1000px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <h3 style="color: #333; font-size: 1.4rem;">${report.title}</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #333; margin-bottom: 15px;">نظرة عامة على الامتثال</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 2rem; font-weight: 700; color: #28a745;">${report.data.overallCompliance}%</div>
                                <div style="color: #666;">الامتثال العام</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #007bff;">${report.data.eInvoicingCompliance}%</div>
                                <div style="color: #666;">الفوترة الإلكترونية</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #ffc107;">${report.data.vatCompliance}%</div>
                                <div style="color: #666;">ضريبة القيمة المضافة</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: #17a2b8;">${report.data.recordKeepingCompliance}%</div>
                                <div style="color: #666;">حفظ السجلات</div>
                            </div>
                        </div>
                    </div>

                    <div style="background: #e3f2fd; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #1976d2; margin-bottom: 15px;">تفاصيل المتطلبات</h4>
                        <div style="display: grid; gap: 10px;">
                            ${report.data.requirements.map(req => `
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: white; border-radius: 8px;">
                                    <span>${req.name}</span>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <span style="font-weight: 700; color: ${req.status === 'compliant' ? '#28a745' : req.status === 'warning' ? '#ffc107' : '#dc3545'};">${req.score}%</span>
                                        <span style="padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; background: ${req.status === 'compliant' ? '#d4edda' : req.status === 'warning' ? '#fff3cd' : '#f8d7da'}; color: ${req.status === 'compliant' ? '#155724' : req.status === 'warning' ? '#856404' : '#721c24'};">
                                            ${req.status === 'compliant' ? 'متوافق' : req.status === 'warning' ? 'تحذير' : 'غير متوافق'}
                                        </span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    ${report.data.recommendations.length > 0 ? `
                    <div style="background: #fff3cd; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4 style="color: #856404; margin-bottom: 15px;">التوصيات</h4>
                        <ul style="margin: 0; padding-right: 20px;">
                            ${report.data.recommendations.map(rec => `<li style="margin-bottom: 8px; color: #856404;">${rec}</li>`).join('')}
                        </ul>
                    </div>
                    ` : ''}

                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button class="btn btn-primary" onclick="downloadReport('${report.id}')">
                            <i class="fas fa-download"></i>
                            تحميل التقرير
                        </button>
                        <button class="btn btn-secondary" onclick="emailReport('${report.id}')">
                            <i class="fas fa-envelope"></i>
                            إرسال بالبريد
                        </button>
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                            إغلاق
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // تحميل التقرير
        function downloadReport(reportId) {
            showAlert(`سيتم تحميل التقرير ${reportId} كملف PDF\n\n(هذه ميزة تحتاج لتطوير إضافي مع مكتبة PDF)`, 'info');
        }

        // إرسال التقرير بالبريد الإلكتروني
        function emailReport(reportId) {
            showAlert(`سيتم إرسال التقرير ${reportId} بالبريد الإلكتروني\n\n(هذه ميزة تحتاج لتطوير إضافي مع خدمة البريد الإلكتروني)`, 'info');
        }

        function showAlert(message, type = 'info') {
            const colors = {
                'success': '#d4edda',
                'error': '#f8d7da',
                'info': '#cce5ff',
                'warning': '#fff3cd'
            };

            const alert = document.createElement('div');
            alert.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: #333;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 2000;
                max-width: 400px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                white-space: pre-line;
            `;

            alert.innerHTML = `
                ${message}
                <button onclick="this.parentElement.remove()" style="position: absolute; top: 5px; left: 10px; background: none; border: none; font-size: 1.2rem; cursor: pointer;">×</button>
            `;

            document.body.appendChild(alert);

            setTimeout(() => {
                if (alert.parentElement) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
