<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البداية | نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .card h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5rem;
            text-align: center;
        }

        .card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 25px;
            text-align: center;
        }

        .card-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
            font-family: inherit;
            min-width: 120px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        .quick-links {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .quick-links h3 {
            color: white;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.3rem;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .quick-link {
            background: rgba(255,255,255,0.1);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .quick-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-weight: 600;
            color: #28a745;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
            }
            
            .card-buttons {
                flex-direction: column;
            }
            
            .links-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="status-indicator">
        ✅ النظام جاهز
    </div>

    <div class="container">
        <div class="header">
            <h1>🚚 نظام إدارة الشحنات</h1>
            <p>نقطة البداية الموحدة لجميع خدمات النظام</p>
        </div>

        <div class="cards-grid">
            <!-- بطاقة تسجيل الدخول -->
            <div class="card">
                <div class="card-icon">🔐</div>
                <h2>تسجيل الدخول</h2>
                <p>ادخل إلى النظام كعميل أو مدير نظام من خلال صفحة الدخول الموحدة</p>
                <div class="card-buttons">
                    <a href="unified-login.html" class="btn btn-primary">تسجيل الدخول</a>
                    <a href="customer-register.html" class="btn btn-secondary">حساب جديد</a>
                </div>
            </div>

            <!-- بطاقة لوحة تحكم العملاء -->
            <div class="card">
                <div class="card-icon">👤</div>
                <h2>لوحة تحكم العملاء</h2>
                <p>إدارة الشحنات، إنشاء شحنات جديدة، تتبع الطلبات، وإدارة الملف الشخصي</p>
                <div class="card-buttons">
                    <a href="customer-dashboard.html" class="btn btn-primary">لوحة العملاء</a>
                    <a href="shipment-tracking.html" class="btn btn-secondary">تتبع شحنة</a>
                </div>
            </div>

            <!-- بطاقة لوحة تحكم المديرين -->
            <div class="card">
                <div class="card-icon">👨‍💼</div>
                <h2>لوحة تحكم المديرين</h2>
                <p>إدارة النظام، المستخدمين، التقارير، والإعدادات العامة للشركة</p>
                <div class="card-buttons">
                    <a href="main-dashboard.html" class="btn btn-primary">لوحة المديرين</a>
                    <a href="user-management.html" class="btn btn-secondary">إدارة المستخدمين</a>
                </div>
            </div>

            <!-- بطاقة إدارة الصفحات -->
            <div class="card">
                <div class="card-icon">📄</div>
                <h2>إدارة الصفحات</h2>
                <p>تحرير محتوى الموقع، إدارة الصفحات، والمحررات البصرية المتقدمة</p>
                <div class="card-buttons">
                    <a href="pages-management.html" class="btn btn-primary">إدارة الصفحات</a>
                    <a href="visual-page-editor.html" class="btn btn-secondary">المحرر البصري</a>
                </div>
            </div>

            <!-- بطاقة الموقع العام -->
            <div class="card">
                <div class="card-icon">🌐</div>
                <h2>الموقع العام</h2>
                <p>الصفحة الرئيسية للموقع، صفحة من نحن، وشركاء النجاح</p>
                <div class="card-buttons">
                    <a href="home.html" class="btn btn-primary">الصفحة الرئيسية</a>
                    <a href="about-us.html" class="btn btn-secondary">من نحن</a>
                </div>
            </div>

            <!-- بطاقة الأدوات والتقارير -->
            <div class="card">
                <div class="card-icon">📊</div>
                <h2>الأدوات والتقارير</h2>
                <p>تقارير متقدمة، أدوات التشخيص، وإدارة قواعد البيانات</p>
                <div class="card-buttons">
                    <a href="reports.html" class="btn btn-primary">التقارير</a>
                    <a href="page-diagnostics.html" class="btn btn-secondary">التشخيص</a>
                </div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="quick-links">
            <h3>🔗 روابط سريعة</h3>
            <div class="links-grid">
                <a href="shipments.html" class="quick-link">📦 إدارة الشحنات</a>
                <a href="customers.html" class="quick-link">👥 إدارة العملاء</a>
                <a href="financial-system.html" class="quick-link">💰 النظام المالي</a>
                <a href="branches-management.html" class="quick-link">🏢 إدارة الفروع</a>
                <a href="distributors-management.html" class="quick-link">🚛 المناديب والسائقين</a>
                <a href="settings.html" class="quick-link">⚙️ الإعدادات</a>
                <a href="success-partners.html" class="quick-link">🤝 شركاء النجاح</a>
                <a href="currency-converter.html" class="quick-link">💱 محول العملات</a>
            </div>
        </div>
    </div>

    <script>
        // التحقق من حالة النظام
        document.addEventListener('DOMContentLoaded', function() {
            // فحص الملفات المطلوبة
            const requiredFiles = [
                'unified-login.html',
                'customer-dashboard.html',
                'main-dashboard.html',
                'pages-management.html'
            ];
            
            console.log('🚀 تم تحميل نقطة البداية بنجاح');
            console.log('📁 الملفات المطلوبة:', requiredFiles);
            
            // إضافة تأثيرات بصرية
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
            
            // إضافة CSS للرسوم المتحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                
                .card {
                    opacity: 0;
                }
            `;
            document.head.appendChild(style);
        });

        // فحص الروابط
        function checkLinks() {
            const links = document.querySelectorAll('a[href$=".html"]');
            let workingLinks = 0;
            let totalLinks = links.length;
            
            links.forEach(link => {
                fetch(link.href, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            workingLinks++;
                            link.style.borderLeft = '3px solid #28a745';
                        } else {
                            link.style.borderLeft = '3px solid #dc3545';
                        }
                    })
                    .catch(() => {
                        link.style.borderLeft = '3px solid #dc3545';
                    });
            });
            
            setTimeout(() => {
                console.log(`✅ تم فحص ${totalLinks} رابط، ${workingLinks} يعمل بشكل صحيح`);
            }, 2000);
        }

        // تشغيل فحص الروابط بعد 3 ثوانٍ
        setTimeout(checkLinks, 3000);
    </script>
</body>
</html>
