# Backend Environment Variables
# متغيرات البيئة للخلفية

# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/shipment_management"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here-make-it-at-least-32-characters-long"
JWT_EXPIRES_IN="7d"

# CORS
CORS_ORIGIN="http://localhost:3000"

# Email Configuration (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="نظام إدارة الشحنات"

# Currency Exchange API (Optional)
EXCHANGE_API_KEY="your-exchange-api-key"
EXCHANGE_API_URL="https://api.exchangerate-api.com/v4/latest"

# File Upload
UPLOAD_MAX_SIZE="10MB"
ALLOWED_FILE_TYPES="jpg,jpeg,png,pdf,doc,docx"
UPLOAD_PATH="./uploads"

# Security
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# Rate Limiting
RATE_LIMIT_WINDOW="15m"
RATE_LIMIT_MAX_REQUESTS=100

# Supabase (Optional)
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
