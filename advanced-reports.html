<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير المتقدمة - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display);
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #333;
            padding: 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #333;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: #667eea;
            color: white;
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            font-size: 2.5rem;
            color: white;
            margin: 0 0 10px 0;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .report-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .report-icon {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 15px;
        }

        .report-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 15px;
        }

        .report-description {
            color: #666;
            text-align: center;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .report-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn {
            background: #667eea;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .quick-stats {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .reports-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .page-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>📊</span>
                <span>التقارير المتقدمة</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="reports.html">التقارير الأساسية</a>
                <a href="advanced-reports.html" class="active">التقارير المتقدمة</a>
                <a href="shipments.html">الشحنات</a>
                <a href="branches-management.html">الفروع</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">📊 التقارير المتقدمة</h1>
            <p class="page-subtitle">تقارير تفصيلية وتحليلات متقدمة لنظام إدارة الشحنات</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <h2 style="text-align: center; color: #333; margin-bottom: 25px;">📈 نظرة سريعة</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="quickTotalShipments">0</div>
                    <div class="stat-label">إجمالي الشحنات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="quickDeliveredShipments">0</div>
                    <div class="stat-label">شحنات مسلمة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="quickTotalRevenue">0</div>
                    <div class="stat-label">إجمالي الإيرادات (ريال)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="quickActiveBranches">0</div>
                    <div class="stat-label">فروع نشطة</div>
                </div>
            </div>
        </div>

        <!-- شبكة التقارير -->
        <div class="reports-grid">
            <!-- تقرير الأداء الشامل -->
            <div class="report-card">
                <div class="report-icon">🎯</div>
                <h3 class="report-title">تقرير الأداء الشامل</h3>
                <p class="report-description">تحليل شامل لأداء النظام مع مؤشرات الأداء الرئيسية ومقارنات زمنية</p>
                <div class="report-actions">
                    <button class="btn" onclick="generatePerformanceReport()">📊 إنشاء</button>
                    <button class="btn btn-info" onclick="viewPerformanceReport()">👁️ عرض</button>
                </div>
            </div>

            <!-- تقرير التحليل المالي -->
            <div class="report-card">
                <div class="report-icon">💰</div>
                <h3 class="report-title">التحليل المالي المتقدم</h3>
                <p class="report-description">تحليل مفصل للإيرادات والمصروفات مع توقعات مالية وتحليل الربحية</p>
                <div class="report-actions">
                    <button class="btn btn-success" onclick="generateFinancialAnalysis()">💹 إنشاء</button>
                    <button class="btn btn-info" onclick="exportFinancialData()">📤 تصدير</button>
                </div>
            </div>

            <!-- تقرير تحليل العملاء -->
            <div class="report-card">
                <div class="report-icon">👥</div>
                <h3 class="report-title">تحليل العملاء</h3>
                <p class="report-description">تحليل سلوك العملاء وأنماط الشحن مع تحديد العملاء الأكثر نشاطاً</p>
                <div class="report-actions">
                    <button class="btn btn-warning" onclick="generateCustomerAnalysis()">👤 إنشاء</button>
                    <button class="btn btn-info" onclick="viewCustomerInsights()">🔍 تحليل</button>
                </div>
            </div>

            <!-- تقرير كفاءة الفروع -->
            <div class="report-card">
                <div class="report-icon">🏢</div>
                <h3 class="report-title">كفاءة الفروع</h3>
                <p class="report-description">تقييم أداء الفروع ومقارنة الكفاءة مع تحليل التحويلات بين الفروع</p>
                <div class="report-actions">
                    <button class="btn" onclick="generateBranchEfficiency()">🏪 إنشاء</button>
                    <button class="btn btn-info" onclick="compareBranches()">⚖️ مقارنة</button>
                </div>
            </div>

            <!-- تقرير التوزيع الجغرافي -->
            <div class="report-card">
                <div class="report-icon">🗺️</div>
                <h3 class="report-title">التوزيع الجغرافي</h3>
                <p class="report-description">تحليل التوزيع الجغرافي للشحنات وتحديد المناطق الأكثر نشاطاً</p>
                <div class="report-actions">
                    <button class="btn btn-success" onclick="generateGeographicReport()">🌍 إنشاء</button>
                    <button class="btn btn-info" onclick="viewGeographicMap()">🗺️ خريطة</button>
                </div>
            </div>

            <!-- تقرير تحليل الوقت -->
            <div class="report-card">
                <div class="report-icon">⏰</div>
                <h3 class="report-title">تحليل الوقت والسرعة</h3>
                <p class="report-description">تحليل أوقات التسليم ومعدلات السرعة مع تحديد نقاط التحسين</p>
                <div class="report-actions">
                    <button class="btn btn-warning" onclick="generateTimeAnalysis()">⏱️ إنشاء</button>
                    <button class="btn btn-info" onclick="viewTimeMetrics()">📈 مقاييس</button>
                </div>
            </div>
        </div>
    </main>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 تحميل صفحة التقارير المتقدمة...');
            
            try {
                loadQuickStats();
                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // تحميل الإحصائيات السريعة
        function loadQuickStats() {
            try {
                const shipments = db.getAllShipments();
                const branches = db.getAllBranches();
                
                const totalShipments = shipments.length;
                const deliveredShipments = shipments.filter(s => s.status === 'مسلم').length;
                const activeBranches = branches.filter(b => b.isActive).length;
                const totalRevenue = shipments
                    .filter(s => s.status === 'مسلم')
                    .reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);
                
                document.getElementById('quickTotalShipments').textContent = totalShipments;
                document.getElementById('quickDeliveredShipments').textContent = deliveredShipments;
                document.getElementById('quickTotalRevenue').textContent = totalRevenue.toFixed(2);
                document.getElementById('quickActiveBranches').textContent = activeBranches;
                
            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات السريعة:', error);
            }
        }

        // تقرير الأداء الشامل
        function generatePerformanceReport() {
            try {
                const shipments = db.getAllShipments();
                const branches = db.getAllBranches();
                const transfers = db.getAllBranchTransfers();

                // حساب مؤشرات الأداء
                const totalShipments = shipments.length;
                const deliveredShipments = shipments.filter(s => s.status === 'مسلم').length;
                const cancelledShipments = shipments.filter(s => s.status === 'ملغي').length;
                const pendingShipments = shipments.filter(s => s.status === 'معلق').length;

                const deliveryRate = totalShipments > 0 ? ((deliveredShipments / totalShipments) * 100).toFixed(2) : 0;
                const cancellationRate = totalShipments > 0 ? ((cancelledShipments / totalShipments) * 100).toFixed(2) : 0;
                const efficiency = (100 - parseFloat(cancellationRate)).toFixed(2);

                // حساب متوسط وقت التسليم
                const deliveredWithDates = shipments.filter(s => s.status === 'مسلم' && s.createdDate);
                let avgDeliveryTime = 0;
                if (deliveredWithDates.length > 0) {
                    const totalDays = deliveredWithDates.reduce((sum, s) => {
                        const created = new Date(s.createdDate);
                        const delivered = new Date(s.deliveredDate || s.createdDate);
                        const diffTime = Math.abs(delivered - created);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        return sum + diffDays;
                    }, 0);
                    avgDeliveryTime = (totalDays / deliveredWithDates.length).toFixed(1);
                }

                const reportContent = `
📊 تقرير الأداء الشامل
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📈 مؤشرات الأداء الرئيسية:
   • معدل التسليم الناجح: ${deliveryRate}%
   • معدل الإلغاء: ${cancellationRate}%
   • كفاءة التشغيل العامة: ${efficiency}%
   • متوسط وقت التسليم: ${avgDeliveryTime} يوم

📦 إحصائيات الشحنات:
   • إجمالي الشحنات: ${totalShipments}
   • شحنات مسلمة: ${deliveredShipments}
   • شحنات معلقة: ${pendingShipments}
   • شحنات ملغية: ${cancelledShipments}

🏢 إحصائيات الفروع:
   • إجمالي الفروع: ${branches.length}
   • فروع نشطة: ${branches.filter(b => b.isActive).length}
   • إجمالي التحويلات: ${transfers.length}
   • تحويلات معلقة: ${transfers.filter(t => t.status === 'معلق').length}

📊 تقييم الأداء:
   ${efficiency >= 90 ? '🟢 أداء ممتاز' : efficiency >= 80 ? '🟡 أداء جيد' : '🔴 يحتاج تحسين'}
   ${deliveryRate >= 95 ? '🟢 معدل تسليم ممتاز' : deliveryRate >= 85 ? '🟡 معدل تسليم جيد' : '🔴 معدل تسليم يحتاج تحسين'}
   ${avgDeliveryTime <= 2 ? '🟢 سرعة تسليم ممتازة' : avgDeliveryTime <= 5 ? '🟡 سرعة تسليم جيدة' : '🔴 سرعة تسليم بطيئة'}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
تم إنشاء التقرير في: ${new Date().toLocaleString('ar-SA')}
                `;

                showReportWindow('تقرير الأداء الشامل', reportContent);

            } catch (error) {
                console.error('❌ خطأ في إنشاء تقرير الأداء:', error);
                alert('خطأ في إنشاء تقرير الأداء: ' + error.message);
            }
        }

        // التحليل المالي المتقدم
        function generateFinancialAnalysis() {
            try {
                const shipments = db.getAllShipments();
                const deliveredShipments = shipments.filter(s => s.status === 'مسلم');

                const totalRevenue = deliveredShipments.reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);
                const avgShipmentValue = deliveredShipments.length > 0 ? (totalRevenue / deliveredShipments.length).toFixed(2) : 0;

                // تحليل طرق الدفع
                const codShipments = deliveredShipments.filter(s => s.paymentMethod === 'COD');
                const prepaidShipments = deliveredShipments.filter(s => s.paymentMethod === 'مدفوع مسبقاً');
                const deferredShipments = deliveredShipments.filter(s => s.paymentMethod === 'آجل');

                const codRevenue = codShipments.reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);
                const prepaidRevenue = prepaidShipments.reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);
                const deferredRevenue = deferredShipments.reduce((sum, s) => sum + (parseFloat(s.cost) || 0), 0);

                // تحليل حسب النوع
                const typeAnalysis = {};
                deliveredShipments.forEach(s => {
                    if (!typeAnalysis[s.shipmentType]) {
                        typeAnalysis[s.shipmentType] = { count: 0, revenue: 0 };
                    }
                    typeAnalysis[s.shipmentType].count++;
                    typeAnalysis[s.shipmentType].revenue += parseFloat(s.cost) || 0;
                });

                let typeBreakdown = '';
                Object.keys(typeAnalysis).forEach(type => {
                    const data = typeAnalysis[type];
                    typeBreakdown += `   • ${type}: ${data.count} شحنة - ${data.revenue.toFixed(2)} ريال\n`;
                });

                const reportContent = `
💰 التحليل المالي المتقدم
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

💵 الإيرادات الإجمالية:
   • إجمالي الإيرادات: ${totalRevenue.toFixed(2)} ريال
   • متوسط قيمة الشحنة: ${avgShipmentValue} ريال
   • عدد الشحنات المسلمة: ${deliveredShipments.length}

💳 تحليل طرق الدفع:
   • الدفع عند الاستلام (COD): ${codShipments.length} شحنة - ${codRevenue.toFixed(2)} ريال
   • مدفوع مسبقاً: ${prepaidShipments.length} شحنة - ${prepaidRevenue.toFixed(2)} ريال
   • آجل: ${deferredShipments.length} شحنة - ${deferredRevenue.toFixed(2)} ريال

📦 تحليل الإيرادات حسب النوع:
${typeBreakdown}

📊 مؤشرات مالية:
   • نسبة COD: ${totalRevenue > 0 ? ((codRevenue / totalRevenue) * 100).toFixed(1) : 0}%
   • نسبة المدفوع مسبقاً: ${totalRevenue > 0 ? ((prepaidRevenue / totalRevenue) * 100).toFixed(1) : 0}%
   • العمولات المقدرة (10%): ${(totalRevenue * 0.1).toFixed(2)} ريال
   • صافي الربح المقدر: ${(totalRevenue * 0.9).toFixed(2)} ريال

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
تم إنشاء التقرير في: ${new Date().toLocaleString('ar-SA')}
                `;

                showReportWindow('التحليل المالي المتقدم', reportContent);

            } catch (error) {
                console.error('❌ خطأ في إنشاء التحليل المالي:', error);
                alert('خطأ في إنشاء التحليل المالي: ' + error.message);
            }
        }

        // تحليل العملاء
        function generateCustomerAnalysis() {
            try {
                const shipments = db.getAllShipments();

                // تحليل المرسلين
                const senderAnalysis = {};
                shipments.forEach(s => {
                    if (!senderAnalysis[s.senderName]) {
                        senderAnalysis[s.senderName] = { count: 0, revenue: 0, phone: s.senderPhone };
                    }
                    senderAnalysis[s.senderName].count++;
                    if (s.status === 'مسلم') {
                        senderAnalysis[s.senderName].revenue += parseFloat(s.cost) || 0;
                    }
                });

                // ترتيب العملاء حسب عدد الشحنات
                const topSenders = Object.keys(senderAnalysis)
                    .map(name => ({ name, ...senderAnalysis[name] }))
                    .sort((a, b) => b.count - a.count)
                    .slice(0, 10);

                // تحليل المدن
                const cityAnalysis = {};
                shipments.forEach(s => {
                    if (!cityAnalysis[s.receiverCity]) {
                        cityAnalysis[s.receiverCity] = 0;
                    }
                    cityAnalysis[s.receiverCity]++;
                });

                const topCities = Object.keys(cityAnalysis)
                    .map(city => ({ city, count: cityAnalysis[city] }))
                    .sort((a, b) => b.count - a.count)
                    .slice(0, 10);

                let topSendersText = '';
                topSenders.forEach((sender, index) => {
                    topSendersText += `   ${index + 1}. ${sender.name} - ${sender.count} شحنة - ${sender.revenue.toFixed(2)} ريال\n`;
                });

                let topCitiesText = '';
                topCities.forEach((city, index) => {
                    topCitiesText += `   ${index + 1}. ${city.city} - ${city.count} شحنة\n`;
                });

                const reportContent = `
👥 تحليل العملاء
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 إحصائيات عامة:
   • إجمالي العملاء (المرسلين): ${Object.keys(senderAnalysis).length}
   • إجمالي المدن المخدومة: ${Object.keys(cityAnalysis).length}
   • متوسط الشحنات لكل عميل: ${(shipments.length / Object.keys(senderAnalysis).length).toFixed(1)}

🏆 أفضل 10 عملاء (حسب عدد الشحنات):
${topSendersText}

🌍 أفضل 10 مدن (حسب عدد الشحنات):
${topCitiesText}

📈 تحليل سلوك العملاء:
   • عملاء نشطون (أكثر من 5 شحنات): ${topSenders.filter(s => s.count > 5).length}
   • عملاء متوسطون (2-5 شحنات): ${topSenders.filter(s => s.count >= 2 && s.count <= 5).length}
   • عملاء جدد (شحنة واحدة): ${topSenders.filter(s => s.count === 1).length}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
تم إنشاء التقرير في: ${new Date().toLocaleString('ar-SA')}
                `;

                showReportWindow('تحليل العملاء', reportContent);

            } catch (error) {
                console.error('❌ خطأ في إنشاء تحليل العملاء:', error);
                alert('خطأ في إنشاء تحليل العملاء: ' + error.message);
            }
        }

        // دالة مساعدة لعرض التقارير
        function showReportWindow(title, content) {
            const reportWindow = window.open('', '_blank', 'width=800,height=700,scrollbars=yes');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>${title} - نظام إدارة الشحنات</title>
                    <style>
                        body {
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            margin: 0;
                            padding: 20px;
                            color: #333;
                        }
                        .report-container {
                            background: white;
                            border-radius: 15px;
                            padding: 30px;
                            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                            max-width: 900px;
                            margin: 0 auto;
                        }
                        .report-header {
                            text-align: center;
                            border-bottom: 3px solid #667eea;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .report-title {
                            font-size: 2rem;
                            color: #667eea;
                            margin: 0;
                            font-weight: bold;
                        }
                        .report-content {
                            white-space: pre-line;
                            font-family: 'Courier New', monospace;
                            font-size: 14px;
                            line-height: 1.6;
                            background: #f8f9fa;
                            padding: 25px;
                            border-radius: 10px;
                            border-left: 5px solid #667eea;
                        }
                        .action-buttons {
                            text-align: center;
                            margin-top: 25px;
                            display: flex;
                            gap: 15px;
                            justify-content: center;
                        }
                        .btn {
                            background: #667eea;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                            transition: background 0.3s;
                        }
                        .btn:hover {
                            background: #5a6fd8;
                        }
                        .btn-success {
                            background: #28a745;
                        }
                        .btn-success:hover {
                            background: #218838;
                        }
                        @media print {
                            body { background: white; }
                            .action-buttons { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="report-header">
                            <h1 class="report-title">${title}</h1>
                            <div style="color: #666; margin-top: 10px;">نظام إدارة الشحنات</div>
                        </div>
                        <div class="report-content">${content}</div>
                        <div class="action-buttons">
                            <button class="btn" onclick="window.print()">🖨️ طباعة</button>
                            <button class="btn btn-success" onclick="downloadReport()">💾 تحميل</button>
                            <button class="btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>
                    <script>
                        function downloadReport() {
                            const content = document.querySelector('.report-content').textContent;
                            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                            const link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = '${title.replace(/\s+/g, '_')}_' + new Date().toISOString().split('T')[0] + '.txt';
                            link.click();
                        }
                    </script>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // وظائف إضافية للتقارير الأخرى
        function viewPerformanceReport() {
            generatePerformanceReport();
        }

        function exportFinancialData() {
            try {
                const shipments = db.getAllShipments();
                const financialData = shipments.filter(s => s.status === 'مسلم').map(s => ({
                    trackingNumber: s.trackingNumber,
                    senderName: s.senderName,
                    receiverName: s.receiverName,
                    cost: s.cost,
                    paymentMethod: s.paymentMethod,
                    createdDate: s.createdDate,
                    deliveredDate: s.deliveredDate
                }));

                const dataStr = JSON.stringify(financialData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `financial-data-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                alert('تم تصدير البيانات المالية بنجاح!');

            } catch (error) {
                console.error('❌ خطأ في تصدير البيانات المالية:', error);
                alert('خطأ في تصدير البيانات المالية: ' + error.message);
            }
        }

        function viewCustomerInsights() {
            generateCustomerAnalysis();
        }

        function generateBranchEfficiency() {
            alert('🏢 تقرير كفاءة الفروع قيد التطوير...');
        }

        function compareBranches() {
            alert('⚖️ مقارنة الفروع قيد التطوير...');
        }

        function generateGeographicReport() {
            alert('🗺️ التقرير الجغرافي قيد التطوير...');
        }

        function viewGeographicMap() {
            alert('🌍 الخريطة الجغرافية قيد التطوير...');
        }

        function generateTimeAnalysis() {
            alert('⏰ تحليل الوقت قيد التطوير...');
        }

        function viewTimeMetrics() {
            alert('📈 مقاييس الوقت قيد التطوير...');
        }
    </script>
</body>
</html>
