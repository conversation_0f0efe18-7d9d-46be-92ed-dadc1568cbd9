<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المالي الجديد</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: #6f42c1;
            color: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-purple { background: #6f42c1; color: white; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            border-color: #6f42c1;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #6f42c1;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .financial-chart {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        .chart-placeholder {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.2rem;
        }

        .transaction-list {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .transaction-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .transaction-info h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .transaction-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .transaction-amount {
            font-size: 1.2rem;
            font-weight: 700;
        }

        .amount-positive { color: #28a745; }
        .amount-negative { color: #dc3545; }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .back-btn {
                justify-content: center;
                width: 100%;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tabs-nav {
                flex-direction: column;
            }
            
            .tab-button {
                flex-direction: row;
                justify-content: center;
            }
            
            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .transaction-item {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                    <span>🏠</span>
                    <span>العودة للوحة التحكم</span>
                    <span>←</span>
                </a>
                <div class="header-actions">
                    <button class="btn btn-info btn-sm" onclick="window.location.reload()">
                        <span>🔄</span> تحديث الصفحة
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportFinancialData()">
                        <span>📥</span> تصدير البيانات المالية
                    </button>
                </div>
            </div>
            <h1>💳 النظام المالي الجديد</h1>
            <p>نظام متطور لإدارة الأموال والمحاسبة والتقارير المالية</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navigation Tabs -->
            <div class="tabs-nav">
                <button class="tab-button active" onclick="showTab('dashboard')">
                    <span>📊</span>
                    <span>لوحة المعلومات</span>
                </button>
                <button class="tab-button" onclick="showTab('transactions')">
                    <span>💰</span>
                    <span>المعاملات</span>
                </button>
                <button class="tab-button" onclick="showTab('invoices')">
                    <span>🧾</span>
                    <span>الفواتير</span>
                </button>
                <button class="tab-button" onclick="showTab('reports')">
                    <span>📈</span>
                    <span>التقارير المالية</span>
                </button>
            </div>

            <!-- Tab Contents -->
            
            <!-- لوحة المعلومات -->
            <div id="dashboard" class="tab-content active">
                <div class="section-header">
                    <h2 class="section-title">📊 لوحة المعلومات المالية</h2>
                    <div class="section-actions">
                        <button class="btn btn-purple" onclick="generateFinancialReport()">
                            <span>📊</span> تقرير مالي
                        </button>
                        <button class="btn btn-info" onclick="refreshDashboard()">
                            <span>🔄</span> تحديث
                        </button>
                    </div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalRevenue">0</div>
                        <div class="stat-label">إجمالي الإيرادات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalExpenses">0</div>
                        <div class="stat-label">إجمالي المصروفات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="netProfit">0</div>
                        <div class="stat-label">صافي الربح</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="pendingPayments">0</div>
                        <div class="stat-label">المدفوعات المعلقة</div>
                    </div>
                </div>

                <div class="financial-chart">
                    <h3>📈 الرسم البياني المالي</h3>
                    <div class="chart-placeholder">
                        📊 سيتم عرض الرسم البياني للإيرادات والمصروفات هنا
                    </div>
                </div>
            </div>

            <!-- المعاملات -->
            <div id="transactions" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">💰 المعاملات المالية</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="addTransaction()">
                            <span>➕</span> إضافة معاملة
                        </button>
                        <button class="btn btn-warning" onclick="filterTransactions()">
                            <span>🔍</span> تصفية
                        </button>
                        <button class="btn btn-success" onclick="exportTransactions()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>
                
                <div class="transaction-list" id="transactionsList">
                    <div class="empty-state">
                        <div class="empty-state-icon">💰</div>
                        <h3>لا توجد معاملات مالية</h3>
                        <p>ابدأ بإضافة معاملة مالية جديدة</p>
                        <button class="btn btn-primary" onclick="addTransaction()">➕ إضافة معاملة</button>
                    </div>
                </div>
            </div>

            <!-- الفواتير -->
            <div id="invoices" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">🧾 إدارة الفواتير</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="createInvoice()">
                            <span>➕</span> إنشاء فاتورة
                        </button>
                        <button class="btn btn-warning" onclick="viewPendingInvoices()">
                            <span>⏳</span> الفواتير المعلقة
                        </button>
                        <button class="btn btn-success" onclick="exportInvoices()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>
                
                <div id="invoicesList">
                    <div class="empty-state">
                        <div class="empty-state-icon">🧾</div>
                        <h3>لا توجد فواتير</h3>
                        <p>ابدأ بإنشاء فاتورة جديدة</p>
                        <button class="btn btn-primary" onclick="createInvoice()">➕ إنشاء فاتورة</button>
                    </div>
                </div>
            </div>

            <!-- التقارير المالية -->
            <div id="reports" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">📈 التقارير المالية</h2>
                    <div class="section-actions">
                        <button class="btn btn-purple" onclick="generateMonthlyReport()">
                            <span>📊</span> تقرير شهري
                        </button>
                        <button class="btn btn-info" onclick="generateYearlyReport()">
                            <span>📈</span> تقرير سنوي
                        </button>
                        <button class="btn btn-success" onclick="exportReports()">
                            <span>📤</span> تصدير التقارير
                        </button>
                    </div>
                </div>
                
                <div id="reportsList">
                    <div class="empty-state">
                        <div class="empty-state-icon">📈</div>
                        <h3>لا توجد تقارير مالية</h3>
                        <p>ابدأ بإنشاء تقرير مالي</p>
                        <button class="btn btn-purple" onclick="generateMonthlyReport()">📊 إنشاء تقرير</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // متغيرات عامة
        let financialData = {
            revenue: 0,
            expenses: 0,
            transactions: [],
            invoices: [],
            reports: []
        };

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💳 تحميل النظام المالي الجديد...');
            initializeFinancialSystem();
        });

        // تهيئة النظام
        function initializeFinancialSystem() {
            loadDefaultFinancialData();
            loadFinancialData();
            updateFinancialStatistics();
            console.log('✅ تم تحميل النظام المالي بنجاح');
        }

        // تحميل البيانات الافتراضية
        function loadDefaultFinancialData() {
            if (!localStorage.getItem('new_financial_data')) {
                const defaultData = {
                    revenue: 125000,
                    expenses: 85000,
                    transactions: [
                        {
                            id: 'TXN001',
                            type: 'إيراد',
                            amount: 2500,
                            description: 'رسوم توصيل',
                            date: new Date().toISOString().split('T')[0],
                            category: 'خدمات'
                        },
                        {
                            id: 'TXN002',
                            type: 'مصروف',
                            amount: -800,
                            description: 'وقود السيارات',
                            date: new Date().toISOString().split('T')[0],
                            category: 'تشغيل'
                        }
                    ],
                    invoices: [],
                    reports: []
                };
                localStorage.setItem('new_financial_data', JSON.stringify(defaultData));
                console.log('✅ تم إنشاء البيانات المالية الافتراضية');
            }
        }

        // تحديث الإحصائيات المالية
        function updateFinancialStatistics() {
            const totalRevenueEl = document.getElementById('totalRevenue');
            const totalExpensesEl = document.getElementById('totalExpenses');
            const netProfitEl = document.getElementById('netProfit');
            const pendingPaymentsEl = document.getElementById('pendingPayments');

            if (totalRevenueEl) totalRevenueEl.textContent = financialData.revenue.toLocaleString() + ' ريال';
            if (totalExpensesEl) totalExpensesEl.textContent = financialData.expenses.toLocaleString() + ' ريال';
            if (netProfitEl) netProfitEl.textContent = (financialData.revenue - financialData.expenses).toLocaleString() + ' ريال';
            if (pendingPaymentsEl) pendingPaymentsEl.textContent = '15,000 ريال'; // تقدير
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = event.target.closest('.tab-button');
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            console.log(`📋 تم التبديل إلى تبويب: ${tabName}`);
        }

        // وظائف النظام المالي
        function generateFinancialReport() {
            alert('📊 سيتم إنشاء تقرير مالي شامل...');
            console.log('📊 إنشاء تقرير مالي');
        }

        function refreshDashboard() {
            loadFinancialData();
            updateFinancialStatistics();
            alert('✅ تم تحديث لوحة المعلومات المالية!');
        }

        function addTransaction() {
            alert('💰 سيتم فتح نموذج إضافة معاملة مالية جديدة...');
            console.log('➕ إضافة معاملة مالية');
        }

        function filterTransactions() {
            alert('🔍 سيتم فتح نموذج تصفية المعاملات...');
            console.log('🔍 تصفية المعاملات');
        }

        function exportTransactions() {
            const dataStr = JSON.stringify(financialData.transactions, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `transactions_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            alert('✅ تم تصدير المعاملات المالية بنجاح!');
            console.log('✅ تم تصدير المعاملات المالية');
        }

        function createInvoice() {
            alert('🧾 سيتم فتح نموذج إنشاء فاتورة جديدة...');
            console.log('➕ إنشاء فاتورة');
        }

        function viewPendingInvoices() {
            alert('⏳ عرض الفواتير المعلقة...');
            console.log('⏳ الفواتير المعلقة');
        }

        function exportInvoices() {
            alert('📤 تصدير الفواتير...');
            console.log('📤 تصدير الفواتير');
        }

        function generateMonthlyReport() {
            alert('📊 سيتم إنشاء تقرير مالي شهري...');
            console.log('📊 تقرير شهري');
        }

        function generateYearlyReport() {
            alert('📈 سيتم إنشاء تقرير مالي سنوي...');
            console.log('📈 تقرير سنوي');
        }

        function exportReports() {
            alert('📤 تصدير التقارير المالية...');
            console.log('📤 تصدير التقارير');
        }

        function exportFinancialData() {
            const dataStr = JSON.stringify(financialData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `financial_data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            alert('✅ تم تصدير البيانات المالية بنجاح!');
            console.log('✅ تم تصدير البيانات المالية');
        }

        function loadFinancialData() {
            try {
                financialData = JSON.parse(localStorage.getItem('new_financial_data') || '{}');
                console.log('💳 تم تحميل البيانات المالية');
            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات المالية:', error);
                financialData = { revenue: 0, expenses: 0, transactions: [], invoices: [], reports: [] };
            }
        }

        // جعل الوظائف متاحة عالمياً
        window.generateFinancialReport = generateFinancialReport;
        window.refreshDashboard = refreshDashboard;
        window.addTransaction = addTransaction;
        window.filterTransactions = filterTransactions;
        window.exportTransactions = exportTransactions;
        window.createInvoice = createInvoice;
        window.viewPendingInvoices = viewPendingInvoices;
        window.exportInvoices = exportInvoices;
        window.generateMonthlyReport = generateMonthlyReport;
        window.generateYearlyReport = generateYearlyReport;
        window.exportReports = exportReports;
        window.exportFinancialData = exportFinancialData;
        window.showTab = showTab;

        console.log('💳 تم تحميل النظام المالي الجديد بنجاح!');
    </script>
</body>
</html>
