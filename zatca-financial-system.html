<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المالي المتوافق مع هيئة الزكاة والضريبة | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 30px 40px;
            border-radius: 25px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .zatca-badge {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            color: white;
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
        }

        .main-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 25px;
            padding: 35px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
        }

        .card-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }

        .card-subtitle {
            color: #666;
            font-size: 1rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin-bottom: 25px;
        }

        .feature-list li {
            padding: 12px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #555;
            font-size: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✅";
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            width: 100%;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(30, 126, 52, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(30, 126, 52, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
        }

        .btn-info:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(23, 162, 184, 0.4);
        }

        .compliance-status {
            background: white;
            border-radius: 25px;
            padding: 35px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .compliance-status::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
        }

        .status-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .status-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
        }

        .status-item {
            text-align: center;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .status-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .status-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .status-icon.success {
            color: #28a745;
        }

        .status-icon.warning {
            color: #ffc107;
        }

        .status-icon.info {
            color: #17a2b8;
        }

        .status-item-title {
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .status-item-description {
            color: #666;
            font-size: 0.95rem;
        }

        .quick-actions {
            background: white;
            border-radius: 25px;
            padding: 35px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .quick-actions::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .actions-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 25px;
            text-align: center;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            margin-bottom: 20px;
            padding: 12px 20px;
            border-radius: 25px;
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s;
            font-weight: 500;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(5px);
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .feature-card {
                padding: 25px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-link">
            <i class="fas fa-arrow-right"></i>
            العودة للصفحة الرئيسية
        </a>

        <div class="header">
            <h1>النظام المالي المتوافق مع هيئة الزكاة والضريبة</h1>
            <p>نظام شامل ومتكامل للإدارة المالية والامتثال الضريبي</p>
            <div class="zatca-badge">
                <i class="fas fa-shield-alt"></i>
                معتمد من هيئة الزكاة والضريبة والجمارك
            </div>
        </div>

        <!-- حالة الامتثال -->
        <div class="compliance-status">
            <div class="status-header">
                <h2 class="status-title">حالة الامتثال الحالية</h2>
            </div>

            <div class="status-grid">
                <div class="status-item">
                    <div class="status-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="status-item-title">الفوترة الإلكترونية</div>
                    <div class="status-item-description">متوافق 100% مع المتطلبات</div>
                </div>

                <div class="status-item">
                    <div class="status-icon success">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="status-item-title">ضريبة القيمة المضافة</div>
                    <div class="status-item-description">محدث وفقاً لآخر التعديلات</div>
                </div>

                <div class="status-item">
                    <div class="status-icon info">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="status-item-title">حفظ السجلات</div>
                    <div class="status-item-description">آمن ومحفوظ لمدة 6 سنوات</div>
                </div>

                <div class="status-item">
                    <div class="status-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="status-item-title">التقارير الدورية</div>
                    <div class="status-item-description">مستحق خلال 5 أيام</div>
                </div>
            </div>
        </div>

        <!-- الميزات الرئيسية -->
        <div class="main-grid">
            <!-- نظام الامتثال -->
            <div class="feature-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <div class="card-title">نظام الامتثال الشامل</div>
                        <div class="card-subtitle">متوافق مع جميع متطلبات هيئة الزكاة والضريبة</div>
                    </div>
                </div>

                <ul class="feature-list">
                    <li>الفوترة الإلكترونية المعتمدة</li>
                    <li>حساب ضريبة القيمة المضافة تلقائياً</li>
                    <li>التوقيع الرقمي ورمز QR</li>
                    <li>حفظ السجلات لمدة 6 سنوات</li>
                    <li>التقارير الضريبية الدورية</li>
                </ul>

                <a href="zatca-compliance.html" class="btn btn-success">
                    <i class="fas fa-cog"></i>
                    إدارة نظام الامتثال
                </a>
            </div>

            <!-- إدارة ضريبة القيمة المضافة -->
            <div class="feature-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div>
                        <div class="card-title">إدارة ضريبة القيمة المضافة</div>
                        <div class="card-subtitle">نظام متكامل لحساب وإدارة الضريبة</div>
                    </div>
                </div>

                <ul class="feature-list">
                    <li>حساب الضريبة بنسبة 15% تلقائياً</li>
                    <li>تقارير شهرية وربعية</li>
                    <li>إدارة الإقرارات الضريبية</li>
                    <li>حاسبة ضريبة متقدمة</li>
                    <li>تتبع المدفوعات والمستحقات</li>
                </ul>

                <a href="vat-management.html" class="btn btn-primary">
                    <i class="fas fa-calculator"></i>
                    إدارة ضريبة القيمة المضافة
                </a>
            </div>

            <!-- التقارير الضريبية -->
            <div class="feature-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div>
                        <div class="card-title">التقارير الضريبية</div>
                        <div class="card-subtitle">تقارير شاملة ومفصلة للامتثال</div>
                    </div>
                </div>

                <ul class="feature-list">
                    <li>تقارير المبيعات والإيرادات</li>
                    <li>تقارير ضريبة القيمة المضافة</li>
                    <li>تقارير الفواتير الإلكترونية</li>
                    <li>تقارير الامتثال والمراجعة</li>
                    <li>تصدير بصيغ متعددة</li>
                </ul>

                <a href="zatca-reports.html" class="btn btn-info">
                    <i class="fas fa-file-alt"></i>
                    عرض التقارير الضريبية
                </a>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="quick-actions">
            <h2 class="actions-title">الإجراءات السريعة</h2>

            <div class="actions-grid">
                <button class="btn btn-success" onclick="createElectronicInvoice()">
                    <i class="fas fa-file-invoice"></i>
                    إنشاء فاتورة إلكترونية
                </button>

                <button class="btn btn-primary" onclick="calculateVAT()">
                    <i class="fas fa-calculator"></i>
                    حساب ضريبة القيمة المضافة
                </button>

                <button class="btn btn-info" onclick="generateMonthlyReport()">
                    <i class="fas fa-chart-line"></i>
                    تقرير شهري
                </button>

                <button class="btn btn-success" onclick="checkCompliance()">
                    <i class="fas fa-shield-alt"></i>
                    فحص الامتثال
                </button>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏛️ تحميل النظام المالي المتوافق مع هيئة الزكاة والضريبة...');
            initializeFinancialSystem();
            updateComplianceStatus();
        });

        // تهيئة النظام المالي
        function initializeFinancialSystem() {
            // تهيئة إعدادات الشركة
            if (!localStorage.getItem('companySettings')) {
                const companySettings = {
                    name: 'شركة الشحن السريع',
                    taxNumber: '300000000000003',
                    address: 'الرياض، المملكة العربية السعودية',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    eInvoicingEnabled: true,
                    vatRegistered: true,
                    zatcaCompliant: true,
                    lastUpdated: new Date().toISOString()
                };
                localStorage.setItem('companySettings', JSON.stringify(companySettings));
            }

            // تهيئة إعدادات النظام المالي
            if (!localStorage.getItem('financialSettings')) {
                const financialSettings = {
                    currency: 'SAR',
                    vatRate: 15,
                    fiscalYearStart: '01-01',
                    reportingPeriod: 'monthly',
                    autoBackup: true,
                    auditTrail: true,
                    multiCurrency: true,
                    supportedCurrencies: ['SAR', 'USD', 'EUR', 'KWD'],
                    lastUpdated: new Date().toISOString()
                };
                localStorage.setItem('financialSettings', JSON.stringify(financialSettings));
            }
        }

        // تحديث حالة الامتثال
        function updateComplianceStatus() {
            // يمكن إضافة منطق لتحديث حالة الامتثال بناءً على البيانات الفعلية
            console.log('✅ تم تحديث حالة الامتثال');
        }

        // إنشاء فاتورة إلكترونية
        function createElectronicInvoice() {
            window.location.href = 'zatca-compliance.html#create-invoice';
        }

        // حساب ضريبة القيمة المضافة
        function calculateVAT() {
            window.location.href = 'vat-management.html#calculator';
        }

        // إنشاء تقرير شهري
        function generateMonthlyReport() {
            window.location.href = 'zatca-reports.html#monthly-report';
        }

        // فحص الامتثال
        function checkCompliance() {
            window.location.href = 'zatca-compliance.html#compliance-check';
        }
    </script>
</body>
</html>
