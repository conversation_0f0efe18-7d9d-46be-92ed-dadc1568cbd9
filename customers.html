<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* نفس الأنماط من shipments.html */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display);
            font-weight: 600;
            background: var(--bg-secondary);
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-links a:hover, .nav-links a.active {
            background: rgba(255,255,255,0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: #333;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: transform 0.2s;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .search-filter {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
        }

        .customers-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: right;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #e1e5e9;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            text-align: right;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 500;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .actions {
            display: flex;
            gap: 8px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e1e5e9;
        }

        .modal-header h2 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
        }

        .close {
            font-size: 2rem;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 600;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 500;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 1.1rem;
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif;
            font-weight: 500;
        }

        /* تطبيق خط SF Pro Arabic Display على جميع العناصر */
        *, *::before, *::after {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        input, select, textarea, button {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 500 !important;
        }

        .nav-links a {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .search-filter {
                flex-direction: column;
                align-items: stretch;
            }

            .table {
                font-size: 0.9rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🚚</span>
                <span>نظام إدارة الشحنات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="shipments.html">الشحنات</a>
                <a href="customers.html" class="active">العملاء</a>
                <a href="distributors.html">الموزعين</a>
                <a href="index.html">تسجيل الخروج</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">إدارة العملاء</h1>
            <button class="btn" onclick="openAddModal()">
                ➕ إضافة عميل جديد
            </button>
        </div>

        <div class="search-filter">
            <input type="text" class="search-input" id="searchInput" placeholder="البحث بالاسم، البريد الإلكتروني، أو رقم الهاتف...">
            <button class="btn btn-secondary" onclick="clearSearch()">مسح البحث</button>
        </div>

        <div class="customers-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>رقم الهاتف</th>
                        <th>المدينة</th>
                        <th>البلد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="customersTableBody">
                    <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
                </tbody>
            </table>
            
            <div id="noDataMessage" class="no-data" style="display: none;">
                لا توجد عملاء مطابقين للبحث
            </div>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل العميل -->
    <div id="customerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة عميل جديد</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="customerForm">
                <div class="form-group">
                    <label class="form-label">الاسم *</label>
                    <input type="text" class="form-input" id="name" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-input" id="email">
                    </div>
                    <div class="form-group">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-input" id="phone">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">العنوان</label>
                    <input type="text" class="form-input" id="address">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">المدينة</label>
                        <input type="text" class="form-input" id="city">
                    </div>
                    <div class="form-group">
                        <label class="form-label">البلد</label>
                        <input type="text" class="form-input" id="country">
                    </div>
                </div>
                
                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ العميل</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script>
        let currentEditId = null;

        // تحميل العملاء عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadCustomers();
            setupEventListeners();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterCustomers);
            document.getElementById('customerForm').addEventListener('submit', saveCustomer);
        }

        // تحميل وعرض العملاء
        function loadCustomers() {
            const customers = db.getAllCustomers();
            displayCustomers(customers);
        }

        // عرض العملاء في الجدول
        function displayCustomers(customers) {
            const tbody = document.getElementById('customersTableBody');
            const noDataMessage = document.getElementById('noDataMessage');
            
            if (customers.length === 0) {
                tbody.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }
            
            noDataMessage.style.display = 'none';
            
            tbody.innerHTML = customers.map(customer => `
                <tr>
                    <td><strong>${customer.name}</strong></td>
                    <td>${customer.email || '-'}</td>
                    <td>${customer.phone || '-'}</td>
                    <td>${customer.city || '-'}</td>
                    <td>${customer.country || '-'}</td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-small" onclick="viewCustomer('${customer.id}')">عرض</button>
                            <button class="btn btn-small btn-secondary" onclick="editCustomer('${customer.id}')">تعديل</button>
                            <button class="btn btn-small btn-danger" onclick="deleteCustomer('${customer.id}')">حذف</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // فلترة العملاء
        function filterCustomers() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const customers = db.getAllCustomers();
            
            const filteredCustomers = customers.filter(customer => 
                customer.name.toLowerCase().includes(searchTerm) ||
                (customer.email && customer.email.toLowerCase().includes(searchTerm)) ||
                (customer.phone && customer.phone.includes(searchTerm))
            );
            
            displayCustomers(filteredCustomers);
        }

        // مسح البحث
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            loadCustomers();
        }

        // فتح نافذة إضافة عميل
        function openAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = 'إضافة عميل جديد';
            document.getElementById('customerForm').reset();
            document.getElementById('customerModal').style.display = 'block';
        }

        // تعديل عميل
        function editCustomer(id) {
            const customers = db.getAllCustomers();
            const customer = customers.find(c => c.id === id);
            if (!customer) return;
            
            currentEditId = id;
            document.getElementById('modalTitle').textContent = 'تعديل العميل';
            
            // ملء النموذج بالبيانات الحالية
            document.getElementById('name').value = customer.name || '';
            document.getElementById('email').value = customer.email || '';
            document.getElementById('phone').value = customer.phone || '';
            document.getElementById('address').value = customer.address || '';
            document.getElementById('city').value = customer.city || '';
            document.getElementById('country').value = customer.country || '';
            
            document.getElementById('customerModal').style.display = 'block';
        }

        // عرض تفاصيل العميل
        function viewCustomer(id) {
            const customers = db.getAllCustomers();
            const customer = customers.find(c => c.id === id);
            if (!customer) return;
            
            // حساب عدد الشحنات للعميل
            const shipments = db.getAllShipments();
            const customerShipments = shipments.filter(s => 
                s.senderName === customer.name || s.receiverName === customer.name
            );
            
            alert(`تفاصيل العميل:\n\n` +
                  `الاسم: ${customer.name}\n` +
                  `البريد الإلكتروني: ${customer.email || 'غير محدد'}\n` +
                  `رقم الهاتف: ${customer.phone || 'غير محدد'}\n` +
                  `العنوان: ${customer.address || 'غير محدد'}\n` +
                  `المدينة: ${customer.city || 'غير محدد'}\n` +
                  `البلد: ${customer.country || 'غير محدد'}\n` +
                  `عدد الشحنات: ${customerShipments.length}`);
        }

        // حذف عميل
        function deleteCustomer(id) {
            const customers = db.getAllCustomers();
            const customer = customers.find(c => c.id === id);
            if (!customer) return;
            
            if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`)) {
                const updatedCustomers = customers.filter(c => c.id !== id);
                localStorage.setItem('customers', JSON.stringify(updatedCustomers));
                loadCustomers();
                alert('تم حذف العميل بنجاح');
            }
        }

        // حفظ العميل
        function saveCustomer(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                address: document.getElementById('address').value,
                city: document.getElementById('city').value,
                country: document.getElementById('country').value
            };
            
            if (currentEditId) {
                // تحديث عميل موجود
                const customers = db.getAllCustomers();
                const index = customers.findIndex(c => c.id === currentEditId);
                if (index !== -1) {
                    customers[index] = { ...customers[index], ...formData };
                    localStorage.setItem('customers', JSON.stringify(customers));
                    alert('تم تحديث العميل بنجاح');
                }
            } else {
                // إضافة عميل جديد
                const newCustomer = db.addCustomer(formData);
                alert(`تم إنشاء العميل بنجاح\nمعرف العميل: ${newCustomer.id}`);
            }
            
            closeModal();
            loadCustomers();
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('customerModal').style.display = 'none';
            currentEditId = null;
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('customerModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
