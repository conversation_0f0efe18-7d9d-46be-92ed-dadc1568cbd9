<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الخطوط - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .font-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            background: #f9fafb;
        }

        .font-section h2 {
            color: #4f46e5;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 3px solid #4f46e5;
            padding-bottom: 10px;
        }

        .font-info {
            background: #e0e7ff;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #3730a3;
        }

        .text-samples {
            display: grid;
            gap: 20px;
        }

        .sample {
            padding: 20px;
            border: 1px solid #d1d5db;
            border-radius: 10px;
            background: white;
        }

        .sample-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 10px;
            font-weight: 600;
        }

        /* أوزان مختلفة للخط */
        .weight-300 { font-weight: 300; }
        .weight-400 { font-weight: 400; }
        .weight-500 { font-weight: 500; }
        .weight-600 { font-weight: 600; }
        .weight-700 { font-weight: 700; }
        .weight-800 { font-weight: 800; }
        .weight-900 { font-weight: 900; }

        /* أحجام مختلفة */
        .size-small { font-size: 0.875rem; }
        .size-normal { font-size: 1rem; }
        .size-large { font-size: 1.25rem; }
        .size-xl { font-size: 1.5rem; }
        .size-2xl { font-size: 2rem; }
        .size-3xl { font-size: 2.5rem; }

        .controls {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .controls h3 {
            margin-bottom: 15px;
            color: #4f46e5;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }

        select, input[type="range"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 5px;
            font-family: inherit;
        }

        .font-stack-display {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }

        .font-stack-display h4 {
            color: #92400e;
            margin-bottom: 10px;
        }

        .current-font {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #451a03;
            background: white;
            padding: 10px;
            border-radius: 5px;
        }

        .arabic-text {
            direction: rtl;
            text-align: right;
        }

        .english-text {
            direction: ltr;
            text-align: left;
        }

        .mixed-text {
            direction: rtl;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>🎨 تحكم في الخط</h3>
        
        <div class="control-group">
            <label>نوع الخط:</label>
            <select id="fontFamily">
                <option value="var(--font-arabic-display)">SF Pro Arabic Display (افتراضي)</option>
                <option value="'Cairo', sans-serif">Cairo</option>
                <option value="'Tajawal', sans-serif">Tajawal</option>
                <option value="'Inter', sans-serif">Inter</option>
                <option value="'-apple-system', sans-serif">Apple System</option>
                <option value="'Segoe UI', sans-serif">Segoe UI</option>
            </select>
        </div>

        <div class="control-group">
            <label>وزن الخط:</label>
            <select id="fontWeight">
                <option value="300">خفيف (300)</option>
                <option value="400">عادي (400)</option>
                <option value="500">متوسط (500)</option>
                <option value="600" selected>نصف عريض (600)</option>
                <option value="700">عريض (700)</option>
                <option value="800">عريض جداً (800)</option>
                <option value="900">أسود (900)</option>
            </select>
        </div>

        <div class="control-group">
            <label>حجم الخط: <span id="fontSizeValue">16px</span></label>
            <input type="range" id="fontSize" min="12" max="48" value="16">
        </div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔤 اختبار الخطوط</h1>
            <p>نظام إدارة الشحنات - Font Testing System</p>
        </div>

        <div class="content">
            <div class="font-stack-display">
                <h4>📋 مجموعة الخطوط الحالية:</h4>
                <div class="current-font" id="currentFontStack">
                    font-family: var(--font-arabic-display);
                </div>
            </div>

            <!-- النصوص العربية -->
            <div class="font-section">
                <h2>🔤 النصوص العربية</h2>
                <div class="font-info">
                    الخط المستخدم: SF Pro Arabic Display → Cairo → Tajawal → SF Arabic
                </div>
                
                <div class="text-samples">
                    <div class="sample arabic-text">
                        <div class="sample-label">عنوان رئيسي (2.5rem - 700)</div>
                        <div class="size-3xl weight-700">نظام إدارة الشحنات المتكامل</div>
                    </div>

                    <div class="sample arabic-text">
                        <div class="sample-label">عنوان فرعي (2rem - 600)</div>
                        <div class="size-2xl weight-600">إدارة الشحنات والمناديب والعملاء</div>
                    </div>

                    <div class="sample arabic-text">
                        <div class="sample-label">نص عادي (1rem - 400)</div>
                        <div class="size-normal weight-400">مرحباً بك في نظام إدارة الشحنات. يمكنك من خلال هذا النظام إدارة جميع عمليات الشحن والتوصيل بكفاءة عالية. النظام يدعم اللغة العربية بشكل كامل ويوفر واجهة سهلة الاستخدام.</div>
                    </div>

                    <div class="sample arabic-text">
                        <div class="sample-label">نص مهم (1rem - 600)</div>
                        <div class="size-normal weight-600">تنبيه: يرجى التأكد من صحة بيانات الشحنة قبل الإرسال</div>
                    </div>

                    <div class="sample arabic-text">
                        <div class="sample-label">أرقام وتواريخ</div>
                        <div class="size-normal weight-500">
                            رقم الشحنة: ١٢٣٤٥٦٧٨٩٠<br>
                            التاريخ: ٢٠٢٥/٠١/٠٧<br>
                            المبلغ: ٢٥٠.٥٠ ريال سعودي
                        </div>
                    </div>
                </div>
            </div>

            <!-- النصوص الإنجليزية -->
            <div class="font-section">
                <h2>🔤 English Text</h2>
                <div class="font-info">
                    Font Used: SF Pro Display → Inter → -apple-system → BlinkMacSystemFont
                </div>
                
                <div class="text-samples">
                    <div class="sample english-text">
                        <div class="sample-label">Main Heading (2.5rem - 700)</div>
                        <div class="size-3xl weight-700">Shipment Management System</div>
                    </div>

                    <div class="sample english-text">
                        <div class="sample-label">Subheading (2rem - 600)</div>
                        <div class="size-2xl weight-600">Manage Shipments, Distributors & Customers</div>
                    </div>

                    <div class="sample english-text">
                        <div class="sample-label">Body Text (1rem - 400)</div>
                        <div class="size-normal weight-400">Welcome to the Shipment Management System. This system allows you to efficiently manage all shipping and delivery operations. The system fully supports Arabic language and provides an easy-to-use interface.</div>
                    </div>

                    <div class="sample english-text">
                        <div class="sample-label">Important Text (1rem - 600)</div>
                        <div class="size-normal weight-600">Warning: Please verify shipment data before sending</div>
                    </div>

                    <div class="sample english-text">
                        <div class="sample-label">Numbers and Dates</div>
                        <div class="size-normal weight-500">
                            Shipment ID: 1234567890<br>
                            Date: 2025/01/07<br>
                            Amount: 250.50 SAR
                        </div>
                    </div>
                </div>
            </div>

            <!-- النصوص المختلطة -->
            <div class="font-section">
                <h2>🔤 النصوص المختلطة (Mixed Text)</h2>
                <div class="font-info">
                    خليط من العربية والإنجليزية والأرقام
                </div>
                
                <div class="text-samples">
                    <div class="sample mixed-text">
                        <div class="sample-label">عناوين مختلطة</div>
                        <div class="size-xl weight-600">
                            نظام إدارة الشحنات - Shipment Management System<br>
                            إدارة العملاء - Customer Management<br>
                            التقارير المالية - Financial Reports
                        </div>
                    </div>

                    <div class="sample mixed-text">
                        <div class="sample-label">بيانات الشحنة</div>
                        <div class="size-normal weight-500">
                            اسم العميل: Ahmed Mohammed (أحمد محمد)<br>
                            رقم الهاتف: +966 50 123 4567<br>
                            العنوان: Riyadh, Saudi Arabia (الرياض، المملكة العربية السعودية)<br>
                            البريد الإلكتروني: <EMAIL>
                        </div>
                    </div>

                    <div class="sample mixed-text">
                        <div class="sample-label">واجهة المستخدم</div>
                        <div class="size-normal weight-400">
                            Dashboard (لوحة التحكم) | Settings (الإعدادات) | Reports (التقارير)<br>
                            Login (تسجيل الدخول) | Logout (تسجيل الخروج) | Profile (الملف الشخصي)
                        </div>
                    </div>
                </div>
            </div>

            <!-- أوزان الخط -->
            <div class="font-section">
                <h2>⚖️ أوزان الخط المختلفة</h2>
                <div class="text-samples">
                    <div class="sample">
                        <div class="weight-300 size-large">خفيف (300) - نص تجريبي للخط الخفيف</div>
                        <div class="weight-400 size-large">عادي (400) - نص تجريبي للخط العادي</div>
                        <div class="weight-500 size-large">متوسط (500) - نص تجريبي للخط المتوسط</div>
                        <div class="weight-600 size-large">نصف عريض (600) - نص تجريبي للخط نصف العريض</div>
                        <div class="weight-700 size-large">عريض (700) - نص تجريبي للخط العريض</div>
                        <div class="weight-800 size-large">عريض جداً (800) - نص تجريبي للخط العريض جداً</div>
                        <div class="weight-900 size-large">أسود (900) - نص تجريبي للخط الأسود</div>
                    </div>
                </div>
            </div>

            <!-- أحجام الخط -->
            <div class="font-section">
                <h2>📏 أحجام الخط المختلفة</h2>
                <div class="text-samples">
                    <div class="sample">
                        <div class="size-small weight-600">صغير (0.875rem) - نص تجريبي</div>
                        <div class="size-normal weight-600">عادي (1rem) - نص تجريبي</div>
                        <div class="size-large weight-600">كبير (1.25rem) - نص تجريبي</div>
                        <div class="size-xl weight-600">كبير جداً (1.5rem) - نص تجريبي</div>
                        <div class="size-2xl weight-600">ضخم (2rem) - نص تجريبي</div>
                        <div class="size-3xl weight-600">عملاق (2.5rem) - نص تجريبي</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // التحكم في الخط
        const fontFamily = document.getElementById('fontFamily');
        const fontWeight = document.getElementById('fontWeight');
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const currentFontStack = document.getElementById('currentFontStack');
        const body = document.body;

        function updateFont() {
            const family = fontFamily.value;
            const weight = fontWeight.value;
            const size = fontSize.value + 'px';

            body.style.fontFamily = family;
            body.style.fontWeight = weight;
            body.style.fontSize = size;

            fontSizeValue.textContent = size;
            currentFontStack.textContent = `font-family: ${family}; font-weight: ${weight}; font-size: ${size};`;
        }

        fontFamily.addEventListener('change', updateFont);
        fontWeight.addEventListener('change', updateFont);
        fontSize.addEventListener('input', updateFont);

        // تحديث القيمة الأولية
        updateFont();

        // إضافة معلومات الخط المكتشف
        function detectFont() {
            const testElement = document.createElement('span');
            testElement.style.fontFamily = fontFamily.value;
            testElement.textContent = 'Test';
            testElement.style.position = 'absolute';
            testElement.style.visibility = 'hidden';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const actualFont = computedStyle.fontFamily;
            
            document.body.removeChild(testElement);
            
            console.log('الخط المطلوب:', fontFamily.value);
            console.log('الخط المستخدم فعلياً:', actualFont);
        }

        fontFamily.addEventListener('change', detectFont);
        detectFont();
    </script>
</body>
</html>
