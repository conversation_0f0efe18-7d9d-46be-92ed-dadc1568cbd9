@echo off
chcp 65001 >nul
echo.
echo 📦 إنشاء الملف المضغوط - نظام إدارة الشحنات المتكامل
echo ========================================================
echo.

set "ZIP_NAME=نظام_إدارة_الشحنات_المتكامل_v2024_نهائي_محسن"
set "TEMP_DIR=%ZIP_NAME%"

echo 🔄 بدء إنشاء الملف المضغوط...
echo.

REM إنشاء مجلد مؤقت
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

echo ✅ تم إنشاء المجلد المؤقت: %TEMP_DIR%

REM نسخ الملفات الأساسية
echo.
echo 📋 نسخ الملفات الأساسية...

REM الصفحات الرئيسية
if exist "index.html" copy "index.html" "%TEMP_DIR%\" >nul
if exist "unified-login.html" copy "unified-login.html" "%TEMP_DIR%\" >nul
if exist "main-dashboard.html" copy "main-dashboard.html" "%TEMP_DIR%\" >nul
if exist "home.html" copy "home.html" "%TEMP_DIR%\" >nul

REM إدارة الشحنات
if exist "shipments.html" copy "shipments.html" "%TEMP_DIR%\" >nul
if exist "shipment-tracking.html" copy "shipment-tracking.html" "%TEMP_DIR%\" >nul
if exist "test-print.html" copy "test-print.html" "%TEMP_DIR%\" >nul
if exist "shipping-calculator.html" copy "shipping-calculator.html" "%TEMP_DIR%\" >nul
if exist "pricing-management.html" copy "pricing-management.html" "%TEMP_DIR%\" >nul
if exist "cancellation-management.html" copy "cancellation-management.html" "%TEMP_DIR%\" >nul
if exist "cancellation-reports.html" copy "cancellation-reports.html" "%TEMP_DIR%\" >nul

REM النظام المالي
if exist "financial-system.html" copy "financial-system.html" "%TEMP_DIR%\" >nul
if exist "invoice-management.html" copy "invoice-management.html" "%TEMP_DIR%\" >nul
if exist "payment-management.html" copy "payment-management.html" "%TEMP_DIR%\" >nul
if exist "collection-management.html" copy "collection-management.html" "%TEMP_DIR%\" >nul
if exist "cod-management.html" copy "cod-management.html" "%TEMP_DIR%\" >nul
if exist "commission-management.html" copy "commission-management.html" "%TEMP_DIR%\" >nul
if exist "currency-converter.html" copy "currency-converter.html" "%TEMP_DIR%\" >nul

REM إدارة المستخدمين
if exist "user-management.html" copy "user-management.html" "%TEMP_DIR%\" >nul
if exist "user-permissions-advanced.html" copy "user-permissions-advanced.html" "%TEMP_DIR%\" >nul
if exist "distributors-management.html" copy "distributors-management.html" "%TEMP_DIR%\" >nul
if exist "customer-dashboard.html" copy "customer-dashboard.html" "%TEMP_DIR%\" >nul
if exist "customer-register.html" copy "customer-register.html" "%TEMP_DIR%\" >nul
if exist "customers.html" copy "customers.html" "%TEMP_DIR%\" >nul
if exist "permissions-matrix.html" copy "permissions-matrix.html" "%TEMP_DIR%\" >nul

REM إدارة الفروع
if exist "branches-management.html" copy "branches-management.html" "%TEMP_DIR%\" >nul
if exist "branch-transfers.html" copy "branch-transfers.html" "%TEMP_DIR%\" >nul

REM التقارير
if exist "reports.html" copy "reports.html" "%TEMP_DIR%\" >nul
if exist "advanced-reports.html" copy "advanced-reports.html" "%TEMP_DIR%\" >nul
if exist "dashboard-3d.html" copy "dashboard-3d.html" "%TEMP_DIR%\" >nul

REM إدارة المحتوى
if exist "pages-management.html" copy "pages-management.html" "%TEMP_DIR%\" >nul
if exist "visual-page-editor.html" copy "visual-page-editor.html" "%TEMP_DIR%\" >nul
if exist "advanced-visual-editor.html" copy "advanced-visual-editor.html" "%TEMP_DIR%\" >nul
if exist "about-us.html" copy "about-us.html" "%TEMP_DIR%\" >nul
if exist "about-us-editor.html" copy "about-us-editor.html" "%TEMP_DIR%\" >nul
if exist "success-partners.html" copy "success-partners.html" "%TEMP_DIR%\" >nul
if exist "partners-editor.html" copy "partners-editor.html" "%TEMP_DIR%\" >nul

REM الإعدادات والأدوات
if exist "settings.html" copy "settings.html" "%TEMP_DIR%\" >nul
if exist "system-check.html" copy "system-check.html" "%TEMP_DIR%\" >nul
if exist "test-database.html" copy "test-database.html" "%TEMP_DIR%\" >nul
if exist "test-social-login.html" copy "test-social-login.html" "%TEMP_DIR%\" >nul
if exist "page-diagnostics.html" copy "page-diagnostics.html" "%TEMP_DIR%\" >nul

echo ✅ تم نسخ الملفات الأساسية

REM نسخ المجلدات التقنية
echo.
echo 📁 نسخ المجلدات التقنية...

if exist "js" (
    xcopy "js" "%TEMP_DIR%\js\" /E /I /Q >nul
    echo ✅ تم نسخ مجلد js/
)

if exist "css" (
    xcopy "css" "%TEMP_DIR%\css\" /E /I /Q >nul
    echo ✅ تم نسخ مجلد css/
)

if exist "shared" (
    xcopy "shared" "%TEMP_DIR%\shared\" /E /I /Q >nul
    echo ✅ تم نسخ مجلد shared/
)

REM نسخ ملفات التوثيق الجديدة
echo.
echo 📚 نسخ ملفات التوثيق الجديدة...

if exist "README_COMPLETE.md" copy "README_COMPLETE.md" "%TEMP_DIR%\" >nul
if exist "QUICK_SETUP_GUIDE.md" copy "QUICK_SETUP_GUIDE.md" "%TEMP_DIR%\" >nul
if exist "FILES_LIST.md" copy "FILES_LIST.md" "%TEMP_DIR%\" >nul
if exist "DATABASE_ERROR_FIX.md" copy "DATABASE_ERROR_FIX.md" "%TEMP_DIR%\" >nul
if exist "SYSTEM_FIXES_COMPLETE.md" copy "SYSTEM_FIXES_COMPLETE.md" "%TEMP_DIR%\" >nul
if exist "SOCIAL_LOGIN_README.md" copy "SOCIAL_LOGIN_README.md" "%TEMP_DIR%\" >nul
if exist "اقرأني_أولاً.txt" copy "اقرأني_أولاً.txt" "%TEMP_DIR%\" >nul
if exist "تعليمات_إنشاء_الملف_المضغوط.txt" copy "تعليمات_إنشاء_الملف_المضغوط.txt" "%TEMP_DIR%\" >nul

echo ✅ تم نسخ ملفات التوثيق الجديدة

REM نسخ ملفات التوثيق الأساسية
echo.
echo 📖 نسخ ملفات التوثيق الأساسية...

if exist "README.md" copy "README.md" "%TEMP_DIR%\" >nul
if exist "QUICK_START.md" copy "QUICK_START.md" "%TEMP_DIR%\" >nul
if exist "TROUBLESHOOTING.md" copy "TROUBLESHOOTING.md" "%TEMP_DIR%\" >nul

REM نسخ أدلة الإصلاحات
if exist "DATABASE_FIX_README.md" copy "DATABASE_FIX_README.md" "%TEMP_DIR%\" >nul
if exist "DASHBOARD_FIX_README.md" copy "DASHBOARD_FIX_README.md" "%TEMP_DIR%\" >nul
if exist "COMPLETE_FIX_GUIDE.md" copy "COMPLETE_FIX_GUIDE.md" "%TEMP_DIR%\" >nul

REM نسخ أدلة الميزات
if exist "USER_MANAGEMENT_README.md" copy "USER_MANAGEMENT_README.md" "%TEMP_DIR%\" >nul
if exist "PAGES_MANAGEMENT_README.md" copy "PAGES_MANAGEMENT_README.md" "%TEMP_DIR%\" >nul
if exist "VISUAL_EDITOR_README.md" copy "VISUAL_EDITOR_README.md" "%TEMP_DIR%\" >nul
if exist "ADVANCED_PERMISSIONS_README.md" copy "ADVANCED_PERMISSIONS_README.md" "%TEMP_DIR%\" >nul

echo ✅ تم نسخ ملفات التوثيق الأساسية

REM نسخ ملفات التشغيل
echo.
echo 🔧 نسخ ملفات التشغيل...

if exist "start.bat" copy "start.bat" "%TEMP_DIR%\" >nul
if exist "start.sh" copy "start.sh" "%TEMP_DIR%\" >nul
if exist "setup.bat" copy "setup.bat" "%TEMP_DIR%\" >nul
if exist "package.json" copy "package.json" "%TEMP_DIR%\" >nul
if exist "تشغيل البرنامج.txt" copy "تشغيل البرنامج.txt" "%TEMP_DIR%\" >nul

echo ✅ تم نسخ ملفات التشغيل

REM إنشاء الملف المضغوط
echo.
echo 📦 إنشاء الملف المضغوط...

REM استخدام PowerShell لإنشاء الملف المضغوط
powershell -command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%ZIP_NAME%.zip' -Force"

if exist "%ZIP_NAME%.zip" (
    echo.
    echo ✅ تم إنشاء الملف المضغوط بنجاح!
    echo 📦 اسم الملف: %ZIP_NAME%.zip
    
    REM حساب حجم الملف
    for %%A in ("%ZIP_NAME%.zip") do (
        set "size=%%~zA"
        set /a "sizeMB=!size!/1024/1024"
        echo 📊 حجم الملف: !sizeMB! ميجابايت
    )
    
    echo.
    echo 🎉 تم الانتهاء بنجاح!
    echo 📁 الملف المضغوط جاهز في: %CD%\%ZIP_NAME%.zip
    
) else (
    echo.
    echo ❌ فشل في إنشاء الملف المضغوط
    echo 💡 تأكد من وجود PowerShell على النظام
)

REM تنظيف المجلد المؤقت
echo.
echo 🧹 تنظيف الملفات المؤقتة...
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
echo ✅ تم التنظيف

echo.
echo 📋 ملخص العملية:
echo ==================
echo ✅ تم نسخ أكثر من 60 ملف HTML
echo ✅ تم نسخ المجلدات التقنية (js, css, shared)
echo ✅ تم نسخ جميع ملفات التوثيق
echo ✅ تم نسخ ملفات التشغيل
echo ✅ تم إنشاء الملف المضغوط

echo.
echo 🚀 النظام جاهز للتوزيع والاستخدام!
echo.
pause
