<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المحرر البصري للصفحات | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
        }

        .editor-container {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            height: 100vh;
        }

        .sidebar {
            background: white;
            border-right: 1px solid #e1e5e9;
            overflow-y: auto;
        }

        .sidebar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-content {
            padding: 20px;
        }

        .section-list {
            list-style: none;
        }

        .section-item {
            background: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s;
            cursor: pointer;
        }

        .section-item:hover {
            border-color: #667eea;
        }

        .section-item.active {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .section-header {
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .section-info h3 {
            font-size: 1rem;
            color: #333;
            margin-bottom: 3px;
        }

        .section-info p {
            font-size: 0.85rem;
            color: #666;
        }

        .main-editor {
            background: white;
            overflow-y: auto;
            position: relative;
        }

        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #e1e5e9;
            padding: 15px 20px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .toolbar-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .toolbar-btn:hover {
            background: #5a6fd8;
        }

        .toolbar-btn.secondary {
            background: #6c757d;
        }

        .toolbar-btn.success {
            background: #28a745;
        }

        .toolbar-btn.danger {
            background: #dc3545;
        }

        .editor-content {
            padding: 20px;
            min-height: calc(100vh - 80px);
        }

        .preview-frame {
            width: 100%;
            height: calc(100vh - 80px);
            border: none;
            background: white;
        }

        .properties-panel {
            background: white;
            border-left: 1px solid #e1e5e9;
            overflow-y: auto;
        }

        .panel-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
        }

        .panel-header h3 {
            color: #333;
            margin-bottom: 5px;
        }

        .panel-content {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 0.95rem;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .color-picker {
            width: 100%;
            height: 40px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            cursor: pointer;
        }

        .btn-group {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.85rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .section-preview {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #f8f9fa;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .section-preview:hover {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .section-preview.active {
            border-color: #667eea;
            background: #e3f2fd;
            border-style: solid;
        }

        .preview-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #667eea;
        }

        .preview-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .preview-description {
            color: #666;
            font-size: 0.9rem;
        }

        .back-link {
            display: inline-block;
            margin: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            transform: translateY(-100px);
            transition: transform 0.3s;
            z-index: 1000;
        }

        .save-indicator.show {
            transform: translateY(0);
        }

        @media (max-width: 1024px) {
            .editor-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr auto;
            }
            
            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e1e5e9;
            }
            
            .properties-panel {
                border-left: none;
                border-top: 1px solid #e1e5e9;
            }
        }
    </style>
</head>
<body>
    <div class="save-indicator" id="saveIndicator">
        ✅ تم الحفظ تلقائياً
    </div>

    <a href="pages-management.html" class="back-link">← العودة لإدارة الصفحات</a>

    <div class="editor-container">
        <!-- الشريط الجانبي - أقسام الصفحة -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🎨 المحرر البصري</h2>
                <p>تحرير الصفحة الرئيسية</p>
            </div>
            <div class="sidebar-content">
                <ul class="section-list" id="sectionList">
                    <li class="section-item active" data-section="hero">
                        <div class="section-header">
                            <div class="section-icon">🏠</div>
                            <div class="section-info">
                                <h3>القسم الرئيسي</h3>
                                <p>العنوان والوصف الرئيسي</p>
                            </div>
                        </div>
                    </li>
                    <li class="section-item" data-section="tracking">
                        <div class="section-header">
                            <div class="section-icon">📍</div>
                            <div class="section-info">
                                <h3>تتبع الشحنات</h3>
                                <p>قسم تتبع الطلبات</p>
                            </div>
                        </div>
                    </li>
                    <li class="section-item" data-section="services">
                        <div class="section-header">
                            <div class="section-icon">⚙️</div>
                            <div class="section-info">
                                <h3>الخدمات</h3>
                                <p>خدمات الشركة المتنوعة</p>
                            </div>
                        </div>
                    </li>
                    <li class="section-item" data-section="stats">
                        <div class="section-header">
                            <div class="section-icon">📊</div>
                            <div class="section-info">
                                <h3>الإحصائيات</h3>
                                <p>أرقام ومؤشرات الأداء</p>
                            </div>
                        </div>
                    </li>
                    <li class="section-item" data-section="footer">
                        <div class="section-header">
                            <div class="section-icon">📄</div>
                            <div class="section-info">
                                <h3>التذييل</h3>
                                <p>معلومات التواصل والروابط</p>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <!-- المحرر الرئيسي -->
        <div class="main-editor">
            <div class="editor-toolbar">
                <button class="toolbar-btn" onclick="saveChanges()">💾 حفظ</button>
                <button class="toolbar-btn secondary" onclick="previewPage()">👁️ معاينة</button>
                <button class="toolbar-btn success" onclick="publishPage()">🚀 نشر</button>
                <button class="toolbar-btn danger" onclick="resetChanges()">🔄 إعادة تعيين</button>
                <span style="margin-left: 20px; color: #666;">آخر حفظ: <span id="lastSaved">الآن</span></span>
            </div>
            <div class="editor-content" id="editorContent">
                <!-- سيتم تحميل محتوى القسم المحدد هنا -->
                <div class="section-preview active" data-section="hero">
                    <div class="preview-icon">🏠</div>
                    <div class="preview-title">القسم الرئيسي</div>
                    <div class="preview-description">انقر لتحرير العنوان والوصف الرئيسي للصفحة</div>
                </div>
            </div>
        </div>

        <!-- لوحة الخصائص -->
        <div class="properties-panel">
            <div class="panel-header">
                <h3 id="panelTitle">خصائص القسم الرئيسي</h3>
                <p id="panelDescription">تحرير محتوى وتصميم القسم</p>
            </div>
            <div class="panel-content" id="panelContent">
                <!-- سيتم تحميل خصائص القسم المحدد هنا -->
            </div>
        </div>
    </div>

    <script>
        // بيانات الصفحة الرئيسية
        let pageData = {
            hero: {
                title: "شحن سريع وآمن في جميع أنحاء المملكة",
                description: "نوفر خدمات الشحن والتوصيل السريع مع ضمان الأمان والجودة. شريكك الموثوق في عالم الشحن والخدمات اللوجستية",
                primaryButton: "اكتشف خدماتنا",
                secondaryButton: "تتبع شحنتك",
                backgroundImage: "",
                backgroundColor: "#667eea"
            },
            tracking: {
                title: "تتبع شحنتك",
                description: "أدخل رقم الشحنة لمعرفة حالة طلبك",
                placeholder: "أدخل رقم الشحنة",
                buttonText: "تتبع الآن"
            },
            services: {
                title: "خدماتنا المتميزة",
                description: "نقدم مجموعة شاملة من خدمات الشحن والتوصيل لتلبية جميع احتياجاتكم",
                items: [
                    {
                        icon: "🚚",
                        title: "الشحن السريع",
                        description: "خدمة توصيل سريعة خلال 24-48 ساعة داخل المملكة مع ضمان الأمان والجودة"
                    },
                    {
                        icon: "🌍",
                        title: "الشحن الدولي",
                        description: "خدمات شحن دولية موثوقة إلى جميع أنحاء العالم مع تتبع مباشر"
                    },
                    {
                        icon: "📦",
                        title: "التغليف الآمن",
                        description: "خدمات تغليف احترافية لضمان وصول شحناتكم بأمان تام"
                    },
                    {
                        icon: "💰",
                        title: "الدفع عند الاستلام",
                        description: "خدمة الدفع عند الاستلام لراحة وثقة أكبر في التعامل"
                    },
                    {
                        icon: "📱",
                        title: "تتبع مباشر",
                        description: "تتبع شحناتكم لحظة بلحظة عبر تطبيقنا المتطور"
                    },
                    {
                        icon: "🛒",
                        title: "حلول التجارة الإلكترونية",
                        description: "حلول شحن متكاملة لمتاجر التجارة الإلكترونية"
                    }
                ]
            },
            stats: {
                items: [
                    { number: "50,000+", label: "شحنة شهرياً" },
                    { number: "99.5%", label: "معدل النجاح" },
                    { number: "24/7", label: "خدمة العملاء" },
                    { number: "15+", label: "مدينة نخدمها" }
                ]
            },
            footer: {
                companyName: "شركة الشحن السريع",
                description: "شريكك الموثوق في عالم الشحن والخدمات اللوجستية. نقدم خدمات متميزة منذ أكثر من 10 سنوات.",
                phone: "+966 11 123 4567",
                email: "<EMAIL>",
                address: "الرياض، المملكة العربية السعودية"
            }
        };

        let currentSection = 'hero';

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadSection('hero');
            setupEventListeners();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // النقر على أقسام الشريط الجانبي
            document.querySelectorAll('.section-item').forEach(item => {
                item.addEventListener('click', function() {
                    const section = this.dataset.section;
                    loadSection(section);
                });
            });

            // الحفظ التلقائي
            setInterval(autoSave, 30000); // كل 30 ثانية
        }

        // تحميل قسم معين
        function loadSection(sectionName) {
            currentSection = sectionName;

            // تحديث الشريط الجانبي
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

            // تحديث لوحة الخصائص
            updatePropertiesPanel(sectionName);

            // تحديث المحرر الرئيسي
            updateMainEditor(sectionName);
        }

        // تحديث لوحة الخصائص
        function updatePropertiesPanel(sectionName) {
            const panelTitle = document.getElementById('panelTitle');
            const panelDescription = document.getElementById('panelDescription');
            const panelContent = document.getElementById('panelContent');

            const sectionTitles = {
                hero: 'خصائص القسم الرئيسي',
                tracking: 'خصائص قسم التتبع',
                services: 'خصائص قسم الخدمات',
                stats: 'خصائص قسم الإحصائيات',
                footer: 'خصائص التذييل'
            };

            panelTitle.textContent = sectionTitles[sectionName];
            panelDescription.textContent = 'تحرير محتوى وتصميم القسم';

            // تحميل نموذج التحرير للقسم
            panelContent.innerHTML = generateSectionForm(sectionName);
        }

        // إنشاء نموذج التحرير للقسم
        function generateSectionForm(sectionName) {
            const data = pageData[sectionName];

            switch(sectionName) {
                case 'hero':
                    return `
                        <div class="form-group">
                            <label>العنوان الرئيسي</label>
                            <input type="text" class="form-control" value="${data.title}" onchange="updateData('hero', 'title', this.value)">
                        </div>
                        <div class="form-group">
                            <label>الوصف</label>
                            <textarea class="form-control" rows="4" onchange="updateData('hero', 'description', this.value)">${data.description}</textarea>
                        </div>
                        <div class="form-group">
                            <label>النص الأول للزر</label>
                            <input type="text" class="form-control" value="${data.primaryButton}" onchange="updateData('hero', 'primaryButton', this.value)">
                        </div>
                        <div class="form-group">
                            <label>النص الثاني للزر</label>
                            <input type="text" class="form-control" value="${data.secondaryButton}" onchange="updateData('hero', 'secondaryButton', this.value)">
                        </div>
                        <div class="form-group">
                            <label>لون الخلفية</label>
                            <input type="color" class="color-picker" value="${data.backgroundColor}" onchange="updateData('hero', 'backgroundColor', this.value)">
                        </div>
                        <div class="btn-group">
                            <button class="btn-small btn-primary" onclick="previewSection('hero')">معاينة</button>
                            <button class="btn-small btn-success" onclick="saveSection('hero')">حفظ</button>
                        </div>
                    `;

                case 'tracking':
                    return `
                        <div class="form-group">
                            <label>عنوان القسم</label>
                            <input type="text" class="form-control" value="${data.title}" onchange="updateData('tracking', 'title', this.value)">
                        </div>
                        <div class="form-group">
                            <label>الوصف</label>
                            <textarea class="form-control" rows="3" onchange="updateData('tracking', 'description', this.value)">${data.description}</textarea>
                        </div>
                        <div class="form-group">
                            <label>نص المربع النصي</label>
                            <input type="text" class="form-control" value="${data.placeholder}" onchange="updateData('tracking', 'placeholder', this.value)">
                        </div>
                        <div class="form-group">
                            <label>نص الزر</label>
                            <input type="text" class="form-control" value="${data.buttonText}" onchange="updateData('tracking', 'buttonText', this.value)">
                        </div>
                        <div class="btn-group">
                            <button class="btn-small btn-primary" onclick="previewSection('tracking')">معاينة</button>
                            <button class="btn-small btn-success" onclick="saveSection('tracking')">حفظ</button>
                        </div>
                    `;

                case 'services':
                    return `
                        <div class="form-group">
                            <label>عنوان القسم</label>
                            <input type="text" class="form-control" value="${data.title}" onchange="updateData('services', 'title', this.value)">
                        </div>
                        <div class="form-group">
                            <label>الوصف</label>
                            <textarea class="form-control" rows="3" onchange="updateData('services', 'description', this.value)">${data.description}</textarea>
                        </div>
                        <div class="form-group">
                            <label>الخدمات</label>
                            <div id="servicesList">
                                ${data.items.map((item, index) => `
                                    <div style="background: #f8f9fa; padding: 15px; margin-bottom: 10px; border-radius: 8px;">
                                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                            <input type="text" placeholder="الأيقونة" value="${item.icon}" style="width: 60px;" onchange="updateServiceItem(${index}, 'icon', this.value)">
                                            <input type="text" placeholder="العنوان" value="${item.title}" style="flex: 1;" onchange="updateServiceItem(${index}, 'title', this.value)">
                                        </div>
                                        <textarea placeholder="الوصف" rows="2" style="width: 100%;" onchange="updateServiceItem(${index}, 'description', this.value)">${item.description}</textarea>
                                        <button class="btn-small btn-danger" onclick="removeServiceItem(${index})" style="margin-top: 5px;">حذف</button>
                                    </div>
                                `).join('')}
                            </div>
                            <button class="btn-small btn-success" onclick="addServiceItem()">إضافة خدمة</button>
                        </div>
                        <div class="btn-group">
                            <button class="btn-small btn-primary" onclick="previewSection('services')">معاينة</button>
                            <button class="btn-small btn-success" onclick="saveSection('services')">حفظ</button>
                        </div>
                    `;

                case 'stats':
                    return `
                        <div class="form-group">
                            <label>الإحصائيات</label>
                            <div id="statsList">
                                ${data.items.map((item, index) => `
                                    <div style="background: #f8f9fa; padding: 15px; margin-bottom: 10px; border-radius: 8px;">
                                        <div style="display: flex; gap: 10px;">
                                            <input type="text" placeholder="الرقم" value="${item.number}" style="flex: 1;" onchange="updateStatItem(${index}, 'number', this.value)">
                                            <input type="text" placeholder="التسمية" value="${item.label}" style="flex: 1;" onchange="updateStatItem(${index}, 'label', this.value)">
                                        </div>
                                        <button class="btn-small btn-danger" onclick="removeStatItem(${index})" style="margin-top: 5px;">حذف</button>
                                    </div>
                                `).join('')}
                            </div>
                            <button class="btn-small btn-success" onclick="addStatItem()">إضافة إحصائية</button>
                        </div>
                        <div class="btn-group">
                            <button class="btn-small btn-primary" onclick="previewSection('stats')">معاينة</button>
                            <button class="btn-small btn-success" onclick="saveSection('stats')">حفظ</button>
                        </div>
                    `;

                case 'footer':
                    return `
                        <div class="form-group">
                            <label>اسم الشركة</label>
                            <input type="text" class="form-control" value="${data.companyName}" onchange="updateData('footer', 'companyName', this.value)">
                        </div>
                        <div class="form-group">
                            <label>وصف الشركة</label>
                            <textarea class="form-control" rows="3" onchange="updateData('footer', 'description', this.value)">${data.description}</textarea>
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="text" class="form-control" value="${data.phone}" onchange="updateData('footer', 'phone', this.value)">
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني</label>
                            <input type="email" class="form-control" value="${data.email}" onchange="updateData('footer', 'email', this.value)">
                        </div>
                        <div class="form-group">
                            <label>العنوان</label>
                            <input type="text" class="form-control" value="${data.address}" onchange="updateData('footer', 'address', this.value)">
                        </div>
                        <div class="btn-group">
                            <button class="btn-small btn-primary" onclick="previewSection('footer')">معاينة</button>
                            <button class="btn-small btn-success" onclick="saveSection('footer')">حفظ</button>
                        </div>
                    `;

                default:
                    return '<p>اختر قسماً للتحرير</p>';
            }
        }

        // تحديث المحرر الرئيسي
        function updateMainEditor(sectionName) {
            const editorContent = document.getElementById('editorContent');
            const data = pageData[sectionName];

            const sectionPreviews = {
                hero: `
                    <div class="section-preview active" style="background: ${data.backgroundColor}; color: white; min-height: 200px; display: flex; flex-direction: column; justify-content: center;">
                        <div class="preview-icon" style="color: white;">🏠</div>
                        <div class="preview-title" style="color: white; font-size: 1.5rem;">${data.title}</div>
                        <div class="preview-description" style="color: rgba(255,255,255,0.9); margin: 10px 0;">${data.description}</div>
                        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 15px;">
                            <button style="background: white; color: ${data.backgroundColor}; border: none; padding: 10px 20px; border-radius: 25px;">${data.primaryButton}</button>
                            <button style="background: transparent; color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px;">${data.secondaryButton}</button>
                        </div>
                    </div>
                `,
                tracking: `
                    <div class="section-preview active">
                        <div class="preview-icon">📍</div>
                        <div class="preview-title">${data.title}</div>
                        <div class="preview-description">${data.description}</div>
                        <div style="margin-top: 15px; display: flex; gap: 10px; justify-content: center;">
                            <input type="text" placeholder="${data.placeholder}" style="padding: 10px; border: 2px solid #ddd; border-radius: 5px; width: 200px;">
                            <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px;">${data.buttonText}</button>
                        </div>
                    </div>
                `,
                services: `
                    <div class="section-preview active">
                        <div class="preview-icon">⚙️</div>
                        <div class="preview-title">${data.title}</div>
                        <div class="preview-description">${data.description}</div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-top: 15px;">
                            ${data.items.slice(0, 6).map(item => `
                                <div style="background: #f8f9fa; padding: 10px; border-radius: 8px; text-align: center;">
                                    <div style="font-size: 1.5rem; margin-bottom: 5px;">${item.icon}</div>
                                    <div style="font-weight: 600; font-size: 0.9rem; margin-bottom: 3px;">${item.title}</div>
                                    <div style="font-size: 0.8rem; color: #666;">${item.description.substring(0, 50)}...</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `,
                stats: `
                    <div class="section-preview active" style="background: #f8f9fa;">
                        <div class="preview-icon">📊</div>
                        <div class="preview-title">الإحصائيات</div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-top: 15px;">
                            ${data.items.map(item => `
                                <div style="text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: bold; color: #667eea;">${item.number}</div>
                                    <div style="font-size: 0.9rem; color: #666;">${item.label}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `,
                footer: `
                    <div class="section-preview active" style="background: #333; color: white;">
                        <div class="preview-icon" style="color: white;">📄</div>
                        <div class="preview-title" style="color: white;">${data.companyName}</div>
                        <div class="preview-description" style="color: rgba(255,255,255,0.8);">${data.description}</div>
                        <div style="margin-top: 15px; font-size: 0.9rem; color: rgba(255,255,255,0.7);">
                            <div>📞 ${data.phone}</div>
                            <div>✉️ ${data.email}</div>
                            <div>📍 ${data.address}</div>
                        </div>
                    </div>
                `
            };

            editorContent.innerHTML = sectionPreviews[sectionName] || '<p>لا يوجد معاينة متاحة</p>';
        }

        // تحديث البيانات
        function updateData(section, field, value) {
            pageData[section][field] = value;
            updateMainEditor(section);
            showSaveIndicator();
        }

        // تحديث عنصر خدمة
        function updateServiceItem(index, field, value) {
            pageData.services.items[index][field] = value;
            updateMainEditor('services');
            showSaveIndicator();
        }

        // إضافة خدمة جديدة
        function addServiceItem() {
            pageData.services.items.push({
                icon: "🔧",
                title: "خدمة جديدة",
                description: "وصف الخدمة الجديدة"
            });
            updatePropertiesPanel('services');
            updateMainEditor('services');
            showSaveIndicator();
        }

        // حذف خدمة
        function removeServiceItem(index) {
            if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
                pageData.services.items.splice(index, 1);
                updatePropertiesPanel('services');
                updateMainEditor('services');
                showSaveIndicator();
            }
        }

        // تحديث عنصر إحصائية
        function updateStatItem(index, field, value) {
            pageData.stats.items[index][field] = value;
            updateMainEditor('stats');
            showSaveIndicator();
        }

        // إضافة إحصائية جديدة
        function addStatItem() {
            pageData.stats.items.push({
                number: "0",
                label: "إحصائية جديدة"
            });
            updatePropertiesPanel('stats');
            updateMainEditor('stats');
            showSaveIndicator();
        }

        // حذف إحصائية
        function removeStatItem(index) {
            if (confirm('هل أنت متأكد من حذف هذه الإحصائية؟')) {
                pageData.stats.items.splice(index, 1);
                updatePropertiesPanel('stats');
                updateMainEditor('stats');
                showSaveIndicator();
            }
        }

        // معاينة قسم
        function previewSection(section) {
            updateMainEditor(section);
            alert(`تم تحديث معاينة قسم: ${section}`);
        }

        // حفظ قسم
        function saveSection(section) {
            localStorage.setItem('pageData', JSON.stringify(pageData));
            showSaveIndicator();
            alert(`تم حفظ قسم: ${section}`);
        }

        // حفظ جميع التغييرات
        function saveChanges() {
            localStorage.setItem('pageData', JSON.stringify(pageData));
            showSaveIndicator();
            updateLastSaved();
            alert('تم حفظ جميع التغييرات بنجاح!');
        }

        // الحفظ التلقائي
        function autoSave() {
            localStorage.setItem('pageData', JSON.stringify(pageData));
            showSaveIndicator();
            updateLastSaved();
        }

        // إظهار مؤشر الحفظ
        function showSaveIndicator() {
            const indicator = document.getElementById('saveIndicator');
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }

        // تحديث وقت آخر حفظ
        function updateLastSaved() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('lastSaved').textContent = timeString;
        }

        // معاينة الصفحة
        function previewPage() {
            // إنشاء نافذة معاينة
            const previewWindow = window.open('', '_blank', 'width=1200,height=800');
            previewWindow.document.write(generatePreviewHTML());
            previewWindow.document.close();
        }

        // إنشاء HTML للمعاينة
        function generatePreviewHTML() {
            return `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>معاينة الصفحة الرئيسية</title>
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body { font-family: 'SF Pro Arabic', Arial, sans-serif; line-height: 1.6; }
                        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
                        .hero { background: ${pageData.hero.backgroundColor}; color: white; padding: 100px 0; text-align: center; }
                        .hero h1 { font-size: 3rem; margin-bottom: 20px; }
                        .hero p { font-size: 1.2rem; margin-bottom: 30px; }
                        .btn { padding: 15px 30px; margin: 0 10px; border-radius: 25px; text-decoration: none; display: inline-block; }
                        .btn-primary { background: white; color: ${pageData.hero.backgroundColor}; }
                        .btn-outline { background: transparent; color: white; border: 2px solid white; }
                        .section { padding: 80px 0; }
                        .section h2 { text-align: center; font-size: 2.5rem; margin-bottom: 20px; }
                        .section p { text-align: center; font-size: 1.1rem; margin-bottom: 50px; color: #666; }
                        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; }
                        .service-card { background: #f8f9fa; padding: 30px; border-radius: 15px; text-align: center; }
                        .service-icon { font-size: 3rem; margin-bottom: 20px; }
                        .stats { background: #f8f9fa; }
                        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px; text-align: center; }
                        .stat-item h3 { font-size: 2.5rem; color: #667eea; margin-bottom: 10px; }
                        .footer { background: #333; color: white; padding: 50px 0; text-align: center; }
                    </style>
                </head>
                <body>
                    <section class="hero">
                        <div class="container">
                            <h1>${pageData.hero.title}</h1>
                            <p>${pageData.hero.description}</p>
                            <a href="#" class="btn btn-primary">${pageData.hero.primaryButton}</a>
                            <a href="#" class="btn btn-outline">${pageData.hero.secondaryButton}</a>
                        </div>
                    </section>

                    <section class="section">
                        <div class="container">
                            <h2>${pageData.tracking.title}</h2>
                            <p>${pageData.tracking.description}</p>
                            <div style="text-align: center;">
                                <input type="text" placeholder="${pageData.tracking.placeholder}" style="padding: 15px; border: 2px solid #ddd; border-radius: 5px; width: 300px; margin-left: 10px;">
                                <button style="background: #667eea; color: white; border: none; padding: 15px 30px; border-radius: 5px;">${pageData.tracking.buttonText}</button>
                            </div>
                        </div>
                    </section>

                    <section class="section">
                        <div class="container">
                            <h2>${pageData.services.title}</h2>
                            <p>${pageData.services.description}</p>
                            <div class="services-grid">
                                ${pageData.services.items.map(item => `
                                    <div class="service-card">
                                        <div class="service-icon">${item.icon}</div>
                                        <h3>${item.title}</h3>
                                        <p>${item.description}</p>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </section>

                    <section class="section stats">
                        <div class="container">
                            <div class="stats-grid">
                                ${pageData.stats.items.map(item => `
                                    <div class="stat-item">
                                        <h3>${item.number}</h3>
                                        <p>${item.label}</p>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </section>

                    <footer class="footer">
                        <div class="container">
                            <h3>${pageData.footer.companyName}</h3>
                            <p>${pageData.footer.description}</p>
                            <div style="margin-top: 20px;">
                                <p>📞 ${pageData.footer.phone} | ✉️ ${pageData.footer.email}</p>
                                <p>📍 ${pageData.footer.address}</p>
                            </div>
                        </div>
                    </footer>
                </body>
                </html>
            `;
        }

        // نشر الصفحة
        function publishPage() {
            if (confirm('هل أنت متأكد من نشر الصفحة؟ سيتم تحديث الصفحة الرئيسية.')) {
                // حفظ البيانات
                localStorage.setItem('pageData', JSON.stringify(pageData));

                // محاكاة النشر
                alert('تم نشر الصفحة بنجاح! ستكون التغييرات مرئية للزوار.');
                showSaveIndicator();
            }
        }

        // إعادة تعيين التغييرات
        function resetChanges() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟ سيتم فقدان جميع التعديلات غير المحفوظة.')) {
                // إعادة تحميل البيانات الافتراضية
                location.reload();
            }
        }

        // تحميل البيانات المحفوظة عند بدء التشغيل
        function loadSavedData() {
            const savedData = localStorage.getItem('pageData');
            if (savedData) {
                pageData = JSON.parse(savedData);
            }
        }

        // تحميل البيانات المحفوظة
        loadSavedData();
    </script>
</body>
</html>
