<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر صفحة من نحن | شركة الشحن السريع</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: #f5f7fa;
            min-height: 100vh;
        }

        .editor-container {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            height: 100vh;
        }

        .sidebar {
            background: white;
            border-right: 1px solid #e1e5e9;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.05);
        }

        .sidebar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .sections-list {
            padding: 20px;
        }

        .section-item {
            background: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 10px;
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s;
            cursor: pointer;
        }

        .section-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .section-item.active {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .section-header {
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
        }

        .main-editor {
            background: white;
            overflow-y: auto;
            position: relative;
        }

        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #e1e5e9;
            padding: 15px 20px;
            display: flex;
            gap: 10px;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .toolbar-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s;
        }

        .toolbar-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .toolbar-btn.success {
            background: #28a745;
        }

        .toolbar-btn.danger {
            background: #dc3545;
        }

        .editor-content {
            padding: 20px;
        }

        .drag-drop-area {
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .drag-drop-area:hover,
        .drag-drop-area.dragover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .elements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .element-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: grab;
            transition: all 0.3s;
        }

        .element-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .element-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #667eea;
        }

        .element-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .element-description {
            font-size: 0.85rem;
            color: #666;
        }

        .properties-panel {
            background: white;
            border-left: 1px solid #e1e5e9;
            overflow-y: auto;
            box-shadow: -2px 0 10px rgba(0,0,0,0.05);
        }

        .panel-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .panel-content {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .color-picker {
            width: 100%;
            height: 45px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
        }

        .btn-group {
            display: flex;
            gap: 8px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 8px 15px;
            font-size: 0.85rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.95);
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
            transition: all 0.3s;
        }

        .back-link:hover {
            background: white;
            transform: translateY(-2px);
        }

        .preview-area {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            min-height: 200px;
        }

        .preview-element {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s;
        }

        .preview-element:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        @media (max-width: 1024px) {
            .editor-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr auto;
            }
            
            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e1e5e9;
                max-height: 300px;
            }
            
            .properties-panel {
                border-left: none;
                border-top: 1px solid #e1e5e9;
                max-height: 400px;
            }
        }
    </style>
</head>
<body>
    <a href="pages-management.html" class="back-link">← العودة لإدارة الصفحات</a>

    <div class="editor-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>📖 محرر صفحة من نحن</h2>
                <p>تحرير بصري مع السحب والإفلات</p>
            </div>
            
            <div class="sections-list" id="sectionsList">
                <div class="section-item active" data-section="hero" onclick="selectSection('hero')">
                    <div class="section-header">
                        <div class="section-icon">📖</div>
                        <div class="section-info">
                            <h3>القسم الرئيسي</h3>
                            <p>عنوان الصفحة والمقدمة</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="story" onclick="selectSection('story')">
                    <div class="section-header">
                        <div class="section-icon">📚</div>
                        <div class="section-info">
                            <h3>قصة الشركة</h3>
                            <p>تاريخ وقصة تأسيس الشركة</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="values" onclick="selectSection('values')">
                    <div class="section-header">
                        <div class="section-icon">⭐</div>
                        <div class="section-info">
                            <h3>القيم والمبادئ</h3>
                            <p>قيم ومبادئ الشركة</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="timeline" onclick="selectSection('timeline')">
                    <div class="section-header">
                        <div class="section-icon">📅</div>
                        <div class="section-info">
                            <h3>الخط الزمني</h3>
                            <p>تطور الشركة عبر السنين</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="team" onclick="selectSection('team')">
                    <div class="section-header">
                        <div class="section-icon">👥</div>
                        <div class="section-info">
                            <h3>فريق العمل</h3>
                            <p>أعضاء الفريق والقيادة</p>
                        </div>
                    </div>
                </div>
                
                <div class="section-item" data-section="achievements" onclick="selectSection('achievements')">
                    <div class="section-header">
                        <div class="section-icon">🏆</div>
                        <div class="section-info">
                            <h3>الإنجازات</h3>
                            <p>إنجازات وجوائز الشركة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحرر الرئيسي -->
        <div class="main-editor">
            <div class="editor-toolbar">
                <button class="toolbar-btn" onclick="saveChanges()">💾 حفظ</button>
                <button class="toolbar-btn" onclick="previewPage()">👁️ معاينة</button>
                <button class="toolbar-btn success" onclick="publishPage()">🚀 نشر</button>
                <button class="toolbar-btn danger" onclick="resetChanges()">🔄 إعادة تعيين</button>
            </div>

            <div class="editor-content">
                <!-- منطقة السحب والإفلات -->
                <div class="drag-drop-area" id="dragDropArea">
                    <div style="font-size: 3rem; margin-bottom: 15px;">📁</div>
                    <div style="font-size: 1.2rem; font-weight: 600; margin-bottom: 10px;">اسحب العناصر هنا لإضافتها</div>
                    <div style="color: #666;">أو انقر لاختيار عنصر من المكتبة</div>
                </div>

                <!-- مكتبة العناصر -->
                <h3 style="margin-bottom: 20px; color: #333;">🧩 مكتبة العناصر المخصصة لصفحة من نحن</h3>
                <div class="elements-grid" id="elementsGrid">
                    <!-- سيتم تحميل العناصر هنا -->
                </div>

                <!-- منطقة المعاينة -->
                <h3 style="margin-bottom: 20px; color: #333;">👁️ معاينة القسم المحدد</h3>
                <div class="preview-area" id="previewArea">
                    <div style="text-align: center; color: #666; padding: 40px 20px;">
                        <div style="font-size: 3rem; margin-bottom: 15px;">📖</div>
                        <p>اختر قسماً من الشريط الجانبي لرؤية المعاينة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة الخصائص -->
        <div class="properties-panel">
            <div class="panel-header">
                <h3 id="panelTitle">خصائص القسم الرئيسي</h3>
                <p id="panelDescription">عنوان الصفحة والمقدمة</p>
            </div>
            <div class="panel-content" id="panelContent">
                <!-- سيتم تحميل خصائص القسم هنا -->
            </div>
        </div>
    </div>

    <script>
        // بيانات العناصر المخصصة لصفحة من نحن
        let aboutElements = [
            { id: 'company-intro', name: 'مقدمة الشركة', icon: '🏢', description: 'نص تعريفي عن الشركة', type: 'content' },
            { id: 'mission-vision', name: 'الرؤية والرسالة', icon: '🎯', description: 'رؤية ورسالة الشركة', type: 'content' },
            { id: 'value-card', name: 'بطاقة قيمة', icon: '⭐', description: 'بطاقة تعرض قيمة من قيم الشركة', type: 'special' },
            { id: 'timeline-event', name: 'حدث زمني', icon: '📅', description: 'حدث في تاريخ الشركة', type: 'special' },
            { id: 'team-member', name: 'عضو فريق', icon: '👤', description: 'بطاقة عضو في الفريق', type: 'special' },
            { id: 'achievement', name: 'إنجاز', icon: '🏆', description: 'إنجاز أو جائزة', type: 'special' },
            { id: 'statistic', name: 'إحصائية', icon: '📊', description: 'رقم أو إحصائية مهمة', type: 'special' },
            { id: 'quote', name: 'اقتباس', icon: '💬', description: 'اقتباس أو شهادة', type: 'content' },
            { id: 'image-gallery', name: 'معرض صور', icon: '🖼️', description: 'مجموعة صور', type: 'media' },
            { id: 'contact-info', name: 'معلومات تواصل', icon: '📞', description: 'معلومات الاتصال', type: 'content' }
        ];

        let currentSection = 'hero';
        let selectedElement = null;

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadElementsLibrary();
            selectSection('hero');
        });

        // اختيار قسم
        function selectSection(sectionId) {
            currentSection = sectionId;

            // تحديث الواجهة
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

            // تحديث لوحة الخصائص
            updatePropertiesPanel(sectionId);

            // تحديث المعاينة
            updatePreview(sectionId);

            console.log(`تم اختيار القسم: ${sectionId}`);
        }

        // تحميل مكتبة العناصر
        function loadElementsLibrary() {
            const elementsGrid = document.getElementById('elementsGrid');
            elementsGrid.innerHTML = '';

            aboutElements.forEach(element => {
                const elementCard = document.createElement('div');
                elementCard.className = 'element-card';
                elementCard.draggable = true;
                elementCard.dataset.elementId = element.id;

                elementCard.innerHTML = `
                    <div class="element-icon">${element.icon}</div>
                    <div class="element-title">${element.name}</div>
                    <div class="element-description">${element.description}</div>
                `;

                elementCard.addEventListener('click', () => selectElement(element));

                elementsGrid.appendChild(elementCard);
            });
        }

        // اختيار عنصر
        function selectElement(element) {
            selectedElement = element;

            // تحديث لوحة الخصائص
            updateElementProperties(element);

            // تحديث الواجهة
            document.querySelectorAll('.element-card').forEach(card => {
                card.style.borderColor = card.dataset.elementId === element.id ? '#667eea' : '#e1e5e9';
            });

            console.log(`تم اختيار العنصر: ${element.name}`);
        }

        // تحديث لوحة الخصائص للقسم
        function updatePropertiesPanel(sectionId) {
            const panelTitle = document.getElementById('panelTitle');
            const panelDescription = document.getElementById('panelDescription');
            const panelContent = document.getElementById('panelContent');

            const sectionData = {
                hero: { name: 'القسم الرئيسي', description: 'عنوان الصفحة والمقدمة' },
                story: { name: 'قصة الشركة', description: 'تاريخ وقصة تأسيس الشركة' },
                values: { name: 'القيم والمبادئ', description: 'قيم ومبادئ الشركة' },
                timeline: { name: 'الخط الزمني', description: 'تطور الشركة عبر السنين' },
                team: { name: 'فريق العمل', description: 'أعضاء الفريق والقيادة' },
                achievements: { name: 'الإنجازات', description: 'إنجازات وجوائز الشركة' }
            };

            const section = sectionData[sectionId];
            panelTitle.textContent = `خصائص ${section.name}`;
            panelDescription.textContent = section.description;

            panelContent.innerHTML = generateSectionProperties(sectionId);
        }

        // تحديث لوحة الخصائص للعنصر
        function updateElementProperties(element) {
            const panelTitle = document.getElementById('panelTitle');
            const panelDescription = document.getElementById('panelDescription');
            const panelContent = document.getElementById('panelContent');

            panelTitle.textContent = `خصائص ${element.name}`;
            panelDescription.textContent = element.description;

            panelContent.innerHTML = generateElementProperties(element);
        }

        // إنشاء خصائص القسم
        function generateSectionProperties(sectionId) {
            const commonProperties = `
                <div class="form-group">
                    <label>عنوان القسم</label>
                    <input type="text" class="form-control" placeholder="أدخل عنوان القسم" value="${getSectionTitle(sectionId)}">
                </div>
                <div class="form-group">
                    <label>وصف القسم</label>
                    <textarea class="form-control" rows="3" placeholder="أدخل وصف القسم">${getSectionDescription(sectionId)}</textarea>
                </div>
                <div class="form-group">
                    <label>لون الخلفية</label>
                    <input type="color" class="color-picker" value="#ffffff">
                </div>
                <div class="form-group">
                    <label>ترتيب العرض</label>
                    <select class="form-control">
                        <option>عمودي</option>
                        <option>أفقي</option>
                        <option>شبكي</option>
                    </select>
                </div>
                <div class="btn-group">
                    <button class="btn-small btn-primary" onclick="applyChanges()">تطبيق</button>
                    <button class="btn-small btn-secondary" onclick="resetSection()">إعادة تعيين</button>
                </div>
            `;

            return commonProperties;
        }

        // إنشاء خصائص العنصر
        function generateElementProperties(element) {
            const baseProperties = `
                <div class="form-group">
                    <label>نوع العنصر</label>
                    <input type="text" class="form-control" value="${element.name}" readonly>
                </div>
            `;

            const specificProperties = {
                'company-intro': `
                    <div class="form-group">
                        <label>نص المقدمة</label>
                        <textarea class="form-control" rows="4" placeholder="أدخل نص المقدمة">نحن شركة رائدة في مجال الشحن والتوصيل...</textarea>
                    </div>
                    <div class="form-group">
                        <label>الكلمات المفتاحية</label>
                        <input type="text" class="form-control" placeholder="شحن، توصيل، سرعة">
                    </div>
                `,
                'mission-vision': `
                    <div class="form-group">
                        <label>الرؤية</label>
                        <textarea class="form-control" rows="3" placeholder="أدخل رؤية الشركة">أن نكون الشركة الرائدة في...</textarea>
                    </div>
                    <div class="form-group">
                        <label>الرسالة</label>
                        <textarea class="form-control" rows="3" placeholder="أدخل رسالة الشركة">نسعى لتقديم خدمات...</textarea>
                    </div>
                `,
                'value-card': `
                    <div class="form-group">
                        <label>اسم القيمة</label>
                        <input type="text" class="form-control" placeholder="مثل: الجودة" value="الجودة">
                    </div>
                    <div class="form-group">
                        <label>وصف القيمة</label>
                        <textarea class="form-control" rows="3" placeholder="وصف القيمة">نلتزم بأعلى معايير الجودة في جميع خدماتنا</textarea>
                    </div>
                    <div class="form-group">
                        <label>أيقونة القيمة</label>
                        <input type="text" class="form-control" placeholder="⭐" value="⭐">
                    </div>
                `,
                'timeline-event': `
                    <div class="form-group">
                        <label>السنة</label>
                        <input type="number" class="form-control" placeholder="2024" value="2024">
                    </div>
                    <div class="form-group">
                        <label>عنوان الحدث</label>
                        <input type="text" class="form-control" placeholder="إنجاز مهم" value="إنجاز مهم">
                    </div>
                    <div class="form-group">
                        <label>وصف الحدث</label>
                        <textarea class="form-control" rows="3" placeholder="وصف الحدث">تفاصيل الحدث المهم في تاريخ الشركة</textarea>
                    </div>
                `,
                'team-member': `
                    <div class="form-group">
                        <label>اسم عضو الفريق</label>
                        <input type="text" class="form-control" placeholder="أحمد محمد" value="أحمد محمد">
                    </div>
                    <div class="form-group">
                        <label>المنصب</label>
                        <input type="text" class="form-control" placeholder="مدير العمليات" value="مدير العمليات">
                    </div>
                    <div class="form-group">
                        <label>نبذة مختصرة</label>
                        <textarea class="form-control" rows="3" placeholder="نبذة عن عضو الفريق">خبرة 10 سنوات في مجال الشحن والتوصيل</textarea>
                    </div>
                    <div class="form-group">
                        <label>صورة شخصية</label>
                        <input type="file" class="form-control" accept="image/*">
                    </div>
                `
            };

            const elementSpecific = specificProperties[element.id] || '';

            return baseProperties + elementSpecific + `
                <div class="btn-group">
                    <button class="btn-small btn-primary" onclick="updateElement()">تحديث</button>
                    <button class="btn-small btn-success" onclick="addToPreview()">إضافة للمعاينة</button>
                    <button class="btn-small btn-danger" onclick="deleteElement()">حذف</button>
                </div>
            `;
        }

        // تحديث المعاينة
        function updatePreview(sectionId) {
            const previewArea = document.getElementById('previewArea');

            const previews = {
                hero: `
                    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px;">
                        <h1 style="font-size: 2.5rem; margin-bottom: 20px;">من نحن</h1>
                        <p style="font-size: 1.2rem; line-height: 1.6;">نحن شركة رائدة في مجال الشحن والتوصيل، نقدم خدمات متميزة منذ أكثر من 10 سنوات</p>
                    </div>
                `,
                story: `
                    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        <h2 style="color: #667eea; margin-bottom: 20px;">📚 قصة الشركة</h2>
                        <p style="line-height: 1.8; color: #333;">بدأت رحلتنا في عام 2010 بحلم بسيط: تقديم خدمات شحن موثوقة وسريعة. اليوم، نحن نخدم آلاف العملاء في جميع أنحاء المملكة.</p>
                    </div>
                `,
                values: `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div style="background: white; padding: 25px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <div style="font-size: 3rem; margin-bottom: 15px;">⭐</div>
                            <h3 style="color: #667eea; margin-bottom: 10px;">الجودة</h3>
                            <p style="color: #666;">نلتزم بأعلى معايير الجودة</p>
                        </div>
                        <div style="background: white; padding: 25px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <div style="font-size: 3rem; margin-bottom: 15px;">⚡</div>
                            <h3 style="color: #667eea; margin-bottom: 10px;">السرعة</h3>
                            <p style="color: #666;">توصيل سريع وفي الوقت المحدد</p>
                        </div>
                    </div>
                `,
                timeline: `
                    <div style="border-left: 4px solid #667eea; padding-left: 30px;">
                        <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <span style="background: #667eea; color: white; padding: 5px 15px; border-radius: 20px; font-size: 0.9rem;">2010</span>
                            <h3 style="color: #333; margin: 15px 0 10px;">البداية</h3>
                            <p style="color: #666;">تأسيس الشركة في الرياض</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <span style="background: #667eea; color: white; padding: 5px 15px; border-radius: 20px; font-size: 0.9rem;">2024</span>
                            <h3 style="color: #333; margin: 15px 0 10px;">التوسع</h3>
                            <p style="color: #666;">خدمة أكثر من 100,000 عميل</p>
                        </div>
                    </div>
                `,
                team: `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="background: white; padding: 25px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <div style="width: 80px; height: 80px; background: #667eea; border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">👤</div>
                            <h3 style="color: #333; margin-bottom: 5px;">أحمد محمد</h3>
                            <p style="color: #667eea; font-weight: 500; margin-bottom: 10px;">المدير التنفيذي</p>
                            <p style="color: #666; font-size: 0.9rem;">خبرة 15 سنة في الإدارة</p>
                        </div>
                    </div>
                `,
                achievements: `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="background: white; padding: 25px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                            <div style="font-size: 3rem; margin-bottom: 15px;">🏆</div>
                            <h3 style="color: #667eea; margin-bottom: 10px;">جائزة التميز</h3>
                            <p style="color: #666;">أفضل شركة شحن 2023</p>
                        </div>
                    </div>
                `
            };

            previewArea.innerHTML = previews[sectionId] || `
                <div style="text-align: center; color: #666; padding: 40px 20px;">
                    <div style="font-size: 3rem; margin-bottom: 15px;">📖</div>
                    <p>معاينة ${sectionId}</p>
                </div>
            `;
        }

        // الحصول على عنوان القسم
        function getSectionTitle(sectionId) {
            const titles = {
                hero: 'من نحن',
                story: 'قصة الشركة',
                values: 'قيمنا ومبادئنا',
                timeline: 'رحلة النجاح',
                team: 'فريق العمل',
                achievements: 'إنجازاتنا'
            };
            return titles[sectionId] || 'عنوان القسم';
        }

        // الحصول على وصف القسم
        function getSectionDescription(sectionId) {
            const descriptions = {
                hero: 'مقدمة تعريفية عن الشركة ومجال عملها',
                story: 'تاريخ تأسيس الشركة وتطورها عبر السنين',
                values: 'القيم والمبادئ التي تحكم عمل الشركة',
                timeline: 'الأحداث المهمة في تاريخ الشركة',
                team: 'أعضاء الفريق والقيادة في الشركة',
                achievements: 'الإنجازات والجوائز التي حققتها الشركة'
            };
            return descriptions[sectionId] || 'وصف القسم';
        }

        // وظائف شريط الأدوات
        function saveChanges() {
            alert('تم حفظ التغييرات بنجاح!');
        }

        function previewPage() {
            window.open('about-us.html', '_blank');
        }

        function publishPage() {
            if (confirm('هل أنت متأكد من نشر صفحة من نحن؟')) {
                alert('تم نشر الصفحة بنجاح!');
            }
        }

        function resetChanges() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
                selectSection('hero');
                alert('تم إعادة تعيين التغييرات');
            }
        }

        // وظائف العناصر
        function applyChanges() {
            alert('تم تطبيق التغييرات على القسم');
        }

        function resetSection() {
            if (confirm('هل تريد إعادة تعيين خصائص هذا القسم؟')) {
                updatePropertiesPanel(currentSection);
                alert('تم إعادة تعيين خصائص القسم');
            }
        }

        function updateElement() {
            alert('تم تحديث العنصر');
        }

        function addToPreview() {
            alert('تم إضافة العنصر للمعاينة');
        }

        function deleteElement() {
            if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                alert('تم حذف العنصر');
            }
        }
    </script>
</body>
</html>
