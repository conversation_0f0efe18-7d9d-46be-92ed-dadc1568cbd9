<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 إدارة التسعير - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header p {
            color: #6c757d;
            font-size: 1.1rem;
        }

        .tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            gap: 10px;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            color: #6c757d;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .pricing-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .card-title {
            font-size: 1.3rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .pricing-details {
            margin: 20px 0;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
        }

        .detail-value {
            color: #2c3e50;
        }

        .card-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .add-btn {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }

        .add-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tabs {
                flex-direction: column;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }

            .back-btn {
                position: static;
                margin-bottom: 20px;
                align-self: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                <span>🏠</span>
                <span>العودة للوحة التحكم</span>
                <span>←</span>
            </a>
            <h1>💰 إدارة التسعير</h1>
            <p>إدارة أسعار الشحنات والخدمات المختلفة</p>
        </div>

        <!-- Tabs -->
        <div class="tabs">
            <button class="tab-button active" onclick="showTab('weight-pricing')">
                📦 التسعير حسب الوزن
            </button>
            <button class="tab-button" onclick="showTab('zone-pricing')">
                🌍 التسعير حسب المنطقة
            </button>
            <button class="tab-button" onclick="showTab('service-pricing')">
                🛎️ تسعير الخدمات
            </button>
            <button class="tab-button" onclick="showTab('special-pricing')">
                ⭐ العروض الخاصة
            </button>
        </div>

        <!-- Weight Pricing Tab -->
        <div id="weight-pricing" class="tab-content active">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">📦 التسعير حسب الوزن</h2>
            <div class="pricing-grid" id="weightPricingGrid">
                <!-- سيتم تحميل البيانات هنا -->
            </div>
        </div>

        <!-- Zone Pricing Tab -->
        <div id="zone-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">🌍 التسعير حسب المنطقة</h2>
            <div class="pricing-grid" id="zonePricingGrid">
                <!-- سيتم تحميل البيانات هنا -->
            </div>
        </div>

        <!-- Service Pricing Tab -->
        <div id="service-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">🛎️ تسعير الخدمات</h2>
            <div class="pricing-grid" id="servicePricingGrid">
                <!-- سيتم تحميل البيانات هنا -->
            </div>
        </div>

        <!-- Special Pricing Tab -->
        <div id="special-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">⭐ العروض الخاصة</h2>
            <div class="pricing-grid" id="specialPricingGrid">
                <!-- سيتم تحميل البيانات هنا -->
            </div>
        </div>
    </div>

    <!-- Add Button -->
    <button class="add-btn" onclick="addPricing()" title="إضافة تسعير جديد">
        ➕
    </button>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // بيانات التسعير
        let pricingData = {
            weightPricing: [
                { id: 1, name: 'مستندات', minWeight: 0, maxWeight: 0.5, basePrice: 10, perKgPrice: 2, active: true },
                { id: 2, name: 'طرد صغير', minWeight: 0.5, maxWeight: 2, basePrice: 15, perKgPrice: 5, active: true },
                { id: 3, name: 'طرد متوسط', minWeight: 2, maxWeight: 10, basePrice: 25, perKgPrice: 8, active: true },
                { id: 4, name: 'طرد كبير', minWeight: 10, maxWeight: 30, basePrice: 50, perKgPrice: 12, active: true },
                { id: 5, name: 'شحنة ثقيلة', minWeight: 30, maxWeight: 100, basePrice: 100, perKgPrice: 15, active: true }
            ],
            zonePricing: [
                { id: 1, name: 'الرياض', type: 'محلي', basePrice: 15, extraPrice: 0, deliveryTime: '1-2 أيام', active: true },
                { id: 2, name: 'جدة', type: 'محلي', basePrice: 18, extraPrice: 3, deliveryTime: '2-3 أيام', active: true },
                { id: 3, name: 'الدمام', type: 'محلي', basePrice: 20, extraPrice: 5, deliveryTime: '2-3 أيام', active: true },
                { id: 4, name: 'مكة المكرمة', type: 'محلي', basePrice: 22, extraPrice: 7, deliveryTime: '2-4 أيام', active: true },
                { id: 5, name: 'المدينة المنورة', type: 'محلي', basePrice: 25, extraPrice: 10, deliveryTime: '3-4 أيام', active: true },
                { id: 6, name: 'الكويت', type: 'دولي', basePrice: 50, extraPrice: 35, deliveryTime: '5-7 أيام', active: true },
                { id: 7, name: 'حولي', type: 'دولي', basePrice: 55, extraPrice: 40, deliveryTime: '5-7 أيام', active: true }
            ],
            servicePricing: [
                { id: 1, name: 'التوصيل العادي', description: 'خدمة التوصيل الاعتيادية', price: 0, type: 'مجاني', active: true },
                { id: 2, name: 'التوصيل السريع', description: 'توصيل في نفس اليوم', price: 20, type: 'إضافي', active: true },
                { id: 3, name: 'التوصيل الطارئ', description: 'توصيل خلال ساعتين', price: 50, type: 'إضافي', active: true },
                { id: 4, name: 'التأمين على الشحنة', description: 'تأمين ضد الفقدان والتلف', price: 15, type: 'اختياري', active: true },
                { id: 5, name: 'التغليف الخاص', description: 'تغليف احترافي للشحنات الحساسة', price: 25, type: 'اختياري', active: true },
                { id: 6, name: 'التتبع المتقدم', description: 'تتبع مباشر مع إشعارات SMS', price: 10, type: 'اختياري', active: true }
            ],
            specialPricing: [
                { id: 1, name: 'عملاء VIP', description: 'خصم للعملاء المميزين', discount: 15, type: 'نسبة مئوية', minOrders: 50, active: true },
                { id: 2, name: 'الشحنات الكبيرة', description: 'خصم للشحنات أكثر من 20 كيلو', discount: 10, type: 'نسبة مئوية', minWeight: 20, active: true },
                { id: 3, name: 'العملاء الجدد', description: 'خصم ترحيبي للعملاء الجدد', discount: 20, type: 'مبلغ ثابت', maxUses: 1, active: true },
                { id: 4, name: 'الطلبات الشهرية', description: 'خصم للعملاء مع أكثر من 10 شحنات شهرياً', discount: 12, type: 'نسبة مئوية', minMonthlyOrders: 10, active: true },
                { id: 5, name: 'المناسبات الخاصة', description: 'خصم في المناسبات والأعياد', discount: 25, type: 'نسبة مئوية', seasonal: true, active: false }
            ]
        };

        let currentTab = 'weight-pricing';

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💰 تحميل صفحة إدارة التسعير...');
            loadPricingData();
            loadAllPricing();
            console.log('✅ تم تحميل الصفحة بنجاح');
        });

        // تحميل البيانات من التخزين المحلي
        function loadPricingData() {
            const savedData = localStorage.getItem('pricing_data');
            if (savedData) {
                try {
                    pricingData = JSON.parse(savedData);
                    console.log('✅ تم تحميل بيانات التسعير من التخزين المحلي');
                } catch (error) {
                    console.warn('⚠️ خطأ في تحميل البيانات المحفوظة، استخدام البيانات الافتراضية');
                }
            }
        }

        // حفظ البيانات في التخزين المحلي
        function savePricingData() {
            localStorage.setItem('pricing_data', JSON.stringify(pricingData));
            console.log('💾 تم حفظ بيانات التسعير');
        }

        // تحميل جميع أنواع التسعير
        function loadAllPricing() {
            loadWeightPricing();
            loadZonePricing();
            loadServicePricing();
            loadSpecialPricing();
        }

        // تحميل التسعير حسب الوزن
        function loadWeightPricing() {
            const grid = document.getElementById('weightPricingGrid');
            grid.innerHTML = '';

            pricingData.weightPricing.forEach(pricing => {
                const card = createWeightPricingCard(pricing);
                grid.appendChild(card);
            });

            console.log(`✅ تم تحميل ${pricingData.weightPricing.length} عنصر تسعير حسب الوزن`);
        }

        // إنشاء بطاقة تسعير حسب الوزن
        function createWeightPricingCard(pricing) {
            const card = document.createElement('div');
            card.className = 'pricing-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <span>📦</span>
                        ${pricing.name}
                    </div>
                    <span class="status-badge ${pricing.active ? 'status-active' : 'status-inactive'}">
                        ${pricing.active ? 'نشط' : 'معطل'}
                    </span>
                </div>
                <div class="pricing-details">
                    <div class="detail-item">
                        <span class="detail-label">نطاق الوزن:</span>
                        <span class="detail-value">${pricing.minWeight} - ${pricing.maxWeight} كيلو</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">السعر الأساسي:</span>
                        <span class="detail-value">${pricing.basePrice} ريال</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">سعر الكيلو:</span>
                        <span class="detail-value">${pricing.perKgPrice} ريال/كيلو</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="editWeightPricing(${pricing.id})">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleWeightPricing(${pricing.id})">
                        <span>${pricing.active ? '❌' : '✅'}</span>
                        ${pricing.active ? 'إيقاف' : 'تفعيل'}
                    </button>
                </div>
            `;
            return card;
        }

        // تحميل التسعير حسب المنطقة
        function loadZonePricing() {
            const grid = document.getElementById('zonePricingGrid');
            grid.innerHTML = '';

            pricingData.zonePricing.forEach(pricing => {
                const card = createZonePricingCard(pricing);
                grid.appendChild(card);
            });

            console.log(`✅ تم تحميل ${pricingData.zonePricing.length} عنصر تسعير حسب المنطقة`);
        }

        // إنشاء بطاقة تسعير حسب المنطقة
        function createZonePricingCard(pricing) {
            const card = document.createElement('div');
            card.className = 'pricing-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <span>🌍</span>
                        ${pricing.name}
                    </div>
                    <span class="status-badge ${pricing.active ? 'status-active' : 'status-inactive'}">
                        ${pricing.active ? 'نشط' : 'معطل'}
                    </span>
                </div>
                <div class="pricing-details">
                    <div class="detail-item">
                        <span class="detail-label">نوع المنطقة:</span>
                        <span class="detail-value">${pricing.type}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">السعر الأساسي:</span>
                        <span class="detail-value">${pricing.basePrice} ريال</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">السعر الإضافي:</span>
                        <span class="detail-value">${pricing.extraPrice} ريال</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">وقت التوصيل:</span>
                        <span class="detail-value">${pricing.deliveryTime}</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="editZonePricing(${pricing.id})">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleZonePricing(${pricing.id})">
                        <span>${pricing.active ? '❌' : '✅'}</span>
                        ${pricing.active ? 'إيقاف' : 'تفعيل'}
                    </button>
                </div>
            `;
            return card;
        }

        // تحميل تسعير الخدمات
        function loadServicePricing() {
            const grid = document.getElementById('servicePricingGrid');
            grid.innerHTML = '';

            pricingData.servicePricing.forEach(pricing => {
                const card = createServicePricingCard(pricing);
                grid.appendChild(card);
            });

            console.log(`✅ تم تحميل ${pricingData.servicePricing.length} عنصر تسعير الخدمات`);
        }

        // إنشاء بطاقة تسعير الخدمات
        function createServicePricingCard(pricing) {
            const card = document.createElement('div');
            card.className = 'pricing-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <span>🛎️</span>
                        ${pricing.name}
                    </div>
                    <span class="status-badge ${pricing.active ? 'status-active' : 'status-inactive'}">
                        ${pricing.active ? 'نشط' : 'معطل'}
                    </span>
                </div>
                <div class="pricing-details">
                    <div class="detail-item">
                        <span class="detail-label">الوصف:</span>
                        <span class="detail-value">${pricing.description}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">السعر:</span>
                        <span class="detail-value">${pricing.price} ريال</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">النوع:</span>
                        <span class="detail-value">${pricing.type}</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="editServicePricing(${pricing.id})">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleServicePricing(${pricing.id})">
                        <span>${pricing.active ? '❌' : '✅'}</span>
                        ${pricing.active ? 'إيقاف' : 'تفعيل'}
                    </button>
                </div>
            `;
            return card;
        }

        // تحميل التسعير الخاص
        function loadSpecialPricing() {
            const grid = document.getElementById('specialPricingGrid');
            grid.innerHTML = '';

            pricingData.specialPricing.forEach(pricing => {
                const card = createSpecialPricingCard(pricing);
                grid.appendChild(card);
            });

            console.log(`✅ تم تحميل ${pricingData.specialPricing.length} عنصر التسعير الخاص`);
        }

        // إنشاء بطاقة التسعير الخاص
        function createSpecialPricingCard(pricing) {
            const card = document.createElement('div');
            card.className = 'pricing-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <span>⭐</span>
                        ${pricing.name}
                    </div>
                    <span class="status-badge ${pricing.active ? 'status-active' : 'status-inactive'}">
                        ${pricing.active ? 'نشط' : 'معطل'}
                    </span>
                </div>
                <div class="pricing-details">
                    <div class="detail-item">
                        <span class="detail-label">الوصف:</span>
                        <span class="detail-value">${pricing.description}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الخصم:</span>
                        <span class="detail-value">${pricing.discount}${pricing.type === 'نسبة مئوية' ? '%' : ' ريال'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">النوع:</span>
                        <span class="detail-value">${pricing.type}</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="editSpecialPricing(${pricing.id})">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleSpecialPricing(${pricing.id})">
                        <span>${pricing.active ? '❌' : '✅'}</span>
                        ${pricing.active ? 'إيقاف' : 'تفعيل'}
                    </button>
                </div>
            `;
            return card;
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            currentTab = tabName;
            console.log(`📋 تم التبديل إلى تبويب: ${tabName}`);
        }

        // وظائف التعديل والتفعيل
        function editWeightPricing(id) {
            alert(`✏️ سيتم فتح نموذج تعديل التسعير حسب الوزن رقم: ${id}`);
        }

        function toggleWeightPricing(id) {
            const item = pricingData.weightPricing.find(p => p.id === id);
            if (item) {
                item.active = !item.active;
                savePricingData();
                loadWeightPricing();
                alert(`✅ تم ${item.active ? 'تفعيل' : 'إيقاف'} التسعير بنجاح!`);
            }
        }

        function editZonePricing(id) {
            alert(`✏️ سيتم فتح نموذج تعديل التسعير حسب المنطقة رقم: ${id}`);
        }

        function toggleZonePricing(id) {
            const item = pricingData.zonePricing.find(p => p.id === id);
            if (item) {
                item.active = !item.active;
                savePricingData();
                loadZonePricing();
                alert(`✅ تم ${item.active ? 'تفعيل' : 'إيقاف'} التسعير بنجاح!`);
            }
        }

        function editServicePricing(id) {
            alert(`✏️ سيتم فتح نموذج تعديل تسعير الخدمة رقم: ${id}`);
        }

        function toggleServicePricing(id) {
            const item = pricingData.servicePricing.find(p => p.id === id);
            if (item) {
                item.active = !item.active;
                savePricingData();
                loadServicePricing();
                alert(`✅ تم ${item.active ? 'تفعيل' : 'إيقاف'} الخدمة بنجاح!`);
            }
        }

        function editSpecialPricing(id) {
            alert(`✏️ سيتم فتح نموذج تعديل العرض الخاص رقم: ${id}`);
        }

        function toggleSpecialPricing(id) {
            const item = pricingData.specialPricing.find(p => p.id === id);
            if (item) {
                item.active = !item.active;
                savePricingData();
                loadSpecialPricing();
                alert(`✅ تم ${item.active ? 'تفعيل' : 'إيقاف'} العرض بنجاح!`);
            }
        }

        function addPricing() {
            const tabNames = {
                'weight-pricing': 'التسعير حسب الوزن',
                'zone-pricing': 'التسعير حسب المنطقة',
                'service-pricing': 'تسعير الخدمات',
                'special-pricing': 'العروض الخاصة'
            };

            alert(`➕ سيتم فتح نموذج إضافة ${tabNames[currentTab] || 'تسعير جديد'}`);
        }

        // جعل الوظائف متاحة عالمياً
        window.showTab = showTab;
        window.editWeightPricing = editWeightPricing;
        window.toggleWeightPricing = toggleWeightPricing;
        window.editZonePricing = editZonePricing;
        window.toggleZonePricing = toggleZonePricing;
        window.editServicePricing = editServicePricing;
        window.toggleServicePricing = toggleServicePricing;
        window.editSpecialPricing = editSpecialPricing;
        window.toggleSpecialPricing = toggleSpecialPricing;
        window.addPricing = addPricing;

        console.log('💰 تم تحميل نظام إدارة التسعير بنجاح!');
    </script>
</body>
</html>
