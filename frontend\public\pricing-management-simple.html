<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 إدارة التسعير - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header p {
            color: #6c757d;
            font-size: 1.1rem;
        }

        .tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            gap: 10px;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            color: #6c757d;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .pricing-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .card-title {
            font-size: 1.3rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .pricing-details {
            margin: 20px 0;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
        }

        .detail-value {
            color: #2c3e50;
        }

        .card-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .add-btn {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }

        .add-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 3% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1001;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
            text-align: right;
            direction: rtl;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tabs {
                flex-direction: column;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }

            .back-btn {
                position: static;
                margin-bottom: 20px;
                align-self: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                <span>🏠</span>
                <span>العودة للوحة التحكم</span>
                <span>←</span>
            </a>
            <h1>💰 إدارة التسعير</h1>
            <p>إدارة أسعار الشحنات والخدمات المختلفة</p>
        </div>

        <!-- Tabs -->
        <div class="tabs">
            <button class="tab-button active" onclick="showTab('weight-pricing')">
                📦 التسعير حسب الوزن
            </button>
            <button class="tab-button" onclick="showTab('zone-pricing')">
                🌍 التسعير حسب المنطقة
            </button>
            <button class="tab-button" onclick="showTab('service-pricing')">
                🛎️ تسعير الخدمات
            </button>
            <button class="tab-button" onclick="showTab('special-pricing')">
                ⭐ العروض الخاصة
            </button>
        </div>

        <!-- Weight Pricing Tab -->
        <div id="weight-pricing" class="tab-content active">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">📦 التسعير حسب الوزن</h2>
            <div class="pricing-grid" id="weightPricingGrid">
                <!-- سيتم تحميل البيانات هنا -->
            </div>
        </div>

        <!-- Zone Pricing Tab -->
        <div id="zone-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">🌍 التسعير حسب المنطقة</h2>
            <div class="pricing-grid" id="zonePricingGrid">
                <!-- سيتم تحميل البيانات هنا -->
            </div>
        </div>

        <!-- Service Pricing Tab -->
        <div id="service-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">🛎️ تسعير الخدمات</h2>
            <div class="pricing-grid" id="servicePricingGrid">
                <!-- سيتم تحميل البيانات هنا -->
            </div>
        </div>

        <!-- Special Pricing Tab -->
        <div id="special-pricing" class="tab-content">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">⭐ العروض الخاصة</h2>
            <div class="pricing-grid" id="specialPricingGrid">
                <!-- سيتم تحميل البيانات هنا -->
            </div>
        </div>
    </div>

    <!-- Add Button -->
    <button class="add-btn" onclick="addPricing()" title="إضافة تسعير جديد">
        ➕
    </button>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // بيانات التسعير
        let pricingData = {
            weightPricing: [
                { id: 1, name: 'مستندات', minWeight: 0, maxWeight: 0.5, basePrice: 10, perKgPrice: 2, active: true },
                { id: 2, name: 'طرد صغير', minWeight: 0.5, maxWeight: 2, basePrice: 15, perKgPrice: 5, active: true },
                { id: 3, name: 'طرد متوسط', minWeight: 2, maxWeight: 10, basePrice: 25, perKgPrice: 8, active: true },
                { id: 4, name: 'طرد كبير', minWeight: 10, maxWeight: 30, basePrice: 50, perKgPrice: 12, active: true },
                { id: 5, name: 'شحنة ثقيلة', minWeight: 30, maxWeight: 100, basePrice: 100, perKgPrice: 15, active: true }
            ],
            zonePricing: [
                { id: 1, name: 'الرياض', type: 'محلي', basePrice: 15, extraPrice: 0, deliveryTime: '1-2 أيام', active: true },
                { id: 2, name: 'جدة', type: 'محلي', basePrice: 18, extraPrice: 3, deliveryTime: '2-3 أيام', active: true },
                { id: 3, name: 'الدمام', type: 'محلي', basePrice: 20, extraPrice: 5, deliveryTime: '2-3 أيام', active: true },
                { id: 4, name: 'مكة المكرمة', type: 'محلي', basePrice: 22, extraPrice: 7, deliveryTime: '2-4 أيام', active: true },
                { id: 5, name: 'المدينة المنورة', type: 'محلي', basePrice: 25, extraPrice: 10, deliveryTime: '3-4 أيام', active: true },
                { id: 6, name: 'الكويت', type: 'دولي', basePrice: 50, extraPrice: 35, deliveryTime: '5-7 أيام', active: true },
                { id: 7, name: 'حولي', type: 'دولي', basePrice: 55, extraPrice: 40, deliveryTime: '5-7 أيام', active: true }
            ],
            servicePricing: [
                { id: 1, name: 'التوصيل العادي', description: 'خدمة التوصيل الاعتيادية', price: 0, type: 'مجاني', active: true },
                { id: 2, name: 'التوصيل السريع', description: 'توصيل في نفس اليوم', price: 20, type: 'إضافي', active: true },
                { id: 3, name: 'التوصيل الطارئ', description: 'توصيل خلال ساعتين', price: 50, type: 'إضافي', active: true },
                { id: 4, name: 'التأمين على الشحنة', description: 'تأمين ضد الفقدان والتلف', price: 15, type: 'اختياري', active: true },
                { id: 5, name: 'التغليف الخاص', description: 'تغليف احترافي للشحنات الحساسة', price: 25, type: 'اختياري', active: true },
                { id: 6, name: 'التتبع المتقدم', description: 'تتبع مباشر مع إشعارات SMS', price: 10, type: 'اختياري', active: true }
            ],
            specialPricing: [
                { id: 1, name: 'عملاء VIP', description: 'خصم للعملاء المميزين', discount: 15, type: 'نسبة مئوية', minOrders: 50, active: true },
                { id: 2, name: 'الشحنات الكبيرة', description: 'خصم للشحنات أكثر من 20 كيلو', discount: 10, type: 'نسبة مئوية', minWeight: 20, active: true },
                { id: 3, name: 'العملاء الجدد', description: 'خصم ترحيبي للعملاء الجدد', discount: 20, type: 'مبلغ ثابت', maxUses: 1, active: true },
                { id: 4, name: 'الطلبات الشهرية', description: 'خصم للعملاء مع أكثر من 10 شحنات شهرياً', discount: 12, type: 'نسبة مئوية', minMonthlyOrders: 10, active: true },
                { id: 5, name: 'المناسبات الخاصة', description: 'خصم في المناسبات والأعياد', discount: 25, type: 'نسبة مئوية', seasonal: true, active: false }
            ]
        };

        let currentTab = 'weight-pricing';

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💰 تحميل صفحة إدارة التسعير...');
            loadPricingData();
            loadAllPricing();
            console.log('✅ تم تحميل الصفحة بنجاح');
        });

        // تحميل البيانات من التخزين المحلي
        function loadPricingData() {
            const savedData = localStorage.getItem('pricing_data');
            if (savedData) {
                try {
                    pricingData = JSON.parse(savedData);
                    console.log('✅ تم تحميل بيانات التسعير من التخزين المحلي');
                } catch (error) {
                    console.warn('⚠️ خطأ في تحميل البيانات المحفوظة، استخدام البيانات الافتراضية');
                }
            }
        }

        // حفظ البيانات في التخزين المحلي
        function savePricingData() {
            localStorage.setItem('pricing_data', JSON.stringify(pricingData));
            console.log('💾 تم حفظ بيانات التسعير');
        }

        // تحميل جميع أنواع التسعير
        function loadAllPricing() {
            loadWeightPricing();
            loadZonePricing();
            loadServicePricing();
            loadSpecialPricing();
        }

        // تحميل التسعير حسب الوزن
        function loadWeightPricing() {
            const grid = document.getElementById('weightPricingGrid');
            grid.innerHTML = '';

            pricingData.weightPricing.forEach(pricing => {
                const card = createWeightPricingCard(pricing);
                grid.appendChild(card);
            });

            console.log(`✅ تم تحميل ${pricingData.weightPricing.length} عنصر تسعير حسب الوزن`);
        }

        // إنشاء بطاقة تسعير حسب الوزن
        function createWeightPricingCard(pricing) {
            const card = document.createElement('div');
            card.className = 'pricing-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <span>📦</span>
                        ${pricing.name}
                    </div>
                    <span class="status-badge ${pricing.active ? 'status-active' : 'status-inactive'}">
                        ${pricing.active ? 'نشط' : 'معطل'}
                    </span>
                </div>
                <div class="pricing-details">
                    <div class="detail-item">
                        <span class="detail-label">نطاق الوزن:</span>
                        <span class="detail-value">${pricing.minWeight} - ${pricing.maxWeight} كيلو</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">السعر الأساسي:</span>
                        <span class="detail-value">${pricing.basePrice} ريال</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">سعر الكيلو:</span>
                        <span class="detail-value">${pricing.perKgPrice} ريال/كيلو</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="editWeightPricing(${pricing.id})">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleWeightPricing(${pricing.id})">
                        <span>${pricing.active ? '❌' : '✅'}</span>
                        ${pricing.active ? 'إيقاف' : 'تفعيل'}
                    </button>
                    <button class="btn btn-delete" onclick="deleteWeightPricing(${pricing.id})">
                        <span>🗑️</span> حذف
                    </button>
                </div>
            `;
            return card;
        }

        // تحميل التسعير حسب المنطقة
        function loadZonePricing() {
            const grid = document.getElementById('zonePricingGrid');
            grid.innerHTML = '';

            pricingData.zonePricing.forEach(pricing => {
                const card = createZonePricingCard(pricing);
                grid.appendChild(card);
            });

            console.log(`✅ تم تحميل ${pricingData.zonePricing.length} عنصر تسعير حسب المنطقة`);
        }

        // إنشاء بطاقة تسعير حسب المنطقة
        function createZonePricingCard(pricing) {
            const card = document.createElement('div');
            card.className = 'pricing-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <span>🌍</span>
                        ${pricing.name}
                    </div>
                    <span class="status-badge ${pricing.active ? 'status-active' : 'status-inactive'}">
                        ${pricing.active ? 'نشط' : 'معطل'}
                    </span>
                </div>
                <div class="pricing-details">
                    <div class="detail-item">
                        <span class="detail-label">نوع المنطقة:</span>
                        <span class="detail-value">${pricing.type}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">السعر الأساسي:</span>
                        <span class="detail-value">${pricing.basePrice} ريال</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">السعر الإضافي:</span>
                        <span class="detail-value">${pricing.extraPrice} ريال</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">وقت التوصيل:</span>
                        <span class="detail-value">${pricing.deliveryTime}</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="editZonePricing(${pricing.id})">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleZonePricing(${pricing.id})">
                        <span>${pricing.active ? '❌' : '✅'}</span>
                        ${pricing.active ? 'إيقاف' : 'تفعيل'}
                    </button>
                    <button class="btn btn-delete" onclick="deleteZonePricing(${pricing.id})">
                        <span>🗑️</span> حذف
                    </button>
                </div>
            `;
            return card;
        }

        // تحميل تسعير الخدمات
        function loadServicePricing() {
            const grid = document.getElementById('servicePricingGrid');
            grid.innerHTML = '';

            pricingData.servicePricing.forEach(pricing => {
                const card = createServicePricingCard(pricing);
                grid.appendChild(card);
            });

            console.log(`✅ تم تحميل ${pricingData.servicePricing.length} عنصر تسعير الخدمات`);
        }

        // إنشاء بطاقة تسعير الخدمات
        function createServicePricingCard(pricing) {
            const card = document.createElement('div');
            card.className = 'pricing-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <span>🛎️</span>
                        ${pricing.name}
                    </div>
                    <span class="status-badge ${pricing.active ? 'status-active' : 'status-inactive'}">
                        ${pricing.active ? 'نشط' : 'معطل'}
                    </span>
                </div>
                <div class="pricing-details">
                    <div class="detail-item">
                        <span class="detail-label">الوصف:</span>
                        <span class="detail-value">${pricing.description}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">السعر:</span>
                        <span class="detail-value">${pricing.price} ريال</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">النوع:</span>
                        <span class="detail-value">${pricing.type}</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="editServicePricing(${pricing.id})">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleServicePricing(${pricing.id})">
                        <span>${pricing.active ? '❌' : '✅'}</span>
                        ${pricing.active ? 'إيقاف' : 'تفعيل'}
                    </button>
                    <button class="btn btn-delete" onclick="deleteServicePricing(${pricing.id})">
                        <span>🗑️</span> حذف
                    </button>
                </div>
            `;
            return card;
        }

        // تحميل التسعير الخاص
        function loadSpecialPricing() {
            const grid = document.getElementById('specialPricingGrid');
            grid.innerHTML = '';

            pricingData.specialPricing.forEach(pricing => {
                const card = createSpecialPricingCard(pricing);
                grid.appendChild(card);
            });

            console.log(`✅ تم تحميل ${pricingData.specialPricing.length} عنصر التسعير الخاص`);
        }

        // إنشاء بطاقة التسعير الخاص
        function createSpecialPricingCard(pricing) {
            const card = document.createElement('div');
            card.className = 'pricing-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <span>⭐</span>
                        ${pricing.name}
                    </div>
                    <span class="status-badge ${pricing.active ? 'status-active' : 'status-inactive'}">
                        ${pricing.active ? 'نشط' : 'معطل'}
                    </span>
                </div>
                <div class="pricing-details">
                    <div class="detail-item">
                        <span class="detail-label">الوصف:</span>
                        <span class="detail-value">${pricing.description}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الخصم:</span>
                        <span class="detail-value">${pricing.discount}${pricing.type === 'نسبة مئوية' ? '%' : ' ريال'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">النوع:</span>
                        <span class="detail-value">${pricing.type}</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="editSpecialPricing(${pricing.id})">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn ${pricing.active ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleSpecialPricing(${pricing.id})">
                        <span>${pricing.active ? '❌' : '✅'}</span>
                        ${pricing.active ? 'إيقاف' : 'تفعيل'}
                    </button>
                    <button class="btn btn-delete" onclick="deleteSpecialPricing(${pricing.id})">
                        <span>🗑️</span> حذف
                    </button>
                </div>
            `;
            return card;
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            currentTab = tabName;
            console.log(`📋 تم التبديل إلى تبويب: ${tabName}`);
        }

        // وظائف التعديل والتفعيل
        function editWeightPricing(id) {
            const item = pricingData.weightPricing.find(p => p.id === id);
            if (!item) {
                alert('❌ لم يتم العثور على عنصر التسعير');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>✏️ تعديل التسعير حسب الوزن</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="editWeightForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editWeightName">اسم الفئة *</label>
                                    <input type="text" id="editWeightName" value="${item.name}" required>
                                </div>
                                <div class="form-group">
                                    <label for="editWeightActive">الحالة</label>
                                    <select id="editWeightActive">
                                        <option value="true" ${item.active ? 'selected' : ''}>نشط</option>
                                        <option value="false" ${!item.active ? 'selected' : ''}>معطل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editWeightMin">الحد الأدنى للوزن (كيلو) *</label>
                                    <input type="number" id="editWeightMin" value="${item.minWeight}" min="0" step="0.1" required>
                                </div>
                                <div class="form-group">
                                    <label for="editWeightMax">الحد الأقصى للوزن (كيلو) *</label>
                                    <input type="number" id="editWeightMax" value="${item.maxWeight}" min="0" step="0.1" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editWeightBase">السعر الأساسي (ريال) *</label>
                                    <input type="number" id="editWeightBase" value="${item.basePrice}" min="0" step="0.5" required>
                                </div>
                                <div class="form-group">
                                    <label for="editWeightPerKg">سعر الكيلو الإضافي (ريال) *</label>
                                    <input type="number" id="editWeightPerKg" value="${item.perKgPrice}" min="0" step="0.5" required>
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">✅ حفظ التعديلات</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('editWeightForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('editWeightName').value;
                const minWeight = parseFloat(document.getElementById('editWeightMin').value);
                const maxWeight = parseFloat(document.getElementById('editWeightMax').value);
                const basePrice = parseFloat(document.getElementById('editWeightBase').value);
                const perKgPrice = parseFloat(document.getElementById('editWeightPerKg').value);
                const active = document.getElementById('editWeightActive').value === 'true';

                if (minWeight >= maxWeight) {
                    alert('❌ الحد الأدنى للوزن يجب أن يكون أقل من الحد الأقصى');
                    return;
                }

                // تحديث البيانات
                item.name = name;
                item.minWeight = minWeight;
                item.maxWeight = maxWeight;
                item.basePrice = basePrice;
                item.perKgPrice = perKgPrice;
                item.active = active;

                savePricingData();
                loadWeightPricing();
                modal.remove();
                alert('✅ تم تحديث التسعير بنجاح!');
            });
        }

        function toggleWeightPricing(id) {
            const item = pricingData.weightPricing.find(p => p.id === id);
            if (item) {
                item.active = !item.active;
                savePricingData();
                loadWeightPricing();
                alert(`✅ تم ${item.active ? 'تفعيل' : 'إيقاف'} التسعير بنجاح!`);
            }
        }

        function deleteWeightPricing(id) {
            const item = pricingData.weightPricing.find(p => p.id === id);
            if (!item) {
                alert('❌ لم يتم العثور على عنصر التسعير');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف فئة التسعير "${item.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                pricingData.weightPricing = pricingData.weightPricing.filter(p => p.id !== id);
                savePricingData();
                loadWeightPricing();
                alert('✅ تم حذف التسعير بنجاح!');
            }
        }

        function editZonePricing(id) {
            const item = pricingData.zonePricing.find(p => p.id === id);
            if (!item) {
                alert('❌ لم يتم العثور على عنصر التسعير');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>✏️ تعديل التسعير حسب المنطقة</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="editZoneForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editZoneName">اسم المنطقة *</label>
                                    <input type="text" id="editZoneName" value="${item.name}" required>
                                </div>
                                <div class="form-group">
                                    <label for="editZoneType">نوع المنطقة</label>
                                    <select id="editZoneType">
                                        <option value="محلي" ${item.type === 'محلي' ? 'selected' : ''}>محلي</option>
                                        <option value="دولي" ${item.type === 'دولي' ? 'selected' : ''}>دولي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editZoneBase">السعر الأساسي (ريال) *</label>
                                    <input type="number" id="editZoneBase" value="${item.basePrice}" min="0" step="0.5" required>
                                </div>
                                <div class="form-group">
                                    <label for="editZoneExtra">السعر الإضافي (ريال) *</label>
                                    <input type="number" id="editZoneExtra" value="${item.extraPrice}" min="0" step="0.5" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editZoneDelivery">وقت التوصيل *</label>
                                    <input type="text" id="editZoneDelivery" value="${item.deliveryTime}" required>
                                </div>
                                <div class="form-group">
                                    <label for="editZoneActive">الحالة</label>
                                    <select id="editZoneActive">
                                        <option value="true" ${item.active ? 'selected' : ''}>نشط</option>
                                        <option value="false" ${!item.active ? 'selected' : ''}>معطل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">✅ حفظ التعديلات</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('editZoneForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('editZoneName').value;
                const type = document.getElementById('editZoneType').value;
                const basePrice = parseFloat(document.getElementById('editZoneBase').value);
                const extraPrice = parseFloat(document.getElementById('editZoneExtra').value);
                const deliveryTime = document.getElementById('editZoneDelivery').value;
                const active = document.getElementById('editZoneActive').value === 'true';

                // تحديث البيانات
                item.name = name;
                item.type = type;
                item.basePrice = basePrice;
                item.extraPrice = extraPrice;
                item.deliveryTime = deliveryTime;
                item.active = active;

                savePricingData();
                loadZonePricing();
                modal.remove();
                alert('✅ تم تحديث التسعير بنجاح!');
            });
        }

        function toggleZonePricing(id) {
            const item = pricingData.zonePricing.find(p => p.id === id);
            if (item) {
                item.active = !item.active;
                savePricingData();
                loadZonePricing();
                alert(`✅ تم ${item.active ? 'تفعيل' : 'إيقاف'} التسعير بنجاح!`);
            }
        }

        function deleteZonePricing(id) {
            const item = pricingData.zonePricing.find(p => p.id === id);
            if (!item) {
                alert('❌ لم يتم العثور على عنصر التسعير');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف تسعير منطقة "${item.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                pricingData.zonePricing = pricingData.zonePricing.filter(p => p.id !== id);
                savePricingData();
                loadZonePricing();
                alert('✅ تم حذف التسعير بنجاح!');
            }
        }

        function editServicePricing(id) {
            const item = pricingData.servicePricing.find(p => p.id === id);
            if (!item) {
                alert('❌ لم يتم العثور على عنصر التسعير');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>✏️ تعديل تسعير الخدمة</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="editServiceForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editServiceName">اسم الخدمة *</label>
                                    <input type="text" id="editServiceName" value="${item.name}" required>
                                </div>
                                <div class="form-group">
                                    <label for="editServiceType">نوع الخدمة</label>
                                    <select id="editServiceType">
                                        <option value="مجاني" ${item.type === 'مجاني' ? 'selected' : ''}>مجاني</option>
                                        <option value="إضافي" ${item.type === 'إضافي' ? 'selected' : ''}>إضافي</option>
                                        <option value="اختياري" ${item.type === 'اختياري' ? 'selected' : ''}>اختياري</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="editServiceDesc">وصف الخدمة *</label>
                                <textarea id="editServiceDesc" rows="3" required>${item.description}</textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editServicePrice">السعر (ريال) *</label>
                                    <input type="number" id="editServicePrice" value="${item.price}" min="0" step="0.5" required>
                                </div>
                                <div class="form-group">
                                    <label for="editServiceActive">الحالة</label>
                                    <select id="editServiceActive">
                                        <option value="true" ${item.active ? 'selected' : ''}>نشط</option>
                                        <option value="false" ${!item.active ? 'selected' : ''}>معطل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">✅ حفظ التعديلات</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('editServiceForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('editServiceName').value;
                const description = document.getElementById('editServiceDesc').value;
                const price = parseFloat(document.getElementById('editServicePrice').value);
                const type = document.getElementById('editServiceType').value;
                const active = document.getElementById('editServiceActive').value === 'true';

                // تحديث البيانات
                item.name = name;
                item.description = description;
                item.price = price;
                item.type = type;
                item.active = active;

                savePricingData();
                loadServicePricing();
                modal.remove();
                alert('✅ تم تحديث تسعير الخدمة بنجاح!');
            });
        }

        function toggleServicePricing(id) {
            const item = pricingData.servicePricing.find(p => p.id === id);
            if (item) {
                item.active = !item.active;
                savePricingData();
                loadServicePricing();
                alert(`✅ تم ${item.active ? 'تفعيل' : 'إيقاف'} الخدمة بنجاح!`);
            }
        }

        function deleteServicePricing(id) {
            const item = pricingData.servicePricing.find(p => p.id === id);
            if (!item) {
                alert('❌ لم يتم العثور على عنصر التسعير');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف خدمة "${item.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                pricingData.servicePricing = pricingData.servicePricing.filter(p => p.id !== id);
                savePricingData();
                loadServicePricing();
                alert('✅ تم حذف تسعير الخدمة بنجاح!');
            }
        }

        function editSpecialPricing(id) {
            const item = pricingData.specialPricing.find(p => p.id === id);
            if (!item) {
                alert('❌ لم يتم العثور على عنصر التسعير');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>✏️ تعديل العرض الخاص</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="editSpecialForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editSpecialName">اسم العرض *</label>
                                    <input type="text" id="editSpecialName" value="${item.name}" required>
                                </div>
                                <div class="form-group">
                                    <label for="editSpecialType">نوع الخصم</label>
                                    <select id="editSpecialType">
                                        <option value="نسبة مئوية" ${item.type === 'نسبة مئوية' ? 'selected' : ''}>نسبة مئوية</option>
                                        <option value="مبلغ ثابت" ${item.type === 'مبلغ ثابت' ? 'selected' : ''}>مبلغ ثابت</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="editSpecialDesc">وصف العرض *</label>
                                <textarea id="editSpecialDesc" rows="3" required>${item.description}</textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editSpecialDiscount">قيمة الخصم *</label>
                                    <input type="number" id="editSpecialDiscount" value="${item.discount}" min="0" step="0.5" required>
                                </div>
                                <div class="form-group">
                                    <label for="editSpecialActive">الحالة</label>
                                    <select id="editSpecialActive">
                                        <option value="true" ${item.active ? 'selected' : ''}>نشط</option>
                                        <option value="false" ${!item.active ? 'selected' : ''}>معطل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="editSpecialMinOrders">الحد الأدنى للطلبات</label>
                                    <input type="number" id="editSpecialMinOrders" value="${item.minOrders || ''}" min="0">
                                </div>
                                <div class="form-group">
                                    <label for="editSpecialMaxUses">الحد الأقصى للاستخدام</label>
                                    <input type="number" id="editSpecialMaxUses" value="${item.maxUses || ''}" min="0">
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">✅ حفظ التعديلات</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('editSpecialForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('editSpecialName').value;
                const description = document.getElementById('editSpecialDesc').value;
                const discount = parseFloat(document.getElementById('editSpecialDiscount').value);
                const type = document.getElementById('editSpecialType').value;
                const active = document.getElementById('editSpecialActive').value === 'true';
                const minOrders = parseInt(document.getElementById('editSpecialMinOrders').value) || undefined;
                const maxUses = parseInt(document.getElementById('editSpecialMaxUses').value) || undefined;

                if (type === 'نسبة مئوية' && discount > 100) {
                    alert('❌ نسبة الخصم لا يمكن أن تكون أكثر من 100%');
                    return;
                }

                // تحديث البيانات
                item.name = name;
                item.description = description;
                item.discount = discount;
                item.type = type;
                item.active = active;
                if (minOrders) item.minOrders = minOrders;
                if (maxUses) item.maxUses = maxUses;

                savePricingData();
                loadSpecialPricing();
                modal.remove();
                alert('✅ تم تحديث العرض الخاص بنجاح!');
            });
        }

        function toggleSpecialPricing(id) {
            const item = pricingData.specialPricing.find(p => p.id === id);
            if (item) {
                item.active = !item.active;
                savePricingData();
                loadSpecialPricing();
                alert(`✅ تم ${item.active ? 'تفعيل' : 'إيقاف'} العرض بنجاح!`);
            }
        }

        function deleteSpecialPricing(id) {
            const item = pricingData.specialPricing.find(p => p.id === id);
            if (!item) {
                alert('❌ لم يتم العثور على عنصر التسعير');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف العرض الخاص "${item.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                pricingData.specialPricing = pricingData.specialPricing.filter(p => p.id !== id);
                savePricingData();
                loadSpecialPricing();
                alert('✅ تم حذف العرض الخاص بنجاح!');
            }
        }

        function addPricing() {
            switch(currentTab) {
                case 'weight-pricing':
                    addWeightPricing();
                    break;
                case 'zone-pricing':
                    addZonePricing();
                    break;
                case 'service-pricing':
                    addServicePricing();
                    break;
                case 'special-pricing':
                    addSpecialPricing();
                    break;
                default:
                    alert('❌ يرجى اختيار نوع التسعير أولاً');
            }
        }

        // إضافة تسعير حسب الوزن
        function addWeightPricing() {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>➕ إضافة تسعير حسب الوزن</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="addWeightForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addWeightName">اسم الفئة *</label>
                                    <input type="text" id="addWeightName" required placeholder="مثال: طرد متوسط">
                                </div>
                                <div class="form-group">
                                    <label for="addWeightActive">الحالة</label>
                                    <select id="addWeightActive">
                                        <option value="true" selected>نشط</option>
                                        <option value="false">معطل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addWeightMin">الحد الأدنى للوزن (كيلو) *</label>
                                    <input type="number" id="addWeightMin" min="0" step="0.1" required placeholder="0">
                                </div>
                                <div class="form-group">
                                    <label for="addWeightMax">الحد الأقصى للوزن (كيلو) *</label>
                                    <input type="number" id="addWeightMax" min="0" step="0.1" required placeholder="5">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addWeightBase">السعر الأساسي (ريال) *</label>
                                    <input type="number" id="addWeightBase" min="0" step="0.5" required placeholder="20">
                                </div>
                                <div class="form-group">
                                    <label for="addWeightPerKg">سعر الكيلو الإضافي (ريال) *</label>
                                    <input type="number" id="addWeightPerKg" min="0" step="0.5" required placeholder="5">
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">✅ إضافة التسعير</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('addWeightForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('addWeightName').value;
                const minWeight = parseFloat(document.getElementById('addWeightMin').value);
                const maxWeight = parseFloat(document.getElementById('addWeightMax').value);
                const basePrice = parseFloat(document.getElementById('addWeightBase').value);
                const perKgPrice = parseFloat(document.getElementById('addWeightPerKg').value);
                const active = document.getElementById('addWeightActive').value === 'true';

                if (minWeight >= maxWeight) {
                    alert('❌ الحد الأدنى للوزن يجب أن يكون أقل من الحد الأقصى');
                    return;
                }

                // إنشاء ID جديد
                const newId = Math.max(...pricingData.weightPricing.map(p => p.id), 0) + 1;

                // إضافة العنصر الجديد
                const newItem = {
                    id: newId,
                    name: name,
                    minWeight: minWeight,
                    maxWeight: maxWeight,
                    basePrice: basePrice,
                    perKgPrice: perKgPrice,
                    active: active
                };

                pricingData.weightPricing.push(newItem);
                savePricingData();
                loadWeightPricing();
                modal.remove();
                alert('✅ تم إضافة التسعير بنجاح!');
            });
        }

        // إضافة تسعير المناطق
        function addZonePricing() {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>➕ إضافة تسعير منطقة</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="addZoneForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addZoneName">اسم المنطقة *</label>
                                    <input type="text" id="addZoneName" required placeholder="مثال: الطائف">
                                </div>
                                <div class="form-group">
                                    <label for="addZoneType">نوع المنطقة</label>
                                    <select id="addZoneType">
                                        <option value="محلي" selected>محلي</option>
                                        <option value="دولي">دولي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addZoneBase">السعر الأساسي (ريال) *</label>
                                    <input type="number" id="addZoneBase" min="0" step="0.5" required placeholder="25">
                                </div>
                                <div class="form-group">
                                    <label for="addZoneExtra">السعر الإضافي (ريال) *</label>
                                    <input type="number" id="addZoneExtra" min="0" step="0.5" required placeholder="5">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addZoneDelivery">وقت التوصيل *</label>
                                    <input type="text" id="addZoneDelivery" required placeholder="مثال: 2-3 أيام">
                                </div>
                                <div class="form-group">
                                    <label for="addZoneActive">الحالة</label>
                                    <select id="addZoneActive">
                                        <option value="true" selected>نشط</option>
                                        <option value="false">معطل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">✅ إضافة التسعير</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('addZoneForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('addZoneName').value;
                const type = document.getElementById('addZoneType').value;
                const basePrice = parseFloat(document.getElementById('addZoneBase').value);
                const extraPrice = parseFloat(document.getElementById('addZoneExtra').value);
                const deliveryTime = document.getElementById('addZoneDelivery').value;
                const active = document.getElementById('addZoneActive').value === 'true';

                // إنشاء ID جديد
                const newId = Math.max(...pricingData.zonePricing.map(p => p.id), 0) + 1;

                // إضافة العنصر الجديد
                const newItem = {
                    id: newId,
                    name: name,
                    type: type,
                    basePrice: basePrice,
                    extraPrice: extraPrice,
                    deliveryTime: deliveryTime,
                    active: active
                };

                pricingData.zonePricing.push(newItem);
                savePricingData();
                loadZonePricing();
                modal.remove();
                alert('✅ تم إضافة تسعير المنطقة بنجاح!');
            });
        }

        // إضافة تسعير الخدمات
        function addServicePricing() {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>➕ إضافة خدمة جديدة</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="addServiceForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addServiceName">اسم الخدمة *</label>
                                    <input type="text" id="addServiceName" required placeholder="مثال: التوصيل المبرد">
                                </div>
                                <div class="form-group">
                                    <label for="addServiceType">نوع الخدمة</label>
                                    <select id="addServiceType">
                                        <option value="مجاني">مجاني</option>
                                        <option value="إضافي" selected>إضافي</option>
                                        <option value="اختياري">اختياري</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="addServiceDesc">وصف الخدمة *</label>
                                <textarea id="addServiceDesc" rows="3" required placeholder="وصف مفصل للخدمة المقدمة"></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addServicePrice">السعر (ريال) *</label>
                                    <input type="number" id="addServicePrice" min="0" step="0.5" required placeholder="30">
                                </div>
                                <div class="form-group">
                                    <label for="addServiceActive">الحالة</label>
                                    <select id="addServiceActive">
                                        <option value="true" selected>نشط</option>
                                        <option value="false">معطل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">✅ إضافة الخدمة</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('addServiceForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('addServiceName').value;
                const description = document.getElementById('addServiceDesc').value;
                const price = parseFloat(document.getElementById('addServicePrice').value);
                const type = document.getElementById('addServiceType').value;
                const active = document.getElementById('addServiceActive').value === 'true';

                // إنشاء ID جديد
                const newId = Math.max(...pricingData.servicePricing.map(p => p.id), 0) + 1;

                // إضافة العنصر الجديد
                const newItem = {
                    id: newId,
                    name: name,
                    description: description,
                    price: price,
                    type: type,
                    active: active
                };

                pricingData.servicePricing.push(newItem);
                savePricingData();
                loadServicePricing();
                modal.remove();
                alert('✅ تم إضافة الخدمة بنجاح!');
            });
        }

        // جعل الوظائف متاحة عالمياً
        window.showTab = showTab;
        window.editWeightPricing = editWeightPricing;
        window.toggleWeightPricing = toggleWeightPricing;
        window.deleteWeightPricing = deleteWeightPricing;
        window.editZonePricing = editZonePricing;
        window.toggleZonePricing = toggleZonePricing;
        window.deleteZonePricing = deleteZonePricing;
        window.editServicePricing = editServicePricing;
        window.toggleServicePricing = toggleServicePricing;
        window.deleteServicePricing = deleteServicePricing;
        window.editSpecialPricing = editSpecialPricing;
        window.toggleSpecialPricing = toggleSpecialPricing;
        window.deleteSpecialPricing = deleteSpecialPricing;
        window.addPricing = addPricing;

        // إضافة العروض الخاصة
        function addSpecialPricing() {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>➕ إضافة عرض خاص</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="addSpecialForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addSpecialName">اسم العرض *</label>
                                    <input type="text" id="addSpecialName" required placeholder="مثال: خصم الصيف">
                                </div>
                                <div class="form-group">
                                    <label for="addSpecialType">نوع الخصم</label>
                                    <select id="addSpecialType">
                                        <option value="نسبة مئوية" selected>نسبة مئوية</option>
                                        <option value="مبلغ ثابت">مبلغ ثابت</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="addSpecialDesc">وصف العرض *</label>
                                <textarea id="addSpecialDesc" rows="3" required placeholder="وصف مفصل للعرض الخاص"></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addSpecialDiscount">قيمة الخصم *</label>
                                    <input type="number" id="addSpecialDiscount" min="0" step="0.5" required placeholder="15">
                                </div>
                                <div class="form-group">
                                    <label for="addSpecialActive">الحالة</label>
                                    <select id="addSpecialActive">
                                        <option value="true" selected>نشط</option>
                                        <option value="false">معطل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="addSpecialMinOrders">الحد الأدنى للطلبات</label>
                                    <input type="number" id="addSpecialMinOrders" min="0" placeholder="اختياري">
                                </div>
                                <div class="form-group">
                                    <label for="addSpecialMaxUses">الحد الأقصى للاستخدام</label>
                                    <input type="number" id="addSpecialMaxUses" min="0" placeholder="اختياري">
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">✅ إضافة العرض</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            document.getElementById('addSpecialForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('addSpecialName').value;
                const description = document.getElementById('addSpecialDesc').value;
                const discount = parseFloat(document.getElementById('addSpecialDiscount').value);
                const type = document.getElementById('addSpecialType').value;
                const active = document.getElementById('addSpecialActive').value === 'true';
                const minOrders = parseInt(document.getElementById('addSpecialMinOrders').value) || undefined;
                const maxUses = parseInt(document.getElementById('addSpecialMaxUses').value) || undefined;

                if (type === 'نسبة مئوية' && discount > 100) {
                    alert('❌ نسبة الخصم لا يمكن أن تكون أكثر من 100%');
                    return;
                }

                // إنشاء ID جديد
                const newId = Math.max(...pricingData.specialPricing.map(p => p.id), 0) + 1;

                // إضافة العنصر الجديد
                const newItem = {
                    id: newId,
                    name: name,
                    description: description,
                    discount: discount,
                    type: type,
                    active: active
                };

                if (minOrders) newItem.minOrders = minOrders;
                if (maxUses) newItem.maxUses = maxUses;

                pricingData.specialPricing.push(newItem);
                savePricingData();
                loadSpecialPricing();
                modal.remove();
                alert('✅ تم إضافة العرض الخاص بنجاح!');
            });
        }

        console.log('💰 تم تحميل نظام إدارة التسعير بنجاح!');
    </script>
</body>
</html>
