<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الشحنات - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        @font-face {
            font-family: 'SF Pro AR Display';
            src: url('fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
                 url('fonts/SFProARDisplay-Semibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', 'SF Pro AR Display', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            font-weight: 600;
            background: var(--bg-secondary);
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .shipments-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .status-transit {
            background: rgba(0, 123, 255, 0.2);
            color: #004085;
        }

        .status-delivered {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .status-cancelled {
            background: rgba(220, 53, 69, 0.2);
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-data h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .actions {
                justify-content: center;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🚚</span>
                <span>نظام إدارة الشحنات</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="shipments.html" class="active">الشحنات</a>
                <a href="shipment-tracking.html">تتبع الشحنات</a>
                <a href="pricing-management.html">التسعير</a>
                <a href="customers.html">العملاء</a>
                <a href="financial-system.html">النظام المالي</a>
                <a href="reports.html">التقارير</a>
                <a href="settings.html">الإعدادات</a>
                <a href="index.html">تسجيل الخروج</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">📦 إدارة الشحنات</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn" onclick="addNewShipment()">
                    ➕ إضافة شحنة جديدة
                </button>
                <button class="btn btn-secondary" onclick="loadShipments()">
                    🔄 تحديث القائمة
                </button>
            </div>
        </div>

        <!-- جدول الشحنات -->
        <div class="shipments-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم التتبع</th>
                        <th>المرسل</th>
                        <th>المستقبل</th>
                        <th>الوزن</th>
                        <th>التكلفة</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="shipmentsTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>
            
            <!-- رسالة عدم وجود بيانات -->
            <div id="noDataMessage" class="no-data" style="display: none;">
                <h3>📦 لا توجد شحنات</h3>
                <p>لم يتم العثور على أي شحنات. ابدأ بإضافة شحنة جديدة.</p>
                <button class="btn" onclick="addNewShipment()" style="margin-top: 15px;">
                    ➕ إضافة شحنة جديدة
                </button>
            </div>
        </div>
    </main>

    <!-- نافذة إلغاء الشحنة -->
    <div id="cancelModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5);">
        <div class="modal-content" style="background-color: white; margin: 10% auto; padding: 0; border-radius: 12px; width: 90%; max-width: 600px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%); color: white; padding: 20px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">إلغاء شحنة</h2>
                <span class="close" onclick="closeCancelModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="cancelForm">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">معلومات الشحنة</label>
                        <div id="shipmentInfo" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <!-- سيتم ملء معلومات الشحنة هنا -->
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">فئة سبب الإلغاء *</label>
                        <select id="cancelCategory" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display);">
                            <option value="">اختر الفئة</option>
                            <option value="اسباب عدم التوصيل">أسباب عدم التوصيل</option>
                            <option value="اسباب رفض الشحنات">أسباب رفض الشحنات</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">سبب الإلغاء *</label>
                        <select id="cancelReason" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display);">
                            <option value="">اختر السبب</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">ملاحظات إضافية</label>
                        <textarea id="cancelNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..." style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical;"></textarea>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeCancelModal()">إلغاء</button>
                        <button type="submit" class="btn btn-danger">❌ إلغاء الشحنة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة شحنة جديدة -->
    <div id="addShipmentModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); backdrop-filter: blur(5px);">
        <div class="modal-content" style="background-color: white; margin: 2% auto; padding: 0; border-radius: 12px; width: 95%; max-width: 900px; max-height: 95vh; overflow-y: auto; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #007bff 0%, #28a745 100%); color: white; padding: 20px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">إضافة شحنة جديدة</h2>
                <span class="close" onclick="closeAddShipmentModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer; line-height: 1;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="addShipmentForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <!-- معلومات المرسل -->
                        <div style="grid-column: 1 / -1;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📤 معلومات المرسل</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم المرسل *</label>
                            <input type="text" id="senderName" required placeholder="أدخل اسم المرسل" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">هاتف المرسل *</label>
                            <input type="tel" id="senderPhone" required placeholder="+966501234567" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px; grid-column: 1 / -1;">
                            <label style="font-weight: 600; color: #495057;">عنوان المرسل *</label>
                            <textarea id="senderAddress" required rows="2" placeholder="أدخل عنوان المرسل الكامل" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical; transition: border-color 0.3s ease;"></textarea>
                        </div>

                        <!-- معلومات المستقبل -->
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📥 معلومات المستقبل</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم المستقبل *</label>
                            <input type="text" id="receiverName" required placeholder="أدخل اسم المستقبل" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">هاتف المستقبل *</label>
                            <input type="tel" id="receiverPhone" required placeholder="+966501234567" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">المدينة *</label>
                            <select id="receiverCity" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر المدينة</option>
                                <option value="الرياض">الرياض</option>
                                <option value="جدة">جدة</option>
                                <option value="الدمام">الدمام</option>
                                <option value="مكة المكرمة">مكة المكرمة</option>
                                <option value="المدينة المنورة">المدينة المنورة</option>
                                <option value="الطائف">الطائف</option>
                                <option value="تبوك">تبوك</option>
                                <option value="بريدة">بريدة</option>
                                <option value="خميس مشيط">خميس مشيط</option>
                                <option value="حائل">حائل</option>
                                <option value="الكويت">الكويت</option>
                                <option value="حولي">حولي</option>
                                <option value="الفروانية">الفروانية</option>
                                <option value="الأحمدي">الأحمدي</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">المنطقة *</label>
                            <input type="text" id="receiverRegion" required placeholder="أدخل المنطقة أو الحي" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px; grid-column: 1 / -1;">
                            <label style="font-weight: 600; color: #495057;">عنوان المستقبل *</label>
                            <textarea id="receiverAddress" required rows="2" placeholder="أدخل عنوان المستقبل الكامل" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical; transition: border-color 0.3s ease;"></textarea>
                        </div>

                        <!-- تفاصيل الشحنة -->
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📦 تفاصيل الشحنة</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">نوع الشحنة *</label>
                            <select id="shipmentType" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر نوع الشحنة</option>
                                <option value="مستندات">مستندات</option>
                                <option value="طرد صغير">طرد صغير</option>
                                <option value="طرد متوسط">طرد متوسط</option>
                                <option value="طرد كبير">طرد كبير</option>
                                <option value="أجهزة إلكترونية">أجهزة إلكترونية</option>
                                <option value="ملابس">ملابس</option>
                                <option value="كتب">كتب</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الوزن (كيلو) *</label>
                            <input type="number" id="weight" required min="0.1" step="0.1" placeholder="1.5" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">التكلفة (ريال) *</label>
                            <input type="number" id="cost" required min="1" step="0.01" placeholder="25.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">طريقة الدفع *</label>
                            <select id="paymentMethod" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر طريقة الدفع</option>
                                <option value="مدفوع مسبقاً">مدفوع مسبقاً</option>
                                <option value="COD">الدفع عند الاستلام (COD)</option>
                                <option value="آجل">آجل</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الأولوية</label>
                            <select id="priority" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="عادي">عادي</option>
                                <option value="عاجل">عاجل</option>
                                <option value="طارئ">طارئ</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الفرع الحالي</label>
                            <select id="currentBranch" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر الفرع</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px; grid-column: 1 / -1;">
                            <label style="font-weight: 600; color: #495057;">ملاحظات</label>
                            <textarea id="notes" rows="3" placeholder="أدخل أي ملاحظات إضافية..." style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical; transition: border-color 0.3s ease;"></textarea>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeAddShipmentModal()" style="background: #6c757d;">إلغاء</button>
                        <button type="submit" class="btn" style="background: #007bff;">📦 إضافة الشحنة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل الشحنة -->
    <div id="editShipmentModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); backdrop-filter: blur(5px);">
        <div class="modal-content" style="background-color: white; margin: 2% auto; padding: 0; border-radius: 12px; width: 95%; max-width: 900px; max-height: 95vh; overflow-y: auto; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; padding: 20px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">تعديل الشحنة</h2>
                <span class="close" onclick="closeEditShipmentModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer; line-height: 1;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="editShipmentForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <!-- معلومات الشحنة الحالية -->
                        <div style="grid-column: 1 / -1;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📋 معلومات الشحنة الحالية</h3>
                            <div id="currentShipmentInfo" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                                <!-- سيتم ملء معلومات الشحنة هنا -->
                            </div>
                        </div>

                        <!-- معلومات المرسل -->
                        <div style="grid-column: 1 / -1;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📤 معلومات المرسل</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم المرسل *</label>
                            <input type="text" id="editSenderName" required placeholder="أدخل اسم المرسل" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">هاتف المرسل *</label>
                            <input type="tel" id="editSenderPhone" required placeholder="+966501234567" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px; grid-column: 1 / -1;">
                            <label style="font-weight: 600; color: #495057;">عنوان المرسل *</label>
                            <textarea id="editSenderAddress" required rows="2" placeholder="أدخل عنوان المرسل الكامل" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical; transition: border-color 0.3s ease;"></textarea>
                        </div>

                        <!-- معلومات المستقبل -->
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📥 معلومات المستقبل</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">اسم المستقبل *</label>
                            <input type="text" id="editReceiverName" required placeholder="أدخل اسم المستقبل" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">هاتف المستقبل *</label>
                            <input type="tel" id="editReceiverPhone" required placeholder="+966501234567" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">المدينة *</label>
                            <select id="editReceiverCity" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر المدينة</option>
                                <option value="الرياض">الرياض</option>
                                <option value="جدة">جدة</option>
                                <option value="الدمام">الدمام</option>
                                <option value="مكة المكرمة">مكة المكرمة</option>
                                <option value="المدينة المنورة">المدينة المنورة</option>
                                <option value="الطائف">الطائف</option>
                                <option value="تبوك">تبوك</option>
                                <option value="بريدة">بريدة</option>
                                <option value="خميس مشيط">خميس مشيط</option>
                                <option value="حائل">حائل</option>
                                <option value="الكويت">الكويت</option>
                                <option value="حولي">حولي</option>
                                <option value="الفروانية">الفروانية</option>
                                <option value="الأحمدي">الأحمدي</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">المنطقة *</label>
                            <input type="text" id="editReceiverRegion" required placeholder="أدخل المنطقة أو الحي" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px; grid-column: 1 / -1;">
                            <label style="font-weight: 600; color: #495057;">عنوان المستقبل *</label>
                            <textarea id="editReceiverAddress" required rows="2" placeholder="أدخل عنوان المستقبل الكامل" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical; transition: border-color 0.3s ease;"></textarea>
                        </div>

                        <!-- تفاصيل الشحنة -->
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h3 style="color: #495057; margin-bottom: 15px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">📦 تفاصيل الشحنة</h3>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">نوع الشحنة *</label>
                            <select id="editShipmentType" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر نوع الشحنة</option>
                                <option value="مستندات">مستندات</option>
                                <option value="طرد صغير">طرد صغير</option>
                                <option value="طرد متوسط">طرد متوسط</option>
                                <option value="طرد كبير">طرد كبير</option>
                                <option value="أجهزة إلكترونية">أجهزة إلكترونية</option>
                                <option value="ملابس">ملابس</option>
                                <option value="كتب">كتب</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الوزن (كيلو) *</label>
                            <input type="number" id="editWeight" required min="0.1" step="0.1" placeholder="1.5" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">التكلفة (ريال) *</label>
                            <input type="number" id="editCost" required min="1" step="0.01" placeholder="25.00" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">طريقة الدفع *</label>
                            <select id="editPaymentMethod" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر طريقة الدفع</option>
                                <option value="مدفوع مسبقاً">مدفوع مسبقاً</option>
                                <option value="COD">الدفع عند الاستلام (COD)</option>
                                <option value="آجل">آجل</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الحالة *</label>
                            <select id="editStatus" required style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="معلق">معلق</option>
                                <option value="في الطريق">في الطريق</option>
                                <option value="مسلم">مسلم</option>
                                <option value="في التحويل">في التحويل</option>
                                <option value="في الطريق للفرع">في الطريق للفرع</option>
                                <option value="في الفرع">في الفرع</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الأولوية</label>
                            <select id="editPriority" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="عادي">عادي</option>
                                <option value="عاجل">عاجل</option>
                                <option value="طارئ">طارئ</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="font-weight: 600; color: #495057;">الفرع الحالي</label>
                            <select id="editCurrentBranch" style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); transition: border-color 0.3s ease;">
                                <option value="">اختر الفرع</option>
                            </select>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: 8px; grid-column: 1 / -1;">
                            <label style="font-weight: 600; color: #495057;">ملاحظات</label>
                            <textarea id="editNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..." style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical; transition: border-color 0.3s ease;"></textarea>
                        </div>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeEditShipmentModal()" style="background: #6c757d;">إلغاء</button>
                        <button type="submit" class="btn" style="background: #ffc107; color: #212529;">💾 حفظ التعديلات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تحويل الشحنة -->
    <div id="transferModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5);">
        <div class="modal-content" style="background-color: white; margin: 10% auto; padding: 0; border-radius: 12px; width: 90%; max-width: 600px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);">
            <div class="modal-header" style="background: linear-gradient(135deg, #17a2b8 0%, #28a745 100%); color: white; padding: 20px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 1.5rem;">تحويل شحنة بين الفروع</h2>
                <span class="close" onclick="closeTransferModal()" style="color: white; font-size: 2rem; font-weight: bold; cursor: pointer;">&times;</span>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="transferForm">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">معلومات الشحنة</label>
                        <div id="transferShipmentInfo" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <!-- سيتم ملء معلومات الشحنة هنا -->
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">من فرع *</label>
                        <select id="fromBranch" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display);">
                            <option value="">اختر الفرع المرسل</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">إلى فرع *</label>
                        <select id="toBranch" required style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display);">
                            <option value="">اختر الفرع المستقبل</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">الأولوية</label>
                        <select id="transferPriority" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display);">
                            <option value="عادي">عادي</option>
                            <option value="عاجل">عاجل</option>
                            <option value="طارئ">طارئ</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">سبب التحويل</label>
                        <input type="text" id="transferReason" placeholder="أدخل سبب التحويل..." style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display);">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">ملاحظات</label>
                        <textarea id="transferNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..." style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-family: var(--font-arabic-display); resize: vertical;"></textarea>
                    </div>

                    <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                        <button type="button" class="btn" onclick="closeTransferModal()">إلغاء</button>
                        <button type="submit" class="btn" style="background: #17a2b8;">🔄 تحويل الشحنة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/database-simple.js"></script>
    <script>
        // تحميل الشحنات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل صفحة الشحنات...');
            
            // التأكد من وجود قاعدة البيانات
            if (typeof db === 'undefined') {
                console.error('❌ قاعدة البيانات غير متاحة');
                alert('خطأ: قاعدة البيانات غير متاحة. يرجى إعادة تحميل الصفحة.');
                return;
            }
            
            try {
                loadShipments();

                // التحقق من معامل URL لفتح نموذج إضافة الشحنة
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('action') === 'add') {
                    // فتح نموذج إضافة الشحنة تلقائياً
                    setTimeout(() => {
                        addNewShipment();
                    }, 500);
                }

                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // تحميل وعرض الشحنات
        function loadShipments() {
            try {
                const shipments = db.getAllShipments();
                displayShipments(shipments);
                console.log('📦 تم تحميل', shipments.length, 'شحنة');
            } catch (error) {
                console.error('❌ خطأ في تحميل الشحنات:', error);
                alert('خطأ في تحميل الشحنات: ' + error.message);
            }
        }

        // عرض الشحنات في الجدول
        function displayShipments(shipments) {
            const tbody = document.getElementById('shipmentsTableBody');
            const noDataMessage = document.getElementById('noDataMessage');
            
            if (shipments.length === 0) {
                tbody.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }

            noDataMessage.style.display = 'none';
            
            tbody.innerHTML = shipments.map(shipment => 
                `<tr>
                    <td><strong>${shipment.trackingNumber}</strong></td>
                    <td>${shipment.senderName}</td>
                    <td>${shipment.receiverName}</td>
                    <td>${shipment.weight || 0} كيلو</td>
                    <td>${shipment.cost || 0} ${shipment.currency || 'SAR'}</td>
                    <td><span class="status-badge status-${getStatusClass(shipment.status)}">${shipment.status}</span></td>
                    <td>${formatDate(shipment.createdDate)}</td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-small" onclick="viewShipment('${shipment.id}')">عرض</button>
                            <button class="btn btn-small btn-warning" onclick="editShipment('${shipment.id}')">تعديل</button>
                            ${shipment.status !== 'ملغي' && shipment.status !== 'مسلم' ?
                                `<button class="btn btn-small" style="background: #dc3545;" onclick="showCancelModal('${shipment.id}')">إلغاء</button>` :
                                ''}
                            ${shipment.status !== 'ملغي' && shipment.status !== 'مسلم' && shipment.status !== 'في التحويل' ?
                                `<button class="btn btn-small" style="background: #17a2b8;" onclick="showTransferModal('${shipment.id}')">تحويل</button>` :
                                ''}
                            <button class="btn btn-small btn-danger" onclick="deleteShipment('${shipment.id}')">حذف</button>
                        </div>
                    </td>
                </tr>`
            ).join('');
        }

        // إضافة شحنة جديدة
        function addNewShipment() {
            try {
                // تحميل قائمة الفروع
                loadBranchesForNewShipment();

                // إعداد مستمع الأحداث لنموذج إضافة الشحنة
                document.getElementById('addShipmentForm').addEventListener('submit', submitNewShipment);

                // إظهار النافذة
                document.getElementById('addShipmentModal').style.display = 'block';

                // التركيز على أول حقل
                document.getElementById('senderName').focus();

            } catch (error) {
                console.error('❌ خطأ في فتح نافذة إضافة الشحنة:', error);
                alert('خطأ في فتح نافذة إضافة الشحنة: ' + error.message);
            }
        }

        // إغلاق نافذة إضافة شحنة
        function closeAddShipmentModal() {
            document.getElementById('addShipmentModal').style.display = 'none';
            document.getElementById('addShipmentForm').reset();
        }

        // تحميل قائمة الفروع للشحنة الجديدة
        function loadBranchesForNewShipment() {
            try {
                const branches = db.getActiveBranches();
                const branchSelect = document.getElementById('currentBranch');

                branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

                branches.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.id;
                    option.textContent = branch.name;
                    branchSelect.appendChild(option);
                });

                // تحديد الفرع الرئيسي كافتراضي
                const mainBranch = branches.find(b => b.isMainBranch);
                if (mainBranch) {
                    branchSelect.value = mainBranch.id;
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل الفروع:', error);
            }
        }

        // تنفيذ إضافة شحنة جديدة
        function submitNewShipment(e) {
            e.preventDefault();

            try {
                // جمع البيانات من النموذج
                const shipmentData = {
                    senderName: document.getElementById('senderName').value.trim(),
                    senderPhone: document.getElementById('senderPhone').value.trim(),
                    senderAddress: document.getElementById('senderAddress').value.trim(),
                    receiverName: document.getElementById('receiverName').value.trim(),
                    receiverPhone: document.getElementById('receiverPhone').value.trim(),
                    receiverCity: document.getElementById('receiverCity').value,
                    receiverRegion: document.getElementById('receiverRegion').value.trim(),
                    receiverAddress: document.getElementById('receiverAddress').value.trim(),
                    shipmentType: document.getElementById('shipmentType').value,
                    weight: parseFloat(document.getElementById('weight').value),
                    cost: parseFloat(document.getElementById('cost').value),
                    paymentMethod: document.getElementById('paymentMethod').value,
                    priority: document.getElementById('priority').value || 'عادي',
                    currentBranch: document.getElementById('currentBranch').value,
                    notes: document.getElementById('notes').value.trim(),
                    status: 'معلق',
                    createdDate: new Date().toISOString().split('T')[0],
                    createdTime: new Date().toLocaleTimeString('ar-SA')
                };

                // التحقق من صحة البيانات
                if (!shipmentData.senderName || !shipmentData.senderPhone || !shipmentData.senderAddress ||
                    !shipmentData.receiverName || !shipmentData.receiverPhone || !shipmentData.receiverCity ||
                    !shipmentData.receiverRegion || !shipmentData.receiverAddress || !shipmentData.shipmentType ||
                    !shipmentData.weight || !shipmentData.cost || !shipmentData.paymentMethod) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                // التحقق من صحة أرقام الهواتف
                const phoneRegex = /^\+?[0-9]{10,15}$/;
                if (!phoneRegex.test(shipmentData.senderPhone.replace(/\s/g, ''))) {
                    alert('يرجى إدخال رقم هاتف صحيح للمرسل');
                    return;
                }
                if (!phoneRegex.test(shipmentData.receiverPhone.replace(/\s/g, ''))) {
                    alert('يرجى إدخال رقم هاتف صحيح للمستقبل');
                    return;
                }

                // التحقق من الوزن والتكلفة
                if (shipmentData.weight <= 0) {
                    alert('يرجى إدخال وزن صحيح للشحنة');
                    return;
                }
                if (shipmentData.cost <= 0) {
                    alert('يرجى إدخال تكلفة صحيحة للشحنة');
                    return;
                }

                // إضافة الشحنة
                const newShipment = db.addShipment(shipmentData);

                if (newShipment) {
                    alert(`✅ تم إضافة الشحنة بنجاح!\n\n📦 رقم التتبع: ${newShipment.trackingNumber}\n👤 المرسل: ${newShipment.senderName}\n👤 المستقبل: ${newShipment.receiverName}\n📍 المدينة: ${newShipment.receiverCity}\n💰 التكلفة: ${newShipment.cost} ريال\n\nيمكنك الآن تتبع الشحنة باستخدام رقم التتبع.`);
                    closeAddShipmentModal();
                    loadShipments();
                } else {
                    alert('❌ خطأ في إضافة الشحنة. يرجى المحاولة مرة أخرى.');
                }

            } catch (error) {
                console.error('❌ خطأ في إضافة الشحنة:', error);
                alert('خطأ في إضافة الشحنة: ' + error.message);
            }
        }

        // عرض تفاصيل الشحنة
        function viewShipment(id) {
            try {
                const shipment = db.getShipmentById(id);
                if (!shipment) {
                    alert('لم يتم العثور على الشحنة');
                    return;
                }
                
                let details = `تفاصيل الشحنة ${shipment.trackingNumber}:\n\n` +
                      `المرسل: ${shipment.senderName}\n` +
                      `المستقبل: ${shipment.receiverName}\n` +
                      `المحتويات: ${shipment.contents || 'غير محدد'}\n` +
                      `الوزن: ${shipment.weight || 0} كيلو\n` +
                      `التكلفة: ${shipment.cost || 0} ${shipment.currency || 'SAR'}\n` +
                      `الحالة: ${shipment.status}\n` +
                      `تاريخ الإنشاء: ${formatDate(shipment.createdDate)}\n` +
                      `الموزع: ${shipment.distributorName || 'غير محدد'}\n`;

                // إضافة تفاصيل الإلغاء إذا كانت الشحنة ملغية
                if (shipment.status === 'ملغي') {
                    details += `\n--- تفاصيل الإلغاء ---\n` +
                              `سبب الإلغاء: ${shipment.cancellationReason || 'غير محدد'}\n` +
                              `فئة الإلغاء: ${shipment.cancellationCategory || 'غير محدد'}\n` +
                              `تاريخ الإلغاء: ${formatDate(shipment.cancellationDate)}\n` +
                              `ملاحظات الإلغاء: ${shipment.cancellationNotes || 'لا توجد ملاحظات'}\n`;
                }

                details += `ملاحظات: ${shipment.notes || 'لا توجد ملاحظات'}`;

                alert(details);
            } catch (error) {
                console.error('❌ خطأ في عرض الشحنة:', error);
                alert('خطأ في عرض الشحنة: ' + error.message);
            }
        }

        // تعديل شحنة
        function editShipment(id) {
            try {
                const shipment = db.getShipmentById(id);
                if (!shipment) {
                    alert('لم يتم العثور على الشحنة');
                    return;
                }

                // عرض معلومات الشحنة الحالية
                document.getElementById('currentShipmentInfo').innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div><strong>رقم التتبع:</strong> ${shipment.trackingNumber}</div>
                        <div><strong>تاريخ الإنشاء:</strong> ${shipment.createdDate}</div>
                        <div><strong>الحالة الحالية:</strong> <span style="color: ${getStatusColor(shipment.status)};">${shipment.status}</span></div>
                        <div><strong>آخر تحديث:</strong> ${shipment.updatedDate || 'لم يتم التحديث'}</div>
                    </div>
                `;

                // ملء النموذج ببيانات الشحنة الحالية
                document.getElementById('editSenderName').value = shipment.senderName;
                document.getElementById('editSenderPhone').value = shipment.senderPhone;
                document.getElementById('editSenderAddress').value = shipment.senderAddress;
                document.getElementById('editReceiverName').value = shipment.receiverName;
                document.getElementById('editReceiverPhone').value = shipment.receiverPhone;
                document.getElementById('editReceiverCity').value = shipment.receiverCity;
                document.getElementById('editReceiverRegion').value = shipment.receiverRegion;
                document.getElementById('editReceiverAddress').value = shipment.receiverAddress;
                document.getElementById('editShipmentType').value = shipment.shipmentType;
                document.getElementById('editWeight').value = shipment.weight;
                document.getElementById('editCost').value = shipment.cost;
                document.getElementById('editPaymentMethod').value = shipment.paymentMethod;
                document.getElementById('editStatus').value = shipment.status;
                document.getElementById('editPriority').value = shipment.priority || 'عادي';
                document.getElementById('editCurrentBranch').value = shipment.currentBranch || '';
                document.getElementById('editNotes').value = shipment.notes || '';

                // تحميل قائمة الفروع
                loadBranchesForEditShipment();

                // حفظ معرف الشحنة للتعديل
                document.getElementById('editShipmentForm').dataset.shipmentId = id;

                // إعداد مستمع الأحداث لنموذج التعديل
                document.getElementById('editShipmentForm').addEventListener('submit', submitEditShipment);

                // إظهار النافذة
                document.getElementById('editShipmentModal').style.display = 'block';

                // التركيز على أول حقل
                document.getElementById('editSenderName').focus();

            } catch (error) {
                console.error('❌ خطأ في فتح نافذة تعديل الشحنة:', error);
                alert('خطأ في فتح نافذة تعديل الشحنة: ' + error.message);
            }
        }

        // إغلاق نافذة تعديل الشحنة
        function closeEditShipmentModal() {
            document.getElementById('editShipmentModal').style.display = 'none';
            document.getElementById('editShipmentForm').reset();
            delete document.getElementById('editShipmentForm').dataset.shipmentId;
        }

        // تحميل قائمة الفروع لتعديل الشحنة
        function loadBranchesForEditShipment() {
            try {
                const branches = db.getActiveBranches();
                const branchSelect = document.getElementById('editCurrentBranch');

                branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

                branches.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.id;
                    option.textContent = branch.name;
                    branchSelect.appendChild(option);
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل الفروع:', error);
            }
        }

        // تنفيذ تعديل الشحنة
        function submitEditShipment(e) {
            e.preventDefault();

            try {
                const shipmentId = document.getElementById('editShipmentForm').dataset.shipmentId;
                if (!shipmentId) {
                    alert('خطأ: لم يتم العثور على معرف الشحنة');
                    return;
                }

                // جمع البيانات المحدثة من النموذج
                const updatedData = {
                    senderName: document.getElementById('editSenderName').value.trim(),
                    senderPhone: document.getElementById('editSenderPhone').value.trim(),
                    senderAddress: document.getElementById('editSenderAddress').value.trim(),
                    receiverName: document.getElementById('editReceiverName').value.trim(),
                    receiverPhone: document.getElementById('editReceiverPhone').value.trim(),
                    receiverCity: document.getElementById('editReceiverCity').value,
                    receiverRegion: document.getElementById('editReceiverRegion').value.trim(),
                    receiverAddress: document.getElementById('editReceiverAddress').value.trim(),
                    shipmentType: document.getElementById('editShipmentType').value,
                    weight: parseFloat(document.getElementById('editWeight').value),
                    cost: parseFloat(document.getElementById('editCost').value),
                    paymentMethod: document.getElementById('editPaymentMethod').value,
                    status: document.getElementById('editStatus').value,
                    priority: document.getElementById('editPriority').value || 'عادي',
                    currentBranch: document.getElementById('editCurrentBranch').value,
                    notes: document.getElementById('editNotes').value.trim(),
                    updatedDate: new Date().toISOString().split('T')[0],
                    updatedTime: new Date().toLocaleTimeString('ar-SA')
                };

                // التحقق من صحة البيانات
                if (!updatedData.senderName || !updatedData.senderPhone || !updatedData.senderAddress ||
                    !updatedData.receiverName || !updatedData.receiverPhone || !updatedData.receiverCity ||
                    !updatedData.receiverRegion || !updatedData.receiverAddress || !updatedData.shipmentType ||
                    !updatedData.weight || !updatedData.cost || !updatedData.paymentMethod || !updatedData.status) {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return;
                }

                // التحقق من صحة أرقام الهواتف
                const phoneRegex = /^\+?[0-9]{10,15}$/;
                if (!phoneRegex.test(updatedData.senderPhone.replace(/\s/g, ''))) {
                    alert('يرجى إدخال رقم هاتف صحيح للمرسل');
                    return;
                }
                if (!phoneRegex.test(updatedData.receiverPhone.replace(/\s/g, ''))) {
                    alert('يرجى إدخال رقم هاتف صحيح للمستقبل');
                    return;
                }

                // التحقق من الوزن والتكلفة
                if (updatedData.weight <= 0) {
                    alert('يرجى إدخال وزن صحيح للشحنة');
                    return;
                }
                if (updatedData.cost <= 0) {
                    alert('يرجى إدخال تكلفة صحيحة للشحنة');
                    return;
                }

                // تحديث الشحنة
                const result = db.updateShipment(shipmentId, updatedData);

                if (result) {
                    alert(`✅ تم تحديث الشحنة بنجاح!\n\n📦 رقم التتبع: ${result.trackingNumber}\n👤 المرسل: ${result.senderName}\n👤 المستقبل: ${result.receiverName}\n📍 المدينة: ${result.receiverCity}\n📋 الحالة: ${result.status}\n💰 التكلفة: ${result.cost} ريال\n\nتم حفظ جميع التعديلات بنجاح.`);
                    closeEditShipmentModal();
                    loadShipments();
                } else {
                    alert('❌ خطأ في تحديث الشحنة. يرجى المحاولة مرة أخرى.');
                }

            } catch (error) {
                console.error('❌ خطأ في تحديث الشحنة:', error);
                alert('خطأ في تحديث الشحنة: ' + error.message);
            }
        }

        // دالة مساعدة للحصول على لون الحالة
        function getStatusColor(status) {
            const statusColors = {
                'معلق': '#ffc107',
                'في الطريق': '#007bff',
                'مسلم': '#28a745',
                'ملغي': '#dc3545',
                'في التحويل': '#17a2b8',
                'في الطريق للفرع': '#6f42c1',
                'في الفرع': '#20c997'
            };
            return statusColors[status] || '#6c757d';
        }

        // حذف شحنة
        function deleteShipment(id) {
            try {
                const shipment = db.getShipmentById(id);
                if (!shipment) {
                    alert('لم يتم العثور على الشحنة');
                    return;
                }

                if (confirm(`هل أنت متأكد من حذف الشحنة ${shipment.trackingNumber}؟`)) {
                    const result = db.deleteShipment(id);
                    if (result) {
                        alert('تم حذف الشحنة بنجاح');
                        loadShipments();
                    } else {
                        alert('خطأ في حذف الشحنة');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في حذف الشحنة:', error);
                alert('خطأ في حذف الشحنة: ' + error.message);
            }
        }

        // متغير لحفظ معرف الشحنة المراد إلغاؤها
        let currentCancelShipmentId = null;

        // متغير لحفظ معرف الشحنة المراد تحويلها
        let currentTransferShipmentId = null;

        // إظهار نافذة إلغاء الشحنة
        function showCancelModal(shipmentId) {
            try {
                const shipment = db.getShipmentById(shipmentId);
                if (!shipment) {
                    alert('لم يتم العثور على الشحنة');
                    return;
                }

                currentCancelShipmentId = shipmentId;

                // عرض معلومات الشحنة
                document.getElementById('shipmentInfo').innerHTML = `
                    <strong>رقم التتبع:</strong> ${shipment.trackingNumber}<br>
                    <strong>المرسل:</strong> ${shipment.senderName}<br>
                    <strong>المستقبل:</strong> ${shipment.receiverName}<br>
                    <strong>الحالة الحالية:</strong> ${shipment.status}<br>
                    <strong>تاريخ الإنشاء:</strong> ${formatDate(shipment.createdDate)}
                `;

                // إعداد مستمع الأحداث لتغيير الفئة
                document.getElementById('cancelCategory').addEventListener('change', loadCancelReasons);

                // إعداد مستمع الأحداث لنموذج الإلغاء
                document.getElementById('cancelForm').addEventListener('submit', submitCancellation);

                // إظهار النافذة
                document.getElementById('cancelModal').style.display = 'block';

            } catch (error) {
                console.error('❌ خطأ في إظهار نافذة الإلغاء:', error);
                alert('خطأ في إظهار نافذة الإلغاء: ' + error.message);
            }
        }

        // إغلاق نافذة الإلغاء
        function closeCancelModal() {
            document.getElementById('cancelModal').style.display = 'none';
            document.getElementById('cancelForm').reset();
            currentCancelShipmentId = null;
        }

        // تحميل أسباب الإلغاء حسب الفئة
        function loadCancelReasons() {
            try {
                const category = document.getElementById('cancelCategory').value;
                const reasonSelect = document.getElementById('cancelReason');

                reasonSelect.innerHTML = '<option value="">اختر السبب</option>';

                if (category) {
                    const reasons = db.getCancellationReasonsByCategory(category);

                    reasons.forEach(reason => {
                        const option = document.createElement('option');
                        option.value = reason.id;
                        option.textContent = reason.reason;
                        reasonSelect.appendChild(option);
                    });
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل أسباب الإلغاء:', error);
            }
        }

        // تنفيذ إلغاء الشحنة
        function submitCancellation(e) {
            e.preventDefault();

            try {
                const reasonId = document.getElementById('cancelReason').value;
                const notes = document.getElementById('cancelNotes').value.trim();

                if (!reasonId) {
                    alert('يرجى اختيار سبب الإلغاء');
                    return;
                }

                const shipment = db.getShipmentById(currentCancelShipmentId);
                const reason = db.getCancellationReasonById(reasonId);

                if (!shipment || !reason) {
                    alert('خطأ في البيانات');
                    return;
                }

                if (confirm(`هل أنت متأكد من إلغاء الشحنة؟\n\nالشحنة: ${shipment.trackingNumber}\nالسبب: ${reason.reason}`)) {
                    const cancelledShipment = db.cancelShipment(currentCancelShipmentId, reasonId, notes);

                    if (cancelledShipment) {
                        alert(`تم إلغاء الشحنة بنجاح\n\nرقم الشحنة: ${shipment.trackingNumber}\nسبب الإلغاء: ${reason.reason}`);
                        closeCancelModal();
                        loadShipments();
                    } else {
                        alert('خطأ في إلغاء الشحنة');
                    }
                }

            } catch (error) {
                console.error('❌ خطأ في إلغاء الشحنة:', error);
                alert('خطأ في إلغاء الشحنة: ' + error.message);
            }
        }

        // دوال مساعدة
        function getStatusClass(status) {
            const statusMap = {
                'معلق': 'pending',
                'في الطريق': 'transit',
                'مسلم': 'delivered',
                'ملغي': 'cancelled'
            };
            return statusMap[status] || 'pending';
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // إظهار نافذة تحويل الشحنة
        function showTransferModal(shipmentId) {
            try {
                const shipment = db.getShipmentById(shipmentId);
                if (!shipment) {
                    alert('لم يتم العثور على الشحنة');
                    return;
                }

                currentTransferShipmentId = shipmentId;

                // عرض معلومات الشحنة
                document.getElementById('transferShipmentInfo').innerHTML = `
                    <strong>رقم التتبع:</strong> ${shipment.trackingNumber}<br>
                    <strong>المرسل:</strong> ${shipment.senderName}<br>
                    <strong>المستقبل:</strong> ${shipment.receiverName}<br>
                    <strong>الحالة الحالية:</strong> ${shipment.status}<br>
                    <strong>الفرع الحالي:</strong> ${shipment.currentBranch || 'غير محدد'}
                `;

                // تحميل قائمة الفروع
                loadBranchesForTransfer();

                // إعداد مستمع الأحداث لنموذج التحويل
                document.getElementById('transferForm').addEventListener('submit', submitTransfer);

                // إظهار النافذة
                document.getElementById('transferModal').style.display = 'block';

            } catch (error) {
                console.error('❌ خطأ في إظهار نافذة التحويل:', error);
                alert('خطأ في إظهار نافذة التحويل: ' + error.message);
            }
        }

        // إغلاق نافذة التحويل
        function closeTransferModal() {
            document.getElementById('transferModal').style.display = 'none';
            document.getElementById('transferForm').reset();
            currentTransferShipmentId = null;
        }

        // تحميل قائمة الفروع للتحويل
        function loadBranchesForTransfer() {
            try {
                const branches = db.getActiveBranches();
                const fromBranchSelect = document.getElementById('fromBranch');
                const toBranchSelect = document.getElementById('toBranch');

                fromBranchSelect.innerHTML = '<option value="">اختر الفرع المرسل</option>';
                toBranchSelect.innerHTML = '<option value="">اختر الفرع المستقبل</option>';

                branches.forEach(branch => {
                    const option1 = document.createElement('option');
                    option1.value = branch.id;
                    option1.textContent = branch.name;
                    fromBranchSelect.appendChild(option1);

                    const option2 = document.createElement('option');
                    option2.value = branch.id;
                    option2.textContent = branch.name;
                    toBranchSelect.appendChild(option2);
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل الفروع:', error);
            }
        }

        // تنفيذ تحويل الشحنة
        function submitTransfer(e) {
            e.preventDefault();

            try {
                const fromBranchId = document.getElementById('fromBranch').value;
                const toBranchId = document.getElementById('toBranch').value;
                const priority = document.getElementById('transferPriority').value;
                const reason = document.getElementById('transferReason').value.trim();
                const notes = document.getElementById('transferNotes').value.trim();

                if (!fromBranchId || !toBranchId) {
                    alert('يرجى اختيار الفرع المرسل والمستقبل');
                    return;
                }

                if (fromBranchId === toBranchId) {
                    alert('لا يمكن تحويل الشحنة إلى نفس الفرع');
                    return;
                }

                const shipment = db.getShipmentById(currentTransferShipmentId);
                const fromBranch = db.getBranchById(fromBranchId);
                const toBranch = db.getBranchById(toBranchId);

                if (!shipment || !fromBranch || !toBranch) {
                    alert('خطأ في البيانات');
                    return;
                }

                const transferData = {
                    shipmentId: currentTransferShipmentId,
                    fromBranchId: fromBranchId,
                    toBranchId: toBranchId,
                    fromBranchName: fromBranch.name,
                    toBranchName: toBranch.name,
                    priority: priority,
                    transferReason: reason || 'تحويل عادي',
                    notes: notes,
                    requestedBy: 'المستخدم الحالي'
                };

                if (confirm(`هل أنت متأكد من تحويل الشحنة؟\n\nالشحنة: ${shipment.trackingNumber}\nمن: ${fromBranch.name}\nإلى: ${toBranch.name}`)) {
                    const newTransfer = db.createBranchTransfer(transferData);

                    if (newTransfer) {
                        alert(`تم إنشاء التحويل بنجاح!\n\nرقم التتبع: ${newTransfer.trackingNumber}\nمن: ${fromBranch.name}\nإلى: ${toBranch.name}`);
                        closeTransferModal();
                        loadShipments();
                    } else {
                        alert('خطأ في إنشاء التحويل');
                    }
                }

            } catch (error) {
                console.error('❌ خطأ في تحويل الشحنة:', error);
                alert('خطأ في تحويل الشحنة: ' + error.message);
            }
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const addShipmentModal = document.getElementById('addShipmentModal');
            const editShipmentModal = document.getElementById('editShipmentModal');
            const cancelModal = document.getElementById('cancelModal');
            const transferModal = document.getElementById('transferModal');

            if (event.target === addShipmentModal) {
                closeAddShipmentModal();
            }
            if (event.target === editShipmentModal) {
                closeEditShipmentModal();
            }
            if (event.target === cancelModal) {
                closeCancelModal();
            }
            if (event.target === transferModal) {
                closeTransferModal();
            }
        }

        // تحسين تجربة المستخدم - تنسيق أرقام الهواتف تلقائياً
        document.addEventListener('DOMContentLoaded', function() {
            // تنسيق أرقام الهواتف
            setTimeout(() => {
                const phoneInputs = ['senderPhone', 'receiverPhone'];
                phoneInputs.forEach(inputId => {
                    const phoneInput = document.getElementById(inputId);
                    if (phoneInput) {
                        phoneInput.addEventListener('input', function() {
                            let value = this.value.replace(/[^0-9+]/g, '');
                            if (value && !value.startsWith('+')) {
                                value = '+' + value;
                            }
                            this.value = value;
                        });
                    }
                });

                // تحديث التكلفة تلقائياً بناءً على نظام التسعير الجديد (نموذج الإضافة)
                const weightInput = document.getElementById('weight');
                const typeSelect = document.getElementById('shipmentType');
                const citySelect = document.getElementById('receiverCity');
                const costInput = document.getElementById('cost');

                if (weightInput && typeSelect && costInput && citySelect) {
                    function calculateCost() {
                        try {
                            const weight = parseFloat(weightInput.value) || 0;
                            const type = typeSelect.value;
                            const city = citySelect.value;

                            if (weight > 0 && type && city) {
                                // استخدام نظام التسعير الجديد
                                const shipmentData = {
                                    weight: weight,
                                    shipmentType: type,
                                    receiverCity: city
                                };

                                const calculatedCost = db.calculateShipmentCost(shipmentData);
                                if (calculatedCost > 0) {
                                    costInput.value = calculatedCost.toFixed(2);
                                }
                            }
                        } catch (error) {
                            console.error('خطأ في حساب التكلفة:', error);
                            // العودة للحساب التقليدي في حالة الخطأ
                            const weight = parseFloat(weightInput.value) || 0;
                            const type = typeSelect.value;

                            let baseCost = 15;
                            let weightCost = weight * 5;

                            switch(type) {
                                case 'مستندات':
                                    baseCost = 10;
                                    weightCost = weight * 2;
                                    break;
                                case 'طرد صغير':
                                    baseCost = 15;
                                    weightCost = weight * 5;
                                    break;
                                case 'طرد متوسط':
                                    baseCost = 20;
                                    weightCost = weight * 7;
                                    break;
                                case 'طرد كبير':
                                    baseCost = 30;
                                    weightCost = weight * 10;
                                    break;
                                case 'أجهزة إلكترونية':
                                    baseCost = 25;
                                    weightCost = weight * 8;
                                    break;
                                default:
                                    baseCost = 15;
                                    weightCost = weight * 5;
                            }

                            const totalCost = baseCost + weightCost;
                            if (totalCost > 0) {
                                costInput.value = totalCost.toFixed(2);
                            }
                        }
                    }

                    weightInput.addEventListener('input', calculateCost);
                    typeSelect.addEventListener('change', calculateCost);
                    citySelect.addEventListener('change', calculateCost);
                }

                // تحديث التكلفة تلقائياً بناءً على الوزن ونوع الشحنة (نموذج التعديل)
                const editWeightInput = document.getElementById('editWeight');
                const editTypeSelect = document.getElementById('editShipmentType');
                const editCostInput = document.getElementById('editCost');

                if (editWeightInput && editTypeSelect && editCostInput) {
                    function calculateEditCost() {
                        const weight = parseFloat(editWeightInput.value) || 0;
                        const type = editTypeSelect.value;

                        let baseCost = 15; // تكلفة أساسية
                        let weightCost = weight * 5; // 5 ريال لكل كيلو

                        // تعديل التكلفة حسب نوع الشحنة
                        switch(type) {
                            case 'مستندات':
                                baseCost = 10;
                                weightCost = weight * 2;
                                break;
                            case 'طرد صغير':
                                baseCost = 15;
                                weightCost = weight * 5;
                                break;
                            case 'طرد متوسط':
                                baseCost = 20;
                                weightCost = weight * 7;
                                break;
                            case 'طرد كبير':
                                baseCost = 30;
                                weightCost = weight * 10;
                                break;
                            case 'أجهزة إلكترونية':
                                baseCost = 25;
                                weightCost = weight * 8;
                                break;
                            default:
                                baseCost = 15;
                                weightCost = weight * 5;
                        }

                        const totalCost = baseCost + weightCost;
                        if (totalCost > 0) {
                            editCostInput.value = totalCost.toFixed(2);
                        }
                    }

                    editWeightInput.addEventListener('input', calculateEditCost);
                    editTypeSelect.addEventListener('change', calculateEditCost);
                }

                // تنسيق أرقام الهواتف في نموذج التعديل
                const editPhoneInputs = ['editSenderPhone', 'editReceiverPhone'];
                editPhoneInputs.forEach(inputId => {
                    const phoneInput = document.getElementById(inputId);
                    if (phoneInput) {
                        phoneInput.addEventListener('input', function() {
                            let value = this.value.replace(/[^0-9+]/g, '');
                            if (value && !value.startsWith('+')) {
                                value = '+' + value;
                            }
                            this.value = value;
                        });
                    }
                });
            }, 100);
        });
    </script>
</body>
</html>
