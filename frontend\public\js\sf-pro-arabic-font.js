/**
 * SF Pro Arabic Font Applier
 * مطبق خط SF Pro Arabic على جميع العناصر
 */

(function() {
    'use strict';

    // إعدادات الخط
    const FONT_CONFIG = {
        fontFamily: "'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
        fontWeight: '600',
        webkitFontSmoothing: 'antialiased',
        mozOsxFontSmoothing: 'grayscale',
        textRendering: 'optimizeLegibility'
    };

    // تطبيق الخط على عنصر واحد
    function applyFontToElement(element) {
        if (!element || element.nodeType !== 1) return;

        try {
            element.style.fontFamily = FONT_CONFIG.fontFamily;
            element.style.fontWeight = FONT_CONFIG.fontWeight;
            element.style.webkitFontSmoothing = FONT_CONFIG.webkitFontSmoothing;
            element.style.mozOsxFontSmoothing = FONT_CONFIG.mozOsxFontSmoothing;
            element.style.textRendering = FONT_CONFIG.textRendering;
        } catch (error) {
            console.warn('خطأ في تطبيق الخط على العنصر:', error);
        }
    }

    // تطبيق الخط على جميع العناصر الموجودة
    function applyFontToAllElements() {
        try {
            // تطبيق على body أولاً
            applyFontToElement(document.body);

            // تطبيق على جميع العناصر
            const allElements = document.querySelectorAll('*');
            allElements.forEach(applyFontToElement);

            console.log(`✅ تم تطبيق خط SF Pro AR Display على ${allElements.length} عنصر`);
        } catch (error) {
            console.error('خطأ في تطبيق الخط على جميع العناصر:', error);
        }
    }

    // إضافة CSS عام للخط
    function addGlobalFontCSS() {
        try {
            // التحقق من وجود style tag مسبقاً
            if (document.getElementById('sf-pro-arabic-global-font')) {
                return;
            }

            const style = document.createElement('style');
            style.id = 'sf-pro-arabic-global-font';
            style.textContent = `
                /* SF Pro Arabic Global Font Styles */
                * {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                    -webkit-font-smoothing: ${FONT_CONFIG.webkitFontSmoothing} !important;
                    -moz-osx-font-smoothing: ${FONT_CONFIG.mozOsxFontSmoothing} !important;
                    text-rendering: ${FONT_CONFIG.textRendering} !important;
                }

                body {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                h1, h2, h3, h4, h5, h6 {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                p, span, div, a, button, input, textarea, select, label {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                table, th, td, ul, ol, li {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                .btn, .button, .link, .nav-link, .menu-link, .tab-button {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                .card, .container, .wrapper, .section, .panel {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                .sidebar, .menu, .nav, .navbar, .header, .footer {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                .alert, .notification, .message, .toast {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                .modal, .popup, .dialog, .overlay {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                /* عناصر خاصة بنظام الصلاحيات */
                .permission-card, .role-chip, .permission-item, .permission-label {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                .settings-container, .settings-header, .settings-tabs, .tab-content {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                .test-section, .test-result, .test-container {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                .stats-grid, .stat-card, .stat-number, .stat-label {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }

                /* عناصر ديناميكية */
                .dynamic-content, .generated-content, .ajax-content {
                    font-family: ${FONT_CONFIG.fontFamily} !important;
                    font-weight: ${FONT_CONFIG.fontWeight} !important;
                }
            `;

            document.head.appendChild(style);
            console.log('✅ تم إضافة CSS عام لخط SF Pro AR Display');
        } catch (error) {
            console.error('خطأ في إضافة CSS عام للخط:', error);
        }
    }

    // مراقب للعناصر الجديدة
    function setupMutationObserver() {
        if (!window.MutationObserver) {
            console.warn('MutationObserver غير مدعوم في هذا المتصفح');
            return;
        }

        try {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) { // Element node
                            applyFontToElement(node);
                            
                            // تطبيق على العناصر الفرعية
                            const childElements = node.querySelectorAll('*');
                            childElements.forEach(applyFontToElement);
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: false,
                characterData: false
            });

            console.log('✅ تم تفعيل مراقب العناصر الجديدة للخط');
        } catch (error) {
            console.error('خطأ في إعداد مراقب العناصر:', error);
        }
    }

    // تهيئة النظام
    function initializeFontSystem() {
        console.log('🔤 بدء تهيئة نظام خط SF Pro AR Display...');

        // إضافة CSS عام
        addGlobalFontCSS();

        // تطبيق الخط على العناصر الموجودة
        applyFontToAllElements();

        // إعداد مراقب للعناصر الجديدة
        setupMutationObserver();

        console.log('✅ تم تهيئة نظام الخط بنجاح');
    }

    // تشغيل النظام عند تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeFontSystem);
    } else {
        initializeFontSystem();
    }

    // تشغيل النظام عند تحميل النافذة (للتأكد)
    window.addEventListener('load', function() {
        setTimeout(applyFontToAllElements, 100);
    });

    // إعادة تطبيق الخط كل 5 ثوانٍ (للعناصر الديناميكية)
    setInterval(function() {
        const elementsWithoutFont = document.querySelectorAll('*:not([style*="font-family"])');
        if (elementsWithoutFont.length > 0) {
            console.log(`🔄 إعادة تطبيق الخط على ${elementsWithoutFont.length} عنصر جديد`);
            elementsWithoutFont.forEach(applyFontToElement);
        }
    }, 5000);

    // تصدير الوظائف للاستخدام الخارجي
    window.SFProArabicFont = {
        apply: applyFontToAllElements,
        applyToElement: applyFontToElement,
        config: FONT_CONFIG
    };

})();

console.log('🔤 تم تحميل نظام خط SF Pro AR Display Semibold');
