// Logging Utility
// أداة السجلات

import winston from 'winston'
import { config } from '../config/config'
import path from 'path'
import fs from 'fs'

// Ensure logs directory exists
const logsDir = path.dirname(config.logging.file)
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true })
}

// Custom format for Arabic/English logs
const customFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`
    
    // Add stack trace for errors
    if (stack) {
      logMessage += `\nStack: ${stack}`
    }
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += `\nMeta: ${JSON.stringify(meta, null, 2)}`
    }
    
    return logMessage
  })
)

// Create logger instance
export const logger = winston.createLogger({
  level: config.logging.level,
  format: customFormat,
  defaultMeta: {
    service: 'shipment-management-api',
    environment: config.nodeEnv
  },
  transports: [
    // File transport for all logs
    new winston.transports.File({
      filename: config.logging.file,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Separate file for errors
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    })
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log')
    })
  ],
  
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log')
    })
  ]
})

// Add console transport for development
if (config.nodeEnv === 'development') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, stack }) => {
        let logMessage = `${timestamp} ${level}: ${message}`
        if (stack) {
          logMessage += `\n${stack}`
        }
        return logMessage
      })
    )
  }))
}

// Helper functions for structured logging
export const loggers = {
  // Authentication logs
  auth: {
    login: (userId: string, email: string, ip?: string) => {
      logger.info('User login successful', {
        userId,
        email,
        ip,
        action: 'login'
      })
    },
    
    loginFailed: (email: string, reason: string, ip?: string) => {
      logger.warn('User login failed', {
        email,
        reason,
        ip,
        action: 'login_failed'
      })
    },
    
    logout: (userId: string, email: string) => {
      logger.info('User logout', {
        userId,
        email,
        action: 'logout'
      })
    },
    
    register: (userId: string, email: string) => {
      logger.info('User registration successful', {
        userId,
        email,
        action: 'register'
      })
    }
  },
  
  // Shipment logs
  shipment: {
    created: (shipmentId: string, trackingNumber: string, userId: string) => {
      logger.info('Shipment created', {
        shipmentId,
        trackingNumber,
        userId,
        action: 'shipment_created'
      })
    },
    
    updated: (shipmentId: string, trackingNumber: string, userId: string, changes: object) => {
      logger.info('Shipment updated', {
        shipmentId,
        trackingNumber,
        userId,
        changes,
        action: 'shipment_updated'
      })
    },
    
    statusChanged: (shipmentId: string, trackingNumber: string, oldStatus: string, newStatus: string, userId: string) => {
      logger.info('Shipment status changed', {
        shipmentId,
        trackingNumber,
        oldStatus,
        newStatus,
        userId,
        action: 'shipment_status_changed'
      })
    },
    
    delivered: (shipmentId: string, trackingNumber: string, distributorId: string) => {
      logger.info('Shipment delivered', {
        shipmentId,
        trackingNumber,
        distributorId,
        action: 'shipment_delivered'
      })
    }
  },
  
  // API logs
  api: {
    request: (method: string, url: string, userId?: string, ip?: string) => {
      logger.debug('API request', {
        method,
        url,
        userId,
        ip,
        action: 'api_request'
      })
    },
    
    error: (method: string, url: string, error: Error, userId?: string, ip?: string) => {
      logger.error('API error', {
        method,
        url,
        error: error.message,
        stack: error.stack,
        userId,
        ip,
        action: 'api_error'
      })
    },
    
    rateLimited: (ip: string, endpoint: string) => {
      logger.warn('Rate limit exceeded', {
        ip,
        endpoint,
        action: 'rate_limited'
      })
    }
  },
  
  // Database logs
  database: {
    connected: () => {
      logger.info('Database connected successfully', {
        action: 'database_connected'
      })
    },
    
    disconnected: () => {
      logger.info('Database disconnected', {
        action: 'database_disconnected'
      })
    },
    
    error: (error: Error, query?: string) => {
      logger.error('Database error', {
        error: error.message,
        stack: error.stack,
        query,
        action: 'database_error'
      })
    },
    
    migration: (migration: string, status: 'started' | 'completed' | 'failed') => {
      logger.info(`Database migration ${status}`, {
        migration,
        status,
        action: 'database_migration'
      })
    }
  },
  
  // Security logs
  security: {
    suspiciousActivity: (userId: string, activity: string, details: object, ip?: string) => {
      logger.warn('Suspicious activity detected', {
        userId,
        activity,
        details,
        ip,
        action: 'suspicious_activity'
      })
    },
    
    unauthorizedAccess: (endpoint: string, ip?: string, userAgent?: string) => {
      logger.warn('Unauthorized access attempt', {
        endpoint,
        ip,
        userAgent,
        action: 'unauthorized_access'
      })
    },
    
    tokenExpired: (userId: string, tokenType: string) => {
      logger.info('Token expired', {
        userId,
        tokenType,
        action: 'token_expired'
      })
    }
  },
  
  // System logs
  system: {
    startup: () => {
      logger.info('Application started', {
        nodeVersion: process.version,
        platform: process.platform,
        environment: config.nodeEnv,
        action: 'system_startup'
      })
    },
    
    shutdown: () => {
      logger.info('Application shutdown', {
        action: 'system_shutdown'
      })
    },
    
    healthCheck: (status: 'healthy' | 'unhealthy', details?: object) => {
      logger.info(`Health check: ${status}`, {
        status,
        details,
        action: 'health_check'
      })
    }
  }
}

// Export default logger
export default logger
