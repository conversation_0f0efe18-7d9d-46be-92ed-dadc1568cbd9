<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة عمولات المناديب - النظام المالي</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: var(--font-arabic-display);
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total {
            border-left-color: #17a2b8;
        }

        .stat-card.unpaid {
            border-left-color: #ffc107;
        }

        .stat-card.paid {
            border-left-color: #28a745;
        }

        .stat-card.distributors {
            border-left-color: #6f42c1;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .card-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: var(--font-english-display);
        }

        .card-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input,
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            min-width: 200px;
        }

        .search-input:focus,
        .filter-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .commissions-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-calculated {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .status-paid {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .status-pending {
            background: rgba(23, 162, 184, 0.2);
            color: #0c5460;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-data h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .commission-calculator {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .calculator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
        }

        .form-input {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: var(--font-arabic-display);
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input,
            .filter-select {
                min-width: auto;
            }

            .calculator-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                justify-content: center;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <a href="hr-management.html" class="back-link">← العودة للموارد البشرية</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>🤝</span>
                <span>إدارة عمولات المناديب</span>
            </div>

            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="hr-management.html">الموارد البشرية</a>
                <a href="distributors-management.html">إدارة المناديب</a>
                <a href="commission-management.html" class="active">العمولات</a>
                <a href="financial-system.html">النظام المالي</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">🤝 إدارة عمولات المناديب</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="calculateAllCommissions()">
                    🧮 حساب جميع العمولات
                </button>
                <button class="btn" onclick="loadCommissions()">
                    🔄 تحديث القائمة
                </button>
                <a href="distributors-management.html" class="btn btn-info">
                    👥 إدارة المناديب
                </a>
            </div>
        </div>

        <!-- إحصائيات العمولات -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="card-icon">💰</div>
                <div class="card-amount" id="totalCommissions">0</div>
                <div class="card-label">إجمالي العمولات</div>
            </div>

            <div class="stat-card unpaid">
                <div class="card-icon">⏳</div>
                <div class="card-amount" id="unpaidCommissions">0</div>
                <div class="card-label">عمولات غير مسددة</div>
            </div>

            <div class="stat-card paid">
                <div class="card-icon">✅</div>
                <div class="card-amount" id="paidCommissions">0</div>
                <div class="card-label">عمولات مسددة</div>
            </div>

            <div class="stat-card distributors">
                <div class="card-icon">👥</div>
                <div class="card-amount" id="activeDistributors">0</div>
                <div class="card-label">مناديب نشطين</div>
            </div>
        </div>

        <!-- حاسبة العمولات -->
        <div class="commission-calculator">
            <h2>🧮 حاسبة العمولات</h2>
            <div class="calculator-grid">
                <div class="form-group">
                    <label class="form-label">المندوب</label>
                    <select class="form-input" id="distributorSelect">
                        <option value="">اختر المندوب</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">الفترة (شهر/سنة)</label>
                    <input type="month" class="form-input" id="commissionPeriod">
                </div>

                <div class="form-group">
                    <label class="form-label">معدل العمولة (ريال/شحنة)</label>
                    <input type="number" class="form-input" id="commissionRate" value="10" step="0.01">
                </div>

                <div class="form-group">
                    <button class="btn btn-success" onclick="calculateCommission()">
                        🧮 حساب العمولة
                    </button>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم والبحث -->
        <div class="controls">
            <input type="text" id="searchInput" class="search-input" placeholder="🔍 البحث في العمولات...">

            <select id="statusFilter" class="filter-select">
                <option value="">جميع الحالات</option>
                <option value="محسوب">محسوب</option>
                <option value="مسدد">مسدد</option>
                <option value="معلق">معلق</option>
            </select>

            <select id="distributorFilter" class="filter-select">
                <option value="">جميع المناديب</option>
            </select>

            <button class="btn" onclick="clearFilters()">مسح الفلاتر</button>
        </div>

        <!-- جدول العمولات -->
        <div class="commissions-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم العمولة</th>
                        <th>المندوب</th>
                        <th>الفترة</th>
                        <th>عدد الشحنات</th>
                        <th>معدل العمولة</th>
                        <th>إجمالي العمولة</th>
                        <th>الحالة</th>
                        <th>تاريخ الحساب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="commissionsTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>

            <!-- رسالة عدم وجود بيانات -->
            <div id="noDataMessage" class="no-data" style="display: none;">
                <h3>🤝 لا توجد عمولات</h3>
                <p>لم يتم العثور على أي عمولات محسوبة. ابدأ بحساب العمولات للمناديب.</p>
                <button class="btn btn-success" onclick="calculateAllCommissions()" style="margin-top: 15px;">
                    🧮 حساب جميع العمولات
                </button>
            </div>
        </div>
    </main>

    <script src="js/database-simple.js"></script>
    <script src="js/financial-database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🤝 تحميل صفحة إدارة العمولات...');

            try {
                loadCommissions();
                loadDistributors();
                setupEventListeners();
                loadStats();

                // تعيين الشهر الحالي كافتراضي
                const currentDate = new Date();
                const currentMonth = currentDate.toISOString().slice(0, 7);
                document.getElementById('commissionPeriod').value = currentMonth;

                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterCommissions);
            document.getElementById('statusFilter').addEventListener('change', filterCommissions);
            document.getElementById('distributorFilter').addEventListener('change', filterCommissions);
        }

        // تحميل قائمة المناديب
        function loadDistributors() {
            try {
                const distributors = db.getAllDistributors();
                const distributorSelect = document.getElementById('distributorSelect');
                const distributorFilter = document.getElementById('distributorFilter');

                // مسح الخيارات الحالية
                distributorSelect.innerHTML = '<option value="">اختر المندوب</option>';
                distributorFilter.innerHTML = '<option value="">جميع المناديب</option>';

                distributors.forEach(distributor => {
                    const option1 = document.createElement('option');
                    option1.value = distributor.id;
                    option1.textContent = distributor.name;
                    distributorSelect.appendChild(option1);

                    const option2 = document.createElement('option');
                    option2.value = distributor.name;
                    option2.textContent = distributor.name;
                    distributorFilter.appendChild(option2);
                });

            } catch (error) {
                console.error('❌ خطأ في تحميل المناديب:', error);
            }
        }

        // تحميل وعرض العمولات
        function loadCommissions() {
            try {
                const commissions = financialDb.getAllCommissions();
                displayCommissions(commissions);
                console.log('🤝 تم تحميل', commissions.length, 'عمولة');
            } catch (error) {
                console.error('❌ خطأ في تحميل العمولات:', error);
                alert('خطأ في تحميل العمولات: ' + error.message);
            }
        }

        // عرض العمولات في الجدول
        function displayCommissions(commissions) {
            const tbody = document.getElementById('commissionsTableBody');
            const noDataMessage = document.getElementById('noDataMessage');

            if (commissions.length === 0) {
                tbody.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }

            noDataMessage.style.display = 'none';

            tbody.innerHTML = commissions.map(commission =>
                `<tr>
                    <td><strong>${commission.id}</strong></td>
                    <td>${commission.distributorName}</td>
                    <td>${commission.period}</td>
                    <td>${commission.totalDeliveries}</td>
                    <td>${commission.commissionRate.toFixed(2)} ريال</td>
                    <td><strong>${commission.totalAmount.toFixed(2)} ${commission.currency}</strong></td>
                    <td><span class="status-badge status-${getStatusClass(commission.status)}">${commission.status}</span></td>
                    <td>${formatDate(commission.calculatedDate)}</td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-small" onclick="viewCommission('${commission.id}')">عرض</button>
                            ${commission.status === 'محسوب' ?
                                `<button class="btn btn-small btn-success" onclick="payCommission('${commission.id}')">سداد</button>` :
                                ''}
                            <button class="btn btn-small btn-warning" onclick="editCommission('${commission.id}')">تعديل</button>
                        </div>
                    </td>
                </tr>`
            ).join('');
        }

        // تحميل الإحصائيات
        function loadStats() {
            try {
                const commissions = financialDb.getAllCommissions();
                const distributors = db.getAllDistributors();

                const totalAmount = commissions.reduce((sum, c) => sum + (parseFloat(c.totalAmount) || 0), 0);
                const unpaidAmount = commissions.filter(c => c.status === 'محسوب').reduce((sum, c) => sum + (parseFloat(c.totalAmount) || 0), 0);
                const paidAmount = commissions.filter(c => c.status === 'مسدد').reduce((sum, c) => sum + (parseFloat(c.totalAmount) || 0), 0);
                const activeCount = distributors.filter(d => d.isAvailable).length;

                document.getElementById('totalCommissions').textContent = totalAmount.toFixed(2);
                document.getElementById('unpaidCommissions').textContent = unpaidAmount.toFixed(2);
                document.getElementById('paidCommissions').textContent = paidAmount.toFixed(2);
                document.getElementById('activeDistributors').textContent = activeCount;

            } catch (error) {
                console.error('❌ خطأ في تحميل الإحصائيات:', error);
            }
        }

        // حساب عمولة مندوب واحد
        function calculateCommission() {
            try {
                const distributorId = document.getElementById('distributorSelect').value;
                const period = document.getElementById('commissionPeriod').value;
                const commissionRate = parseFloat(document.getElementById('commissionRate').value) || 10;

                if (!distributorId || !period) {
                    alert('يرجى اختيار المندوب والفترة');
                    return;
                }

                const commissionData = financialDb.calculateDistributorCommission(distributorId, period);

                if (!commissionData) {
                    alert('لم يتم العثور على المندوب أو لا توجد شحنات في هذه الفترة');
                    return;
                }

                // تحديث معدل العمولة
                commissionData.commissionRate = commissionRate;
                commissionData.totalAmount = commissionData.totalDeliveries * commissionRate;

                const savedCommission = financialDb.addCommission(commissionData);

                if (savedCommission) {
                    alert(`تم حساب العمولة بنجاح!\n\nالمندوب: ${commissionData.distributorName}\nعدد الشحنات: ${commissionData.totalDeliveries}\nإجمالي العمولة: ${commissionData.totalAmount.toFixed(2)} ريال`);
                    loadCommissions();
                    loadStats();
                } else {
                    alert('خطأ في حفظ العمولة');
                }

            } catch (error) {
                console.error('❌ خطأ في حساب العمولة:', error);
                alert('خطأ في حساب العمولة: ' + error.message);
            }
        }

        // حساب جميع العمولات
        function calculateAllCommissions() {
            try {
                const distributors = db.getAllDistributors();
                const currentDate = new Date();
                const currentPeriod = currentDate.toISOString().slice(0, 7); // YYYY-MM
                const commissionRate = 10; // ريال لكل شحنة

                let calculatedCount = 0;
                let totalAmount = 0;

                distributors.forEach(distributor => {
                    const commissionData = financialDb.calculateDistributorCommission(distributor.id, currentPeriod);

                    if (commissionData && commissionData.totalDeliveries > 0) {
                        commissionData.commissionRate = commissionRate;
                        commissionData.totalAmount = commissionData.totalDeliveries * commissionRate;

                        const savedCommission = financialDb.addCommission(commissionData);
                        if (savedCommission) {
                            calculatedCount++;
                            totalAmount += commissionData.totalAmount;
                        }
                    }
                });

                if (calculatedCount > 0) {
                    alert(`تم حساب العمولات بنجاح!\n\nعدد المناديب: ${calculatedCount}\nإجمالي العمولات: ${totalAmount.toFixed(2)} ريال`);
                    loadCommissions();
                    loadStats();
                } else {
                    alert('لا توجد شحنات مسلمة في الفترة الحالية لحساب العمولات');
                }

            } catch (error) {
                console.error('❌ خطأ في حساب جميع العمولات:', error);
                alert('خطأ في حساب جميع العمولات: ' + error.message);
            }
        }

        // فلترة العمولات
        function filterCommissions() {
            try {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;
                const distributorFilter = document.getElementById('distributorFilter').value;

                const allCommissions = financialDb.getAllCommissions();

                const filteredCommissions = allCommissions.filter(commission => {
                    const matchesSearch = !searchTerm ||
                        commission.id.toLowerCase().includes(searchTerm) ||
                        commission.distributorName.toLowerCase().includes(searchTerm) ||
                        commission.period.includes(searchTerm);

                    const matchesStatus = !statusFilter || commission.status === statusFilter;
                    const matchesDistributor = !distributorFilter || commission.distributorName === distributorFilter;

                    return matchesSearch && matchesStatus && matchesDistributor;
                });

                displayCommissions(filteredCommissions);
            } catch (error) {
                console.error('❌ خطأ في فلترة العمولات:', error);
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('distributorFilter').value = '';
            loadCommissions();
        }

        // عرض تفاصيل العمولة
        function viewCommission(id) {
            try {
                const commission = financialDb.getAllCommissions().find(c => c.id === id);
                if (!commission) {
                    alert('لم يتم العثور على العمولة');
                    return;
                }

                alert(`تفاصيل العمولة ${commission.id}:\n\n` +
                      `المندوب: ${commission.distributorName}\n` +
                      `الفترة: ${commission.period}\n` +
                      `عدد الشحنات المسلمة: ${commission.totalDeliveries}\n` +
                      `معدل العمولة: ${commission.commissionRate.toFixed(2)} ريال/شحنة\n` +
                      `إجمالي العمولة: ${commission.totalAmount.toFixed(2)} ${commission.currency}\n` +
                      `الحالة: ${commission.status}\n` +
                      `تاريخ الحساب: ${formatDate(commission.calculatedDate)}\n` +
                      `تاريخ السداد: ${commission.paidDate ? formatDate(commission.paidDate) : 'لم يتم السداد'}\n` +
                      `ملاحظات: ${commission.notes || 'لا توجد ملاحظات'}`);
            } catch (error) {
                console.error('❌ خطأ في عرض العمولة:', error);
                alert('خطأ في عرض العمولة: ' + error.message);
            }
        }

        // سداد العمولة
        function payCommission(id) {
            try {
                const commission = financialDb.getAllCommissions().find(c => c.id === id);
                if (!commission) {
                    alert('لم يتم العثور على العمولة');
                    return;
                }

                if (confirm(`هل أنت متأكد من سداد العمولة؟\n\nالمندوب: ${commission.distributorName}\nالمبلغ: ${commission.totalAmount.toFixed(2)} ${commission.currency}`)) {
                    const updatedCommission = financialDb.updateCommission(id, {
                        status: 'مسدد',
                        paidDate: new Date().toISOString().split('T')[0]
                    });

                    if (updatedCommission) {
                        alert('تم سداد العمولة بنجاح');
                        loadCommissions();
                        loadStats();
                    } else {
                        alert('خطأ في سداد العمولة');
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في سداد العمولة:', error);
                alert('خطأ في سداد العمولة: ' + error.message);
            }
        }

        // تعديل العمولة
        function editCommission(id) {
            try {
                const commission = financialDb.getAllCommissions().find(c => c.id === id);
                if (!commission) {
                    alert('لم يتم العثور على العمولة');
                    return;
                }

                // إنشاء نافذة التعديل
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                    align-items: center; justify-content: center;
                `;

                modal.innerHTML = `
                    <div style="background: white; padding: 30px; border-radius: 20px; width: 90%; max-width: 500px; max-height: 90vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px; color: #333;">✏️ تعديل العمولة</h3>

                        <form id="editCommissionForm">
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم العمولة:</label>
                                <input type="text" value="${commission.id}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;" readonly>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم المندوب:</label>
                                <input type="text" id="editDistributorName" value="${commission.distributorName}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الفترة:</label>
                                <input type="month" id="editPeriod" value="${commission.period}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">عدد الشحنات:</label>
                                <input type="number" id="editTotalDeliveries" value="${commission.totalDeliveries}" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">معدل العمولة (ريال/شحنة):</label>
                                <input type="number" id="editCommissionRate" value="${commission.commissionRate}" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">إجمالي العمولة:</label>
                                <input type="number" id="editTotalAmount" value="${commission.totalAmount}" step="0.01" min="0"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;" required>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">العملة:</label>
                                <select id="editCurrency" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                                    <option value="SAR" ${commission.currency === 'SAR' ? 'selected' : ''}>ريال سعودي (SAR)</option>
                                    <option value="USD" ${commission.currency === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                                    <option value="AED" ${commission.currency === 'AED' ? 'selected' : ''}>درهم إماراتي (AED)</option>
                                    <option value="KWD" ${commission.currency === 'KWD' ? 'selected' : ''}>دينار كويتي (KWD)</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الحالة:</label>
                                <select id="editStatus" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                                    <option value="محسوب" ${commission.status === 'محسوب' ? 'selected' : ''}>محسوب</option>
                                    <option value="مسدد" ${commission.status === 'مسدد' ? 'selected' : ''}>مسدد</option>
                                    <option value="معلق" ${commission.status === 'معلق' ? 'selected' : ''}>معلق</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">تاريخ السداد:</label>
                                <input type="date" id="editPaidDate" value="${commission.paidDate || ''}"
                                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px;">
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">ملاحظات:</label>
                                <textarea id="editNotes" rows="3"
                                          style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; resize: vertical;">${commission.notes || ''}</textarea>
                            </div>

                            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                                <button type="button" onclick="closeEditCommissionModal()"
                                        style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">إلغاء</button>
                                <button type="submit"
                                        style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 8px; cursor: pointer;">حفظ التعديلات</button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);

                // حساب إجمالي العمولة تلقائياً عند تغيير عدد الشحنات أو معدل العمولة
                const deliveriesInput = document.getElementById('editTotalDeliveries');
                const rateInput = document.getElementById('editCommissionRate');
                const totalInput = document.getElementById('editTotalAmount');

                function calculateTotal() {
                    const deliveries = parseFloat(deliveriesInput.value) || 0;
                    const rate = parseFloat(rateInput.value) || 0;
                    totalInput.value = (deliveries * rate).toFixed(2);
                }

                deliveriesInput.addEventListener('input', calculateTotal);
                rateInput.addEventListener('input', calculateTotal);

                // معالجة النموذج
                document.getElementById('editCommissionForm').addEventListener('submit', function(e) {
                    e.preventDefault();

                    const updatedCommission = {
                        distributorName: document.getElementById('editDistributorName').value,
                        period: document.getElementById('editPeriod').value,
                        totalDeliveries: parseInt(document.getElementById('editTotalDeliveries').value),
                        commissionRate: parseFloat(document.getElementById('editCommissionRate').value),
                        totalAmount: parseFloat(document.getElementById('editTotalAmount').value),
                        currency: document.getElementById('editCurrency').value,
                        status: document.getElementById('editStatus').value,
                        paidDate: document.getElementById('editPaidDate').value || null,
                        notes: document.getElementById('editNotes').value,
                        updatedAt: new Date().toISOString()
                    };

                    const result = financialDb.updateCommission(id, updatedCommission);
                    if (result) {
                        alert('تم تحديث بيانات العمولة بنجاح!');
                        loadCommissions();
                        loadStats();
                        closeEditCommissionModal();
                    } else {
                        alert('خطأ في تحديث بيانات العمولة');
                    }
                });

                window.closeEditCommissionModal = function() {
                    document.body.removeChild(modal);
                };

            } catch (error) {
                console.error('❌ خطأ في تعديل العمولة:', error);
                alert('خطأ في تعديل العمولة: ' + error.message);
            }
        }

        // دوال مساعدة
        function getStatusClass(status) {
            const statusMap = {
                'محسوب': 'calculated',
                'مسدد': 'paid',
                'معلق': 'pending'
            };
            return statusMap[status] || 'pending';
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }
    </script>
</body>
</html>