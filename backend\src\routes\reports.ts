// Reports Routes
// مسارات التقارير

import { Router } from 'express'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { asyncHandler } from '../middleware/errorHandler'
import { authenticate, requireManagerOrAdmin } from '../middleware/auth'

const router = Router()
const prisma = new PrismaClient()

// Validation schemas
const dateRangeSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
})

/**
 * @swagger
 * /api/reports/dashboard:
 *   get:
 *     summary: Get dashboard statistics
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 */
router.get('/dashboard', authenticate, asyncHandler(async (req, res) => {
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfYear = new Date(now.getFullYear(), 0, 1)

  // Get basic counts
  const [
    totalShipments,
    pendingShipments,
    inTransitShipments,
    deliveredShipments,
    totalCustomers,
    totalDistributors,
    monthlyShipments,
    yearlyShipments,
  ] = await Promise.all([
    prisma.shipment.count(),
    prisma.shipment.count({ where: { status: 'PENDING' } }),
    prisma.shipment.count({ where: { status: 'IN_TRANSIT' } }),
    prisma.shipment.count({ where: { status: 'DELIVERED' } }),
    prisma.customer.count(),
    prisma.distributor.count(),
    prisma.shipment.count({
      where: {
        createdAt: { gte: startOfMonth },
      },
    }),
    prisma.shipment.count({
      where: {
        createdAt: { gte: startOfYear },
      },
    }),
  ])

  // Get revenue data
  const revenueData = await prisma.shipment.aggregate({
    _sum: { cost: true },
    where: { status: 'DELIVERED' },
  })

  const monthlyRevenue = await prisma.shipment.aggregate({
    _sum: { cost: true },
    where: {
      status: 'DELIVERED',
      createdAt: { gte: startOfMonth },
    },
  })

  // Get recent shipments
  const recentShipments = await prisma.shipment.findMany({
    take: 10,
    orderBy: { createdAt: 'desc' },
    include: {
      sender: { select: { name: true } },
      receiver: { select: { name: true } },
      distributor: { select: { name: true } },
      currency: { select: { code: true, symbol: true } },
    },
  })

  // Get top distributors
  const topDistributors = await prisma.distributor.findMany({
    take: 5,
    orderBy: { totalDeliveries: 'desc' },
    select: {
      id: true,
      name: true,
      totalDeliveries: true,
      rating: true,
    },
  })

  res.json({
    success: true,
    data: {
      overview: {
        totalShipments,
        pendingShipments,
        inTransitShipments,
        deliveredShipments,
        totalCustomers,
        totalDistributors,
        totalRevenue: revenueData._sum.cost || 0,
        monthlyRevenue: monthlyRevenue._sum.cost || 0,
      },
      trends: {
        monthlyShipments,
        yearlyShipments,
      },
      recentShipments,
      topDistributors,
    },
  })
}))

/**
 * @swagger
 * /api/reports/shipments:
 *   get:
 *     summary: Get shipments report
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *     responses:
 *       200:
 *         description: Shipments report retrieved successfully
 */
router.get('/shipments', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const { startDate, endDate } = dateRangeSchema.parse(req.query)
  
  const where: any = {}
  if (startDate || endDate) {
    where.createdAt = {}
    if (startDate) where.createdAt.gte = new Date(startDate)
    if (endDate) where.createdAt.lte = new Date(endDate)
  }

  // Get shipments by status
  const shipmentsByStatus = await prisma.shipment.groupBy({
    by: ['status'],
    where,
    _count: { status: true },
    _sum: { cost: true },
  })

  // Get shipments by month
  const shipmentsByMonth = await prisma.$queryRaw`
    SELECT 
      DATE_TRUNC('month', "createdAt") as month,
      COUNT(*)::int as count,
      SUM(cost)::float as revenue
    FROM "shipments"
    ${startDate || endDate ? 'WHERE' : ''} 
    ${startDate ? `"createdAt" >= ${startDate}` : ''}
    ${startDate && endDate ? 'AND' : ''}
    ${endDate ? `"createdAt" <= ${endDate}` : ''}
    GROUP BY DATE_TRUNC('month', "createdAt")
    ORDER BY month DESC
    LIMIT 12
  `

  // Get top customers by shipment count
  const topCustomers = await prisma.customer.findMany({
    take: 10,
    include: {
      _count: {
        select: {
          sentShipments: true,
          receivedShipments: true,
        },
      },
    },
    orderBy: {
      sentShipments: {
        _count: 'desc',
      },
    },
  })

  res.json({
    success: true,
    data: {
      summary: {
        totalShipments: shipmentsByStatus.reduce((sum, item) => sum + item._count.status, 0),
        totalRevenue: shipmentsByStatus.reduce((sum, item) => sum + (item._sum.cost || 0), 0),
      },
      shipmentsByStatus,
      shipmentsByMonth,
      topCustomers,
    },
  })
}))

/**
 * @swagger
 * /api/reports/distributors:
 *   get:
 *     summary: Get distributors performance report
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Distributors report retrieved successfully
 */
router.get('/distributors', authenticate, requireManagerOrAdmin, asyncHandler(async (req, res) => {
  const { startDate, endDate } = dateRangeSchema.parse(req.query)
  
  const where: any = {}
  if (startDate || endDate) {
    where.createdAt = {}
    if (startDate) where.createdAt.gte = new Date(startDate)
    if (endDate) where.createdAt.lte = new Date(endDate)
  }

  // Get distributor performance
  const distributorPerformance = await prisma.distributor.findMany({
    include: {
      _count: {
        select: {
          shipments: true,
        },
      },
      shipments: {
        where: {
          ...where,
          status: 'DELIVERED',
        },
        select: {
          id: true,
          cost: true,
          actualDelivery: true,
          estimatedDelivery: true,
        },
      },
    },
    orderBy: {
      totalDeliveries: 'desc',
    },
  })

  // Calculate performance metrics
  const performanceMetrics = distributorPerformance.map(distributor => {
    const deliveredShipments = distributor.shipments
    const totalRevenue = deliveredShipments.reduce((sum, shipment) => sum + shipment.cost, 0)
    
    // Calculate on-time delivery rate
    const onTimeDeliveries = deliveredShipments.filter(shipment => 
      shipment.actualDelivery && shipment.estimatedDelivery &&
      new Date(shipment.actualDelivery) <= new Date(shipment.estimatedDelivery)
    ).length
    
    const onTimeRate = deliveredShipments.length > 0 
      ? (onTimeDeliveries / deliveredShipments.length) * 100 
      : 0

    return {
      id: distributor.id,
      name: distributor.name,
      totalDeliveries: distributor.totalDeliveries,
      currentPeriodDeliveries: deliveredShipments.length,
      totalRevenue,
      rating: distributor.rating,
      onTimeDeliveryRate: Math.round(onTimeRate * 100) / 100,
      isAvailable: distributor.isAvailable,
    }
  })

  res.json({
    success: true,
    data: {
      distributors: performanceMetrics,
      summary: {
        totalDistributors: distributorPerformance.length,
        activeDistributors: distributorPerformance.filter(d => d.isAvailable).length,
        averageRating: distributorPerformance.reduce((sum, d) => sum + d.rating, 0) / distributorPerformance.length || 0,
      },
    },
  })
}))

export default router
