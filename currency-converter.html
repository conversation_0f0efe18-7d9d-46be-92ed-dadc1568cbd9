<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول العملات - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: var(--font-arabic-display);
            background: var(--bg-secondary);
            padding: var(--space-6);
        }

        .converter-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
        }

        .converter-section {
            margin-bottom: var(--space-6);
            padding: var(--space-4);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
        }

        .currency-input {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--space-3);
            align-items: end;
            margin-bottom: var(--space-4);
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .form-label {
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .form-input {
            padding: var(--space-3);
            border: 2px solid var(--border-light);
            border-radius: var(--radius-lg);
            font-family: var(--font-arabic-display);
            font-size: 1.1rem;
            transition: border-color var(--transition-fast);
        }

        .form-input:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .currency-select {
            min-width: 200px;
        }

        .amount-input {
            font-size: 1.2rem;
            text-align: center;
        }

        .convert-btn {
            background: var(--color-primary);
            color: white;
            padding: var(--space-3) var(--space-5);
            border: none;
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: var(--font-weight-medium);
            font-size: 1.1rem;
            transition: all var(--transition-fast);
            width: 100%;
            margin: var(--space-3) 0;
        }

        .convert-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .result-display {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: var(--space-5);
            border-radius: var(--radius-lg);
            text-align: center;
            margin: var(--space-4) 0;
            font-size: 1.3rem;
            font-weight: var(--font-weight-semibold);
            box-shadow: var(--shadow-md);
        }

        .exchange-rate {
            background: var(--bg-tertiary);
            padding: var(--space-3);
            border-radius: var(--radius-md);
            margin: var(--space-3) 0;
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .currency-flag {
            font-size: 1.5rem;
            margin-left: var(--space-2);
        }

        .rates-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--space-4);
        }

        .rates-table th,
        .rates-table td {
            padding: var(--space-3);
            text-align: center;
            border-bottom: 1px solid var(--border-light);
        }

        .rates-table th {
            background: var(--bg-tertiary);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .rates-table tr:hover {
            background: var(--bg-tertiary);
        }

        .back-link {
            position: fixed;
            top: var(--space-4);
            right: var(--space-4);
            background: var(--color-primary);
            color: white;
            padding: var(--space-3) var(--space-5);
            border-radius: var(--radius-lg);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
        }

        .swap-btn {
            background: var(--color-warning);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all var(--transition-fast);
            align-self: center;
        }

        .swap-btn:hover {
            transform: rotate(180deg);
        }

        @media (max-width: 768px) {
            .currency-input {
                grid-template-columns: 1fr;
                gap: var(--space-4);
            }
            
            .swap-btn {
                justify-self: center;
            }
        }
    </style>
</head>
<body>
    <a href="main-dashboard.html" class="back-link">← العودة للرئيسية</a>
    
    <div class="converter-container">
        <h1>💱 محول العملات</h1>
        <p>تحويل بين العملات المدعومة في النظام</p>
        
        <div class="converter-section">
            <h2>🔄 تحويل العملات</h2>
            
            <div class="currency-input">
                <div class="form-group">
                    <label class="form-label">من العملة:</label>
                    <select class="form-input currency-select" id="fromCurrency">
                        <option value="SAR">🇸🇦 ريال سعودي (SAR)</option>
                        <option value="KWD">🇰🇼 دينار كويتي (KWD)</option>
                        <option value="USD">🇺🇸 دولار أمريكي (USD)</option>
                        <option value="EUR">🇪🇺 يورو (EUR)</option>
                    </select>
                </div>
                
                <button class="swap-btn" onclick="swapCurrencies()" title="تبديل العملات">⇄</button>
                
                <div class="form-group">
                    <label class="form-label">إلى العملة:</label>
                    <select class="form-input currency-select" id="toCurrency">
                        <option value="KWD">🇰🇼 دينار كويتي (KWD)</option>
                        <option value="SAR">🇸🇦 ريال سعودي (SAR)</option>
                        <option value="USD">🇺🇸 دولار أمريكي (USD)</option>
                        <option value="EUR">🇪🇺 يورو (EUR)</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">المبلغ:</label>
                <input type="number" class="form-input amount-input" id="amount" placeholder="أدخل المبلغ..." step="0.001" value="100">
            </div>
            
            <button class="convert-btn" onclick="convertCurrency()">💱 تحويل</button>
            
            <div class="result-display" id="result" style="display: none;">
                النتيجة ستظهر هنا
            </div>
            
            <div class="exchange-rate" id="exchangeRate" style="display: none;">
                سعر الصرف ستظهر هنا
            </div>
        </div>

        <div class="converter-section">
            <h2>📊 أسعار الصرف الحالية</h2>
            <p><small>* الأسعار تقريبية وقد تختلف عن الأسعار الفعلية في السوق</small></p>
            
            <table class="rates-table">
                <thead>
                    <tr>
                        <th>العملة</th>
                        <th>الرمز</th>
                        <th>مقابل 1 دينار كويتي</th>
                        <th>مقابل 1 ريال سعودي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>🇰🇼 دينار كويتي</td>
                        <td>KWD</td>
                        <td>1.000</td>
                        <td>0.081</td>
                    </tr>
                    <tr>
                        <td>🇸🇦 ريال سعودي</td>
                        <td>SAR</td>
                        <td>12.30</td>
                        <td>1.000</td>
                    </tr>
                    <tr>
                        <td>🇺🇸 دولار أمريكي</td>
                        <td>USD</td>
                        <td>3.28</td>
                        <td>0.267</td>
                    </tr>
                    <tr>
                        <td>🇪🇺 يورو</td>
                        <td>EUR</td>
                        <td>3.01</td>
                        <td>0.245</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="converter-section">
            <h2>💡 معلومات مفيدة</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-4);">
                <div style="background: var(--bg-tertiary); padding: var(--space-3); border-radius: var(--radius-md);">
                    <h4>🇰🇼 الدينار الكويتي</h4>
                    <p>العملة الرسمية لدولة الكويت، ويعتبر من أقوى العملات في العالم.</p>
                </div>
                <div style="background: var(--bg-tertiary); padding: var(--space-3); border-radius: var(--radius-md);">
                    <h4>🇸🇦 الريال السعودي</h4>
                    <p>العملة الرسمية للمملكة العربية السعودية، مربوط بالدولار الأمريكي.</p>
                </div>
                <div style="background: var(--bg-tertiary); padding: var(--space-3); border-radius: var(--radius-md);">
                    <h4>💱 أسعار الصرف</h4>
                    <p>الأسعار المعروضة تقريبية وتستخدم لأغراض الحساب في النظام فقط.</p>
                </div>
                <div style="background: var(--bg-tertiary); padding: var(--space-3); border-radius: var(--radius-md);">
                    <h4>🔄 التحديث</h4>
                    <p>يتم تحديث أسعار الصرف بشكل دوري لضمان دقة الحسابات.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // أسعار الصرف التقريبية (مقابل 1 دينار كويتي)
        const exchangeRates = {
            'KWD': {
                'SAR': 12.30,
                'USD': 3.28,
                'EUR': 3.01,
                'KWD': 1.00
            },
            'SAR': {
                'KWD': 0.081,
                'USD': 0.267,
                'EUR': 0.245,
                'SAR': 1.00
            },
            'USD': {
                'KWD': 0.305,
                'SAR': 3.75,
                'EUR': 0.92,
                'USD': 1.00
            },
            'EUR': {
                'KWD': 0.332,
                'SAR': 4.08,
                'USD': 1.09,
                'EUR': 1.00
            }
        };

        // أسماء العملات
        const currencyNames = {
            'KWD': 'دينار كويتي',
            'SAR': 'ريال سعودي',
            'USD': 'دولار أمريكي',
            'EUR': 'يورو'
        };

        // رموز العملات
        const currencySymbols = {
            'KWD': 'د.ك',
            'SAR': 'ر.س',
            'USD': '$',
            'EUR': '€'
        };

        // أعلام الدول
        const currencyFlags = {
            'KWD': '🇰🇼',
            'SAR': '🇸🇦',
            'USD': '🇺🇸',
            'EUR': '🇪🇺'
        };

        // تحويل العملة
        function convertCurrency() {
            const fromCurrency = document.getElementById('fromCurrency').value;
            const toCurrency = document.getElementById('toCurrency').value;
            const amount = parseFloat(document.getElementById('amount').value);
            
            if (!amount || amount <= 0) {
                alert('يرجى إدخال مبلغ صحيح');
                return;
            }
            
            const rate = exchangeRates[fromCurrency][toCurrency];
            const result = amount * rate;
            
            // عرض النتيجة
            const resultDiv = document.getElementById('result');
            const exchangeRateDiv = document.getElementById('exchangeRate');
            
            resultDiv.innerHTML = 
                currencyFlags[fromCurrency] + ' ' + amount.toFixed(3) + ' ' + currencySymbols[fromCurrency] + 
                ' = ' + 
                currencyFlags[toCurrency] + ' ' + result.toFixed(3) + ' ' + currencySymbols[toCurrency];
            
            exchangeRateDiv.innerHTML = 
                'سعر الصرف: 1 ' + currencyNames[fromCurrency] + ' = ' + rate.toFixed(3) + ' ' + currencyNames[toCurrency];
            
            resultDiv.style.display = 'block';
            exchangeRateDiv.style.display = 'block';
        }

        // تبديل العملات
        function swapCurrencies() {
            const fromCurrency = document.getElementById('fromCurrency');
            const toCurrency = document.getElementById('toCurrency');
            
            const temp = fromCurrency.value;
            fromCurrency.value = toCurrency.value;
            toCurrency.value = temp;
            
            // إعادة التحويل إذا كان هناك مبلغ
            const amount = document.getElementById('amount').value;
            if (amount && amount > 0) {
                convertCurrency();
            }
        }

        // تحويل تلقائي عند تغيير القيم
        document.getElementById('amount').addEventListener('input', function() {
            const amount = this.value;
            if (amount && amount > 0) {
                convertCurrency();
            }
        });

        document.getElementById('fromCurrency').addEventListener('change', function() {
            const amount = document.getElementById('amount').value;
            if (amount && amount > 0) {
                convertCurrency();
            }
        });

        document.getElementById('toCurrency').addEventListener('change', function() {
            const amount = document.getElementById('amount').value;
            if (amount && amount > 0) {
                convertCurrency();
            }
        });

        // تحويل تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            convertCurrency();
        });
    </script>
</body>
</html>
