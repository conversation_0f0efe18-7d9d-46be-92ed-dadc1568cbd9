<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء الجديدة</title>
    <link rel="stylesheet" href="css/sf-pro-arabic.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', sans-serif !important;
            font-weight: 600 !important;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
            color: white;
            text-decoration: none;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #6c757d;
            font-size: 1.2rem;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .data-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
            margin-left: 15px;
        }

        .card-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .card-details {
            margin: 15px 0;
        }

        .card-detail {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: auto;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            border-color: #007bff;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .back-btn {
                justify-content: center;
                width: 100%;
            }

            .header h1 {
                font-size: 2rem;
            }

            .tabs-nav {
                flex-direction: column;
            }
            
            .tab-button {
                flex-direction: row;
                justify-content: center;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <a href="main-dashboard.html" class="back-btn" title="العودة لوحة التحكم">
                    <span>🏠</span>
                    <span>العودة للوحة التحكم</span>
                    <span>←</span>
                </a>
                <div class="header-actions">
                    <button class="btn btn-info btn-sm" onclick="window.location.reload()">
                        <span>🔄</span> تحديث الصفحة
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportAllCustomers()">
                        <span>📥</span> تصدير جميع العملاء
                    </button>
                </div>
            </div>
            <h1>👤 إدارة العملاء الجديدة</h1>
            <p>نظام متطور لإدارة قاعدة بيانات العملاء وتتبع معاملاتهم</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navigation Tabs -->
            <div class="tabs-nav">
                <button class="tab-button active" onclick="showTab('all-customers')">
                    <span>👥</span>
                    <span>جميع العملاء</span>
                </button>
                <button class="tab-button" onclick="showTab('active-customers')">
                    <span>✅</span>
                    <span>العملاء النشطين</span>
                </button>
                <button class="tab-button" onclick="showTab('vip-customers')">
                    <span>⭐</span>
                    <span>العملاء المميزين</span>
                </button>
                <button class="tab-button" onclick="showTab('customer-analytics')">
                    <span>📊</span>
                    <span>تحليلات العملاء</span>
                </button>
            </div>

            <!-- Tab Contents -->
            
            <!-- جميع العملاء -->
            <div id="all-customers" class="tab-content active">
                <div class="section-header">
                    <h2 class="section-title">👥 جميع العملاء</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="addCustomer()">
                            <span>➕</span> إضافة عميل جديد
                        </button>
                        <button class="btn btn-info" onclick="refreshCustomers()">
                            <span>🔄</span> تحديث
                        </button>
                        <button class="btn btn-success" onclick="exportCustomers()">
                            <span>📤</span> تصدير
                        </button>
                    </div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalCustomers">0</div>
                        <div class="stat-label">إجمالي العملاء</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeCustomers">0</div>
                        <div class="stat-label">العملاء النشطين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="vipCustomers">0</div>
                        <div class="stat-label">العملاء المميزين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="newCustomers">0</div>
                        <div class="stat-label">عملاء جدد هذا الشهر</div>
                    </div>
                </div>
                
                <div id="allCustomersGrid" class="data-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">👤</div>
                        <h3>لا توجد عملاء</h3>
                        <p>ابدأ بإضافة عميل جديد</p>
                        <button class="btn btn-primary" onclick="addCustomer()">➕ إضافة عميل</button>
                    </div>
                </div>
            </div>

            <!-- العملاء النشطين -->
            <div id="active-customers" class="tab-content">
                <div class="section-header">
                    <h2 class="section-title">✅ العملاء النشطين</h2>
                    <div class="section-actions">
                        <button class="btn btn-warning" onclick="sendNotifications()">
                            <span>📧</span> إرسال إشعارات
                        </button>
                        <button class="btn btn-info" onclick="refreshActiveCustomers()">
                            <span>🔄</span> تحديث
                        </button>
                    </div>
                </div>
                
                <div id="activeCustomersGrid" class="data-grid">
                    <div class="empty-state">
                        <div class="empty-state-icon">✅</div>
                        <h3>لا توجد عملاء نشطين</h3>
                        <p>لا يوجد عملاء نشطين حالياً</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/sf-pro-arabic-font.js"></script>
    <script>
        // متغيرات عامة
        let customers = [];

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('👤 تحميل نظام إدارة العملاء الجديدة...');
            initializeCustomers();
        });

        // تهيئة النظام
        function initializeCustomers() {
            loadCustomers();
            console.log('✅ تم تحميل نظام إدارة العملاء بنجاح');
        }

        // تحميل البيانات الافتراضية
        function loadDefaultCustomers() {
            if (!localStorage.getItem('new_customers')) {
                const defaultCustomers = [
                    {
                        id: 'CU001',
                        name: 'أحمد محمد علي',
                        phone: '0501234567',
                        email: '<EMAIL>',
                        address: 'الرياض - حي النخيل',
                        type: 'مميز',
                        totalOrders: 15,
                        totalSpent: 2500,
                        joinDate: '2023-01-15',
                        status: 'نشط',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'CU002',
                        name: 'فاطمة سعد الدين',
                        phone: '0507654321',
                        email: '<EMAIL>',
                        address: 'جدة - حي الصفا',
                        type: 'عادي',
                        totalOrders: 8,
                        totalSpent: 1200,
                        joinDate: '2023-03-10',
                        status: 'نشط',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'CU003',
                        name: 'محمد عبدالله',
                        phone: '0509876543',
                        email: '<EMAIL>',
                        address: 'الدمام - حي الشاطئ',
                        type: 'مميز',
                        totalOrders: 22,
                        totalSpent: 3800,
                        joinDate: '2022-11-20',
                        status: 'نشط',
                        createdAt: new Date().toISOString()
                    }
                ];
                localStorage.setItem('new_customers', JSON.stringify(defaultCustomers));
                console.log('✅ تم إنشاء بيانات العملاء الافتراضية');
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalCustomersEl = document.getElementById('totalCustomers');
            const activeCustomersEl = document.getElementById('activeCustomers');
            const vipCustomersEl = document.getElementById('vipCustomers');
            const newCustomersEl = document.getElementById('newCustomers');

            if (totalCustomersEl) totalCustomersEl.textContent = customers.length;
            if (activeCustomersEl) activeCustomersEl.textContent = customers.filter(c => c.status === 'نشط').length;
            if (vipCustomersEl) vipCustomersEl.textContent = customers.filter(c => c.type === 'مميز').length;
            if (newCustomersEl) newCustomersEl.textContent = customers.filter(c => {
                const joinDate = new Date(c.joinDate);
                const now = new Date();
                const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1);
                return joinDate >= monthAgo;
            }).length;
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = event.target.closest('.tab-button');
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            console.log(`📋 تم التبديل إلى تبويب: ${tabName}`);
        }

        // وظائف العملاء
        function addCustomer() {
            // إنشاء نموذج إضافة عميل
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 700px;">
                    <div class="modal-header">
                        <h2>👤 إضافة عميل جديد</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="addCustomerForm" style="text-align: right; direction: rtl;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <div class="form-group">
                                    <label for="customerName">الاسم الكامل *</label>
                                    <input type="text" id="customerName" required placeholder="أدخل الاسم الكامل">
                                </div>
                                <div class="form-group">
                                    <label for="customerPhone">رقم الهاتف *</label>
                                    <input type="tel" id="customerPhone" required placeholder="+966501234567">
                                </div>
                                <div class="form-group">
                                    <label for="customerEmail">البريد الإلكتروني</label>
                                    <input type="email" id="customerEmail" placeholder="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label for="customerType">نوع العميل</label>
                                    <select id="customerType">
                                        <option value="فرد">فرد</option>
                                        <option value="شركة">شركة</option>
                                        <option value="مؤسسة">مؤسسة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top: 15px;">
                                <label for="customerAddress">العنوان *</label>
                                <textarea id="customerAddress" rows="3" required placeholder="أدخل العنوان الكامل"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="customerNotes">ملاحظات</label>
                                <textarea id="customerNotes" rows="2" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <button type="submit" class="btn btn-primary" style="margin-left: 10px;">✅ إضافة العميل</button>
                                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">❌ إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // معالج إرسال النموذج
            document.getElementById('addCustomerForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const newCustomer = {
                    id: 'CUS' + String(Date.now()).slice(-6),
                    name: document.getElementById('customerName').value,
                    phone: document.getElementById('customerPhone').value,
                    email: document.getElementById('customerEmail').value || '',
                    address: document.getElementById('customerAddress').value,
                    type: document.getElementById('customerType').value,
                    notes: document.getElementById('customerNotes').value || '',
                    status: 'نشط',
                    totalOrders: 0,
                    totalSpent: 0,
                    joinDate: new Date().toISOString().split('T')[0],
                    createdAt: new Date().toISOString()
                };

                // إضافة للقائمة
                customers.push(newCustomer);

                // حفظ في التخزين المحلي
                localStorage.setItem('new_customers', JSON.stringify(customers));

                // تحديث العرض والإحصائيات
                displayCustomers();

                // إغلاق النموذج
                modal.remove();

                alert('✅ تم إضافة العميل بنجاح!\nرقم العميل: ' + newCustomer.id);
                console.log('➕ تم إضافة عميل جديد:', newCustomer);
            });
        }

        function refreshCustomers() {
            loadCustomers();
            updateStatistics();
            alert('✅ تم تحديث بيانات العملاء!');
        }

        function exportCustomers() {
            if (customers.length === 0) {
                alert('❌ لا توجد عملاء للتصدير');
                return;
            }

            // إنشاء بيانات Excel
            const excelData = [
                ['رقم العميل', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'النوع', 'إجمالي الطلبات', 'إجمالي المبلغ', 'تاريخ الانضمام', 'الحالة']
            ];

            customers.forEach(customer => {
                excelData.push([
                    customer.id || '',
                    customer.name || '',
                    customer.phone || '',
                    customer.email || '',
                    customer.address || '',
                    customer.type || '',
                    customer.totalOrders || 0,
                    customer.totalSpent || 0,
                    customer.joinDate || '',
                    customer.status || ''
                ]);
            });

            // تحويل إلى CSV مع دعم UTF-8
            const csvContent = '\uFEFF' + excelData.map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `العملاء_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            alert('✅ تم تصدير بيانات العملاء بصيغة Excel بنجاح!');
            console.log('✅ تم تصدير بيانات العملاء');
        }

        function exportAllCustomers() {
            exportCustomers();
        }

        function sendNotifications() {
            alert('📧 سيتم إرسال إشعارات للعملاء النشطين...');
            console.log('📧 إرسال إشعارات');
        }

        function refreshActiveCustomers() {
            alert('✅ تم تحديث بيانات العملاء النشطين!');
        }

        function loadCustomers() {
            try {
                customers = JSON.parse(localStorage.getItem('new_customers') || '[]');
                console.log(`👥 تم تحميل ${customers.length} عميل`);

                // إذا لم توجد عملاء، تحميل البيانات الافتراضية
                if (customers.length === 0) {
                    loadDefaultCustomers();
                }

                // عرض العملاء
                displayCustomers();

            } catch (error) {
                console.error('❌ خطأ في تحميل العملاء:', error);
                customers = [];
                displayCustomers();
            }
        }

        // تحميل البيانات الافتراضية
        function loadDefaultCustomers() {
            const defaultCustomers = [
                {
                    id: 'CUS001',
                    name: 'أحمد محمد علي',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    address: 'الرياض - حي النخيل - شارع الملك فهد',
                    type: 'فرد',
                    status: 'نشط',
                    totalOrders: 15,
                    totalSpent: 2250,
                    joinDate: '2024-01-15',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'CUS002',
                    name: 'فاطمة أحمد سعد',
                    phone: '+966509876543',
                    email: '<EMAIL>',
                    address: 'جدة - حي الصفا - طريق المدينة',
                    type: 'شركة',
                    status: 'نشط',
                    totalOrders: 28,
                    totalSpent: 4200,
                    joinDate: '2023-11-20',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'CUS003',
                    name: 'محمد خالد النور',
                    phone: '+966512345678',
                    email: '<EMAIL>',
                    address: 'الدمام - حي الفيصلية - شارع الخليج',
                    type: 'مؤسسة',
                    status: 'نشط',
                    totalOrders: 42,
                    totalSpent: 6300,
                    joinDate: '2023-08-10',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'CUS004',
                    name: 'نورا سعد المطيري',
                    phone: '+966598765432',
                    email: '<EMAIL>',
                    address: 'مكة - حي العزيزية - شارع إبراهيم الخليل',
                    type: 'فرد',
                    status: 'غير نشط',
                    totalOrders: 3,
                    totalSpent: 450,
                    joinDate: '2024-03-05',
                    createdAt: new Date().toISOString()
                }
            ];

            customers = defaultCustomers;
            localStorage.setItem('new_customers', JSON.stringify(customers));
            console.log('✅ تم تحميل البيانات الافتراضية للعملاء');
        }

        // عرض العملاء
        function displayCustomers() {
            const customersGrid = document.getElementById('allCustomersGrid');
            const emptyState = document.querySelector('#allCustomersGrid .empty-state');

            if (!customersGrid) return;

            if (customers.length === 0) {
                // إظهار الحالة الفارغة
                customersGrid.style.display = 'none';
                if (emptyState) emptyState.style.display = 'block';
            } else {
                // إخفاء الحالة الفارغة وإظهار العملاء
                if (emptyState) emptyState.style.display = 'none';
                customersGrid.style.display = 'grid';

                // مسح المحتوى السابق
                customersGrid.innerHTML = '';

                // إضافة بطاقات العملاء
                customers.forEach(customer => {
                    const customerCard = createCustomerCard(customer);
                    customersGrid.appendChild(customerCard);
                });
            }

            // تحديث الإحصائيات
            updateStatistics();
        }

        // إنشاء بطاقة عميل
        function createCustomerCard(customer) {
            const card = document.createElement('div');
            card.className = 'data-card';

            const statusClass = customer.status === 'نشط' ? 'status-active' : 'status-inactive';
            const statusText = customer.status === 'نشط' ? 'نشط' : 'غير نشط';

            card.innerHTML = `
                <div class="card-header">
                    <div class="card-icon">👤</div>
                    <div class="card-info">
                        <h3>${customer.name}</h3>
                        <p>رقم العميل: ${customer.id}</p>
                    </div>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </div>
                <div class="card-details">
                    <div class="card-detail">
                        <span>📱 الهاتف:</span>
                        <span>${customer.phone}</span>
                    </div>
                    <div class="card-detail">
                        <span>📧 البريد:</span>
                        <span>${customer.email || 'غير محدد'}</span>
                    </div>
                    <div class="card-detail">
                        <span>🏠 العنوان:</span>
                        <span>${customer.address}</span>
                    </div>
                    <div class="card-detail">
                        <span>📦 إجمالي الطلبات:</span>
                        <span>${customer.totalOrders || 0}</span>
                    </div>
                    <div class="card-detail">
                        <span>💰 إجمالي المبلغ:</span>
                        <span>${customer.totalSpent || 0} ريال</span>
                    </div>
                    <div class="card-detail">
                        <span>📅 تاريخ الانضمام:</span>
                        <span>${customer.joinDate}</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-sm btn-info" onclick="editCustomer('${customer.id}')">
                        <span>✏️</span> تعديل
                    </button>
                    <button class="btn btn-sm btn-success" onclick="viewCustomer('${customer.id}')">
                        <span>👁️</span> عرض
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteCustomer('${customer.id}')">
                        <span>🗑️</span> حذف
                    </button>
                </div>
            `;

            return card;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalCustomers = customers.length;
            const activeCustomers = customers.filter(c => c.status === 'نشط').length;
            const totalOrders = customers.reduce((sum, c) => sum + (c.totalOrders || 0), 0);
            const totalRevenue = customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0);

            // تحديث الأرقام في الإحصائيات
            const statNumbers = document.querySelectorAll('.stat-number');
            if (statNumbers.length >= 4) {
                statNumbers[0].textContent = totalCustomers;
                statNumbers[1].textContent = activeCustomers;
                statNumbers[2].textContent = totalOrders;
                statNumbers[3].textContent = totalRevenue.toLocaleString() + ' ريال';
            }
        }

        // وظائف إضافية للعملاء
        function editCustomer(customerId) {
            const customer = customers.find(c => c.id === customerId);
            if (!customer) {
                alert('❌ لم يتم العثور على العميل');
                return;
            }
            alert(`✏️ سيتم فتح نموذج تعديل العميل: ${customer.name}`);
        }

        function viewCustomer(customerId) {
            const customer = customers.find(c => c.id === customerId);
            if (!customer) {
                alert('❌ لم يتم العثور على العميل');
                return;
            }
            alert(`👁️ عرض تفاصيل العميل: ${customer.name}\nالهاتف: ${customer.phone}\nالعنوان: ${customer.address}`);
        }

        function deleteCustomer(customerId) {
            const customer = customers.find(c => c.id === customerId);
            if (!customer) {
                alert('❌ لم يتم العثور على العميل');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف العميل: ${customer.name}؟`)) {
                customers = customers.filter(c => c.id !== customerId);
                localStorage.setItem('new_customers', JSON.stringify(customers));
                displayCustomers();
                alert('✅ تم حذف العميل بنجاح');
            }
        }

        // وظائف التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // إزالة الحالة النشطة من جميع الأزرار
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // تفعيل الزر المحدد
            const selectedButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            // تحديث المحتوى حسب التبويب
            if (tabName === 'active-customers') {
                displayActiveCustomers();
            } else {
                displayCustomers();
            }
        }

        // عرض العملاء النشطين
        function displayActiveCustomers() {
            const activeCustomersGrid = document.getElementById('activeCustomersGrid');
            const emptyState = document.querySelector('#activeCustomersGrid .empty-state');

            if (!activeCustomersGrid) return;

            const activeCustomers = customers.filter(c => c.status === 'نشط');

            if (activeCustomers.length === 0) {
                // إظهار الحالة الفارغة
                activeCustomersGrid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">✅</div>
                        <h3>لا توجد عملاء نشطين</h3>
                        <p>لا يوجد عملاء نشطين حالياً</p>
                    </div>
                `;
            } else {
                // مسح المحتوى السابق
                activeCustomersGrid.innerHTML = '';

                // إضافة بطاقات العملاء النشطين
                activeCustomers.forEach(customer => {
                    const customerCard = createCustomerCard(customer);
                    activeCustomersGrid.appendChild(customerCard);
                });
            }
        }

        // جعل الوظائف متاحة عالمياً
        window.addCustomer = addCustomer;
        window.refreshCustomers = refreshCustomers;
        window.exportCustomers = exportCustomers;
        window.exportAllCustomers = exportAllCustomers;
        window.sendNotifications = sendNotifications;
        window.refreshActiveCustomers = refreshActiveCustomers;
        window.showTab = showTab;
        window.editCustomer = editCustomer;
        window.viewCustomer = viewCustomer;
        window.deleteCustomer = deleteCustomer;

        console.log('👤 تم تحميل نظام إدارة العملاء الجديدة بنجاح!');
    </script>
</body>
</html>
