<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح تسجيل الدخول</title>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-arabic-display, 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }

        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-btn {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .test-btn.danger:hover {
            box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3);
        }

        .status-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e3f2fd;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .back-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s;
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }

        .test-list {
            list-style: none;
            padding: 0;
        }

        .test-list li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-list li::before {
            content: "🧪";
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <a href="unified-login.html" class="back-link">← العودة لتسجيل الدخول</a>
    
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 اختبار إصلاح تسجيل الدخول</h1>
            <p>تحقق من إصلاح مشكلة الدخول التلقائي</p>
        </div>

        <div class="test-section">
            <h3>✅ الإصلاحات المطبقة</h3>
            <ul class="test-list">
                <li>منع تسجيل الدخول بحقول فارغة</li>
                <li>التحقق من صحة البيانات قبل تسجيل الدخول</li>
                <li>إضافة حسابات تجريبية واضحة</li>
                <li>تحسين رسائل الخطأ</li>
                <li>إضافة خيار تسجيل الخروج</li>
                <li>منع التحويل التلقائي غير المرغوب</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات مطلوبة</h3>
            <a href="unified-login.html" class="test-btn">
                1. اختبار تسجيل الدخول العادي
            </a>
            <p style="color: #666; font-size: 0.9rem; margin-bottom: 15px;">
                جرب تسجيل الدخول ببيانات صحيحة وخاطئة
            </p>

            <button onclick="testEmptyFields()" class="test-btn">
                2. اختبار الحقول الفارغة
            </button>
            <p style="color: #666; font-size: 0.9rem; margin-bottom: 15px;">
                يجب أن يظهر خطأ عند ترك الحقول فارغة
            </p>

            <button onclick="testWrongCredentials()" class="test-btn">
                3. اختبار بيانات خاطئة
            </button>
            <p style="color: #666; font-size: 0.9rem; margin-bottom: 15px;">
                يجب أن يظهر خطأ مع بيانات غير صحيحة
            </p>
        </div>

        <div class="test-section">
            <h3>📋 الحسابات التجريبية الجديدة</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px;">
                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px;">
                    <strong style="color: #1976d2;">👨‍💼 مدير النظام</strong>
                    <p style="margin: 5px 0; font-size: 0.85rem;">admin / 123456</p>
                </div>
                <div style="background: #e8f5e8; padding: 15px; border-radius: 10px;">
                    <strong style="color: #2e7d32;">👨‍💻 موظف</strong>
                    <p style="margin: 5px 0; font-size: 0.85rem;">employee / 123456</p>
                </div>
                <div style="background: #fff3e0; padding: 15px; border-radius: 10px;">
                    <strong style="color: #f57c00;">👤 عميل</strong>
                    <p style="margin: 5px 0; font-size: 0.85rem;">customer / 123456</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 أدوات التشخيص</h3>
            <button onclick="clearLoginData()" class="test-btn danger">
                🗑️ مسح بيانات تسجيل الدخول
            </button>
            <button onclick="checkLoginStatus()" class="test-btn">
                📊 فحص حالة تسجيل الدخول
            </button>
            
            <div class="status-box" id="statusBox" style="display: none;">
                <h4 style="color: #1976d2; margin-bottom: 10px;">📊 حالة تسجيل الدخول</h4>
                <div id="statusContent"></div>
            </div>
        </div>
    </div>

    <script>
        // اختبار الحقول الفارغة
        function testEmptyFields() {
            alert('🧪 اختبار الحقول الفارغة:\n\n1. اذهب لصفحة تسجيل الدخول\n2. اختر نوع المستخدم\n3. اترك الحقول فارغة واضغط "تسجيل الدخول"\n4. يجب أن يظهر خطأ: "يرجى ملء جميع الحقول المطلوبة"');
            window.open('unified-login.html', '_blank');
        }

        // اختبار بيانات خاطئة
        function testWrongCredentials() {
            alert('🧪 اختبار بيانات خاطئة:\n\n1. اذهب لصفحة تسجيل الدخول\n2. اختر نوع المستخدم\n3. أدخل بيانات خاطئة (مثل: <EMAIL> / wrongpass)\n4. يجب أن يظهر خطأ: "البريد الإلكتروني أو كلمة المرور غير صحيحة"');
            window.open('unified-login.html', '_blank');
        }

        // مسح بيانات تسجيل الدخول
        function clearLoginData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات تسجيل الدخول؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userType');
                localStorage.removeItem('userEmail');
                localStorage.removeItem('userName');
                localStorage.removeItem('userPicture');
                localStorage.removeItem('loginProvider');
                localStorage.removeItem('loginTime');
                
                alert('✅ تم مسح بيانات تسجيل الدخول بنجاح!');
                checkLoginStatus();
            }
        }

        // فحص حالة تسجيل الدخول
        function checkLoginStatus() {
            const statusBox = document.getElementById('statusBox');
            const statusContent = document.getElementById('statusContent');
            
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const userType = localStorage.getItem('userType') || 'غير محدد';
            const userEmail = localStorage.getItem('userEmail') || 'غير متوفر';
            const loginProvider = localStorage.getItem('loginProvider') || 'غير محدد';
            const loginTime = localStorage.getItem('loginTime');

            let statusHTML = `
                <div class="status-item">
                    <span><strong>حالة تسجيل الدخول:</strong></span>
                    <span style="color: ${isLoggedIn ? '#4caf50' : '#f44336'};">${isLoggedIn ? 'مسجل دخول' : 'غير مسجل'}</span>
                </div>
                <div class="status-item">
                    <span><strong>نوع المستخدم:</strong></span>
                    <span>${userType}</span>
                </div>
                <div class="status-item">
                    <span><strong>البريد الإلكتروني:</strong></span>
                    <span>${userEmail}</span>
                </div>
                <div class="status-item">
                    <span><strong>مزود تسجيل الدخول:</strong></span>
                    <span>${loginProvider}</span>
                </div>
            `;

            if (loginTime) {
                const date = new Date(loginTime);
                statusHTML += `
                    <div class="status-item">
                        <span><strong>وقت تسجيل الدخول:</strong></span>
                        <span>${date.toLocaleString('ar-SA')}</span>
                    </div>
                `;
            }

            statusContent.innerHTML = statusHTML;
            statusBox.style.display = 'block';
        }

        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 صفحة اختبار إصلاح تسجيل الدخول');
            console.log('✅ الإصلاحات المطبقة:');
            console.log('  - منع تسجيل الدخول بحقول فارغة');
            console.log('  - التحقق من صحة البيانات');
            console.log('  - تحسين رسائل الخطأ');
            console.log('  - إضافة خيار تسجيل الخروج');
        });
    </script>
</body>
</html>
