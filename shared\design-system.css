/* نظام التصميم لبرنامج إدارة الشحنات */

/* متغيرات الألوان */
:root {
  /* ألوان آبل الأساسية */
  --color-primary: #007AFF;
  --color-primary-dark: #0056CC;
  --color-primary-light: #4DA3FF;
  
  --color-secondary: #34C759;
  --color-secondary-dark: #28A745;
  --color-secondary-light: #5DD97C;
  
  --color-warning: #FF9500;
  --color-warning-dark: #E6850E;
  --color-warning-light: #FFB84D;
  
  --color-error: #FF3B30;
  --color-error-dark: #E6342A;
  --color-error-light: #FF6B62;
  
  /* ألوان رمادية */
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;
  
  /* ألوان الخلفية */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --bg-tertiary: #F3F4F6;
  
  /* ألوان النصوص */
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  --text-inverse: #FFFFFF;
  
  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* نصف القطر */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  
  /* المسافات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #000000;
    --bg-secondary: #1C1C1E;
    --bg-tertiary: #2C2C2E;
    
    --text-primary: #FFFFFF;
    --text-secondary: #AEAEB2;
    --text-tertiary: #8E8E93;
    --text-inverse: #000000;
  }
}

/* فئات مساعدة للألوان */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }

/* أزرار */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-family: var(--font-text);
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--color-secondary-dark);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: white;
}

/* بطاقات */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
}

/* حقول الإدخال */
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-family: var(--font-text);
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* دعم RTL */
[dir="rtl"] .btn {
  text-align: right;
}

[dir="rtl"] .input {
  text-align: right;
}

/* تحسينات للعربية */
.arabic-text {
  font-family: var(--font-arabic);
  line-height: 1.8;
  letter-spacing: 0.02em;
}

/* تحسينات للإنجليزية */
.english-text {
  font-family: var(--font-text);
  line-height: 1.6;
  letter-spacing: -0.01em;
}
