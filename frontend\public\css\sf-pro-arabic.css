/**
 * SF Pro Arabic Display Semibold Font
 * خط SF Pro Arabic Display Semibold
 */

@font-face {
    font-family: 'SF Pro AR Display';
    src: url('../fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
         url('../fonts/SFProARDisplay-Semibold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SF Pro Arabic Display';
    src: url('../fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
         url('../fonts/SFProARDisplay-Semibold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SFProARDisplay-Semibold';
    src: url('../fonts/SFProARDisplay-Semibold.woff2') format('woff2'),
         url('../fonts/SFProARDisplay-Semibold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

/* تطبيق الخط على جميع العناصر */
* {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على الجسم */
body {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* تطبيق الخط على العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على الفقرات */
p, span, div, a, button, input, textarea, select, label {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على الجداول */
table, th, td {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على القوائم */
ul, ol, li {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على النماذج */
form, fieldset, legend {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على العناصر التفاعلية */
.btn, .button, .link, .nav-link, .menu-link, .tab-button {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على البطاقات والحاويات */
.card, .container, .wrapper, .section, .panel {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على الشريط الجانبي والقوائم */
.sidebar, .menu, .nav, .navbar, .header, .footer {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على الرسائل والتنبيهات */
.alert, .notification, .message, .toast {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على النوافذ المنبثقة */
.modal, .popup, .dialog, .overlay {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على عناصر الصلاحيات */
.permission-card, .role-chip, .permission-item, .permission-label {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على عناصر الإعدادات */
.settings-container, .settings-header, .settings-tabs, .tab-content {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على عناصر الاختبار */
.test-section, .test-result, .test-container {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على عناصر الإحصائيات */
.stats-grid, .stat-card, .stat-number, .stat-label {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على iframe والمحتوى المدمج */
iframe {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تطبيق الخط على العناصر المخصصة */
.custom-element, .dynamic-content, .generated-content {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
}

/* تحسين عرض النص */
* {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
}

/* متغيرات CSS للخط */
:root {
    --font-arabic-display: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold';
    --font-weight-semibold: 600;
}

/* فئة مساعدة لتطبيق الخط */
.sf-pro-arabic {
    font-family: 'SF Pro AR Display', 'SF Pro Arabic Display', 'SFProARDisplay-Semibold', var(--font-arabic-display), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 600 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
}
