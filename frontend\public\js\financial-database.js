// قاعدة البيانات المالية
class FinancialDatabase {
    constructor() {
        this.initializeFinancialData();
    }

    // تهيئة البيانات المالية
    initializeFinancialData() {
        // تهيئة عمليات التحصيل
        if (!localStorage.getItem('collections')) {
            const initialCollections = [
                {
                    id: 'COL001',
                    distributorId: 'DIST001',
                    distributorName: 'أحمد محمد',
                    amount: 1250.00,
                    currency: 'SAR',
                    collectionDate: '2024-01-15',
                    status: 'مكتمل',
                    shipments: ['SHP001', 'SHP002'],
                    notes: 'تحصيل شحنات الأسبوع الأول',
                    createdDate: '2024-01-15'
                },
                {
                    id: 'COL002',
                    distributorId: 'DIST002',
                    distributorName: 'سارة أحمد',
                    amount: 850.00,
                    currency: 'SAR',
                    collectionDate: '2024-01-16',
                    status: 'معلق',
                    shipments: ['SHP003'],
                    notes: 'في انتظار التحصيل',
                    createdDate: '2024-01-16'
                }
            ];
            localStorage.setItem('collections', JSON.stringify(initialCollections));
        }

        // تهيئة المدفوعات المؤجلة
        if (!localStorage.getItem('deferredPayments')) {
            const initialDeferredPayments = [
                {
                    id: 'DEF001',
                    shipmentId: 'SHP001',
                    customerId: 'CUST001',
                    customerName: 'أحمد محمد العلي',
                    amount: 500.00,
                    currency: 'SAR',
                    dueDate: '2024-01-20',
                    status: 'معلق',
                    createdDate: '2024-01-15',
                    notes: 'دفع مؤجل لمدة 5 أيام'
                },
                {
                    id: 'DEF002',
                    shipmentId: 'SHP002',
                    customerId: 'CUST002',
                    customerName: 'فاطمة سالم',
                    amount: 750.00,
                    currency: 'SAR',
                    dueDate: '2024-01-18',
                    status: 'متأخر',
                    createdDate: '2024-01-14',
                    notes: 'تأخر في السداد'
                }
            ];
            localStorage.setItem('deferredPayments', JSON.stringify(initialDeferredPayments));
        }

        // تهيئة عمليات الدفع عند الاستلام
        if (!localStorage.getItem('codPayments')) {
            const initialCodPayments = [
                {
                    id: 'COD001',
                    shipmentId: 'SHP003',
                    customerId: 'CUST003',
                    customerName: 'خالد أحمد',
                    amount: 300.00,
                    currency: 'SAR',
                    distributorId: 'DIST001',
                    distributorName: 'أحمد محمد',
                    collectionDate: '2024-01-17',
                    status: 'محصل',
                    createdDate: '2024-01-17',
                    notes: 'تم التحصيل عند التسليم'
                }
            ];
            localStorage.setItem('codPayments', JSON.stringify(initialCodPayments));
        }

        // تهيئة العمولات
        if (!localStorage.getItem('commissions')) {
            const initialCommissions = [
                {
                    id: 'COM001',
                    distributorId: 'DIST001',
                    distributorName: 'أحمد محمد',
                    period: '2024-01',
                    totalDeliveries: 15,
                    commissionRate: 10.00,
                    totalAmount: 150.00,
                    currency: 'SAR',
                    status: 'محسوب',
                    calculatedDate: '2024-01-15',
                    paidDate: null,
                    notes: 'عمولة شهر يناير'
                },
                {
                    id: 'COM002',
                    distributorId: 'DIST002',
                    distributorName: 'سارة أحمد',
                    period: '2024-01',
                    totalDeliveries: 12,
                    commissionRate: 10.00,
                    totalAmount: 120.00,
                    currency: 'SAR',
                    status: 'مسدد',
                    calculatedDate: '2024-01-15',
                    paidDate: '2024-01-16',
                    notes: 'تم السداد'
                }
            ];
            localStorage.setItem('commissions', JSON.stringify(initialCommissions));
        }

        // تهيئة الفواتير
        if (!localStorage.getItem('invoices')) {
            const initialInvoices = [
                {
                    id: 'INV001',
                    invoiceNumber: 'INV-2024-001',
                    customerId: 'CUST001',
                    customerName: 'أحمد محمد العلي',
                    shipments: ['SHP001', 'SHP002'],
                    totalAmount: 1250.00,
                    currency: 'SAR',
                    issueDate: '2024-01-15',
                    dueDate: '2024-01-30',
                    status: 'معلق',
                    paidAmount: 0,
                    remainingAmount: 1250.00,
                    notes: 'فاتورة شحنات متعددة'
                }
            ];
            localStorage.setItem('invoices', JSON.stringify(initialInvoices));
        }
    }

    // === عمليات التحصيل ===
    getAllCollections() {
        return JSON.parse(localStorage.getItem('collections') || '[]');
    }

    addCollection(collectionData) {
        const collections = this.getAllCollections();
        const newCollection = {
            id: 'COL' + String(collections.length + 1).padStart(3, '0'),
            ...collectionData,
            createdDate: new Date().toISOString().split('T')[0]
        };
        collections.push(newCollection);
        localStorage.setItem('collections', JSON.stringify(collections));
        return newCollection;
    }

    updateCollection(id, updatedData) {
        const collections = this.getAllCollections();
        const index = collections.findIndex(c => c.id === id);
        if (index !== -1) {
            collections[index] = { ...collections[index], ...updatedData };
            localStorage.setItem('collections', JSON.stringify(collections));
            return collections[index];
        }
        return null;
    }

    getCollectionById(id) {
        const collections = this.getAllCollections();
        return collections.find(c => c.id === id);
    }

    // === المدفوعات المؤجلة ===
    getAllDeferredPayments() {
        return JSON.parse(localStorage.getItem('deferredPayments') || '[]');
    }

    addDeferredPayment(paymentData) {
        const payments = this.getAllDeferredPayments();
        const newPayment = {
            id: 'DEF' + String(payments.length + 1).padStart(3, '0'),
            ...paymentData,
            createdDate: new Date().toISOString().split('T')[0]
        };
        payments.push(newPayment);
        localStorage.setItem('deferredPayments', JSON.stringify(payments));
        return newPayment;
    }

    updateDeferredPayment(id, updatedData) {
        const payments = this.getAllDeferredPayments();
        const index = payments.findIndex(p => p.id === id);
        if (index !== -1) {
            payments[index] = { ...payments[index], ...updatedData };
            localStorage.setItem('deferredPayments', JSON.stringify(payments));
            return payments[index];
        }
        return null;
    }

    // === الدفع عند الاستلام ===
    getAllCodPayments() {
        return JSON.parse(localStorage.getItem('codPayments') || '[]');
    }

    addCodPayment(paymentData) {
        const payments = this.getAllCodPayments();
        const newPayment = {
            id: 'COD' + String(payments.length + 1).padStart(3, '0'),
            ...paymentData,
            createdDate: new Date().toISOString().split('T')[0]
        };
        payments.push(newPayment);
        localStorage.setItem('codPayments', JSON.stringify(payments));
        return newPayment;
    }

    updateCodPayment(id, updatedData) {
        const payments = this.getAllCodPayments();
        const index = payments.findIndex(p => p.id === id);
        if (index !== -1) {
            payments[index] = { ...payments[index], ...updatedData };
            localStorage.setItem('codPayments', JSON.stringify(payments));
            return payments[index];
        }
        return null;
    }

    // === العمولات ===
    getAllCommissions() {
        return JSON.parse(localStorage.getItem('commissions') || '[]');
    }

    addCommission(commissionData) {
        const commissions = this.getAllCommissions();
        const newCommission = {
            id: 'COM' + String(commissions.length + 1).padStart(3, '0'),
            ...commissionData,
            calculatedDate: new Date().toISOString().split('T')[0]
        };
        commissions.push(newCommission);
        localStorage.setItem('commissions', JSON.stringify(commissions));
        return newCommission;
    }

    updateCommission(id, updatedData) {
        const commissions = this.getAllCommissions();
        const index = commissions.findIndex(c => c.id === id);
        if (index !== -1) {
            commissions[index] = { ...commissions[index], ...updatedData };
            localStorage.setItem('commissions', JSON.stringify(commissions));
            return commissions[index];
        }
        return null;
    }

    // حساب عمولة مندوب
    calculateDistributorCommission(distributorId, period) {
        const shipments = db.getAllShipments();
        const distributor = db.getDistributorById ? db.getDistributorById(distributorId) : null;
        
        if (!distributor) return null;

        // فلترة الشحنات للمندوب والفترة
        const distributorShipments = shipments.filter(s => 
            s.distributorName === distributor.name &&
            s.createdDate && s.createdDate.startsWith(period) &&
            s.status === 'مسلم'
        );

        const totalDeliveries = distributorShipments.length;
        const commissionRate = 10.00; // 10 ريال لكل شحنة
        const totalAmount = totalDeliveries * commissionRate;

        return {
            distributorId,
            distributorName: distributor.name,
            period,
            totalDeliveries,
            commissionRate,
            totalAmount,
            currency: 'SAR',
            status: 'محسوب',
            shipments: distributorShipments.map(s => s.id)
        };
    }

    // === الفواتير ===
    getAllInvoices() {
        return JSON.parse(localStorage.getItem('invoices') || '[]');
    }

    addInvoice(invoiceData) {
        const invoices = this.getAllInvoices();
        const invoiceNumber = 'INV-' + new Date().getFullYear() + '-' + String(invoices.length + 1).padStart(3, '0');
        const newInvoice = {
            id: 'INV' + String(invoices.length + 1).padStart(3, '0'),
            invoiceNumber,
            ...invoiceData,
            issueDate: new Date().toISOString().split('T')[0],
            remainingAmount: invoiceData.totalAmount
        };
        invoices.push(newInvoice);
        localStorage.setItem('invoices', JSON.stringify(invoices));
        return newInvoice;
    }

    updateInvoice(id, updatedData) {
        const invoices = this.getAllInvoices();
        const index = invoices.findIndex(i => i.id === id);
        if (index !== -1) {
            invoices[index] = { ...invoices[index], ...updatedData };
            localStorage.setItem('invoices', JSON.stringify(invoices));
            return invoices[index];
        }
        return null;
    }

    // === تقارير مالية ===
    getFinancialSummary() {
        const collections = this.getAllCollections();
        const deferredPayments = this.getAllDeferredPayments();
        const codPayments = this.getAllCodPayments();
        const commissions = this.getAllCommissions();
        const invoices = this.getAllInvoices();

        return {
            totalCollections: collections.reduce((sum, c) => sum + (parseFloat(c.amount) || 0), 0),
            pendingCollections: collections.filter(c => c.status === 'معلق').length,
            totalDeferredPayments: deferredPayments.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0),
            overdueDeferredPayments: deferredPayments.filter(p => p.status === 'متأخر').length,
            totalCodPayments: codPayments.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0),
            totalCommissions: commissions.reduce((sum, c) => sum + (parseFloat(c.totalAmount) || 0), 0),
            unpaidCommissions: commissions.filter(c => c.status === 'محسوب').length,
            totalInvoices: invoices.reduce((sum, i) => sum + (parseFloat(i.totalAmount) || 0), 0),
            pendingInvoices: invoices.filter(i => i.status === 'معلق').length
        };
    }

    // مسح جميع البيانات المالية
    clearAllFinancialData() {
        localStorage.removeItem('collections');
        localStorage.removeItem('deferredPayments');
        localStorage.removeItem('codPayments');
        localStorage.removeItem('commissions');
        localStorage.removeItem('invoices');
        this.initializeFinancialData();
    }
}

// إنشاء مثيل من قاعدة البيانات المالية
const financialDb = new FinancialDatabase();
