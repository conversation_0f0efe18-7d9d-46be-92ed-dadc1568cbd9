<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الإلغاء - نظام إدارة الشحنات</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: var(--font-arabic-display);
            background: var(--bg-secondary);
            margin: 0;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, #6f42c1 0%, #dc3545 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            background: var(--color-primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: var(--font-arabic-display);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-info {
            background: #17a2b8;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .report-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .report-card:hover {
            transform: translateY(-5px);
        }

        .report-header {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 3px solid;
        }

        .report-header.summary {
            border-bottom-color: #dc3545;
        }

        .report-header.trends {
            border-bottom-color: #6f42c1;
        }

        .report-header.reasons {
            border-bottom-color: #ffc107;
        }

        .report-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .report-content {
            padding: 25px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            font-weight: 500;
            color: #495057;
        }

        .stat-value {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 20px 0;
        }

        .chart-placeholder {
            color: #6c757d;
            font-style: italic;
        }

        .reason-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .reason-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .reason-item:last-child {
            border-bottom: none;
        }

        .reason-name {
            font-weight: 500;
            color: #495057;
            flex: 1;
        }

        .reason-count {
            background: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: bold;
            margin-left: 10px;
        }

        .reason-percentage {
            color: #6c757d;
            font-size: 0.875rem;
        }

        .back-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .filter-input,
        .filter-select {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-family: var(--font-arabic-display);
            min-width: 150px;
        }

        .filter-input:focus,
        .filter-select:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .reports-grid {
                grid-template-columns: 1fr;
            }

            .filters {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                width: 100%;
            }

            .filter-input,
            .filter-select {
                min-width: auto;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <a href="cancellation-management.html" class="back-link">← العودة لإدارة الإلغاء</a>

    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span>📊</span>
                <span>تقارير الإلغاء</span>
            </div>
            
            <nav class="nav-links">
                <a href="main-dashboard.html">لوحة التحكم</a>
                <a href="cancellation-management.html">إدارة الإلغاء</a>
                <a href="cancellation-reports.html" class="active">تقارير الإلغاء</a>
                <a href="reports.html">التقارير العامة</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">📊 تقارير الإلغاء</h1>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="exportReport()">
                    📥 تصدير التقرير
                </button>
                <button class="btn" onclick="refreshReports()">
                    🔄 تحديث التقارير
                </button>
            </div>
        </div>

        <!-- فلاتر التقارير -->
        <div class="filters">
            <div class="filter-group">
                <label class="filter-label">من تاريخ</label>
                <input type="date" class="filter-input" id="fromDate">
            </div>
            
            <div class="filter-group">
                <label class="filter-label">إلى تاريخ</label>
                <input type="date" class="filter-input" id="toDate">
            </div>
            
            <div class="filter-group">
                <label class="filter-label">فئة الإلغاء</label>
                <select class="filter-select" id="categoryFilter">
                    <option value="">جميع الفئات</option>
                    <option value="اسباب عدم التوصيل">أسباب عدم التوصيل</option>
                    <option value="اسباب رفض الشحنات">أسباب رفض الشحنات</option>
                </select>
            </div>
            
            <div class="filter-group">
                <button class="btn btn-info" onclick="applyFilters()">تطبيق الفلاتر</button>
            </div>
        </div>

        <!-- تقارير الإلغاء -->
        <div class="reports-grid">
            <!-- ملخص الإحصائيات -->
            <div class="report-card">
                <div class="report-header summary">
                    <h3 class="report-title">
                        <span>📈</span>
                        ملخص إحصائيات الإلغاء
                    </h3>
                </div>
                <div class="report-content">
                    <div class="stat-item">
                        <span class="stat-label">إجمالي الشحنات الملغية</span>
                        <span class="stat-value" id="totalCancelled">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">نسبة الإلغاء</span>
                        <span class="stat-value" id="cancellationRate">0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">أسباب عدم التوصيل</span>
                        <span class="stat-value" id="deliveryIssues">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">أسباب رفض الشحنات</span>
                        <span class="stat-value" id="rejectionIssues">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">إلغاءات الشهر الحالي</span>
                        <span class="stat-value" id="currentMonthCancellations">0</span>
                    </div>
                </div>
            </div>

            <!-- اتجاهات الإلغاء -->
            <div class="report-card">
                <div class="report-header trends">
                    <h3 class="report-title">
                        <span>📉</span>
                        اتجاهات الإلغاء
                    </h3>
                </div>
                <div class="report-content">
                    <div class="chart-container">
                        <div class="chart-placeholder">
                            📊 سيتم إضافة الرسم البياني قريباً
                        </div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">متوسط الإلغاءات اليومية</span>
                        <span class="stat-value" id="dailyAverage">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">أعلى يوم إلغاءات</span>
                        <span class="stat-value" id="peakDay">-</span>
                    </div>
                </div>
            </div>

            <!-- أهم أسباب الإلغاء -->
            <div class="report-card">
                <div class="report-header reasons">
                    <h3 class="report-title">
                        <span>🔍</span>
                        أهم أسباب الإلغاء
                    </h3>
                </div>
                <div class="report-content">
                    <ul class="reason-list" id="topReasons">
                        <!-- سيتم ملء البيانات هنا -->
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <script src="js/database.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 تحميل صفحة تقارير الإلغاء...');
            
            try {
                // تعيين التواريخ الافتراضية (آخر 30 يوم)
                const today = new Date();
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(today.getDate() - 30);
                
                document.getElementById('fromDate').value = thirtyDaysAgo.toISOString().split('T')[0];
                document.getElementById('toDate').value = today.toISOString().split('T')[0];
                
                refreshReports();
                
                console.log('✅ تم تحميل الصفحة بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                alert('خطأ في تحميل الصفحة: ' + error.message);
            }
        });

        // تحديث التقارير
        function refreshReports() {
            try {
                loadSummaryStats();
                loadTopReasons();
                loadTrends();
                
                console.log('📊 تم تحديث التقارير');
            } catch (error) {
                console.error('❌ خطأ في تحديث التقارير:', error);
            }
        }

        // تحميل ملخص الإحصائيات
        function loadSummaryStats() {
            try {
                const stats = db.getCancellationStats();
                const allShipments = db.getAllShipments();
                
                const totalShipments = allShipments.length;
                const cancellationRate = totalShipments > 0 ? ((stats.totalCancelled / totalShipments) * 100).toFixed(1) : 0;
                
                // إحصائيات الشهر الحالي
                const currentMonth = new Date().toISOString().slice(0, 7);
                const currentMonthCancellations = stats.recentCancellations.filter(shipment => {
                    return shipment.cancellationDate && shipment.cancellationDate.startsWith(currentMonth);
                }).length;
                
                document.getElementById('totalCancelled').textContent = stats.totalCancelled;
                document.getElementById('cancellationRate').textContent = cancellationRate + '%';
                document.getElementById('deliveryIssues').textContent = stats.byCategory['اسباب عدم التوصيل'] || 0;
                document.getElementById('rejectionIssues').textContent = stats.byCategory['اسباب رفض الشحنات'] || 0;
                document.getElementById('currentMonthCancellations').textContent = currentMonthCancellations;
                
            } catch (error) {
                console.error('❌ خطأ في تحميل ملخص الإحصائيات:', error);
            }
        }

        // تحميل أهم أسباب الإلغاء
        function loadTopReasons() {
            try {
                const stats = db.getCancellationStats();
                const reasonsList = document.getElementById('topReasons');
                
                // ترتيب الأسباب حسب عدد الاستخدام
                const sortedReasons = Object.entries(stats.byReason)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 10); // أهم 10 أسباب
                
                const totalCancellations = stats.totalCancelled;
                
                reasonsList.innerHTML = sortedReasons.map(([reason, count]) => {
                    const percentage = totalCancellations > 0 ? ((count / totalCancellations) * 100).toFixed(1) : 0;
                    return `
                        <li class="reason-item">
                            <span class="reason-name">${reason}</span>
                            <div>
                                <span class="reason-count">${count}</span>
                                <span class="reason-percentage">${percentage}%</span>
                            </div>
                        </li>
                    `;
                }).join('');
                
                if (sortedReasons.length === 0) {
                    reasonsList.innerHTML = '<li class="reason-item"><span class="reason-name">لا توجد بيانات</span></li>';
                }
                
            } catch (error) {
                console.error('❌ خطأ في تحميل أهم أسباب الإلغاء:', error);
            }
        }

        // تحميل اتجاهات الإلغاء
        function loadTrends() {
            try {
                const stats = db.getCancellationStats();
                
                // حساب متوسط الإلغاءات اليومية
                const recentCancellations = stats.recentCancellations;
                const dailyAverage = recentCancellations.length > 0 ? (recentCancellations.length / 30).toFixed(1) : 0;
                
                // العثور على أعلى يوم إلغاءات
                const dailyCounts = {};
                recentCancellations.forEach(shipment => {
                    if (shipment.cancellationDate) {
                        const date = shipment.cancellationDate;
                        dailyCounts[date] = (dailyCounts[date] || 0) + 1;
                    }
                });
                
                const peakDay = Object.entries(dailyCounts)
                    .sort(([,a], [,b]) => b - a)[0];
                
                document.getElementById('dailyAverage').textContent = dailyAverage;
                document.getElementById('peakDay').textContent = peakDay ? 
                    `${formatDate(peakDay[0])} (${peakDay[1]} إلغاء)` : 'لا توجد بيانات';
                
            } catch (error) {
                console.error('❌ خطأ في تحميل اتجاهات الإلغاء:', error);
            }
        }

        // تطبيق الفلاتر
        function applyFilters() {
            // سيتم تطبيق الفلاتر هنا
            alert('سيتم إضافة وظيفة الفلاتر قريباً');
        }

        // تصدير التقرير
        function exportReport() {
            try {
                const stats = db.getCancellationStats();
                const reportData = {
                    summary: stats,
                    exportDate: new Date().toISOString(),
                    dateRange: {
                        from: document.getElementById('fromDate').value,
                        to: document.getElementById('toDate').value
                    }
                };
                
                const dataStr = JSON.stringify(reportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `cancellation-report-${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                alert('تم تصدير تقرير الإلغاء بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تصدير التقرير:', error);
                alert('خطأ في تصدير التقرير: ' + error.message);
            }
        }

        // دالة مساعدة لتنسيق التاريخ
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }
    </script>
</body>
</html>
